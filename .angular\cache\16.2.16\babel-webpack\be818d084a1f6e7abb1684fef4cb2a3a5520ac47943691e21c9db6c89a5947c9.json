{"ast": null, "code": "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nimport { FunctionsClient } from '@supabase/functions-js';\nimport { PostgrestClient } from '@supabase/postgrest-js';\nimport { RealtimeClient } from '@supabase/realtime-js';\nimport { StorageClient as SupabaseStorageClient } from '@supabase/storage-js';\nimport { DEFAULT_GLOBAL_OPTIONS, DEFAULT_DB_OPTIONS, DEFAULT_AUTH_OPTIONS, DEFAULT_REALTIME_OPTIONS } from './lib/constants';\nimport { fetchWithAuth } from './lib/fetch';\nimport { ensureTrailingSlash, applySettingDefaults } from './lib/helpers';\nimport { SupabaseAuthClient } from './lib/SupabaseAuthClient';\n/**\n * Supabase Client.\n *\n * An isomorphic Javascript client for interacting with Postgres.\n */\nclass SupabaseClient {\n  /**\n   * Create a new client for use in the browser.\n   * @param supabaseUrl The unique Supabase URL which is supplied when you create a new project in your project dashboard.\n   * @param supabaseKey The unique Supabase Key which is supplied when you create a new project in your project dashboard.\n   * @param options.db.schema You can switch in between schemas. The schema needs to be on the list of exposed schemas inside Supabase.\n   * @param options.auth.autoRefreshToken Set to \"true\" if you want to automatically refresh the token before expiring.\n   * @param options.auth.persistSession Set to \"true\" if you want to automatically save the user session into local storage.\n   * @param options.auth.detectSessionInUrl Set to \"true\" if you want to automatically detects OAuth grants in the URL and signs in the user.\n   * @param options.realtime Options passed along to realtime-js constructor.\n   * @param options.storage Options passed along to the storage-js constructor.\n   * @param options.global.fetch A custom fetch implementation.\n   * @param options.global.headers Any additional headers to send with each network request.\n   */\n  constructor(supabaseUrl, supabaseKey, options) {\n    var _a, _b, _c;\n    this.supabaseUrl = supabaseUrl;\n    this.supabaseKey = supabaseKey;\n    if (!supabaseUrl) throw new Error('supabaseUrl is required.');\n    if (!supabaseKey) throw new Error('supabaseKey is required.');\n    const _supabaseUrl = ensureTrailingSlash(supabaseUrl);\n    const baseUrl = new URL(_supabaseUrl);\n    this.realtimeUrl = new URL('realtime/v1', baseUrl);\n    this.realtimeUrl.protocol = this.realtimeUrl.protocol.replace('http', 'ws');\n    this.authUrl = new URL('auth/v1', baseUrl);\n    this.storageUrl = new URL('storage/v1', baseUrl);\n    this.functionsUrl = new URL('functions/v1', baseUrl);\n    // default storage key uses the supabase project ref as a namespace\n    const defaultStorageKey = `sb-${baseUrl.hostname.split('.')[0]}-auth-token`;\n    const DEFAULTS = {\n      db: DEFAULT_DB_OPTIONS,\n      realtime: DEFAULT_REALTIME_OPTIONS,\n      auth: Object.assign(Object.assign({}, DEFAULT_AUTH_OPTIONS), {\n        storageKey: defaultStorageKey\n      }),\n      global: DEFAULT_GLOBAL_OPTIONS\n    };\n    const settings = applySettingDefaults(options !== null && options !== void 0 ? options : {}, DEFAULTS);\n    this.storageKey = (_a = settings.auth.storageKey) !== null && _a !== void 0 ? _a : '';\n    this.headers = (_b = settings.global.headers) !== null && _b !== void 0 ? _b : {};\n    if (!settings.accessToken) {\n      this.auth = this._initSupabaseAuthClient((_c = settings.auth) !== null && _c !== void 0 ? _c : {}, this.headers, settings.global.fetch);\n    } else {\n      this.accessToken = settings.accessToken;\n      this.auth = new Proxy({}, {\n        get: (_, prop) => {\n          throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(prop)} is not possible`);\n        }\n      });\n    }\n    this.fetch = fetchWithAuth(supabaseKey, this._getAccessToken.bind(this), settings.global.fetch);\n    this.realtime = this._initRealtimeClient(Object.assign({\n      headers: this.headers,\n      accessToken: this._getAccessToken.bind(this)\n    }, settings.realtime));\n    this.rest = new PostgrestClient(new URL('rest/v1', baseUrl).href, {\n      headers: this.headers,\n      schema: settings.db.schema,\n      fetch: this.fetch\n    });\n    this.storage = new SupabaseStorageClient(this.storageUrl.href, this.headers, this.fetch, options === null || options === void 0 ? void 0 : options.storage);\n    if (!settings.accessToken) {\n      this._listenForAuthEvents();\n    }\n  }\n  /**\n   * Supabase Functions allows you to deploy and invoke edge functions.\n   */\n  get functions() {\n    return new FunctionsClient(this.functionsUrl.href, {\n      headers: this.headers,\n      customFetch: this.fetch\n    });\n  }\n  /**\n   * Perform a query on a table or a view.\n   *\n   * @param relation - The table or view name to query\n   */\n  from(relation) {\n    return this.rest.from(relation);\n  }\n  // NOTE: signatures must be kept in sync with PostgrestClient.schema\n  /**\n   * Select a schema to query or perform an function (rpc) call.\n   *\n   * The schema needs to be on the list of exposed schemas inside Supabase.\n   *\n   * @param schema - The schema to query\n   */\n  schema(schema) {\n    return this.rest.schema(schema);\n  }\n  // NOTE: signatures must be kept in sync with PostgrestClient.rpc\n  /**\n   * Perform a function call.\n   *\n   * @param fn - The function name to call\n   * @param args - The arguments to pass to the function call\n   * @param options - Named parameters\n   * @param options.head - When set to `true`, `data` will not be returned.\n   * Useful if you only need the count.\n   * @param options.get - When set to `true`, the function will be called with\n   * read-only access mode.\n   * @param options.count - Count algorithm to use to count rows returned by the\n   * function. Only applicable for [set-returning\n   * functions](https://www.postgresql.org/docs/current/functions-srf.html).\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   */\n  rpc(fn, args = {}, options = {}) {\n    return this.rest.rpc(fn, args, options);\n  }\n  /**\n   * Creates a Realtime channel with Broadcast, Presence, and Postgres Changes.\n   *\n   * @param {string} name - The name of the Realtime channel.\n   * @param {Object} opts - The options to pass to the Realtime channel.\n   *\n   */\n  channel(name, opts = {\n    config: {}\n  }) {\n    return this.realtime.channel(name, opts);\n  }\n  /**\n   * Returns all Realtime channels.\n   */\n  getChannels() {\n    return this.realtime.getChannels();\n  }\n  /**\n   * Unsubscribes and removes Realtime channel from Realtime client.\n   *\n   * @param {RealtimeChannel} channel - The name of the Realtime channel.\n   *\n   */\n  removeChannel(channel) {\n    return this.realtime.removeChannel(channel);\n  }\n  /**\n   * Unsubscribes and removes all Realtime channels from Realtime client.\n   */\n  removeAllChannels() {\n    return this.realtime.removeAllChannels();\n  }\n  _getAccessToken() {\n    var _a, _b;\n    return __awaiter(this, void 0, void 0, function* () {\n      if (this.accessToken) {\n        return yield this.accessToken();\n      }\n      const {\n        data\n      } = yield this.auth.getSession();\n      return (_b = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token) !== null && _b !== void 0 ? _b : null;\n    });\n  }\n  _initSupabaseAuthClient({\n    autoRefreshToken,\n    persistSession,\n    detectSessionInUrl,\n    storage,\n    storageKey,\n    flowType,\n    lock,\n    debug\n  }, headers, fetch) {\n    const authHeaders = {\n      Authorization: `Bearer ${this.supabaseKey}`,\n      apikey: `${this.supabaseKey}`\n    };\n    return new SupabaseAuthClient({\n      url: this.authUrl.href,\n      headers: Object.assign(Object.assign({}, authHeaders), headers),\n      storageKey: storageKey,\n      autoRefreshToken,\n      persistSession,\n      detectSessionInUrl,\n      storage,\n      flowType,\n      lock,\n      debug,\n      fetch,\n      // auth checks if there is a custom authorizaiton header using this flag\n      // so it knows whether to return an error when getUser is called with no session\n      hasCustomAuthorizationHeader: 'Authorization' in this.headers\n    });\n  }\n  _initRealtimeClient(options) {\n    return new RealtimeClient(this.realtimeUrl.href, Object.assign(Object.assign({}, options), {\n      params: Object.assign({\n        apikey: this.supabaseKey\n      }, options === null || options === void 0 ? void 0 : options.params)\n    }));\n  }\n  _listenForAuthEvents() {\n    let data = this.auth.onAuthStateChange((event, session) => {\n      this._handleTokenChanged(event, 'CLIENT', session === null || session === void 0 ? void 0 : session.access_token);\n    });\n    return data;\n  }\n  _handleTokenChanged(event, source, token) {\n    if ((event === 'TOKEN_REFRESHED' || event === 'SIGNED_IN') && this.changedAccessToken !== token) {\n      this.changedAccessToken = token;\n    } else if (event === 'SIGNED_OUT') {\n      this.realtime.setAuth();\n      if (source == 'STORAGE') this.auth.signOut();\n      this.changedAccessToken = undefined;\n    }\n  }\n}\n//# sourceMappingURL=SupabaseClient.js.map\nexport { SupabaseClient as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}