{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/\\u062A\\u0637\\u0628\\u064A\\u0642 \\u0646\\u0627\\u0634\\u0631 \\u0627\\u064A\\u0627\\u062A \\u0642\\u0626\\u0627\\u0646\\u064A\\u0629/QuranVidGen/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { ERROR_RESPONSE_BODY_READER, ERROR_INCOMPLETED_DOWNLOAD } from \"./errors.js\";\nimport { HeaderContentLength } from \"./const.js\";\nconst readFromBlobOrFile = blob => new Promise((resolve, reject) => {\n  const fileReader = new FileReader();\n  fileReader.onload = () => {\n    const {\n      result\n    } = fileReader;\n    if (result instanceof ArrayBuffer) {\n      resolve(new Uint8Array(result));\n    } else {\n      resolve(new Uint8Array());\n    }\n  };\n  fileReader.onerror = event => {\n    reject(Error(`File could not be read! Code=${event?.target?.error?.code || -1}`));\n  };\n  fileReader.readAsArrayBuffer(blob);\n});\n/**\n * An util function to fetch data from url string, base64, URL, File or Blob format.\n *\n * Examples:\n * ```ts\n * // URL\n * await fetchFile(\"http://localhost:3000/video.mp4\");\n * // base64\n * await fetchFile(\"data:<type>;base64,wL2dvYWwgbW9yZ...\");\n * // URL\n * await fetchFile(new URL(\"video.mp4\", import.meta.url));\n * // File\n * fileInput.addEventListener('change', (e) => {\n *   await fetchFile(e.target.files[0]);\n * });\n * // Blob\n * const blob = new Blob(...);\n * await fetchFile(blob);\n * ```\n */\nexport const fetchFile = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (file) {\n    let data;\n    if (typeof file === \"string\") {\n      /* From base64 format */\n      if (/data:_data\\/([a-zA-Z]*);base64,([^\"]*)/.test(file)) {\n        data = atob(file.split(\",\")[1]).split(\"\").map(c => c.charCodeAt(0));\n        /* From remote server/URL */\n      } else {\n        data = yield (yield fetch(file)).arrayBuffer();\n      }\n    } else if (file instanceof URL) {\n      data = yield (yield fetch(file)).arrayBuffer();\n    } else if (file instanceof File || file instanceof Blob) {\n      data = yield readFromBlobOrFile(file);\n    } else {\n      return new Uint8Array();\n    }\n    return new Uint8Array(data);\n  });\n  return function fetchFile(_x) {\n    return _ref.apply(this, arguments);\n  };\n}();\n/**\n * importScript dynamically import a script, useful when you\n * want to use different versions of ffmpeg.wasm based on environment.\n *\n * Example:\n *\n * ```ts\n * await importScript(\"http://localhost:3000/ffmpeg.js\");\n * ```\n */\nexport const importScript = /*#__PURE__*/function () {\n  var _ref2 = _asyncToGenerator(function* (url) {\n    return new Promise(resolve => {\n      const script = document.createElement(\"script\");\n      const eventHandler = () => {\n        script.removeEventListener(\"load\", eventHandler);\n        resolve();\n      };\n      script.src = url;\n      script.type = \"text/javascript\";\n      script.addEventListener(\"load\", eventHandler);\n      document.getElementsByTagName(\"head\")[0].appendChild(script);\n    });\n  });\n  return function importScript(_x2) {\n    return _ref2.apply(this, arguments);\n  };\n}();\n/**\n * Download content of a URL with progress.\n *\n * Progress only works when Content-Length is provided by the server.\n *\n */\nexport const downloadWithProgress = /*#__PURE__*/function () {\n  var _ref3 = _asyncToGenerator(function* (url, cb) {\n    const resp = yield fetch(url);\n    let buf;\n    try {\n      // Set total to -1 to indicate that there is not Content-Type Header.\n      const total = parseInt(resp.headers.get(HeaderContentLength) || \"-1\");\n      const reader = resp.body?.getReader();\n      if (!reader) throw ERROR_RESPONSE_BODY_READER;\n      const chunks = [];\n      let received = 0;\n      for (;;) {\n        const {\n          done,\n          value\n        } = yield reader.read();\n        const delta = value ? value.length : 0;\n        if (done) {\n          if (total != -1 && total !== received) throw ERROR_INCOMPLETED_DOWNLOAD;\n          cb && cb({\n            url,\n            total,\n            received,\n            delta,\n            done\n          });\n          break;\n        }\n        chunks.push(value);\n        received += delta;\n        cb && cb({\n          url,\n          total,\n          received,\n          delta,\n          done\n        });\n      }\n      const data = new Uint8Array(received);\n      let position = 0;\n      for (const chunk of chunks) {\n        data.set(chunk, position);\n        position += chunk.length;\n      }\n      buf = data.buffer;\n    } catch (e) {\n      console.log(`failed to send download progress event: `, e);\n      // Fetch arrayBuffer directly when it is not possible to get progress.\n      buf = yield resp.arrayBuffer();\n      cb && cb({\n        url,\n        total: buf.byteLength,\n        received: buf.byteLength,\n        delta: 0,\n        done: true\n      });\n    }\n    return buf;\n  });\n  return function downloadWithProgress(_x3, _x4) {\n    return _ref3.apply(this, arguments);\n  };\n}();\n/**\n * toBlobURL fetches data from an URL and return a blob URL.\n *\n * Example:\n *\n * ```ts\n * await toBlobURL(\"http://localhost:3000/ffmpeg.js\", \"text/javascript\");\n * ```\n */\nexport const toBlobURL = /*#__PURE__*/function () {\n  var _ref4 = _asyncToGenerator(function* (url, mimeType, progress = false, cb) {\n    const buf = progress ? yield downloadWithProgress(url, cb) : yield (yield fetch(url)).arrayBuffer();\n    const blob = new Blob([buf], {\n      type: mimeType\n    });\n    return URL.createObjectURL(blob);\n  });\n  return function toBlobURL(_x5, _x6) {\n    return _ref4.apply(this, arguments);\n  };\n}();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}