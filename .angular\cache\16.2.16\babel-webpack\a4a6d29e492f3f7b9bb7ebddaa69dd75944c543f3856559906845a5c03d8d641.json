{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./supabase.service\";\nexport let LinkShortenerService = /*#__PURE__*/(() => {\n  class LinkShortenerService {\n    constructor(supabaseService) {\n      this.supabaseService = supabaseService;\n      this.STORAGE_KEY = 'linkShorteningStatus';\n      this.ATTEMPTS_KEY = 'shorteningAttempts';\n      this.SHORTENING_INTERVAL = 24 * 60 * 60 * 1000; // 24 hours\n      this.AD_ACCESS_DURATION = 3 * 60 * 60 * 1000; // 3 hours\n      this.statusSubject = new BehaviorSubject(this.getInitialStatus());\n      this.status$ = this.statusSubject.asObservable();\n      this.shortenerSites = [{\n        id: 'cuty',\n        name: 'Cuty.io',\n        url: 'https://cuty.io',\n        isActive: true,\n        description: 'موقع اختصار روابط سريع وموثوق'\n      }, {\n        id: 'linkjust',\n        name: 'LinkJust.com',\n        url: 'https://linkjust.com',\n        isActive: true,\n        description: 'خدمة اختصار روابط مع عوائد جيدة'\n      }, {\n        id: 'swiftlnx',\n        name: 'SwiftLnx.com',\n        url: 'https://swiftlnx.com',\n        isActive: true,\n        description: 'منصة اختصار روابط سريعة'\n      }];\n      this.currentLinkIndex = 0;\n      this.initializeService();\n      this.startStatusUpdater();\n      this.loadShortenerSitesFromDatabase();\n    }\n    initializeService() {\n      const status = this.getStoredStatus();\n      this.updateStatus(status);\n    }\n    startStatusUpdater() {\n      // Update status every minute\n      setInterval(() => {\n        const currentStatus = this.statusSubject.value;\n        const updatedStatus = this.calculateStatus(currentStatus);\n        if (this.hasStatusChanged(currentStatus, updatedStatus)) {\n          this.updateStatus(updatedStatus);\n        }\n      }, 60000); // Every minute\n    }\n\n    getInitialStatus() {\n      return {\n        isRequired: true,\n        lastShortenedAt: null,\n        timeRemaining: 0,\n        canUseApp: false,\n        hasWatchedAd: false,\n        adWatchedAt: null,\n        adTimeRemaining: 0\n      };\n    }\n    getStoredStatus() {\n      try {\n        const stored = localStorage.getItem(this.STORAGE_KEY);\n        if (stored) {\n          const parsed = JSON.parse(stored);\n          return {\n            ...parsed,\n            lastShortenedAt: parsed.lastShortenedAt ? new Date(parsed.lastShortenedAt) : null,\n            adWatchedAt: parsed.adWatchedAt ? new Date(parsed.adWatchedAt) : null\n          };\n        }\n      } catch (error) {\n        console.error('Error loading stored status:', error);\n      }\n      return this.getInitialStatus();\n    }\n    saveStatus(status) {\n      try {\n        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(status));\n      } catch (error) {\n        console.error('Error saving status:', error);\n      }\n    }\n    calculateStatus(currentStatus) {\n      const now = new Date();\n      let canUseApp = false;\n      let timeRemaining = 0;\n      let adTimeRemaining = 0;\n      // Check if user has shortened a link in the last 24 hours\n      if (currentStatus.lastShortenedAt) {\n        const timeSinceLastShorten = now.getTime() - currentStatus.lastShortenedAt.getTime();\n        timeRemaining = Math.max(0, this.SHORTENING_INTERVAL - timeSinceLastShorten);\n        if (timeRemaining > 0) {\n          canUseApp = true;\n        }\n      }\n      // Check if user has watched an ad in the last 3 hours\n      if (currentStatus.adWatchedAt) {\n        const timeSinceAdWatch = now.getTime() - currentStatus.adWatchedAt.getTime();\n        adTimeRemaining = Math.max(0, this.AD_ACCESS_DURATION - timeSinceAdWatch);\n        if (adTimeRemaining > 0) {\n          canUseApp = true;\n        }\n      }\n      return {\n        ...currentStatus,\n        timeRemaining,\n        adTimeRemaining,\n        canUseApp,\n        isRequired: !canUseApp,\n        currentLink: this.getCurrentLink()\n      };\n    }\n    hasStatusChanged(old, updated) {\n      return old.canUseApp !== updated.canUseApp || old.timeRemaining !== updated.timeRemaining || old.adTimeRemaining !== updated.adTimeRemaining || old.currentLink !== updated.currentLink;\n    }\n    updateStatus(status) {\n      this.statusSubject.next(status);\n      this.saveStatus(status);\n    }\n    getAvailableSites() {\n      return this.shortenerSites.filter(site => site.isActive);\n    }\n    getCurrentLink() {\n      const availableSites = this.getAvailableSites();\n      if (availableSites.length === 0) {\n        return '';\n      }\n      const site = availableSites[this.currentLinkIndex % availableSites.length];\n      // Generate a unique link for the user to shorten\n      const timestamp = Date.now();\n      const randomId = Math.random().toString(36).substring(2, 8);\n      return `${site.url}/shorten?ref=quranvidgen&t=${timestamp}&id=${randomId}`;\n    }\n    switchToNextLink() {\n      this.currentLinkIndex++;\n      const newLink = this.getCurrentLink();\n      const currentStatus = this.statusSubject.value;\n      this.updateStatus({\n        ...currentStatus,\n        currentLink: newLink\n      });\n      return newLink;\n    }\n    reportLinkProblem() {\n      this.recordAttempt(false, 'User reported link problem');\n      this.switchToNextLink();\n    }\n    reportGeneralProblem() {\n      this.recordAttempt(false, 'User reported general problem');\n      this.switchToNextLink();\n    }\n    confirmLinkShortened() {\n      const now = new Date();\n      const currentStatus = this.statusSubject.value;\n      this.recordAttempt(true);\n      // Save to database\n      const userId = this.supabaseService.getCurrentUserId();\n      const expiresAt = new Date(now.getTime() + this.SHORTENING_INTERVAL);\n      this.supabaseService.createUserSession({\n        user_id: userId,\n        session_type: 'link_shortened',\n        expires_at: expiresAt.toISOString(),\n        metadata: {\n          site_id: this.getCurrentSiteId()\n        }\n      }).subscribe({\n        next: session => {\n          console.log('Session saved to database:', session);\n        },\n        error: error => {\n          console.error('Error saving session:', error);\n        }\n      });\n      const updatedStatus = {\n        ...currentStatus,\n        lastShortenedAt: now,\n        isRequired: false,\n        canUseApp: true,\n        timeRemaining: this.SHORTENING_INTERVAL\n      };\n      this.updateStatus(updatedStatus);\n    }\n    confirmAdWatched() {\n      const now = new Date();\n      const currentStatus = this.statusSubject.value;\n      // Save to database\n      const userId = this.supabaseService.getCurrentUserId();\n      const expiresAt = new Date(now.getTime() + this.AD_ACCESS_DURATION);\n      this.supabaseService.createUserSession({\n        user_id: userId,\n        session_type: 'ad_watched',\n        expires_at: expiresAt.toISOString(),\n        metadata: {\n          ad_type: 'admob_rewarded'\n        }\n      }).subscribe({\n        next: session => {\n          console.log('Ad session saved to database:', session);\n        },\n        error: error => {\n          console.error('Error saving ad session:', error);\n        }\n      });\n      const updatedStatus = {\n        ...currentStatus,\n        hasWatchedAd: true,\n        adWatchedAt: now,\n        isRequired: false,\n        canUseApp: true,\n        adTimeRemaining: this.AD_ACCESS_DURATION\n      };\n      this.updateStatus(updatedStatus);\n    }\n    recordAttempt(success, error) {\n      try {\n        const attempts = this.getStoredAttempts();\n        const newAttempt = {\n          timestamp: new Date(),\n          siteId: this.getAvailableSites()[this.currentLinkIndex % this.getAvailableSites().length]?.id || 'unknown',\n          success,\n          error\n        };\n        attempts.push(newAttempt);\n        // Keep only last 50 attempts\n        if (attempts.length > 50) {\n          attempts.splice(0, attempts.length - 50);\n        }\n        localStorage.setItem(this.ATTEMPTS_KEY, JSON.stringify(attempts));\n      } catch (error) {\n        console.error('Error recording attempt:', error);\n      }\n    }\n    getStoredAttempts() {\n      try {\n        const stored = localStorage.getItem(this.ATTEMPTS_KEY);\n        if (stored) {\n          const parsed = JSON.parse(stored);\n          return parsed.map(attempt => ({\n            ...attempt,\n            timestamp: new Date(attempt.timestamp)\n          }));\n        }\n      } catch (error) {\n        console.error('Error loading stored attempts:', error);\n      }\n      return [];\n    }\n    getAttemptHistory() {\n      return this.getStoredAttempts();\n    }\n    getCurrentStatus() {\n      return this.statusSubject.value;\n    }\n    forceRefreshStatus() {\n      const currentStatus = this.statusSubject.value;\n      const updatedStatus = this.calculateStatus(currentStatus);\n      this.updateStatus(updatedStatus);\n    }\n    formatTimeRemaining(milliseconds) {\n      if (milliseconds <= 0) {\n        return 'انتهت المدة';\n      }\n      const hours = Math.floor(milliseconds / (1000 * 60 * 60));\n      const minutes = Math.floor(milliseconds % (1000 * 60 * 60) / (1000 * 60));\n      if (hours > 0) {\n        return `${hours} ساعة و ${minutes} دقيقة`;\n      } else {\n        return `${minutes} دقيقة`;\n      }\n    }\n    // Admin functions for managing shortener sites\n    addShortenerSite(site) {\n      const newSite = {\n        ...site,\n        id: Date.now().toString()\n      };\n      this.shortenerSites.push(newSite);\n      this.saveShortenerSites();\n    }\n    updateShortenerSite(id, updates) {\n      const index = this.shortenerSites.findIndex(site => site.id === id);\n      if (index !== -1) {\n        this.shortenerSites[index] = {\n          ...this.shortenerSites[index],\n          ...updates\n        };\n        this.saveShortenerSites();\n      }\n    }\n    removeShortenerSite(id) {\n      this.shortenerSites = this.shortenerSites.filter(site => site.id !== id);\n      this.saveShortenerSites();\n    }\n    saveShortenerSites() {\n      try {\n        localStorage.setItem('shortenerSites', JSON.stringify(this.shortenerSites));\n      } catch (error) {\n        console.error('Error saving shortener sites:', error);\n      }\n    }\n    loadShortenerSitesFromDatabase() {\n      if (this.supabaseService.isConnectedToSupabase()) {\n        this.supabaseService.getShortenerLinks().subscribe({\n          next: links => {\n            if (links.length > 0) {\n              this.shortenerSites = links.map(link => ({\n                id: link.id.toString(),\n                name: link.site_name,\n                url: link.site_url,\n                isActive: link.is_active,\n                description: link.description || ''\n              }));\n            }\n          },\n          error: error => {\n            console.error('Error loading shortener sites from database:', error);\n          }\n        });\n      }\n    }\n    getCurrentSiteId() {\n      const availableSites = this.getAvailableSites();\n      if (availableSites.length === 0) {\n        return 'unknown';\n      }\n      return availableSites[this.currentLinkIndex % availableSites.length]?.id || 'unknown';\n    }\n    // Database integration methods\n    syncWithDatabase() {\n      if (this.supabaseService.isConnectedToSupabase()) {\n        const userId = this.supabaseService.getCurrentUserId();\n        // Load active sessions from database\n        this.supabaseService.getUserActiveSessions(userId).subscribe({\n          next: sessions => {\n            const currentStatus = this.statusSubject.value;\n            let hasActiveSession = false;\n            let lastShortenedAt = null;\n            let adWatchedAt = null;\n            sessions.forEach(session => {\n              if (session.session_type === 'link_shortened') {\n                hasActiveSession = true;\n                lastShortenedAt = new Date(session.created_at);\n              } else if (session.session_type === 'ad_watched') {\n                hasActiveSession = true;\n                adWatchedAt = new Date(session.created_at);\n              }\n            });\n            if (hasActiveSession) {\n              const updatedStatus = {\n                ...currentStatus,\n                lastShortenedAt,\n                adWatchedAt,\n                hasWatchedAd: !!adWatchedAt\n              };\n              this.updateStatus(this.calculateStatus(updatedStatus));\n            }\n          },\n          error: error => {\n            console.error('Error syncing with database:', error);\n          }\n        });\n      }\n    }\n    static {\n      this.ɵfac = function LinkShortenerService_Factory(t) {\n        return new (t || LinkShortenerService)(i0.ɵɵinject(i1.SupabaseService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: LinkShortenerService,\n        factory: LinkShortenerService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return LinkShortenerService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}