::ng-deep .video-settings-dialog {
  .p-dialog-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px 8px 0 0;
  }
  
  .p-dialog-content {
    padding: 0;
    max-height: 70vh;
    overflow-y: auto;
  }
  
  .p-dialog-footer {
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
  }
}

.settings-container {
  padding: 1.5rem;
}

.settings-form {
  .p-panel {
    margin-bottom: 1.5rem;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    
    .p-panel-header {
      background: #f8f9fa;
      border-bottom: 1px solid #e9ecef;
      font-weight: 600;
      color: #495057;
    }
    
    .p-panel-content {
      padding: 1.5rem;
    }
  }
}

.form-section {
  .form-row {
    margin-bottom: 1.5rem;
    
    label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: #495057;
    }
    
    .p-inputtext,
    .p-dropdown,
    .p-slider {
      width: 100%;
    }
  }
}

.api-key-container {
  display: flex;
  gap: 0.5rem;
  align-items: center;

  input {
    flex: 1;
  }
}

.connection-result {
  margin-top: 0.5rem;

  p {
    padding: 0.75rem;
    border-radius: 6px;
    margin: 0;
    font-weight: 500;

    &.success-message {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }

    &.warning-message {
      background: #fff3cd;
      color: #856404;
      border: 1px solid #ffeaa7;
    }

    &.error-message {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
  }
}

.model-option {
  padding: 0.5rem 0;

  .model-name {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.25rem;
  }

  .model-description {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
  }

  .model-badge {
    display: inline-block;
    background: #ffc107;
    color: #212529;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
  }
}

.color-settings {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
  
  .color-row {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    
    label {
      margin-bottom: 0;
      flex: 1;
    }
    
    .color-picker {
      width: 50px;
      height: 40px;
      border: 1px solid #ced4da;
      border-radius: 4px;
      cursor: pointer;
      
      &:hover {
        border-color: #80bdff;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  padding: 1rem;
}

// Responsive design
@media (max-width: 768px) {
  ::ng-deep .video-settings-dialog {
    .p-dialog {
      width: 95vw !important;
      height: 95vh !important;
      margin: 0;
    }
  }
  
  .color-settings {
    grid-template-columns: 1fr;
  }
  
  .api-key-container {
    flex-direction: column;
    align-items: stretch;
  }
  
  .dialog-footer {
    flex-direction: column;
    
    .p-button {
      width: 100%;
    }
  }
}
