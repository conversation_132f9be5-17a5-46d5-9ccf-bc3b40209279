{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/\\u062A\\u0637\\u0628\\u064A\\u0642 \\u0646\\u0627\\u0634\\u0631 \\u0627\\u064A\\u0627\\u062A \\u0642\\u0626\\u0627\\u0646\\u064A\\u0629/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { API_VERSION_HEADER_NAME, BASE64URL_REGEX } from './constants';\nimport { AuthInvalidJwtError } from './errors';\nimport { base64UrlToUint8Array, stringFromBase64URL } from './base64url';\nexport function expiresAt(expiresIn) {\n  const timeNow = Math.round(Date.now() / 1000);\n  return timeNow + expiresIn;\n}\nexport function uuid() {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n    const r = Math.random() * 16 | 0,\n      v = c == 'x' ? r : r & 0x3 | 0x8;\n    return v.toString(16);\n  });\n}\nexport const isBrowser = () => typeof window !== 'undefined' && typeof document !== 'undefined';\nconst localStorageWriteTests = {\n  tested: false,\n  writable: false\n};\n/**\n * Checks whether localStorage is supported on this browser.\n */\nexport const supportsLocalStorage = () => {\n  if (!isBrowser()) {\n    return false;\n  }\n  try {\n    if (typeof globalThis.localStorage !== 'object') {\n      return false;\n    }\n  } catch (e) {\n    // DOM exception when accessing `localStorage`\n    return false;\n  }\n  if (localStorageWriteTests.tested) {\n    return localStorageWriteTests.writable;\n  }\n  const randomKey = `lswt-${Math.random()}${Math.random()}`;\n  try {\n    globalThis.localStorage.setItem(randomKey, randomKey);\n    globalThis.localStorage.removeItem(randomKey);\n    localStorageWriteTests.tested = true;\n    localStorageWriteTests.writable = true;\n  } catch (e) {\n    // localStorage can't be written to\n    // https://www.chromium.org/for-testers/bug-reporting-guidelines/uncaught-securityerror-failed-to-read-the-localstorage-property-from-window-access-is-denied-for-this-document\n    localStorageWriteTests.tested = true;\n    localStorageWriteTests.writable = false;\n  }\n  return localStorageWriteTests.writable;\n};\n/**\n * Extracts parameters encoded in the URL both in the query and fragment.\n */\nexport function parseParametersFromURL(href) {\n  const result = {};\n  const url = new URL(href);\n  if (url.hash && url.hash[0] === '#') {\n    try {\n      const hashSearchParams = new URLSearchParams(url.hash.substring(1));\n      hashSearchParams.forEach((value, key) => {\n        result[key] = value;\n      });\n    } catch (e) {\n      // hash is not a query string\n    }\n  }\n  // search parameters take precedence over hash parameters\n  url.searchParams.forEach((value, key) => {\n    result[key] = value;\n  });\n  return result;\n}\nexport const resolveFetch = customFetch => {\n  let _fetch;\n  if (customFetch) {\n    _fetch = customFetch;\n  } else if (typeof fetch === 'undefined') {\n    _fetch = (...args) => import('@supabase/node-fetch').then(({\n      default: fetch\n    }) => fetch(...args));\n  } else {\n    _fetch = fetch;\n  }\n  return (...args) => _fetch(...args);\n};\nexport const looksLikeFetchResponse = maybeResponse => {\n  return typeof maybeResponse === 'object' && maybeResponse !== null && 'status' in maybeResponse && 'ok' in maybeResponse && 'json' in maybeResponse && typeof maybeResponse.json === 'function';\n};\n// Storage helpers\nexport const setItemAsync = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (storage, key, data) {\n    yield storage.setItem(key, JSON.stringify(data));\n  });\n  return function setItemAsync(_x, _x2, _x3) {\n    return _ref.apply(this, arguments);\n  };\n}();\nexport const getItemAsync = /*#__PURE__*/function () {\n  var _ref2 = _asyncToGenerator(function* (storage, key) {\n    const value = yield storage.getItem(key);\n    if (!value) {\n      return null;\n    }\n    try {\n      return JSON.parse(value);\n    } catch (_a) {\n      return value;\n    }\n  });\n  return function getItemAsync(_x4, _x5) {\n    return _ref2.apply(this, arguments);\n  };\n}();\nexport const removeItemAsync = /*#__PURE__*/function () {\n  var _ref3 = _asyncToGenerator(function* (storage, key) {\n    yield storage.removeItem(key);\n  });\n  return function removeItemAsync(_x6, _x7) {\n    return _ref3.apply(this, arguments);\n  };\n}();\n/**\n * A deferred represents some asynchronous work that is not yet finished, which\n * may or may not culminate in a value.\n * Taken from: https://github.com/mike-north/types/blob/master/src/async.ts\n */\nexport class Deferred {\n  constructor() {\n    // eslint-disable-next-line @typescript-eslint/no-extra-semi\n    ;\n    this.promise = new Deferred.promiseConstructor((res, rej) => {\n      // eslint-disable-next-line @typescript-eslint/no-extra-semi\n      ;\n      this.resolve = res;\n      this.reject = rej;\n    });\n  }\n}\nDeferred.promiseConstructor = Promise;\nexport function decodeJWT(token) {\n  const parts = token.split('.');\n  if (parts.length !== 3) {\n    throw new AuthInvalidJwtError('Invalid JWT structure');\n  }\n  // Regex checks for base64url format\n  for (let i = 0; i < parts.length; i++) {\n    if (!BASE64URL_REGEX.test(parts[i])) {\n      throw new AuthInvalidJwtError('JWT not in base64url format');\n    }\n  }\n  const data = {\n    // using base64url lib\n    header: JSON.parse(stringFromBase64URL(parts[0])),\n    payload: JSON.parse(stringFromBase64URL(parts[1])),\n    signature: base64UrlToUint8Array(parts[2]),\n    raw: {\n      header: parts[0],\n      payload: parts[1]\n    }\n  };\n  return data;\n}\n/**\n * Creates a promise that resolves to null after some time.\n */\nexport function sleep(_x8) {\n  return _sleep.apply(this, arguments);\n}\n/**\n * Converts the provided async function into a retryable function. Each result\n * or thrown error is sent to the isRetryable function which should return true\n * if the function should run again.\n */\nfunction _sleep() {\n  _sleep = _asyncToGenerator(function* (time) {\n    return yield new Promise(accept => {\n      setTimeout(() => accept(null), time);\n    });\n  });\n  return _sleep.apply(this, arguments);\n}\nexport function retryable(fn, isRetryable) {\n  const promise = new Promise((accept, reject) => {\n    // eslint-disable-next-line @typescript-eslint/no-extra-semi\n    ;\n    _asyncToGenerator(function* () {\n      for (let attempt = 0; attempt < Infinity; attempt++) {\n        try {\n          const result = yield fn(attempt);\n          if (!isRetryable(attempt, null, result)) {\n            accept(result);\n            return;\n          }\n        } catch (e) {\n          if (!isRetryable(attempt, e)) {\n            reject(e);\n            return;\n          }\n        }\n      }\n    })();\n  });\n  return promise;\n}\nfunction dec2hex(dec) {\n  return ('0' + dec.toString(16)).substr(-2);\n}\n// Functions below taken from: https://stackoverflow.com/questions/63309409/creating-a-code-verifier-and-challenge-for-pkce-auth-on-spotify-api-in-reactjs\nexport function generatePKCEVerifier() {\n  const verifierLength = 56;\n  const array = new Uint32Array(verifierLength);\n  if (typeof crypto === 'undefined') {\n    const charSet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';\n    const charSetLen = charSet.length;\n    let verifier = '';\n    for (let i = 0; i < verifierLength; i++) {\n      verifier += charSet.charAt(Math.floor(Math.random() * charSetLen));\n    }\n    return verifier;\n  }\n  crypto.getRandomValues(array);\n  return Array.from(array, dec2hex).join('');\n}\nfunction sha256(_x9) {\n  return _sha.apply(this, arguments);\n}\nfunction _sha() {\n  _sha = _asyncToGenerator(function* (randomString) {\n    const encoder = new TextEncoder();\n    const encodedData = encoder.encode(randomString);\n    const hash = yield crypto.subtle.digest('SHA-256', encodedData);\n    const bytes = new Uint8Array(hash);\n    return Array.from(bytes).map(c => String.fromCharCode(c)).join('');\n  });\n  return _sha.apply(this, arguments);\n}\nexport function generatePKCEChallenge(_x0) {\n  return _generatePKCEChallenge.apply(this, arguments);\n}\nfunction _generatePKCEChallenge() {\n  _generatePKCEChallenge = _asyncToGenerator(function* (verifier) {\n    const hasCryptoSupport = typeof crypto !== 'undefined' && typeof crypto.subtle !== 'undefined' && typeof TextEncoder !== 'undefined';\n    if (!hasCryptoSupport) {\n      console.warn('WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256.');\n      return verifier;\n    }\n    const hashed = yield sha256(verifier);\n    return btoa(hashed).replace(/\\+/g, '-').replace(/\\//g, '_').replace(/=+$/, '');\n  });\n  return _generatePKCEChallenge.apply(this, arguments);\n}\nexport function getCodeChallengeAndMethod(_x1, _x10) {\n  return _getCodeChallengeAndMethod.apply(this, arguments);\n}\n/** Parses the API version which is 2YYY-MM-DD. */\nfunction _getCodeChallengeAndMethod() {\n  _getCodeChallengeAndMethod = _asyncToGenerator(function* (storage, storageKey, isPasswordRecovery = false) {\n    const codeVerifier = generatePKCEVerifier();\n    let storedCodeVerifier = codeVerifier;\n    if (isPasswordRecovery) {\n      storedCodeVerifier += '/PASSWORD_RECOVERY';\n    }\n    yield setItemAsync(storage, `${storageKey}-code-verifier`, storedCodeVerifier);\n    const codeChallenge = yield generatePKCEChallenge(codeVerifier);\n    const codeChallengeMethod = codeVerifier === codeChallenge ? 'plain' : 's256';\n    return [codeChallenge, codeChallengeMethod];\n  });\n  return _getCodeChallengeAndMethod.apply(this, arguments);\n}\nconst API_VERSION_REGEX = /^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;\nexport function parseResponseAPIVersion(response) {\n  const apiVersion = response.headers.get(API_VERSION_HEADER_NAME);\n  if (!apiVersion) {\n    return null;\n  }\n  if (!apiVersion.match(API_VERSION_REGEX)) {\n    return null;\n  }\n  try {\n    const date = new Date(`${apiVersion}T00:00:00.0Z`);\n    return date;\n  } catch (e) {\n    return null;\n  }\n}\nexport function validateExp(exp) {\n  if (!exp) {\n    throw new Error('Missing exp claim');\n  }\n  const timeNow = Math.floor(Date.now() / 1000);\n  if (exp <= timeNow) {\n    throw new Error('JWT has expired');\n  }\n}\nexport function getAlgorithm(alg) {\n  switch (alg) {\n    case 'RS256':\n      return {\n        name: 'RSASSA-PKCS1-v1_5',\n        hash: {\n          name: 'SHA-256'\n        }\n      };\n    case 'ES256':\n      return {\n        name: 'ECDSA',\n        namedCurve: 'P-256',\n        hash: {\n          name: 'SHA-256'\n        }\n      };\n    default:\n      throw new Error('Invalid alg claim');\n  }\n}\nconst UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;\nexport function validateUUID(str) {\n  if (!UUID_REGEX.test(str)) {\n    throw new Error('@supabase/auth-js: Expected parameter to be UUID but is not');\n  }\n}\nexport function userNotAvailableProxy() {\n  const proxyTarget = {};\n  return new Proxy(proxyTarget, {\n    get: (target, prop) => {\n      if (prop === '__isUserNotAvailableProxy') {\n        return true;\n      }\n      // Preventative check for common problematic symbols during cloning/inspection\n      // These symbols might be accessed by structuredClone or other internal mechanisms.\n      if (typeof prop === 'symbol') {\n        const sProp = prop.toString();\n        if (sProp === 'Symbol(Symbol.toPrimitive)' || sProp === 'Symbol(Symbol.toStringTag)' || sProp === 'Symbol(util.inspect.custom)') {\n          // Node.js util.inspect\n          return undefined;\n        }\n      }\n      throw new Error(`@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Accessing the \"${prop}\" property of the session object is not supported. Please use getUser() instead.`);\n    },\n    set: (_target, prop) => {\n      throw new Error(`@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Setting the \"${prop}\" property of the session object is not supported. Please use getUser() to fetch a user object you can manipulate.`);\n    },\n    deleteProperty: (_target, prop) => {\n      throw new Error(`@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Deleting the \"${prop}\" property of the session object is not supported. Please use getUser() to fetch a user object you can manipulate.`);\n    }\n  });\n}\n/**\n * Deep clones a JSON-serializable object using JSON.parse(JSON.stringify(obj)).\n * Note: Only works for JSON-safe data.\n */\nexport function deepClone(obj) {\n  return JSON.parse(JSON.stringify(obj));\n}\n//# sourceMappingURL=helpers.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}