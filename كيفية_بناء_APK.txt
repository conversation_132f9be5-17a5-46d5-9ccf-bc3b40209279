🚀 كيفية بناء تطبيق APK - مولد مقاطع القرآن الكريم

═══════════════════════════════════════════════════════════════

📱 الطريقة الأسهل: Android Studio

1️⃣ نزل Android Studio من:
   https://developer.android.com/studio

2️⃣ افتح Android Studio

3️⃣ اختر "Open an existing Android Studio project"

4️⃣ انتقل إلى مجلد: android (داخل مجلد المشروع)

5️⃣ من القائمة: Build → Build Bundle(s) / APK(s) → Build APK(s)

6️⃣ انتظر حتى يكتمل البناء

7️⃣ ستجد APK في: android\app\build\outputs\apk\debug\

═══════════════════════════════════════════════════════════════

🔧 الطريقة البديلة: سطر الأوامر

1️⃣ افتح PowerShell كمدير

2️⃣ انتقل إلى مجلد المشروع:
   cd "C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية"

3️⃣ شغل الأمر:
   powershell -ExecutionPolicy Bypass -File build_apk.ps1

4️⃣ انتظر حتى يكتمل البناء

═══════════════════════════════════════════════════════════════

✅ الحالة الحالية:
- المشروع جاهز 100%
- جميع الملفات موجودة
- Java 17 مثبت
- Capacitor مُعد بشكل صحيح

⚠️ المشكلة الوحيدة:
- مشكلة في تنزيل Gradle (مشكلة إنترنت)
- يمكن حلها باستخدام Android Studio

🎯 النتيجة:
ملف APK جاهز للتثبيت على أجهزة Android

═══════════════════════════════════════════════════════════════

📞 للمساعدة:
راجع ملف "دليل_بناء_APK.md" للتفاصيل الكاملة
