Write-Host "========================================" -ForegroundColor Green
Write-Host "    تنزيل وإعداد Gradle تلقائياً" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# إنشاء مجلد Gradle
$gradleDir = "C:\gradle"
$gradleVersion = "gradle-7.6.4"
$gradleHome = "$gradleDir\$gradleVersion"

Write-Host "إنشاء مجلد Gradle..." -ForegroundColor Yellow
New-Item -ItemType Directory -Force -Path $gradleDir | Out-Null

# قائمة بالروابط البديلة لتنزيل Gradle
$downloadUrls = @(
    "https://services.gradle.org/distributions/gradle-7.6.4-all.zip",
    "https://downloads.gradle-dn.com/distributions/gradle-7.6.4-all.zip",
    "https://github.com/gradle/gradle/releases/download/v7.6.4/gradle-7.6.4-all.zip"
)

$zipFile = "$gradleDir\gradle-7.6.4-all.zip"
$downloaded = $false

foreach ($url in $downloadUrls) {
    Write-Host "محاولة التنزيل من: $url" -ForegroundColor Cyan
    
    try {
        # تنزيل الملف
        Invoke-WebRequest -Uri $url -OutFile $zipFile -TimeoutSec 300
        
        if (Test-Path $zipFile) {
            $fileSize = (Get-Item $zipFile).Length / 1MB
            Write-Host "✅ تم التنزيل بنجاح! حجم الملف: $([math]::Round($fileSize, 2)) MB" -ForegroundColor Green
            $downloaded = $true
            break
        }
    }
    catch {
        Write-Host "❌ فشل التنزيل من هذا الرابط: $($_.Exception.Message)" -ForegroundColor Red
        continue
    }
}

if (-not $downloaded) {
    Write-Host "❌ فشل في تنزيل Gradle من جميع المصادر" -ForegroundColor Red
    Write-Host "الحلول البديلة:" -ForegroundColor Yellow
    Write-Host "1. استخدم Android Studio" -ForegroundColor White
    Write-Host "2. نزل الملف يدوياً من gradle.org" -ForegroundColor White
    Write-Host "3. استخدم VPN وأعد المحاولة" -ForegroundColor White
    Read-Host "اضغط Enter للخروج..."
    exit 1
}

# استخراج الملف
Write-Host "استخراج Gradle..." -ForegroundColor Yellow

try {
    # حذف المجلد القديم إذا كان موجوداً
    if (Test-Path $gradleHome) {
        Remove-Item $gradleHome -Recurse -Force
    }
    
    # استخراج الملف
    Expand-Archive -Path $zipFile -DestinationPath $gradleDir -Force
    
    if (Test-Path $gradleHome) {
        Write-Host "✅ تم استخراج Gradle بنجاح" -ForegroundColor Green
    } else {
        throw "فشل في استخراج الملف"
    }
}
catch {
    Write-Host "❌ خطأ في استخراج Gradle: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج..."
    exit 1
}

# تعيين متغيرات البيئة
Write-Host "إعداد متغيرات البيئة..." -ForegroundColor Yellow

$env:GRADLE_HOME = $gradleHome
$env:PATH = "$gradleHome\bin;$env:PATH"
$env:JAVA_HOME = "C:\Program Files\Microsoft\jdk-*********-hotspot"

Write-Host "GRADLE_HOME: $env:GRADLE_HOME" -ForegroundColor Cyan
Write-Host "JAVA_HOME: $env:JAVA_HOME" -ForegroundColor Cyan

# التحقق من التثبيت
Write-Host "التحقق من تثبيت Gradle..." -ForegroundColor Yellow

try {
    $gradleVersion = & "$gradleHome\bin\gradle.bat" --version 2>&1
    Write-Host "✅ Gradle مثبت بنجاح:" -ForegroundColor Green
    Write-Host $gradleVersion -ForegroundColor White
}
catch {
    Write-Host "❌ خطأ في تشغيل Gradle: $($_.Exception.Message)" -ForegroundColor Red
}

# حذف ملف ZIP
Write-Host "تنظيف الملفات المؤقتة..." -ForegroundColor Yellow
Remove-Item $zipFile -Force -ErrorAction SilentlyContinue

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "تم إعداد Gradle بنجاح!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Write-Host "الخطوات التالية:" -ForegroundColor Yellow
Write-Host "1. افتح PowerShell جديد" -ForegroundColor White
Write-Host "2. انتقل إلى مجلد android" -ForegroundColor White
Write-Host "3. شغل: gradle assembleDebug" -ForegroundColor White

Write-Host ""
Write-Host "أو استخدم الأمر التالي مباشرة:" -ForegroundColor Cyan
Write-Host "cd `"C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android`"" -ForegroundColor White
Write-Host "gradle assembleDebug" -ForegroundColor White

Read-Host "اضغط Enter للمتابعة..."
