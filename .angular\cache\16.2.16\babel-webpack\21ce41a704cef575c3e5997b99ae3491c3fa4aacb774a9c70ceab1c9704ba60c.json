{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/\\u062A\\u0637\\u0628\\u064A\\u0642 \\u0646\\u0627\\u0634\\u0631 \\u0627\\u064A\\u0627\\u062A \\u0642\\u0626\\u0627\\u0646\\u064A\\u0629/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { createClient } from '@supabase/supabase-js';\nimport { from, BehaviorSubject } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nexport let SupabaseService = /*#__PURE__*/(() => {\n  class SupabaseService {\n    constructor() {\n      this.isConnected = new BehaviorSubject(false);\n      this.connectionStatus$ = this.isConnected.asObservable();\n      // Initialize with placeholder values - these should be set from environment or config\n      const supabaseUrl = 'YOUR_SUPABASE_URL';\n      const supabaseKey = 'YOUR_SUPABASE_ANON_KEY';\n      this.supabase = createClient(supabaseUrl, supabaseKey);\n      this.checkConnection();\n    }\n    // Initialize Supabase with actual credentials\n    initializeSupabase(url, key) {\n      this.supabase = createClient(url, key);\n      this.checkConnection();\n    }\n    checkConnection() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        try {\n          const {\n            data,\n            error\n          } = yield _this.supabase.from('app_settings').select('count').limit(1);\n          _this.isConnected.next(!error);\n        } catch (error) {\n          _this.isConnected.next(false);\n        }\n      })();\n    }\n    // Shortener Links Management\n    getShortenerLinks() {\n      return from(this.supabase.from('shortener_links').select('*').eq('is_active', true).order('created_at', {\n        ascending: false\n      })).pipe(map(response => {\n        if (response.error) throw response.error;\n        return response.data || [];\n      }), catchError(error => {\n        console.error('Error fetching shortener links:', error);\n        return [];\n      }));\n    }\n    addShortenerLink(link) {\n      return from(this.supabase.from('shortener_links').insert([link]).select().single()).pipe(map(response => {\n        if (response.error) throw response.error;\n        return response.data;\n      }));\n    }\n    updateShortenerLink(id, updates) {\n      return from(this.supabase.from('shortener_links').update({\n        ...updates,\n        updated_at: new Date().toISOString()\n      }).eq('id', id).select().single()).pipe(map(response => {\n        if (response.error) throw response.error;\n        return response.data;\n      }));\n    }\n    deleteShortenerLink(id) {\n      return from(this.supabase.from('shortener_links').delete().eq('id', id)).pipe(map(response => {\n        if (response.error) throw response.error;\n        return true;\n      }));\n    }\n    // User Sessions Management\n    createUserSession(session) {\n      return from(this.supabase.from('user_sessions').insert([session]).select().single()).pipe(map(response => {\n        if (response.error) throw response.error;\n        return response.data;\n      }));\n    }\n    getUserActiveSessions(userId) {\n      const now = new Date().toISOString();\n      return from(this.supabase.from('user_sessions').select('*').eq('user_id', userId).gt('expires_at', now).order('created_at', {\n        ascending: false\n      })).pipe(map(response => {\n        if (response.error) throw response.error;\n        return response.data || [];\n      }));\n    }\n    cleanupExpiredSessions() {\n      const now = new Date().toISOString();\n      return from(this.supabase.from('user_sessions').delete().lt('expires_at', now)).pipe(map(response => {\n        if (response.error) throw response.error;\n        return true;\n      }));\n    }\n    // Security Logs\n    logSecurityEvent(log) {\n      return from(this.supabase.from('security_logs').insert([log]).select().single()).pipe(map(response => {\n        if (response.error) throw response.error;\n        return response.data;\n      }));\n    }\n    getSecurityLogs(userId, limit = 100) {\n      let query = this.supabase.from('security_logs').select('*').order('created_at', {\n        ascending: false\n      }).limit(limit);\n      if (userId) {\n        query = query.eq('user_id', userId);\n      }\n      return from(query).pipe(map(response => {\n        if (response.error) throw response.error;\n        return response.data || [];\n      }));\n    }\n    // App Settings Management\n    getAppSetting(key) {\n      return from(this.supabase.from('app_settings').select('setting_value').eq('setting_key', key).single()).pipe(map(response => {\n        if (response.error) throw response.error;\n        return response.data?.setting_value;\n      }));\n    }\n    setAppSetting(key, value, description) {\n      const setting = {\n        setting_key: key,\n        setting_value: value,\n        description,\n        updated_at: new Date().toISOString()\n      };\n      return from(this.supabase.from('app_settings').upsert([setting], {\n        onConflict: 'setting_key'\n      }).select().single()).pipe(map(response => {\n        if (response.error) throw response.error;\n        return response.data;\n      }));\n    }\n    getAllAppSettings() {\n      return from(this.supabase.from('app_settings').select('*').order('setting_key')).pipe(map(response => {\n        if (response.error) throw response.error;\n        return response.data || [];\n      }));\n    }\n    // Analytics and Statistics\n    getUserStats(userId) {\n      return from(this.supabase.rpc('get_user_stats', {\n        user_id: userId\n      })).pipe(map(response => {\n        if (response.error) throw response.error;\n        return response.data;\n      }));\n    }\n    getAppStats() {\n      return from(this.supabase.rpc('get_app_stats')).pipe(map(response => {\n        if (response.error) throw response.error;\n        return response.data;\n      }));\n    }\n    // Utility methods\n    isConnectedToSupabase() {\n      return this.isConnected.value;\n    }\n    testConnection() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          const {\n            data,\n            error\n          } = yield _this2.supabase.from('app_settings').select('count').limit(1);\n          const connected = !error;\n          _this2.isConnected.next(connected);\n          return connected;\n        } catch (error) {\n          _this2.isConnected.next(false);\n          return false;\n        }\n      })();\n    }\n    // Generate unique user ID for anonymous users\n    generateUserId() {\n      const stored = localStorage.getItem('quran_app_user_id');\n      if (stored) {\n        return stored;\n      }\n      const newId = 'user_' + Date.now() + '_' + Math.random().toString(36).substring(2, 15);\n      localStorage.setItem('quran_app_user_id', newId);\n      return newId;\n    }\n    getCurrentUserId() {\n      return this.generateUserId();\n    }\n    static {\n      this.ɵfac = function SupabaseService_Factory(t) {\n        return new (t || SupabaseService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: SupabaseService,\n        factory: SupabaseService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return SupabaseService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}