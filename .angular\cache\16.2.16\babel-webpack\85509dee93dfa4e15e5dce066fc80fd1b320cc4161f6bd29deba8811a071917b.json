{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../Services/youtube.service\";\nimport * as i3 from \"../../Services/gemini.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/inputtext\";\nimport * as i6 from \"primeng/api\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/progressbar\";\nimport * as i9 from \"primeng/dialog\";\nimport * as i10 from \"primeng/inputtextarea\";\nfunction UploadDialogComponent_form_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"form\", 6)(1, \"div\", 7)(2, \"label\", 8);\n    i0.ɵɵtext(3, \"\\u0639\\u0646\\u0648\\u0627\\u0646 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 7)(6, \"label\", 10);\n    i0.ɵɵtext(7, \"\\u0648\\u0635\\u0641 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"textarea\", 11);\n    i0.ɵɵtext(9, \"        \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 7)(11, \"label\", 12);\n    i0.ɵɵtext(12, \"\\u0627\\u0644\\u0643\\u0644\\u0645\\u0627\\u062A \\u0627\\u0644\\u0645\\u0641\\u062A\\u0627\\u062D\\u064A\\u0629\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"input\", 13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.uploadForm);\n  }\n}\nfunction UploadDialogComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15);\n    i0.ɵɵelement(2, \"i\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"\\u062C\\u0627\\u0631\\u064A \\u0631\\u0641\\u0639 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"p-progressBar\", 17);\n    i0.ɵɵelementStart(6, \"p\", 18);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"value\", ctx_r1.uploadProgress);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.uploadProgress, \"% \\u0645\\u0643\\u062A\\u0645\\u0644\");\n  }\n}\nfunction UploadDialogComponent_div_4_a_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 23);\n    i0.ɵɵtext(1, \" \\u0645\\u0634\\u0627\\u0647\\u062F\\u0629 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648 \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"href\", ctx_r4.videoUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction UploadDialogComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 20);\n    i0.ɵɵelement(2, \"i\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"\\u062A\\u0645 \\u0631\\u0641\\u0639 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648 \\u0628\\u0646\\u062C\\u0627\\u062D!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"\\u064A\\u0645\\u0643\\u0646\\u0643 \\u0627\\u0644\\u0622\\u0646 \\u0645\\u0634\\u0627\\u0647\\u062F\\u0629 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648 \\u0639\\u0644\\u0649 YouTube\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, UploadDialogComponent_div_4_a_7_Template, 2, 1, \"a\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.videoUrl);\n  }\n}\nfunction UploadDialogComponent_ng_template_5_p_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 27);\n    i0.ɵɵlistener(\"onClick\", function UploadDialogComponent_ng_template_5_p_button_1_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r7.onUpload());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r5.uploadForm.valid);\n  }\n}\nfunction UploadDialogComponent_ng_template_5_p_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 28);\n    i0.ɵɵlistener(\"onClick\", function UploadDialogComponent_ng_template_5_p_button_2_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.onHide());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"outlined\", true);\n  }\n}\nfunction UploadDialogComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵtemplate(1, UploadDialogComponent_ng_template_5_p_button_1_Template, 1, 1, \"p-button\", 25);\n    i0.ɵɵtemplate(2, UploadDialogComponent_ng_template_5_p_button_2_Template, 1, 1, \"p-button\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.isUploading && !ctx_r3.isUploadComplete);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.isUploading);\n  }\n}\nconst _c0 = function () {\n  return {\n    width: \"500px\"\n  };\n};\nexport let UploadDialogComponent = /*#__PURE__*/(() => {\n  class UploadDialogComponent {\n    constructor(fb, youtubeService, geminiService) {\n      this.fb = fb;\n      this.youtubeService = youtubeService;\n      this.geminiService = geminiService;\n      this.visible = false;\n      this.videoBlob = null;\n      this.surahInfo = null;\n      this.visibleChange = new EventEmitter();\n      this.uploadComplete = new EventEmitter();\n      this.isUploading = false;\n      this.isGenerating = false;\n      this.uploadProgress = 0;\n      this.isUploadComplete = false;\n      this.videoUrl = '';\n      this.errorMessage = '';\n      this.successMessage = '';\n      this.privacyOptions = [{\n        label: 'عام',\n        value: 'public'\n      }, {\n        label: 'غير مدرج',\n        value: 'unlisted'\n      }, {\n        label: 'خاص',\n        value: 'private'\n      }];\n      this.uploadForm = this.createForm();\n      this.uploadProgress$ = this.youtubeService.uploadProgress$;\n    }\n    ngOnInit() {\n      // Initialize form when component loads\n    }\n    createForm() {\n      return this.fb.group({\n        title: ['', [Validators.required, Validators.maxLength(100)]],\n        description: ['', [Validators.required, Validators.maxLength(5000)]],\n        tags: [''],\n        privacy: ['unlisted', Validators.required],\n        useAI: [true]\n      });\n    }\n    onVisibilityChange() {\n      if (this.visible && this.surahInfo) {\n        this.resetForm();\n        if (this.uploadForm.get('useAI')?.value) {\n          this.generateContent();\n        }\n      }\n    }\n    resetForm() {\n      this.uploadForm.reset({\n        title: '',\n        description: '',\n        tags: '',\n        privacy: 'unlisted',\n        useAI: true\n      });\n      this.errorMessage = '';\n      this.successMessage = '';\n    }\n    generateContent() {\n      if (!this.surahInfo || !this.geminiService.isApiKeyConfigured()) {\n        this.errorMessage = 'لم يتم تكوين مفتاح Gemini API';\n        return;\n      }\n      this.isGenerating = true;\n      this.errorMessage = '';\n      this.geminiService.generateVideoContent(this.surahInfo).subscribe({\n        next: content => {\n          this.uploadForm.patchValue({\n            title: content.title,\n            description: content.description,\n            tags: content.tags.join(', ')\n          });\n          this.isGenerating = false;\n        },\n        error: error => {\n          this.errorMessage = 'فشل في توليد المحتوى. يرجى المحاولة مرة أخرى.';\n          this.isGenerating = false;\n          console.error('Content generation error:', error);\n        }\n      });\n    }\n    generateTitleOnly() {\n      if (!this.surahInfo || !this.geminiService.isApiKeyConfigured()) {\n        return;\n      }\n      this.geminiService.generateTitleOnly(this.surahInfo).subscribe({\n        next: title => {\n          this.uploadForm.patchValue({\n            title\n          });\n        },\n        error: error => {\n          console.error('Title generation error:', error);\n        }\n      });\n    }\n    generateDescriptionOnly() {\n      if (!this.surahInfo || !this.geminiService.isApiKeyConfigured()) {\n        return;\n      }\n      this.geminiService.generateDescriptionOnly(this.surahInfo).subscribe({\n        next: description => {\n          this.uploadForm.patchValue({\n            description\n          });\n        },\n        error: error => {\n          console.error('Description generation error:', error);\n        }\n      });\n    }\n    onUpload() {\n      if (!this.uploadForm.valid || !this.videoBlob) {\n        this.errorMessage = 'يرجى ملء جميع الحقول المطلوبة';\n        return;\n      }\n      if (!this.youtubeService.isAuthenticated()) {\n        this.errorMessage = 'يرجى تسجيل الدخول إلى YouTube أولاً';\n        return;\n      }\n      const formValue = this.uploadForm.value;\n      const videoDetails = {\n        title: formValue.title,\n        description: formValue.description,\n        tags: formValue.tags ? formValue.tags.split(',').map(tag => tag.trim()) : [],\n        privacy: formValue.privacy\n      };\n      this.isUploading = true;\n      this.errorMessage = '';\n      this.youtubeService.uploadVideo(this.videoBlob, videoDetails).subscribe({\n        next: videoId => {\n          this.isUploading = false;\n          this.successMessage = `تم رفع الفيديو بنجاح! معرف الفيديو: ${videoId}`;\n          this.uploadComplete.emit(videoId);\n          setTimeout(() => this.closeDialog(), 3000);\n        },\n        error: error => {\n          this.isUploading = false;\n          this.errorMessage = 'فشل في رفع الفيديو. يرجى المحاولة مرة أخرى.';\n          console.error('Upload error:', error);\n        }\n      });\n    }\n    closeDialog() {\n      this.visible = false;\n      this.visibleChange.emit(false);\n      this.resetForm();\n    }\n    get isAIEnabled() {\n      return this.geminiService.isApiKeyConfigured();\n    }\n    get canGenerate() {\n      return this.isAIEnabled && !!this.surahInfo && !this.isGenerating;\n    }\n    onHide() {\n      this.visible = false;\n      this.visibleChange.emit(false);\n      this.resetForm();\n    }\n    static {\n      this.ɵfac = function UploadDialogComponent_Factory(t) {\n        return new (t || UploadDialogComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.YoutubeService), i0.ɵɵdirectiveInject(i3.GeminiService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: UploadDialogComponent,\n        selectors: [[\"app-upload-dialog\"]],\n        inputs: {\n          visible: \"visible\",\n          videoBlob: \"videoBlob\",\n          surahInfo: \"surahInfo\"\n        },\n        outputs: {\n          visibleChange: \"visibleChange\",\n          uploadComplete: \"uploadComplete\"\n        },\n        decls: 6,\n        vars: 10,\n        consts: [[\"header\", \"\\u0631\\u0641\\u0639 \\u0625\\u0644\\u0649 YouTube\", \"styleClass\", \"upload-dialog\", 3, \"visible\", \"modal\", \"closable\", \"resizable\", \"visibleChange\", \"onHide\"], [1, \"upload-container\"], [3, \"formGroup\", 4, \"ngIf\"], [\"class\", \"upload-progress\", 4, \"ngIf\"], [\"class\", \"upload-complete\", 4, \"ngIf\"], [\"pTemplate\", \"footer\"], [3, \"formGroup\"], [1, \"form-group\"], [\"for\", \"title\"], [\"id\", \"title\", \"type\", \"text\", \"formControlName\", \"title\", \"pInputText\", \"\", \"placeholder\", \"\\u0623\\u062F\\u062E\\u0644 \\u0639\\u0646\\u0648\\u0627\\u0646 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\", 1, \"w-100\"], [\"for\", \"description\"], [\"id\", \"description\", \"formControlName\", \"description\", \"pInputTextarea\", \"\", \"rows\", \"4\", \"placeholder\", \"\\u0623\\u062F\\u062E\\u0644 \\u0648\\u0635\\u0641 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\", 1, \"w-100\"], [\"for\", \"tags\"], [\"id\", \"tags\", \"type\", \"text\", \"formControlName\", \"tags\", \"pInputText\", \"\", \"placeholder\", \"\\u0642\\u0631\\u0622\\u0646\\u060C \\u062A\\u0644\\u0627\\u0648\\u0629\\u060C \\u0625\\u0633\\u0644\\u0627\\u0645\", 1, \"w-100\"], [1, \"upload-progress\"], [1, \"progress-icon\"], [1, \"pi\", \"pi-spin\", \"pi-spinner\"], [3, \"value\"], [1, \"progress-text\"], [1, \"upload-complete\"], [1, \"success-icon\"], [1, \"pi\", \"pi-check-circle\"], [\"target\", \"_blank\", \"class\", \"video-link\", 3, \"href\", 4, \"ngIf\"], [\"target\", \"_blank\", 1, \"video-link\", 3, \"href\"], [1, \"dialog-footer\"], [\"label\", \"\\u0631\\u0641\\u0639\", \"icon\", \"pi pi-upload\", \"severity\", \"primary\", 3, \"disabled\", \"onClick\", 4, \"ngIf\"], [\"label\", \"\\u0625\\u0644\\u063A\\u0627\\u0621\", \"icon\", \"pi pi-times\", \"severity\", \"secondary\", 3, \"outlined\", \"onClick\", 4, \"ngIf\"], [\"label\", \"\\u0631\\u0641\\u0639\", \"icon\", \"pi pi-upload\", \"severity\", \"primary\", 3, \"disabled\", \"onClick\"], [\"label\", \"\\u0625\\u0644\\u063A\\u0627\\u0621\", \"icon\", \"pi pi-times\", \"severity\", \"secondary\", 3, \"outlined\", \"onClick\"]],\n        template: function UploadDialogComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"p-dialog\", 0);\n            i0.ɵɵlistener(\"visibleChange\", function UploadDialogComponent_Template_p_dialog_visibleChange_0_listener($event) {\n              return ctx.visible = $event;\n            })(\"onHide\", function UploadDialogComponent_Template_p_dialog_onHide_0_listener() {\n              return ctx.onHide();\n            });\n            i0.ɵɵelementStart(1, \"div\", 1);\n            i0.ɵɵtemplate(2, UploadDialogComponent_form_2_Template, 14, 1, \"form\", 2);\n            i0.ɵɵtemplate(3, UploadDialogComponent_div_3_Template, 8, 2, \"div\", 3);\n            i0.ɵɵtemplate(4, UploadDialogComponent_div_4_Template, 8, 1, \"div\", 4);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(5, UploadDialogComponent_ng_template_5_Template, 3, 2, \"ng-template\", 5);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵstyleMap(i0.ɵɵpureFunction0(9, _c0));\n            i0.ɵɵproperty(\"visible\", ctx.visible)(\"modal\", true)(\"closable\", true)(\"resizable\", false);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isUploading && !ctx.isUploadComplete);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isUploading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isUploadComplete);\n          }\n        },\n        dependencies: [i4.NgIf, i5.InputText, i6.PrimeTemplate, i7.Button, i8.ProgressBar, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i9.Dialog, i10.InputTextarea],\n        styles: [\".field[_ngcontent-%COMP%]{margin-bottom:1rem}.block[_ngcontent-%COMP%]{display:block}.w-100[_ngcontent-%COMP%]{width:100%!important}.flex[_ngcontent-%COMP%]{display:flex}.align-items-center[_ngcontent-%COMP%]{align-items:center}.justify-content-between[_ngcontent-%COMP%]{justify-content:space-between}.justify-content-end[_ngcontent-%COMP%]{justify-content:flex-end}.gap-2[_ngcontent-%COMP%]{gap:.5rem}.mb-2[_ngcontent-%COMP%]{margin-bottom:.5rem}.mb-3[_ngcontent-%COMP%]{margin-bottom:1rem}.mb-4[_ngcontent-%COMP%]{margin-bottom:1.5rem}.mt-1[_ngcontent-%COMP%]{margin-top:.25rem}.mt-2[_ngcontent-%COMP%]{margin-top:.5rem}.mt-4[_ngcontent-%COMP%]{margin-top:1.5rem}.ml-2[_ngcontent-%COMP%]{margin-left:.5rem}.me-1[_ngcontent-%COMP%]{margin-right:.25rem}.me-2[_ngcontent-%COMP%]{margin-right:.5rem}.text-danger[_ngcontent-%COMP%]{color:#dc3545!important}.text-muted[_ngcontent-%COMP%]{color:#6c757d!important}.text-primary[_ngcontent-%COMP%]{color:#007bff!important}.alert[_ngcontent-%COMP%]{padding:.75rem 1rem;margin-bottom:1rem;border:1px solid transparent;border-radius:.375rem}.alert-danger[_ngcontent-%COMP%]{color:#721c24;background-color:#f8d7da;border-color:#f5c6cb}.alert-success[_ngcontent-%COMP%]{color:#155724;background-color:#d4edda;border-color:#c3e6cb}.p-inputgroup[_ngcontent-%COMP%]{display:flex;align-items:stretch;width:100%}.p-inputgroup[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .p-inputgroup[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{flex:1}textarea[_ngcontent-%COMP%]{font-family:inherit;resize:vertical;min-height:120px}small[_ngcontent-%COMP%]{font-size:.875em;line-height:1.25}label[_ngcontent-%COMP%]{font-weight:500;margin-bottom:.5rem}  .p-dropdown{width:100%}  .p-checkbox{margin-right:.5rem}  .p-button-sm{padding:.375rem .75rem;font-size:.875rem}  .p-progressbar{height:1rem;background-color:#e9ecef;border-radius:.375rem;overflow:hidden}  .p-progressbar-value{background-color:#007bff;transition:width .3s ease}\"]\n      });\n    }\n  }\n  return UploadDialogComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}