{"name": "zone.js", "version": "0.13.3", "description": "Zones for JavaScript", "main": "./bundles/zone.umd.js", "module": "./fesm2015/zone.js", "es2015": "./fesm2015/zone.js", "fesm2015": "./fesm2015/zone.js", "typings": "./zone.d.ts", "dependencies": {"tslib": "^2.3.0"}, "devDependencies": {"@externs/nodejs": "^1.5.0", "@types/node": "^10.9.4", "domino": "https://github.com/angular/domino.git#aa8de3486307f57a518b4b0d9e5e16d9fbd998d1", "google-closure-compiler": "^20230802.0.0", "jest": "^29.0", "jest-environment-jsdom": "^29.0.3", "jest-environment-node": "^29.0.3", "mocha": "^10.2.0", "mock-require": "3.0.3", "promises-aplus-tests": "^2.1.2"}, "scripts": {"closuretest": "./scripts/closure/closure_compiler.sh", "electrontest": "cd test/extra && node electron.js", "jest:test": "jest --config ./test/jest/jest.config.js ./test/jest/jest.spec.js", "jest:nodetest": "jest --config ./test/jest/jest.node.config.js ./test/jest/jest.spec.js", "promisetest": "node ./test/promise/promise-test.mjs", "promisefinallytest": "mocha ./test/promise/promise.finally.spec.mjs"}, "repository": {"type": "git", "url": "git://github.com/angular/angular.git", "directory": "packages/zone.js"}, "publishConfig": {"registry": "https://wombat-dressing-room.appspot.com"}, "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/angular/angular/issues"}, "exports": {"./package.json": {"default": "./package.json"}, ".": {"types": "./zone.d.ts", "require": "./bundles/zone.umd.js", "default": "./fesm2015/zone.js"}, "./testing": {"require": "./bundles/zone-testing.umd.js", "default": "./fesm2015/zone-testing.js"}, "./node": {"require": "./bundles/zone-node.umd.js", "default": "./fesm2015/zone-node.js"}, "./mix": {"require": "./bundles/zone-mix.umd.js", "default": "./fesm2015/zone-mix.js"}, "./plugins/*.min": {"require": "./bundles/*.umd.min.js", "default": "./fesm2015/*.min.js"}, "./plugins/*": {"require": "./bundles/*.umd.js", "default": "./fesm2015/*.js"}, "./fesm2015/*.js": {"default": "./fesm2015/*.js"}, "./fesm2015/*": {"default": "./fesm2015/*.js"}, "./bundles/*.js": {"default": "./bundles/*.js"}, "./bundles/*": {"default": "./bundles/*.js"}, "./dist/*.min": {"default": "./bundles/*.umd.min.js"}, "./dist/*": {"default": "./bundles/*.umd.js"}}}