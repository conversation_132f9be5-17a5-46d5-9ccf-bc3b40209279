{"ast": null, "code": "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nimport { DEFAULT_HEADERS } from '../lib/constants';\nimport { isStorageError } from '../lib/errors';\nimport { get, post, put, remove } from '../lib/fetch';\nimport { resolveFetch } from '../lib/helpers';\nclass StorageBucketApi {\n  constructor(url, headers = {}, fetch, opts) {\n    const baseUrl = new URL(url);\n    // if legacy uri is used, replace with new storage host (disables request buffering to allow > 50GB uploads)\n    // \"project-ref.supabase.co\" becomes \"project-ref.storage.supabase.co\"\n    if (opts === null || opts === void 0 ? void 0 : opts.useNewHostname) {\n      const isSupabaseHost = /supabase\\.(co|in|red)$/.test(baseUrl.hostname);\n      if (isSupabaseHost && !baseUrl.hostname.includes('storage.supabase.')) {\n        baseUrl.hostname = baseUrl.hostname.replace('supabase.', 'storage.supabase.');\n      }\n    }\n    this.url = baseUrl.href;\n    this.headers = Object.assign(Object.assign({}, DEFAULT_HEADERS), headers);\n    this.fetch = resolveFetch(fetch);\n  }\n  /**\n   * Retrieves the details of all Storage buckets within an existing project.\n   */\n  listBuckets() {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield get(this.fetch, `${this.url}/bucket`, {\n          headers: this.headers\n        });\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Retrieves the details of an existing Storage bucket.\n   *\n   * @param id The unique identifier of the bucket you would like to retrieve.\n   */\n  getBucket(id) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield get(this.fetch, `${this.url}/bucket/${id}`, {\n          headers: this.headers\n        });\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Creates a new Storage bucket\n   *\n   * @param id A unique identifier for the bucket you are creating.\n   * @param options.public The visibility of the bucket. Public buckets don't require an authorization token to download objects, but still require a valid token for all other operations. By default, buckets are private.\n   * @param options.fileSizeLimit specifies the max file size in bytes that can be uploaded to this bucket.\n   * The global file size limit takes precedence over this value.\n   * The default value is null, which doesn't set a per bucket file size limit.\n   * @param options.allowedMimeTypes specifies the allowed mime types that this bucket can accept during upload.\n   * The default value is null, which allows files with all mime types to be uploaded.\n   * Each mime type specified can be a wildcard, e.g. image/*, or a specific mime type, e.g. image/png.\n   * @returns newly created bucket id\n   * @param options.type (private-beta) specifies the bucket type. see `BucketType` for more details.\n   *   - default bucket type is `STANDARD`\n   */\n  createBucket(id, options = {\n    public: false\n  }) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield post(this.fetch, `${this.url}/bucket`, {\n          id,\n          name: id,\n          type: options.type,\n          public: options.public,\n          file_size_limit: options.fileSizeLimit,\n          allowed_mime_types: options.allowedMimeTypes\n        }, {\n          headers: this.headers\n        });\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Updates a Storage bucket\n   *\n   * @param id A unique identifier for the bucket you are updating.\n   * @param options.public The visibility of the bucket. Public buckets don't require an authorization token to download objects, but still require a valid token for all other operations.\n   * @param options.fileSizeLimit specifies the max file size in bytes that can be uploaded to this bucket.\n   * The global file size limit takes precedence over this value.\n   * The default value is null, which doesn't set a per bucket file size limit.\n   * @param options.allowedMimeTypes specifies the allowed mime types that this bucket can accept during upload.\n   * The default value is null, which allows files with all mime types to be uploaded.\n   * Each mime type specified can be a wildcard, e.g. image/*, or a specific mime type, e.g. image/png.\n   */\n  updateBucket(id, options) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield put(this.fetch, `${this.url}/bucket/${id}`, {\n          id,\n          name: id,\n          public: options.public,\n          file_size_limit: options.fileSizeLimit,\n          allowed_mime_types: options.allowedMimeTypes\n        }, {\n          headers: this.headers\n        });\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Removes all objects inside a single bucket.\n   *\n   * @param id The unique identifier of the bucket you would like to empty.\n   */\n  emptyBucket(id) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield post(this.fetch, `${this.url}/bucket/${id}/empty`, {}, {\n          headers: this.headers\n        });\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Deletes an existing bucket. A bucket can't be deleted with existing objects inside it.\n   * You must first `empty()` the bucket.\n   *\n   * @param id The unique identifier of the bucket you would like to delete.\n   */\n  deleteBucket(id) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield remove(this.fetch, `${this.url}/bucket/${id}`, {}, {\n          headers: this.headers\n        });\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n}\n//# sourceMappingURL=StorageBucketApi.js.map\nexport { StorageBucketApi as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}