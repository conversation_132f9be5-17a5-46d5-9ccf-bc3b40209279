{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/\\u062A\\u0637\\u0628\\u064A\\u0642 \\u0646\\u0627\\u0634\\u0631 \\u0627\\u064A\\u0627\\u062A \\u0642\\u0626\\u0627\\u0646\\u064A\\u0629/QuranVidGen/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject, from, throwError } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport { API_KEYS } from '../config/api-keys';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./error-handler.service\";\nexport let YoutubeService = /*#__PURE__*/(() => {\n  class YoutubeService {\n    constructor(errorHandler) {\n      this.errorHandler = errorHandler;\n      this.CLIENT_ID = API_KEYS.GOOGLE_CLIENT_ID;\n      this.API_KEY = API_KEYS.GOOGLE_API_KEY;\n      this.DISCOVERY_DOC = 'https://www.googleapis.com/discovery/v1/apis/youtube/v3/rest';\n      this.SCOPES = 'https://www.googleapis.com/auth/youtube.upload https://www.googleapis.com/auth/youtube';\n      this.authStatusSubject = new BehaviorSubject({\n        isAuthenticated: false\n      });\n      this.authStatus$ = this.authStatusSubject.asObservable();\n      this.uploadProgressSubject = new BehaviorSubject({\n        loaded: 0,\n        total: 0,\n        percentage: 0\n      });\n      this.uploadProgress$ = this.uploadProgressSubject.asObservable();\n      this.initializeGapi();\n    }\n    initializeGapi() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        try {\n          if (typeof gapi === 'undefined') {\n            yield _this.loadGapiScript();\n          }\n          yield gapi.load('auth2', () => {\n            gapi.auth2.init({\n              client_id: _this.CLIENT_ID\n            });\n          });\n          yield gapi.load('client', /*#__PURE__*/_asyncToGenerator(function* () {\n            yield gapi.client.init({\n              apiKey: _this.API_KEY,\n              clientId: _this.CLIENT_ID,\n              discoveryDocs: [_this.DISCOVERY_DOC],\n              scope: _this.SCOPES\n            });\n            // Check if user is already signed in\n            const authInstance = gapi.auth2.getAuthInstance();\n            if (authInstance.isSignedIn.get()) {\n              _this.updateAuthStatus(true);\n            }\n          }));\n        } catch (error) {\n          console.error('Error initializing Google API:', error);\n        }\n      })();\n    }\n    loadGapiScript() {\n      return new Promise((resolve, reject) => {\n        const script = document.createElement('script');\n        script.src = 'https://apis.google.com/js/api.js';\n        script.onload = () => resolve();\n        script.onerror = () => reject(new Error('Failed to load Google API script'));\n        document.head.appendChild(script);\n      });\n    }\n    signIn() {\n      return from(this.performSignIn()).pipe(map(() => {\n        this.updateAuthStatus(true);\n        return true;\n      }), catchError(error => {\n        this.errorHandler.handleError(error, 'youtube-auth');\n        return throwError(() => error);\n      }));\n    }\n    performSignIn() {\n      return _asyncToGenerator(function* () {\n        const authInstance = gapi.auth2.getAuthInstance();\n        yield authInstance.signIn();\n      })();\n    }\n    signOut() {\n      return from(this.performSignOut()).pipe(map(() => {\n        this.updateAuthStatus(false);\n        return true;\n      }), catchError(error => {\n        console.error('Sign out failed:', error);\n        return throwError(() => error);\n      }));\n    }\n    performSignOut() {\n      return _asyncToGenerator(function* () {\n        const authInstance = gapi.auth2.getAuthInstance();\n        yield authInstance.signOut();\n      })();\n    }\n    updateAuthStatus(isAuthenticated) {\n      if (isAuthenticated) {\n        const authInstance = gapi.auth2.getAuthInstance();\n        const user = authInstance.currentUser.get();\n        const profile = user.getBasicProfile();\n        this.authStatusSubject.next({\n          isAuthenticated: true,\n          user: {\n            name: profile.getName(),\n            email: profile.getEmail(),\n            picture: profile.getImageUrl()\n          }\n        });\n      } else {\n        this.authStatusSubject.next({\n          isAuthenticated: false\n        });\n      }\n    }\n    uploadVideo(videoBlob, videoDetails) {\n      return from(this.performVideoUpload(videoBlob, videoDetails)).pipe(catchError(error => {\n        this.errorHandler.handleError(error, 'youtube-upload');\n        return throwError(() => error);\n      }));\n    }\n    performVideoUpload(videoBlob, videoDetails) {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        const metadata = {\n          snippet: {\n            title: videoDetails.title,\n            description: videoDetails.description,\n            tags: videoDetails.tags,\n            categoryId: videoDetails.categoryId || '22' // People & Blogs\n          },\n\n          status: {\n            privacyStatus: videoDetails.privacy\n          }\n        };\n        const form = new FormData();\n        form.append('metadata', new Blob([JSON.stringify(metadata)], {\n          type: 'application/json'\n        }));\n        form.append('media', videoBlob);\n        const xhr = new XMLHttpRequest();\n        return new Promise((resolve, reject) => {\n          xhr.upload.addEventListener('progress', event => {\n            if (event.lengthComputable) {\n              const progress = {\n                loaded: event.loaded,\n                total: event.total,\n                percentage: Math.round(event.loaded / event.total * 100)\n              };\n              _this2.uploadProgressSubject.next(progress);\n            }\n          });\n          xhr.addEventListener('load', () => {\n            if (xhr.status === 200) {\n              const response = JSON.parse(xhr.responseText);\n              resolve(response.id);\n            } else {\n              reject(new Error(`Upload failed with status: ${xhr.status}`));\n            }\n          });\n          xhr.addEventListener('error', () => {\n            reject(new Error('Upload failed'));\n          });\n          const accessToken = gapi.auth2.getAuthInstance().currentUser.get().getAuthResponse().access_token;\n          xhr.open('POST', 'https://www.googleapis.com/upload/youtube/v3/videos?uploadType=multipart&part=snippet,status');\n          xhr.setRequestHeader('Authorization', `Bearer ${accessToken}`);\n          xhr.send(form);\n        });\n      })();\n    }\n    isAuthenticated() {\n      return this.authStatusSubject.value.isAuthenticated;\n    }\n    getCurrentUser() {\n      return this.authStatusSubject.value.user;\n    }\n    static {\n      this.ɵfac = function YoutubeService_Factory(t) {\n        return new (t || YoutubeService)(i0.ɵɵinject(i1.ErrorHandlerService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: YoutubeService,\n        factory: YoutubeService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return YoutubeService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}