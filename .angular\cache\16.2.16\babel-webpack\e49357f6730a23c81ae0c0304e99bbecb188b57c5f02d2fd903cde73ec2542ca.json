{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/\\u062A\\u0637\\u0628\\u064A\\u0642 \\u0646\\u0627\\u0634\\u0631 \\u0627\\u064A\\u0627\\u062A \\u0642\\u0626\\u0627\\u0646\\u064A\\u0629/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*! Capacitor: https://capacitorjs.com/ - MIT License */\nconst createCapacitorPlatforms = win => {\n  const defaultPlatformMap = new Map();\n  defaultPlatformMap.set('web', {\n    name: 'web'\n  });\n  const capPlatforms = win.CapacitorPlatforms || {\n    currentPlatform: {\n      name: 'web'\n    },\n    platforms: defaultPlatformMap\n  };\n  const addPlatform = (name, platform) => {\n    capPlatforms.platforms.set(name, platform);\n  };\n  const setPlatform = name => {\n    if (capPlatforms.platforms.has(name)) {\n      capPlatforms.currentPlatform = capPlatforms.platforms.get(name);\n    }\n  };\n  capPlatforms.addPlatform = addPlatform;\n  capPlatforms.setPlatform = setPlatform;\n  return capPlatforms;\n};\nconst initPlatforms = win => win.CapacitorPlatforms = createCapacitorPlatforms(win);\n/**\n * @deprecated Set `CapacitorCustomPlatform` on the window object prior to runtime executing in the web app instead\n */\nconst CapacitorPlatforms = /*#__PURE__*/initPlatforms(typeof globalThis !== 'undefined' ? globalThis : typeof self !== 'undefined' ? self : typeof window !== 'undefined' ? window : typeof global !== 'undefined' ? global : {});\n/**\n * @deprecated Set `CapacitorCustomPlatform` on the window object prior to runtime executing in the web app instead\n */\nconst addPlatform = CapacitorPlatforms.addPlatform;\n/**\n * @deprecated Set `CapacitorCustomPlatform` on the window object prior to runtime executing in the web app instead\n */\nconst setPlatform = CapacitorPlatforms.setPlatform;\nconst legacyRegisterWebPlugin = (cap, webPlugin) => {\n  var _a;\n  const config = webPlugin.config;\n  const Plugins = cap.Plugins;\n  if (!(config === null || config === void 0 ? void 0 : config.name)) {\n    // TODO: add link to upgrade guide\n    throw new Error(`Capacitor WebPlugin is using the deprecated \"registerWebPlugin()\" function, but without the config. Please use \"registerPlugin()\" instead to register this web plugin.\"`);\n  }\n  // TODO: add link to upgrade guide\n  console.warn(`Capacitor plugin \"${config.name}\" is using the deprecated \"registerWebPlugin()\" function`);\n  if (!Plugins[config.name] || ((_a = config === null || config === void 0 ? void 0 : config.platforms) === null || _a === void 0 ? void 0 : _a.includes(cap.getPlatform()))) {\n    // Add the web plugin into the plugins registry if there already isn't\n    // an existing one. If it doesn't already exist, that means\n    // there's no existing native implementation for it.\n    // - OR -\n    // If we already have a plugin registered (meaning it was defined in the native layer),\n    // then we should only overwrite it if the corresponding web plugin activates on\n    // a certain platform. For example: Geolocation uses the WebPlugin on Android but not iOS\n    Plugins[config.name] = webPlugin;\n  }\n};\nvar ExceptionCode = /*#__PURE__*/function (ExceptionCode) {\n  /**\n   * API is not implemented.\n   *\n   * This usually means the API can't be used because it is not implemented for\n   * the current platform.\n   */\n  ExceptionCode[\"Unimplemented\"] = \"UNIMPLEMENTED\";\n  /**\n   * API is not available.\n   *\n   * This means the API can't be used right now because:\n   *   - it is currently missing a prerequisite, such as network connectivity\n   *   - it requires a particular platform or browser version\n   */\n  ExceptionCode[\"Unavailable\"] = \"UNAVAILABLE\";\n  return ExceptionCode;\n}(ExceptionCode || {});\nclass CapacitorException extends Error {\n  constructor(message, code, data) {\n    super(message);\n    this.message = message;\n    this.code = code;\n    this.data = data;\n  }\n}\nconst getPlatformId = win => {\n  var _a, _b;\n  if (win === null || win === void 0 ? void 0 : win.androidBridge) {\n    return 'android';\n  } else if ((_b = (_a = win === null || win === void 0 ? void 0 : win.webkit) === null || _a === void 0 ? void 0 : _a.messageHandlers) === null || _b === void 0 ? void 0 : _b.bridge) {\n    return 'ios';\n  } else {\n    return 'web';\n  }\n};\nconst createCapacitor = win => {\n  var _a, _b, _c, _d, _e;\n  const capCustomPlatform = win.CapacitorCustomPlatform || null;\n  const cap = win.Capacitor || {};\n  const Plugins = cap.Plugins = cap.Plugins || {};\n  /**\n   * @deprecated Use `capCustomPlatform` instead, default functions like registerPlugin will function with the new object.\n   */\n  const capPlatforms = win.CapacitorPlatforms;\n  const defaultGetPlatform = () => {\n    return capCustomPlatform !== null ? capCustomPlatform.name : getPlatformId(win);\n  };\n  const getPlatform = ((_a = capPlatforms === null || capPlatforms === void 0 ? void 0 : capPlatforms.currentPlatform) === null || _a === void 0 ? void 0 : _a.getPlatform) || defaultGetPlatform;\n  const defaultIsNativePlatform = () => getPlatform() !== 'web';\n  const isNativePlatform = ((_b = capPlatforms === null || capPlatforms === void 0 ? void 0 : capPlatforms.currentPlatform) === null || _b === void 0 ? void 0 : _b.isNativePlatform) || defaultIsNativePlatform;\n  const defaultIsPluginAvailable = pluginName => {\n    const plugin = registeredPlugins.get(pluginName);\n    if (plugin === null || plugin === void 0 ? void 0 : plugin.platforms.has(getPlatform())) {\n      // JS implementation available for the current platform.\n      return true;\n    }\n    if (getPluginHeader(pluginName)) {\n      // Native implementation available.\n      return true;\n    }\n    return false;\n  };\n  const isPluginAvailable = ((_c = capPlatforms === null || capPlatforms === void 0 ? void 0 : capPlatforms.currentPlatform) === null || _c === void 0 ? void 0 : _c.isPluginAvailable) || defaultIsPluginAvailable;\n  const defaultGetPluginHeader = pluginName => {\n    var _a;\n    return (_a = cap.PluginHeaders) === null || _a === void 0 ? void 0 : _a.find(h => h.name === pluginName);\n  };\n  const getPluginHeader = ((_d = capPlatforms === null || capPlatforms === void 0 ? void 0 : capPlatforms.currentPlatform) === null || _d === void 0 ? void 0 : _d.getPluginHeader) || defaultGetPluginHeader;\n  const handleError = err => win.console.error(err);\n  const pluginMethodNoop = (_target, prop, pluginName) => {\n    return Promise.reject(`${pluginName} does not have an implementation of \"${prop}\".`);\n  };\n  const registeredPlugins = new Map();\n  const defaultRegisterPlugin = (pluginName, jsImplementations = {}) => {\n    const registeredPlugin = registeredPlugins.get(pluginName);\n    if (registeredPlugin) {\n      console.warn(`Capacitor plugin \"${pluginName}\" already registered. Cannot register plugins twice.`);\n      return registeredPlugin.proxy;\n    }\n    const platform = getPlatform();\n    const pluginHeader = getPluginHeader(pluginName);\n    let jsImplementation;\n    const loadPluginImplementation = /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* () {\n        if (!jsImplementation && platform in jsImplementations) {\n          jsImplementation = typeof jsImplementations[platform] === 'function' ? jsImplementation = yield jsImplementations[platform]() : jsImplementation = jsImplementations[platform];\n        } else if (capCustomPlatform !== null && !jsImplementation && 'web' in jsImplementations) {\n          jsImplementation = typeof jsImplementations['web'] === 'function' ? jsImplementation = yield jsImplementations['web']() : jsImplementation = jsImplementations['web'];\n        }\n        return jsImplementation;\n      });\n      return function loadPluginImplementation() {\n        return _ref.apply(this, arguments);\n      };\n    }();\n    const createPluginMethod = (impl, prop) => {\n      var _a, _b;\n      if (pluginHeader) {\n        const methodHeader = pluginHeader === null || pluginHeader === void 0 ? void 0 : pluginHeader.methods.find(m => prop === m.name);\n        if (methodHeader) {\n          if (methodHeader.rtype === 'promise') {\n            return options => cap.nativePromise(pluginName, prop.toString(), options);\n          } else {\n            return (options, callback) => cap.nativeCallback(pluginName, prop.toString(), options, callback);\n          }\n        } else if (impl) {\n          return (_a = impl[prop]) === null || _a === void 0 ? void 0 : _a.bind(impl);\n        }\n      } else if (impl) {\n        return (_b = impl[prop]) === null || _b === void 0 ? void 0 : _b.bind(impl);\n      } else {\n        throw new CapacitorException(`\"${pluginName}\" plugin is not implemented on ${platform}`, ExceptionCode.Unimplemented);\n      }\n    };\n    const createPluginMethodWrapper = prop => {\n      let remove;\n      const wrapper = (...args) => {\n        const p = loadPluginImplementation().then(impl => {\n          const fn = createPluginMethod(impl, prop);\n          if (fn) {\n            const p = fn(...args);\n            remove = p === null || p === void 0 ? void 0 : p.remove;\n            return p;\n          } else {\n            throw new CapacitorException(`\"${pluginName}.${prop}()\" is not implemented on ${platform}`, ExceptionCode.Unimplemented);\n          }\n        });\n        if (prop === 'addListener') {\n          p.remove = /*#__PURE__*/_asyncToGenerator(function* () {\n            return remove();\n          });\n        }\n        return p;\n      };\n      // Some flair ✨\n      wrapper.toString = () => `${prop.toString()}() { [capacitor code] }`;\n      Object.defineProperty(wrapper, 'name', {\n        value: prop,\n        writable: false,\n        configurable: false\n      });\n      return wrapper;\n    };\n    const addListener = createPluginMethodWrapper('addListener');\n    const removeListener = createPluginMethodWrapper('removeListener');\n    const addListenerNative = (eventName, callback) => {\n      const call = addListener({\n        eventName\n      }, callback);\n      const remove = /*#__PURE__*/function () {\n        var _ref3 = _asyncToGenerator(function* () {\n          const callbackId = yield call;\n          removeListener({\n            eventName,\n            callbackId\n          }, callback);\n        });\n        return function remove() {\n          return _ref3.apply(this, arguments);\n        };\n      }();\n      const p = new Promise(resolve => call.then(() => resolve({\n        remove\n      })));\n      p.remove = /*#__PURE__*/_asyncToGenerator(function* () {\n        console.warn(`Using addListener() without 'await' is deprecated.`);\n        yield remove();\n      });\n      return p;\n    };\n    const proxy = new Proxy({}, {\n      get(_, prop) {\n        switch (prop) {\n          // https://github.com/facebook/react/issues/20030\n          case '$$typeof':\n            return undefined;\n          case 'toJSON':\n            return () => ({});\n          case 'addListener':\n            return pluginHeader ? addListenerNative : addListener;\n          case 'removeListener':\n            return removeListener;\n          default:\n            return createPluginMethodWrapper(prop);\n        }\n      }\n    });\n    Plugins[pluginName] = proxy;\n    registeredPlugins.set(pluginName, {\n      name: pluginName,\n      proxy,\n      platforms: new Set([...Object.keys(jsImplementations), ...(pluginHeader ? [platform] : [])])\n    });\n    return proxy;\n  };\n  const registerPlugin = ((_e = capPlatforms === null || capPlatforms === void 0 ? void 0 : capPlatforms.currentPlatform) === null || _e === void 0 ? void 0 : _e.registerPlugin) || defaultRegisterPlugin;\n  // Add in convertFileSrc for web, it will already be available in native context\n  if (!cap.convertFileSrc) {\n    cap.convertFileSrc = filePath => filePath;\n  }\n  cap.getPlatform = getPlatform;\n  cap.handleError = handleError;\n  cap.isNativePlatform = isNativePlatform;\n  cap.isPluginAvailable = isPluginAvailable;\n  cap.pluginMethodNoop = pluginMethodNoop;\n  cap.registerPlugin = registerPlugin;\n  cap.Exception = CapacitorException;\n  cap.DEBUG = !!cap.DEBUG;\n  cap.isLoggingEnabled = !!cap.isLoggingEnabled;\n  // Deprecated props\n  cap.platform = cap.getPlatform();\n  cap.isNative = cap.isNativePlatform();\n  return cap;\n};\nconst initCapacitorGlobal = win => win.Capacitor = createCapacitor(win);\nconst Capacitor = /*#__PURE__*/initCapacitorGlobal(typeof globalThis !== 'undefined' ? globalThis : typeof self !== 'undefined' ? self : typeof window !== 'undefined' ? window : typeof global !== 'undefined' ? global : {});\nconst registerPlugin = Capacitor.registerPlugin;\n/**\n * @deprecated Provided for backwards compatibility for Capacitor v2 plugins.\n * Capacitor v3 plugins should import the plugin directly. This \"Plugins\"\n * export is deprecated in v3, and will be removed in v4.\n */\nconst Plugins = Capacitor.Plugins;\n/**\n * Provided for backwards compatibility. Use the registerPlugin() API\n * instead, and provide the web plugin as the \"web\" implmenetation.\n * For example\n *\n * export const Example = registerPlugin('Example', {\n *   web: () => import('./web').then(m => new m.Example())\n * })\n *\n * @deprecated Deprecated in v3, will be removed from v4.\n */\nconst registerWebPlugin = plugin => legacyRegisterWebPlugin(Capacitor, plugin);\n\n/**\n * Base class web plugins should extend.\n */\nclass WebPlugin {\n  constructor(config) {\n    this.listeners = {};\n    this.retainedEventArguments = {};\n    this.windowListeners = {};\n    if (config) {\n      // TODO: add link to upgrade guide\n      console.warn(`Capacitor WebPlugin \"${config.name}\" config object was deprecated in v3 and will be removed in v4.`);\n      this.config = config;\n    }\n  }\n  addListener(eventName, listenerFunc) {\n    var _this = this;\n    let firstListener = false;\n    const listeners = this.listeners[eventName];\n    if (!listeners) {\n      this.listeners[eventName] = [];\n      firstListener = true;\n    }\n    this.listeners[eventName].push(listenerFunc);\n    // If we haven't added a window listener for this event and it requires one,\n    // go ahead and add it\n    const windowListener = this.windowListeners[eventName];\n    if (windowListener && !windowListener.registered) {\n      this.addWindowListener(windowListener);\n    }\n    if (firstListener) {\n      this.sendRetainedArgumentsForEvent(eventName);\n    }\n    const remove = /*#__PURE__*/function () {\n      var _ref5 = _asyncToGenerator(function* () {\n        return _this.removeListener(eventName, listenerFunc);\n      });\n      return function remove() {\n        return _ref5.apply(this, arguments);\n      };\n    }();\n    const p = Promise.resolve({\n      remove\n    });\n    return p;\n  }\n  removeAllListeners() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.listeners = {};\n      for (const listener in _this2.windowListeners) {\n        _this2.removeWindowListener(_this2.windowListeners[listener]);\n      }\n      _this2.windowListeners = {};\n    })();\n  }\n  notifyListeners(eventName, data, retainUntilConsumed) {\n    const listeners = this.listeners[eventName];\n    if (!listeners) {\n      if (retainUntilConsumed) {\n        let args = this.retainedEventArguments[eventName];\n        if (!args) {\n          args = [];\n        }\n        args.push(data);\n        this.retainedEventArguments[eventName] = args;\n      }\n      return;\n    }\n    listeners.forEach(listener => listener(data));\n  }\n  hasListeners(eventName) {\n    return !!this.listeners[eventName].length;\n  }\n  registerWindowListener(windowEventName, pluginEventName) {\n    this.windowListeners[pluginEventName] = {\n      registered: false,\n      windowEventName,\n      pluginEventName,\n      handler: event => {\n        this.notifyListeners(pluginEventName, event);\n      }\n    };\n  }\n  unimplemented(msg = 'not implemented') {\n    return new Capacitor.Exception(msg, ExceptionCode.Unimplemented);\n  }\n  unavailable(msg = 'not available') {\n    return new Capacitor.Exception(msg, ExceptionCode.Unavailable);\n  }\n  removeListener(eventName, listenerFunc) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const listeners = _this3.listeners[eventName];\n      if (!listeners) {\n        return;\n      }\n      const index = listeners.indexOf(listenerFunc);\n      _this3.listeners[eventName].splice(index, 1);\n      // If there are no more listeners for this type of event,\n      // remove the window listener\n      if (!_this3.listeners[eventName].length) {\n        _this3.removeWindowListener(_this3.windowListeners[eventName]);\n      }\n    })();\n  }\n  addWindowListener(handle) {\n    window.addEventListener(handle.windowEventName, handle.handler);\n    handle.registered = true;\n  }\n  removeWindowListener(handle) {\n    if (!handle) {\n      return;\n    }\n    window.removeEventListener(handle.windowEventName, handle.handler);\n    handle.registered = false;\n  }\n  sendRetainedArgumentsForEvent(eventName) {\n    const args = this.retainedEventArguments[eventName];\n    if (!args) {\n      return;\n    }\n    delete this.retainedEventArguments[eventName];\n    args.forEach(arg => {\n      this.notifyListeners(eventName, arg);\n    });\n  }\n}\nconst WebView = /*#__PURE__*/registerPlugin('WebView');\n/******** END WEB VIEW PLUGIN ********/\n/******** COOKIES PLUGIN ********/\n/**\n * Safely web encode a string value (inspired by js-cookie)\n * @param str The string value to encode\n */\nconst encode = str => encodeURIComponent(str).replace(/%(2[346B]|5E|60|7C)/g, decodeURIComponent).replace(/[()]/g, escape);\n/**\n * Safely web decode a string value (inspired by js-cookie)\n * @param str The string value to decode\n */\nconst decode = str => str.replace(/(%[\\dA-F]{2})+/gi, decodeURIComponent);\nclass CapacitorCookiesPluginWeb extends WebPlugin {\n  getCookies() {\n    return _asyncToGenerator(function* () {\n      const cookies = document.cookie;\n      const cookieMap = {};\n      cookies.split(';').forEach(cookie => {\n        if (cookie.length <= 0) return;\n        // Replace first \"=\" with CAP_COOKIE to prevent splitting on additional \"=\"\n        let [key, value] = cookie.replace(/=/, 'CAP_COOKIE').split('CAP_COOKIE');\n        key = decode(key).trim();\n        value = decode(value).trim();\n        cookieMap[key] = value;\n      });\n      return cookieMap;\n    })();\n  }\n  setCookie(options) {\n    return _asyncToGenerator(function* () {\n      try {\n        // Safely Encoded Key/Value\n        const encodedKey = encode(options.key);\n        const encodedValue = encode(options.value);\n        // Clean & sanitize options\n        const expires = `; expires=${(options.expires || '').replace('expires=', '')}`; // Default is \"; expires=\"\n        const path = (options.path || '/').replace('path=', ''); // Default is \"path=/\"\n        const domain = options.url != null && options.url.length > 0 ? `domain=${options.url}` : '';\n        document.cookie = `${encodedKey}=${encodedValue || ''}${expires}; path=${path}; ${domain};`;\n      } catch (error) {\n        return Promise.reject(error);\n      }\n    })();\n  }\n  deleteCookie(options) {\n    return _asyncToGenerator(function* () {\n      try {\n        document.cookie = `${options.key}=; Max-Age=0`;\n      } catch (error) {\n        return Promise.reject(error);\n      }\n    })();\n  }\n  clearCookies() {\n    return _asyncToGenerator(function* () {\n      try {\n        const cookies = document.cookie.split(';') || [];\n        for (const cookie of cookies) {\n          document.cookie = cookie.replace(/^ +/, '').replace(/=.*/, `=;expires=${new Date().toUTCString()};path=/`);\n        }\n      } catch (error) {\n        return Promise.reject(error);\n      }\n    })();\n  }\n  clearAllCookies() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        yield _this4.clearCookies();\n      } catch (error) {\n        return Promise.reject(error);\n      }\n    })();\n  }\n}\nconst CapacitorCookies = registerPlugin('CapacitorCookies', {\n  web: () => new CapacitorCookiesPluginWeb()\n});\n// UTILITY FUNCTIONS\n/**\n * Read in a Blob value and return it as a base64 string\n * @param blob The blob value to convert to a base64 string\n */\nconst readBlobAsBase64 = /*#__PURE__*/function () {\n  var _ref6 = _asyncToGenerator(function* (blob) {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.onload = () => {\n        const base64String = reader.result;\n        // remove prefix \"data:application/pdf;base64,\"\n        resolve(base64String.indexOf(',') >= 0 ? base64String.split(',')[1] : base64String);\n      };\n      reader.onerror = error => reject(error);\n      reader.readAsDataURL(blob);\n    });\n  });\n  return function readBlobAsBase64(_x) {\n    return _ref6.apply(this, arguments);\n  };\n}();\n/**\n * Normalize an HttpHeaders map by lowercasing all of the values\n * @param headers The HttpHeaders object to normalize\n */\nconst normalizeHttpHeaders = (headers = {}) => {\n  const originalKeys = Object.keys(headers);\n  const loweredKeys = Object.keys(headers).map(k => k.toLocaleLowerCase());\n  const normalized = loweredKeys.reduce((acc, key, index) => {\n    acc[key] = headers[originalKeys[index]];\n    return acc;\n  }, {});\n  return normalized;\n};\n/**\n * Builds a string of url parameters that\n * @param params A map of url parameters\n * @param shouldEncode true if you should encodeURIComponent() the values (true by default)\n */\nconst buildUrlParams = (params, shouldEncode = true) => {\n  if (!params) return null;\n  const output = Object.entries(params).reduce((accumulator, entry) => {\n    const [key, value] = entry;\n    let encodedValue;\n    let item;\n    if (Array.isArray(value)) {\n      item = '';\n      value.forEach(str => {\n        encodedValue = shouldEncode ? encodeURIComponent(str) : str;\n        item += `${key}=${encodedValue}&`;\n      });\n      // last character will always be \"&\" so slice it off\n      item.slice(0, -1);\n    } else {\n      encodedValue = shouldEncode ? encodeURIComponent(value) : value;\n      item = `${key}=${encodedValue}`;\n    }\n    return `${accumulator}&${item}`;\n  }, '');\n  // Remove initial \"&\" from the reduce\n  return output.substr(1);\n};\n/**\n * Build the RequestInit object based on the options passed into the initial request\n * @param options The Http plugin options\n * @param extra Any extra RequestInit values\n */\nconst buildRequestInit = (options, extra = {}) => {\n  const output = Object.assign({\n    method: options.method || 'GET',\n    headers: options.headers\n  }, extra);\n  // Get the content-type\n  const headers = normalizeHttpHeaders(options.headers);\n  const type = headers['content-type'] || '';\n  // If body is already a string, then pass it through as-is.\n  if (typeof options.data === 'string') {\n    output.body = options.data;\n  }\n  // Build request initializers based off of content-type\n  else if (type.includes('application/x-www-form-urlencoded')) {\n    const params = new URLSearchParams();\n    for (const [key, value] of Object.entries(options.data || {})) {\n      params.set(key, value);\n    }\n    output.body = params.toString();\n  } else if (type.includes('multipart/form-data') || options.data instanceof FormData) {\n    const form = new FormData();\n    if (options.data instanceof FormData) {\n      options.data.forEach((value, key) => {\n        form.append(key, value);\n      });\n    } else {\n      for (const key of Object.keys(options.data)) {\n        form.append(key, options.data[key]);\n      }\n    }\n    output.body = form;\n    const headers = new Headers(output.headers);\n    headers.delete('content-type'); // content-type will be set by `window.fetch` to includy boundary\n    output.headers = headers;\n  } else if (type.includes('application/json') || typeof options.data === 'object') {\n    output.body = JSON.stringify(options.data);\n  }\n  return output;\n};\n// WEB IMPLEMENTATION\nclass CapacitorHttpPluginWeb extends WebPlugin {\n  /**\n   * Perform an Http request given a set of options\n   * @param options Options to build the HTTP request\n   */\n  request(options) {\n    return _asyncToGenerator(function* () {\n      const requestInit = buildRequestInit(options, options.webFetchExtra);\n      const urlParams = buildUrlParams(options.params, options.shouldEncodeUrlParams);\n      const url = urlParams ? `${options.url}?${urlParams}` : options.url;\n      const response = yield fetch(url, requestInit);\n      const contentType = response.headers.get('content-type') || '';\n      // Default to 'text' responseType so no parsing happens\n      let {\n        responseType = 'text'\n      } = response.ok ? options : {};\n      // If the response content-type is json, force the response to be json\n      if (contentType.includes('application/json')) {\n        responseType = 'json';\n      }\n      let data;\n      let blob;\n      switch (responseType) {\n        case 'arraybuffer':\n        case 'blob':\n          blob = yield response.blob();\n          data = yield readBlobAsBase64(blob);\n          break;\n        case 'json':\n          data = yield response.json();\n          break;\n        case 'document':\n        case 'text':\n        default:\n          data = yield response.text();\n      }\n      // Convert fetch headers to Capacitor HttpHeaders\n      const headers = {};\n      response.headers.forEach((value, key) => {\n        headers[key] = value;\n      });\n      return {\n        data,\n        headers,\n        status: response.status,\n        url: response.url\n      };\n    })();\n  }\n  /**\n   * Perform an Http GET request given a set of options\n   * @param options Options to build the HTTP request\n   */\n  get(options) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      return _this5.request(Object.assign(Object.assign({}, options), {\n        method: 'GET'\n      }));\n    })();\n  }\n  /**\n   * Perform an Http POST request given a set of options\n   * @param options Options to build the HTTP request\n   */\n  post(options) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      return _this6.request(Object.assign(Object.assign({}, options), {\n        method: 'POST'\n      }));\n    })();\n  }\n  /**\n   * Perform an Http PUT request given a set of options\n   * @param options Options to build the HTTP request\n   */\n  put(options) {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      return _this7.request(Object.assign(Object.assign({}, options), {\n        method: 'PUT'\n      }));\n    })();\n  }\n  /**\n   * Perform an Http PATCH request given a set of options\n   * @param options Options to build the HTTP request\n   */\n  patch(options) {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      return _this8.request(Object.assign(Object.assign({}, options), {\n        method: 'PATCH'\n      }));\n    })();\n  }\n  /**\n   * Perform an Http DELETE request given a set of options\n   * @param options Options to build the HTTP request\n   */\n  delete(options) {\n    var _this9 = this;\n    return _asyncToGenerator(function* () {\n      return _this9.request(Object.assign(Object.assign({}, options), {\n        method: 'DELETE'\n      }));\n    })();\n  }\n}\nconst CapacitorHttp = registerPlugin('CapacitorHttp', {\n  web: () => new CapacitorHttpPluginWeb()\n});\n/******** END HTTP PLUGIN ********/\n\nexport { Capacitor, CapacitorCookies, CapacitorException, CapacitorHttp, CapacitorPlatforms, ExceptionCode, Plugins, WebPlugin, WebView, addPlatform, buildRequestInit, registerPlugin, registerWebPlugin, setPlatform };\n//# sourceMappingURL=index.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}