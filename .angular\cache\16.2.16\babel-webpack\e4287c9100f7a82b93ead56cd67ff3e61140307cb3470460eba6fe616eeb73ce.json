{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component } from '@angular/core';\nimport { BaseIcon } from 'primeng/baseicon';\nimport { UniqueComponentId } from 'primeng/utils';\nlet ExclamationTriangleIcon = /*#__PURE__*/(() => {\n  class ExclamationTriangleIcon extends BaseIcon {\n    pathId;\n    ngOnInit() {\n      this.pathId = 'url(#' + UniqueComponentId() + ')';\n    }\n    static ɵfac = /* @__PURE__ */function () {\n      let ɵExclamationTriangleIcon_BaseFactory;\n      return function ExclamationTriangleIcon_Factory(t) {\n        return (ɵExclamationTriangleIcon_BaseFactory || (ɵExclamationTriangleIcon_BaseFactory = i0.ɵɵgetInheritedFactory(ExclamationTriangleIcon)))(t || ExclamationTriangleIcon);\n      };\n    }();\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: ExclamationTriangleIcon,\n      selectors: [[\"ExclamationTriangleIcon\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 8,\n      vars: 7,\n      consts: [[\"width\", \"14\", \"height\", \"14\", \"viewBox\", \"0 0 14 14\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"d\", \"M13.4018 13.1893H0.598161C0.49329 13.189 0.390283 13.1615 0.299143 13.1097C0.208003 13.0578 0.131826 12.9832 0.0780112 12.8932C0.0268539 12.8015 0 12.6982 0 12.5931C0 12.4881 0.0268539 12.3848 0.0780112 12.293L6.47985 1.08982C6.53679 1.00399 6.61408 0.933574 6.70484 0.884867C6.7956 0.836159 6.897 0.810669 7 0.810669C7.103 0.810669 7.2044 0.836159 7.29516 0.884867C7.38592 0.933574 7.46321 1.00399 7.52015 1.08982L13.922 12.293C13.9731 12.3848 14 12.4881 14 12.5931C14 12.6982 13.9731 12.8015 13.922 12.8932C13.8682 12.9832 13.792 13.0578 13.7009 13.1097C13.6097 13.1615 13.5067 13.189 13.4018 13.1893ZM1.63046 11.989H12.3695L7 2.59425L1.63046 11.989Z\", \"fill\", \"currentColor\"], [\"d\", \"M6.99996 8.78801C6.84143 8.78594 6.68997 8.72204 6.57787 8.60993C6.46576 8.49782 6.40186 8.34637 6.39979 8.18784V5.38703C6.39979 5.22786 6.46302 5.0752 6.57557 4.96265C6.68813 4.85009 6.84078 4.78686 6.99996 4.78686C7.15914 4.78686 7.31179 4.85009 7.42435 4.96265C7.5369 5.0752 7.60013 5.22786 7.60013 5.38703V8.18784C7.59806 8.34637 7.53416 8.49782 7.42205 8.60993C7.30995 8.72204 7.15849 8.78594 6.99996 8.78801Z\", \"fill\", \"currentColor\"], [\"d\", \"M6.99996 11.1887C6.84143 11.1866 6.68997 11.1227 6.57787 11.0106C6.46576 10.8985 6.40186 10.7471 6.39979 10.5885V10.1884C6.39979 10.0292 6.46302 9.87658 6.57557 9.76403C6.68813 9.65147 6.84078 9.58824 6.99996 9.58824C7.15914 9.58824 7.31179 9.65147 7.42435 9.76403C7.5369 9.87658 7.60013 10.0292 7.60013 10.1884V10.5885C7.59806 10.7471 7.53416 10.8985 7.42205 11.0106C7.30995 11.1227 7.15849 11.1866 6.99996 11.1887Z\", \"fill\", \"currentColor\"], [3, \"id\"], [\"width\", \"14\", \"height\", \"14\", \"fill\", \"white\"]],\n      template: function ExclamationTriangleIcon_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(0, \"svg\", 0)(1, \"g\");\n          i0.ɵɵelement(2, \"path\", 1)(3, \"path\", 2)(4, \"path\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"defs\")(6, \"clipPath\", 4);\n          i0.ɵɵelement(7, \"rect\", 5);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.getClassNames());\n          i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel)(\"aria-hidden\", ctx.ariaHidden)(\"role\", ctx.role);\n          i0.ɵɵadvance(1);\n          i0.ɵɵattribute(\"clip-path\", ctx.pathId);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"id\", ctx.pathId);\n        }\n      },\n      encapsulation: 2\n    });\n  }\n  return ExclamationTriangleIcon;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ExclamationTriangleIcon };\n//# sourceMappingURL=primeng-icons-exclamationtriangle.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}