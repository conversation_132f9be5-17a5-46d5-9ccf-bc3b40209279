import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { AdMobService, AdConfig } from '../../Services/admob.service';
import { SupabaseService } from '../../Services/supabase.service';

@Component({
  selector: 'app-admob-settings',
  templateUrl: './admob-settings.component.html',
  styleUrls: ['./admob-settings.component.scss']
})
export class AdMobSettingsComponent implements OnInit {
  settingsForm!: FormGroup;
  isLoading = false;
  saveMessage = '';
  adStats: any = {};
  testAdResult = '';

  constructor(
    private fb: FormBuilder,
    private adMobService: AdMobService,
    private supabaseService: SupabaseService
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    this.loadCurrentSettings();
    this.loadAdStats();
  }

  private initializeForm(): void {
    this.settingsForm = this.fb.group({
      appId: ['', [Validators.required, Validators.pattern(/^ca-app-pub-\d+~\d+$/)]],
      adUnitId: ['', [Validators.required, Validators.pattern(/^ca-app-pub-\d+\/\d+$/)]],
      testMode: [false]
    });
  }

  private loadCurrentSettings(): void {
    const currentConfig = this.adMobService.getConfig();
    this.settingsForm.patchValue(currentConfig);
  }

  private loadAdStats(): void {
    this.adStats = this.adMobService.getAdStats();
  }

  onSaveSettings(): void {
    if (this.settingsForm.valid) {
      this.isLoading = true;
      this.saveMessage = '';

      const newConfig: AdConfig = this.settingsForm.value;
      
      // Update AdMob service configuration
      this.adMobService.updateConfig(newConfig);

      // Save to database
      this.supabaseService.setAppSetting('admob_app_id', newConfig.appId, 'معرف تطبيق AdMob').subscribe({
        next: () => {
          this.supabaseService.setAppSetting('admob_ad_unit_id', newConfig.adUnitId, 'معرف وحدة الإعلان في AdMob').subscribe({
            next: () => {
              this.supabaseService.setAppSetting('admob_test_mode', newConfig.testMode, 'وضع الاختبار لـ AdMob').subscribe({
                next: () => {
                  this.isLoading = false;
                  this.saveMessage = 'تم حفظ الإعدادات بنجاح ✅';
                  setTimeout(() => this.saveMessage = '', 3000);
                },
                error: (error) => {
                  this.isLoading = false;
                  this.saveMessage = 'خطأ في حفظ وضع الاختبار ❌';
                  console.error('Error saving test mode:', error);
                }
              });
            },
            error: (error) => {
              this.isLoading = false;
              this.saveMessage = 'خطأ في حفظ معرف الوحدة الإعلانية ❌';
              console.error('Error saving ad unit ID:', error);
            }
          });
        },
        error: (error) => {
          this.isLoading = false;
          this.saveMessage = 'خطأ في حفظ معرف التطبيق ❌';
          console.error('Error saving app ID:', error);
        }
      });
    }
  }

  async onTestAd(): Promise<void> {
    this.testAdResult = '';
    this.isLoading = true;

    try {
      // Enable test mode temporarily
      const originalConfig = this.adMobService.getConfig();
      this.adMobService.updateConfig({ testMode: true });

      // Load and show test ad
      const loaded = await this.adMobService.loadRewardedAd();
      if (!loaded) {
        throw new Error('فشل في تحميل الإعلان التجريبي');
      }

      const adResult$ = await this.adMobService.showRewardedAd();
      
      adResult$.subscribe({
        next: (result) => {
          this.isLoading = false;
          if (result.success) {
            this.testAdResult = 'تم عرض الإعلان التجريبي بنجاح ✅';
          } else {
            this.testAdResult = `فشل في عرض الإعلان: ${result.error} ❌`;
          }
          
          // Restore original configuration
          this.adMobService.updateConfig(originalConfig);
          
          setTimeout(() => this.testAdResult = '', 5000);
        },
        error: (error) => {
          this.isLoading = false;
          this.testAdResult = 'خطأ في عرض الإعلان التجريبي ❌';
          console.error('Test ad error:', error);
          
          // Restore original configuration
          this.adMobService.updateConfig(originalConfig);
          
          setTimeout(() => this.testAdResult = '', 5000);
        }
      });

    } catch (error) {
      this.isLoading = false;
      this.testAdResult = 'فشل في تحميل الإعلان التجريبي ❌';
      console.error('Test ad loading error:', error);
      setTimeout(() => this.testAdResult = '', 5000);
    }
  }

  onRefreshStats(): void {
    this.loadAdStats();
  }

  onResetSettings(): void {
    if (confirm('هل أنت متأكد من إعادة تعيين الإعدادات إلى القيم الافتراضية؟')) {
      this.settingsForm.patchValue({
        appId: 'ca-app-pub-4373910379376809~8789974713',
        adUnitId: 'ca-app-pub-4373910379376809/7815804390',
        testMode: false
      });
    }
  }

  // Helper methods for validation
  get appIdError(): string {
    const control = this.settingsForm.get('appId');
    if (control?.errors && control.touched) {
      if (control.errors['required']) {
        return 'معرف التطبيق مطلوب';
      }
      if (control.errors['pattern']) {
        return 'تنسيق معرف التطبيق غير صحيح (مثال: ca-app-pub-1234567890~1234567890)';
      }
    }
    return '';
  }

  get adUnitIdError(): string {
    const control = this.settingsForm.get('adUnitId');
    if (control?.errors && control.touched) {
      if (control.errors['required']) {
        return 'معرف الوحدة الإعلانية مطلوب';
      }
      if (control.errors['pattern']) {
        return 'تنسيق معرف الوحدة الإعلانية غير صحيح (مثال: ca-app-pub-1234567890/1234567890)';
      }
    }
    return '';
  }
}
