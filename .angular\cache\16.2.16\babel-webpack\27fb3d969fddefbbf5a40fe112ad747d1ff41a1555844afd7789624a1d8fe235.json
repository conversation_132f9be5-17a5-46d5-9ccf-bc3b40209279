{"ast": null, "code": "/**\n * Returns a localStorage-like object that stores the key-value pairs in\n * memory.\n */\nexport function memoryLocalStorageAdapter(store = {}) {\n  return {\n    getItem: key => {\n      return store[key] || null;\n    },\n    setItem: (key, value) => {\n      store[key] = value;\n    },\n    removeItem: key => {\n      delete store[key];\n    }\n  };\n}\n//# sourceMappingURL=local-storage.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}