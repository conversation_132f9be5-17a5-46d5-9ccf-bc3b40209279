@echo off
echo ========================================
echo    بناء تطبيق مولد مقاطع القرآن الكريم
echo ========================================

REM تعيين متغير JAVA_HOME
set JAVA_HOME=C:\Program Files\Microsoft\jdk-17.0.16.8-hotspot
echo JAVA_HOME set to: %JAVA_HOME%

REM التحقق من وجود Java
java -version
if %errorlevel% neq 0 (
    echo خطأ: Java غير مثبت أو غير مُعرف بشكل صحيح
    pause
    exit /b 1
)

REM بناء مشروع Angular
echo.
echo ========================================
echo بناء مشروع Angular...
echo ========================================
call npm run build
if %errorlevel% neq 0 (
    echo خطأ في بناء مشروع Angular
    pause
    exit /b 1
)

REM نسخ ملف index.html إذا لم يكن موجوداً
if not exist "dist\quran-vid-gen\index.html" (
    echo نسخ ملف index.html...
    copy "src\index.html" "dist\quran-vid-gen\index.html"
)

REM مزامنة Capacitor
echo.
echo ========================================
echo مزامنة Capacitor...
echo ========================================
call npx cap sync android
if %errorlevel% neq 0 (
    echo خطأ في مزامنة Capacitor
    pause
    exit /b 1
)

REM بناء APK
echo.
echo ========================================
echo بناء APK...
echo ========================================
cd android
call gradlew.bat assembleDebug
if %errorlevel% neq 0 (
    echo خطأ في بناء APK
    cd ..
    pause
    exit /b 1
)

REM التحقق من وجود APK ونسخه إلى سطح المكتب
if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo.
    echo ========================================
    echo تم بناء APK بنجاح!
    echo ========================================
    copy "app\build\outputs\apk\debug\app-debug.apk" "%USERPROFILE%\Desktop\QuranVidGen-Debug.apk"
    echo تم نسخ APK إلى سطح المكتب: QuranVidGen-Debug.apk
) else (
    echo لم يتم العثور على ملف APK
)

cd ..
echo.
echo انتهت العملية!
pause
