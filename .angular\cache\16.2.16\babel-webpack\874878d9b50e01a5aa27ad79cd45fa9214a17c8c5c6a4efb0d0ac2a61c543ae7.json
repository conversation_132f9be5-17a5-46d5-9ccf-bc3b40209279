{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/\\u062A\\u0637\\u0628\\u064A\\u0642 \\u0646\\u0627\\u0634\\u0631 \\u0627\\u064A\\u0627\\u062A \\u0642\\u0626\\u0627\\u0646\\u064A\\u0629/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../Services/admob.service\";\nimport * as i3 from \"../../Services/supabase.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/inputtext\";\nimport * as i6 from \"primeng/checkbox\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/tooltip\";\nfunction AdMobSettingsComponent_small_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r0.appIdError);\n  }\n}\nfunction AdMobSettingsComponent_small_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r1.adUnitIdError);\n  }\n}\nconst _c0 = function (a0, a1) {\n  return {\n    \"success\": a0,\n    \"error\": a1\n  };\n};\nfunction AdMobSettingsComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(2, _c0, ctx_r2.saveMessage.includes(\"\\u2705\"), ctx_r2.saveMessage.includes(\"\\u274C\")));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.saveMessage, \" \");\n  }\n}\nfunction AdMobSettingsComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵelement(1, \"i\", 43);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(2, _c0, ctx_r3.testAdResult.includes(\"\\u2705\"), ctx_r3.testAdResult.includes(\"\\u274C\")));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.testAdResult, \" \");\n  }\n}\nexport let AdMobSettingsComponent = /*#__PURE__*/(() => {\n  class AdMobSettingsComponent {\n    constructor(fb, adMobService, supabaseService) {\n      this.fb = fb;\n      this.adMobService = adMobService;\n      this.supabaseService = supabaseService;\n      this.isLoading = false;\n      this.saveMessage = '';\n      this.adStats = {};\n      this.testAdResult = '';\n    }\n    ngOnInit() {\n      this.initializeForm();\n      this.loadCurrentSettings();\n      this.loadAdStats();\n    }\n    initializeForm() {\n      this.settingsForm = this.fb.group({\n        appId: ['', [Validators.required, Validators.pattern(/^ca-app-pub-\\d+~\\d+$/)]],\n        adUnitId: ['', [Validators.required, Validators.pattern(/^ca-app-pub-\\d+\\/\\d+$/)]],\n        testMode: [false]\n      });\n    }\n    loadCurrentSettings() {\n      const currentConfig = this.adMobService.getConfig();\n      this.settingsForm.patchValue(currentConfig);\n    }\n    loadAdStats() {\n      this.adStats = this.adMobService.getAdStats();\n    }\n    onSaveSettings() {\n      if (this.settingsForm.valid) {\n        this.isLoading = true;\n        this.saveMessage = '';\n        const newConfig = this.settingsForm.value;\n        // Update AdMob service configuration\n        this.adMobService.updateConfig(newConfig);\n        // Save to database\n        this.supabaseService.setAppSetting('admob_app_id', newConfig.appId, 'معرف تطبيق AdMob').subscribe({\n          next: () => {\n            this.supabaseService.setAppSetting('admob_ad_unit_id', newConfig.adUnitId, 'معرف وحدة الإعلان في AdMob').subscribe({\n              next: () => {\n                this.supabaseService.setAppSetting('admob_test_mode', newConfig.testMode, 'وضع الاختبار لـ AdMob').subscribe({\n                  next: () => {\n                    this.isLoading = false;\n                    this.saveMessage = 'تم حفظ الإعدادات بنجاح ✅';\n                    setTimeout(() => this.saveMessage = '', 3000);\n                  },\n                  error: error => {\n                    this.isLoading = false;\n                    this.saveMessage = 'خطأ في حفظ وضع الاختبار ❌';\n                    console.error('Error saving test mode:', error);\n                  }\n                });\n              },\n              error: error => {\n                this.isLoading = false;\n                this.saveMessage = 'خطأ في حفظ معرف الوحدة الإعلانية ❌';\n                console.error('Error saving ad unit ID:', error);\n              }\n            });\n          },\n          error: error => {\n            this.isLoading = false;\n            this.saveMessage = 'خطأ في حفظ معرف التطبيق ❌';\n            console.error('Error saving app ID:', error);\n          }\n        });\n      }\n    }\n    onTestAd() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.testAdResult = '';\n        _this.isLoading = true;\n        try {\n          // Enable test mode temporarily\n          const originalConfig = _this.adMobService.getConfig();\n          _this.adMobService.updateConfig({\n            testMode: true\n          });\n          // Load and show test ad\n          const loaded = yield _this.adMobService.loadRewardedAd();\n          if (!loaded) {\n            throw new Error('فشل في تحميل الإعلان التجريبي');\n          }\n          const adResult$ = yield _this.adMobService.showRewardedAd();\n          adResult$.subscribe({\n            next: result => {\n              _this.isLoading = false;\n              if (result.success) {\n                _this.testAdResult = 'تم عرض الإعلان التجريبي بنجاح ✅';\n              } else {\n                _this.testAdResult = `فشل في عرض الإعلان: ${result.error} ❌`;\n              }\n              // Restore original configuration\n              _this.adMobService.updateConfig(originalConfig);\n              setTimeout(() => _this.testAdResult = '', 5000);\n            },\n            error: error => {\n              _this.isLoading = false;\n              _this.testAdResult = 'خطأ في عرض الإعلان التجريبي ❌';\n              console.error('Test ad error:', error);\n              // Restore original configuration\n              _this.adMobService.updateConfig(originalConfig);\n              setTimeout(() => _this.testAdResult = '', 5000);\n            }\n          });\n        } catch (error) {\n          _this.isLoading = false;\n          _this.testAdResult = 'فشل في تحميل الإعلان التجريبي ❌';\n          console.error('Test ad loading error:', error);\n          setTimeout(() => _this.testAdResult = '', 5000);\n        }\n      })();\n    }\n    onRefreshStats() {\n      this.loadAdStats();\n    }\n    onResetSettings() {\n      if (confirm('هل أنت متأكد من إعادة تعيين الإعدادات إلى القيم الافتراضية؟')) {\n        this.settingsForm.patchValue({\n          appId: 'ca-app-pub-4373910379376809~8789974713',\n          adUnitId: 'ca-app-pub-4373910379376809/7815804390',\n          testMode: false\n        });\n      }\n    }\n    // Helper methods for validation\n    get appIdError() {\n      const control = this.settingsForm.get('appId');\n      if (control?.errors && control.touched) {\n        if (control.errors['required']) {\n          return 'معرف التطبيق مطلوب';\n        }\n        if (control.errors['pattern']) {\n          return 'تنسيق معرف التطبيق غير صحيح (مثال: ca-app-pub-1234567890~1234567890)';\n        }\n      }\n      return '';\n    }\n    get adUnitIdError() {\n      const control = this.settingsForm.get('adUnitId');\n      if (control?.errors && control.touched) {\n        if (control.errors['required']) {\n          return 'معرف الوحدة الإعلانية مطلوب';\n        }\n        if (control.errors['pattern']) {\n          return 'تنسيق معرف الوحدة الإعلانية غير صحيح (مثال: ca-app-pub-1234567890/1234567890)';\n        }\n      }\n      return '';\n    }\n    static {\n      this.ɵfac = function AdMobSettingsComponent_Factory(t) {\n        return new (t || AdMobSettingsComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AdMobService), i0.ɵɵdirectiveInject(i3.SupabaseService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AdMobSettingsComponent,\n        selectors: [[\"app-admob-settings\"]],\n        decls: 128,\n        vars: 19,\n        consts: [[1, \"admob-settings-container\"], [1, \"settings-header\"], [1, \"settings-section\"], [1, \"admob-form\", 3, \"formGroup\"], [1, \"form-group\"], [\"for\", \"appId\"], [\"id\", \"appId\", \"type\", \"text\", \"formControlName\", \"appId\", \"pInputText\", \"\", \"placeholder\", \"ca-app-pub-4373910379376809~8789974713\", 1, \"w-100\"], [\"class\", \"error-text\", 4, \"ngIf\"], [1, \"help-text\"], [\"for\", \"adUnitId\"], [\"id\", \"adUnitId\", \"type\", \"text\", \"formControlName\", \"adUnitId\", \"pInputText\", \"\", \"placeholder\", \"ca-app-pub-4373910379376809/7815804390\", 1, \"w-100\"], [1, \"checkbox-container\"], [\"formControlName\", \"testMode\", \"label\", \"\\u0648\\u0636\\u0639 \\u0627\\u0644\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631\", 3, \"binary\"], [\"class\", \"save-message\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"form-actions\"], [\"label\", \"\\u062D\\u0641\\u0638 \\u0627\\u0644\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A\", \"icon\", \"pi pi-save\", 3, \"loading\", \"disabled\", \"onClick\"], [\"label\", \"\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631 \\u0627\\u0644\\u0625\\u0639\\u0644\\u0627\\u0646\", \"icon\", \"pi pi-play\", \"severity\", \"info\", 3, \"outlined\", \"loading\", \"onClick\"], [\"label\", \"\\u0625\\u0639\\u0627\\u062F\\u0629 \\u062A\\u0639\\u064A\\u064A\\u0646\", \"icon\", \"pi pi-refresh\", \"severity\", \"secondary\", 3, \"outlined\", \"onClick\"], [\"class\", \"test-result\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"stats-section\"], [1, \"stats-header\"], [\"icon\", \"pi pi-refresh\", \"severity\", \"secondary\", \"size\", \"small\", \"pTooltip\", \"\\u062A\\u062D\\u062F\\u064A\\u062B \\u0627\\u0644\\u0625\\u062D\\u0635\\u0627\\u0626\\u064A\\u0627\\u062A\", 3, \"text\", \"onClick\"], [1, \"stats-grid\"], [1, \"stat-card\"], [1, \"stat-icon\"], [1, \"pi\", \"pi-eye\"], [1, \"stat-info\"], [1, \"pi\", \"pi-check-circle\"], [1, \"pi\", \"pi-percentage\"], [1, \"guide-section\"], [1, \"guide-content\"], [1, \"guide-step\"], [1, \"step-number\"], [1, \"step-content\"], [\"href\", \"https://admob.google.com\", \"target\", \"_blank\"], [1, \"notes-section\"], [1, \"note-item\"], [1, \"pi\", \"pi-exclamation-triangle\", \"text-warning\"], [1, \"pi\", \"pi-shield\", \"text-info\"], [1, \"pi\", \"pi-clock\", \"text-success\"], [1, \"error-text\"], [1, \"save-message\", 3, \"ngClass\"], [1, \"test-result\", 3, \"ngClass\"], [1, \"pi\", \"pi-info-circle\"]],\n        template: function AdMobSettingsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\");\n            i0.ɵɵtext(3, \"\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A AdMob\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"p\");\n            i0.ɵɵtext(5, \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0625\\u0639\\u0644\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0645\\u0643\\u0627\\u0641\\u0626\\u0629\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(6, \"div\", 2)(7, \"h3\");\n            i0.ɵɵtext(8, \"\\u062A\\u0643\\u0648\\u064A\\u0646 AdMob\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"form\", 3)(10, \"div\", 4)(11, \"label\", 5);\n            i0.ɵɵtext(12, \"\\u0645\\u0639\\u0631\\u0641 \\u0627\\u0644\\u062A\\u0637\\u0628\\u064A\\u0642 (App ID) *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(13, \"input\", 6);\n            i0.ɵɵtemplate(14, AdMobSettingsComponent_small_14_Template, 2, 1, \"small\", 7);\n            i0.ɵɵelementStart(15, \"small\", 8);\n            i0.ɵɵtext(16, \"\\u0645\\u0639\\u0631\\u0641 \\u0627\\u0644\\u062A\\u0637\\u0628\\u064A\\u0642 \\u0645\\u0646 \\u0648\\u062D\\u062F\\u0629 \\u062A\\u062D\\u0643\\u0645 AdMob\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(17, \"div\", 4)(18, \"label\", 9);\n            i0.ɵɵtext(19, \"\\u0645\\u0639\\u0631\\u0641 \\u0627\\u0644\\u0648\\u062D\\u062F\\u0629 \\u0627\\u0644\\u0625\\u0639\\u0644\\u0627\\u0646\\u064A\\u0629 (Ad Unit ID) *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(20, \"input\", 10);\n            i0.ɵɵtemplate(21, AdMobSettingsComponent_small_21_Template, 2, 1, \"small\", 7);\n            i0.ɵɵelementStart(22, \"small\", 8);\n            i0.ɵɵtext(23, \"\\u0645\\u0639\\u0631\\u0641 \\u0627\\u0644\\u0648\\u062D\\u062F\\u0629 \\u0627\\u0644\\u0625\\u0639\\u0644\\u0627\\u0646\\u064A\\u0629 \\u0644\\u0644\\u0625\\u0639\\u0644\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0645\\u0643\\u0627\\u0641\\u0626\\u0629\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(24, \"div\", 4)(25, \"div\", 11);\n            i0.ɵɵelement(26, \"p-checkbox\", 12);\n            i0.ɵɵelementStart(27, \"small\", 8);\n            i0.ɵɵtext(28, \"\\u062A\\u0641\\u0639\\u064A\\u0644 \\u0648\\u0636\\u0639 \\u0627\\u0644\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631 \\u0644\\u0639\\u0631\\u0636 \\u0625\\u0639\\u0644\\u0627\\u0646\\u0627\\u062A \\u062A\\u062C\\u0631\\u064A\\u0628\\u064A\\u0629\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(29, AdMobSettingsComponent_div_29_Template, 2, 5, \"div\", 13);\n            i0.ɵɵelementStart(30, \"div\", 14)(31, \"p-button\", 15);\n            i0.ɵɵlistener(\"onClick\", function AdMobSettingsComponent_Template_p_button_onClick_31_listener() {\n              return ctx.onSaveSettings();\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(32, \"p-button\", 16);\n            i0.ɵɵlistener(\"onClick\", function AdMobSettingsComponent_Template_p_button_onClick_32_listener() {\n              return ctx.onTestAd();\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"p-button\", 17);\n            i0.ɵɵlistener(\"onClick\", function AdMobSettingsComponent_Template_p_button_onClick_33_listener() {\n              return ctx.onResetSettings();\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(34, AdMobSettingsComponent_div_34_Template, 3, 5, \"div\", 18);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(35, \"div\", 19)(36, \"div\", 20)(37, \"h3\");\n            i0.ɵɵtext(38, \"\\u0625\\u062D\\u0635\\u0627\\u0626\\u064A\\u0627\\u062A \\u0627\\u0644\\u0625\\u0639\\u0644\\u0627\\u0646\\u0627\\u062A\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(39, \"p-button\", 21);\n            i0.ɵɵlistener(\"onClick\", function AdMobSettingsComponent_Template_p_button_onClick_39_listener() {\n              return ctx.onRefreshStats();\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(40, \"div\", 22)(41, \"div\", 23)(42, \"div\", 24);\n            i0.ɵɵelement(43, \"i\", 25);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(44, \"div\", 26)(45, \"h4\");\n            i0.ɵɵtext(46);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(47, \"p\");\n            i0.ɵɵtext(48, \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0625\\u0639\\u0644\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0645\\u0639\\u0631\\u0648\\u0636\\u0629\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(49, \"div\", 23)(50, \"div\", 24);\n            i0.ɵɵelement(51, \"i\", 27);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(52, \"div\", 26)(53, \"h4\");\n            i0.ɵɵtext(54);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(55, \"p\");\n            i0.ɵɵtext(56, \"\\u0627\\u0644\\u0625\\u0639\\u0644\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0645\\u0643\\u062A\\u0645\\u0644\\u0629\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(57, \"div\", 23)(58, \"div\", 24);\n            i0.ɵɵelement(59, \"i\", 28);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(60, \"div\", 26)(61, \"h4\");\n            i0.ɵɵtext(62);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(63, \"p\");\n            i0.ɵɵtext(64, \"\\u0645\\u0639\\u062F\\u0644 \\u0627\\u0644\\u0646\\u062C\\u0627\\u062D\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(65, \"div\", 29)(66, \"h3\");\n            i0.ɵɵtext(67, \"\\u062F\\u0644\\u064A\\u0644 \\u0627\\u0644\\u062A\\u0643\\u0627\\u0645\\u0644\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(68, \"div\", 30)(69, \"div\", 31)(70, \"div\", 32);\n            i0.ɵɵtext(71, \"1\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(72, \"div\", 33)(73, \"h4\");\n            i0.ɵɵtext(74, \"\\u0625\\u0646\\u0634\\u0627\\u0621 \\u062D\\u0633\\u0627\\u0628 AdMob\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(75, \"p\");\n            i0.ɵɵtext(76, \"\\u0642\\u0645 \\u0628\\u0625\\u0646\\u0634\\u0627\\u0621 \\u062D\\u0633\\u0627\\u0628 \\u0639\\u0644\\u0649 \");\n            i0.ɵɵelementStart(77, \"a\", 34);\n            i0.ɵɵtext(78, \"AdMob\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(79, \" \\u0648\\u0623\\u0646\\u0634\\u0626 \\u062A\\u0637\\u0628\\u064A\\u0642\\u0627\\u064B \\u062C\\u062F\\u064A\\u062F\\u0627\\u064B\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(80, \"div\", 31)(81, \"div\", 32);\n            i0.ɵɵtext(82, \"2\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(83, \"div\", 33)(84, \"h4\");\n            i0.ɵɵtext(85, \"\\u0625\\u0646\\u0634\\u0627\\u0621 \\u0648\\u062D\\u062F\\u0629 \\u0625\\u0639\\u0644\\u0627\\u0646\\u064A\\u0629\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(86, \"p\");\n            i0.ɵɵtext(87, \"\\u0623\\u0646\\u0634\\u0626 \\u0648\\u062D\\u062F\\u0629 \\u0625\\u0639\\u0644\\u0627\\u0646\\u064A\\u0629 \\u0645\\u0646 \\u0646\\u0648\\u0639 \\\"Rewarded\\\" \\u0644\\u0644\\u0625\\u0639\\u0644\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0645\\u0643\\u0627\\u0641\\u0626\\u0629\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(88, \"div\", 31)(89, \"div\", 32);\n            i0.ɵɵtext(90, \"3\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(91, \"div\", 33)(92, \"h4\");\n            i0.ɵɵtext(93, \"\\u0646\\u0633\\u062E \\u0627\\u0644\\u0645\\u0639\\u0631\\u0641\\u0627\\u062A\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(94, \"p\");\n            i0.ɵɵtext(95, \"\\u0627\\u0646\\u0633\\u062E \\u0645\\u0639\\u0631\\u0641 \\u0627\\u0644\\u062A\\u0637\\u0628\\u064A\\u0642 \\u0648\\u0645\\u0639\\u0631\\u0641 \\u0627\\u0644\\u0648\\u062D\\u062F\\u0629 \\u0627\\u0644\\u0625\\u0639\\u0644\\u0627\\u0646\\u064A\\u0629 \\u0648\\u0623\\u062F\\u062E\\u0644\\u0647\\u0645\\u0627 \\u0641\\u064A \\u0627\\u0644\\u0646\\u0645\\u0648\\u0630\\u062C \\u0623\\u0639\\u0644\\u0627\\u0647\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(96, \"div\", 31)(97, \"div\", 32);\n            i0.ɵɵtext(98, \"4\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(99, \"div\", 33)(100, \"h4\");\n            i0.ɵɵtext(101, \"\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631 \\u0627\\u0644\\u062A\\u0643\\u0627\\u0645\\u0644\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(102, \"p\");\n            i0.ɵɵtext(103, \"\\u0627\\u0633\\u062A\\u062E\\u062F\\u0645 \\u0632\\u0631 \\\"\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631 \\u0627\\u0644\\u0625\\u0639\\u0644\\u0627\\u0646\\\" \\u0644\\u0644\\u062A\\u0623\\u0643\\u062F \\u0645\\u0646 \\u0639\\u0645\\u0644 \\u0627\\u0644\\u062A\\u0643\\u0627\\u0645\\u0644 \\u0628\\u0634\\u0643\\u0644 \\u0635\\u062D\\u064A\\u062D\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(104, \"div\", 35)(105, \"h3\");\n            i0.ɵɵtext(106, \"\\u0645\\u0644\\u0627\\u062D\\u0638\\u0627\\u062A \\u0645\\u0647\\u0645\\u0629\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(107, \"div\", 36);\n            i0.ɵɵelement(108, \"i\", 37);\n            i0.ɵɵelementStart(109, \"div\")(110, \"h4\");\n            i0.ɵɵtext(111, \"\\u0648\\u0636\\u0639 \\u0627\\u0644\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(112, \"p\");\n            i0.ɵɵtext(113, \"\\u062A\\u0623\\u0643\\u062F \\u0645\\u0646 \\u0625\\u064A\\u0642\\u0627\\u0641 \\u0648\\u0636\\u0639 \\u0627\\u0644\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631 \\u0641\\u064A \\u0627\\u0644\\u0625\\u0646\\u062A\\u0627\\u062C \\u0644\\u062A\\u062C\\u0646\\u0628 \\u0627\\u0646\\u062A\\u0647\\u0627\\u0643 \\u0633\\u064A\\u0627\\u0633\\u0627\\u062A AdMob\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(114, \"div\", 36);\n            i0.ɵɵelement(115, \"i\", 38);\n            i0.ɵɵelementStart(116, \"div\")(117, \"h4\");\n            i0.ɵɵtext(118, \"\\u0633\\u064A\\u0627\\u0633\\u0627\\u062A AdMob\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(119, \"p\");\n            i0.ɵɵtext(120, \"\\u062A\\u0623\\u0643\\u062F \\u0645\\u0646 \\u0627\\u0644\\u0627\\u0645\\u062A\\u062B\\u0627\\u0644 \\u0644\\u0633\\u064A\\u0627\\u0633\\u0627\\u062A AdMob \\u0648\\u0639\\u062F\\u0645 \\u0627\\u0644\\u0646\\u0642\\u0631 \\u0639\\u0644\\u0649 \\u0627\\u0644\\u0625\\u0639\\u0644\\u0627\\u0646\\u0627\\u062A \\u0628\\u0646\\u0641\\u0633\\u0643\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(121, \"div\", 36);\n            i0.ɵɵelement(122, \"i\", 39);\n            i0.ɵɵelementStart(123, \"div\")(124, \"h4\");\n            i0.ɵɵtext(125, \"\\u0648\\u0642\\u062A \\u0627\\u0644\\u062A\\u0641\\u0639\\u064A\\u0644\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(126, \"p\");\n            i0.ɵɵtext(127, \"\\u0642\\u062F \\u064A\\u0633\\u062A\\u063A\\u0631\\u0642 \\u062A\\u0641\\u0639\\u064A\\u0644 \\u0627\\u0644\\u0625\\u0639\\u0644\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u062D\\u0642\\u064A\\u0642\\u064A\\u0629 \\u0628\\u0639\\u0636 \\u0627\\u0644\\u0648\\u0642\\u062A \\u0628\\u0639\\u062F \\u0625\\u0646\\u0634\\u0627\\u0621 \\u0627\\u0644\\u062D\\u0633\\u0627\\u0628\");\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"formGroup\", ctx.settingsForm);\n            i0.ɵɵadvance(4);\n            i0.ɵɵclassProp(\"error\", ctx.appIdError);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.appIdError);\n            i0.ɵɵadvance(6);\n            i0.ɵɵclassProp(\"error\", ctx.adUnitIdError);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.adUnitIdError);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"binary\", true);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.saveMessage);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"loading\", ctx.isLoading)(\"disabled\", !ctx.settingsForm.valid);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"outlined\", true)(\"loading\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"outlined\", true);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.testAdResult);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"text\", true);\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate(ctx.adStats.totalShown || 0);\n            i0.ɵɵadvance(8);\n            i0.ɵɵtextInterpolate(ctx.adStats.totalRewarded || 0);\n            i0.ɵɵadvance(8);\n            i0.ɵɵtextInterpolate1(\"\", ctx.adStats.successRate || 0, \"%\");\n          }\n        },\n        dependencies: [i4.NgClass, i4.NgIf, i5.InputText, i6.Checkbox, i7.Button, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i8.Tooltip],\n        styles: [\".admob-settings-container[_ngcontent-%COMP%]{max-width:1000px;margin:0 auto;padding:2rem}.settings-header[_ngcontent-%COMP%]{text-align:center;margin-bottom:3rem}.settings-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:#2c3e50;font-size:2rem;margin-bottom:.5rem}.settings-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#6c757d;font-size:1.1rem;margin:0}.settings-section[_ngcontent-%COMP%], .stats-section[_ngcontent-%COMP%], .guide-section[_ngcontent-%COMP%], .notes-section[_ngcontent-%COMP%]{background:white;border-radius:12px;padding:2rem;margin-bottom:2rem;box-shadow:0 2px 8px #0000001a}.settings-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .stats-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .guide-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .notes-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#2c3e50;font-size:1.4rem;margin-bottom:1.5rem;border-bottom:2px solid #667eea;padding-bottom:.5rem}.admob-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]{margin-bottom:2rem}.admob-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:block;margin-bottom:.5rem;font-weight:600;color:#495057}.admob-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{width:100%;padding:.75rem;border:2px solid #e9ecef;border-radius:8px;font-size:1rem;transition:border-color .3s ease}.admob-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus{border-color:#667eea;outline:none;box-shadow:0 0 0 3px #667eea1a}.admob-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input.error[_ngcontent-%COMP%]{border-color:#dc3545}.admob-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .help-text[_ngcontent-%COMP%]{display:block;margin-top:.5rem;color:#6c757d;font-size:.9rem}.admob-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .error-text[_ngcontent-%COMP%]{display:block;margin-top:.5rem;color:#dc3545;font-size:.9rem;font-weight:500}.admob-form[_ngcontent-%COMP%]   .checkbox-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.5rem}.save-message[_ngcontent-%COMP%], .test-result[_ngcontent-%COMP%]{padding:1rem;border-radius:8px;margin:1rem 0;display:flex;align-items:center;gap:.5rem;font-weight:500}.save-message.success[_ngcontent-%COMP%], .test-result.success[_ngcontent-%COMP%]{background:#d4edda;color:#155724;border:1px solid #c3e6cb}.save-message.error[_ngcontent-%COMP%], .test-result.error[_ngcontent-%COMP%]{background:#f8d7da;color:#721c24;border:1px solid #f5c6cb}.form-actions[_ngcontent-%COMP%]{display:flex;gap:1rem;margin-top:2rem;flex-wrap:wrap}.form-actions[_ngcontent-%COMP%]     .p-button{padding:.75rem 1.5rem;font-weight:600;border-radius:8px}.stats-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:1.5rem}.stats-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin-bottom:0;border-bottom:none;padding-bottom:0}.stats-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:1.5rem}.stat-card[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f8f9fa 0%,#e9ecef 100%);border-radius:12px;padding:1.5rem;display:flex;align-items:center;gap:1rem;border:1px solid #dee2e6}.stat-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);color:#fff;width:50px;height:50px;border-radius:10px;display:flex;align-items:center;justify-content:center}.stat-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]   .pi[_ngcontent-%COMP%]{font-size:1.3rem}.stat-card[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#2c3e50;font-size:1.8rem;margin-bottom:.25rem;font-weight:700}.stat-card[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#6c757d;margin:0;font-size:.9rem;font-weight:500}.guide-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:1.5rem}.guide-step[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:1rem}.guide-step[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);color:#fff;width:40px;height:40px;border-radius:50%;display:flex;align-items:center;justify-content:center;font-weight:700;font-size:1.1rem;flex-shrink:0}.guide-step[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#2c3e50;margin-bottom:.5rem;font-size:1.1rem}.guide-step[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#6c757d;margin:0;line-height:1.5}.guide-step[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#667eea;text-decoration:none}.guide-step[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{text-decoration:underline}.note-item[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:1rem;margin-bottom:1.5rem;padding:1rem;background:#f8f9fa;border-radius:8px;border-left:4px solid #667eea}.note-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.note-item[_ngcontent-%COMP%]   .pi[_ngcontent-%COMP%]{font-size:1.5rem;margin-top:.2rem;flex-shrink:0}.note-item[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#2c3e50;margin-bottom:.5rem;font-size:1rem}.note-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#6c757d;margin:0;line-height:1.5;font-size:.9rem}.text-warning[_ngcontent-%COMP%]{color:#ffc107!important}.text-info[_ngcontent-%COMP%]{color:#17a2b8!important}.text-success[_ngcontent-%COMP%]{color:#28a745!important}@media (max-width: 768px){.admob-settings-container[_ngcontent-%COMP%]{padding:1rem}.settings-section[_ngcontent-%COMP%], .stats-section[_ngcontent-%COMP%], .guide-section[_ngcontent-%COMP%], .notes-section[_ngcontent-%COMP%]{padding:1.5rem}.settings-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.6rem}.settings-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1rem}.form-actions[_ngcontent-%COMP%]{flex-direction:column}.form-actions[_ngcontent-%COMP%]     .p-button{width:100%}.stats-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}.stats-header[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:1rem}.guide-step[_ngcontent-%COMP%]{flex-direction:column;text-align:center}.guide-step[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%]{align-self:center}.note-item[_ngcontent-%COMP%]{flex-direction:column;text-align:center}.note-item[_ngcontent-%COMP%]   .pi[_ngcontent-%COMP%]{align-self:center}}\"]\n      });\n    }\n  }\n  return AdMobSettingsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}