{"ast": null, "code": "import SupabaseClient from './SupabaseClient';\nexport * from '@supabase/auth-js';\nexport { PostgrestError } from '@supabase/postgrest-js';\nexport { FunctionsHttpError, FunctionsFetchError, FunctionsRelayError, FunctionsError, FunctionRegion } from '@supabase/functions-js';\nexport * from '@supabase/realtime-js';\nexport { default as SupabaseClient } from './SupabaseClient';\n/**\n * Creates a new Supabase Client.\n */\nexport const createClient = (supabaseUrl, supabaseKey, options) => {\n  return new SupabaseClient(supabaseUrl, supabaseKey, options);\n};\n// Check for Node.js <= 18 deprecation\nfunction shouldShowDeprecationWarning() {\n  if (typeof window !== 'undefined' || typeof process === 'undefined' || process.version === undefined || process.version === null) {\n    return false;\n  }\n  const versionMatch = process.version.match(/^v(\\d+)\\./);\n  if (!versionMatch) {\n    return false;\n  }\n  const majorVersion = parseInt(versionMatch[1], 10);\n  return majorVersion <= 18;\n}\nif (shouldShowDeprecationWarning()) {\n  console.warn(`⚠️  Node.js 18 and below are deprecated and will no longer be supported in future versions of @supabase/supabase-js. ` + `Please upgrade to Node.js 20 or later. ` + `For more information, visit: https://github.com/orgs/supabase/discussions/37217`);\n}\n//# sourceMappingURL=index.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}