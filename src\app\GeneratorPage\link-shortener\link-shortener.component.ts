import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import { LinkShortenerService, LinkShorteningStatus } from '../../Services/link-shortener.service';
import { AdMobService, AdResult } from '../../Services/admob.service';

@Component({
  selector: 'app-link-shortener',
  templateUrl: './link-shortener.component.html',
  styleUrls: ['./link-shortener.component.scss']
})
export class LinkShortenerComponent implements OnInit, OnDestroy {
  status: LinkShorteningStatus | null = null;
  showInterface = false;
  isProcessing = false;
  isLoadingAd = false;
  adError = '';

  private statusSubscription?: Subscription;

  constructor(
    private linkShortenerService: LinkShortenerService,
    private adMobService: AdMobService
  ) {}

  ngOnInit(): void {
    this.statusSubscription = this.linkShortenerService.status$.subscribe(
      (status) => {
        this.status = status;
        this.showInterface = status.isRequired;
      }
    );

    // Preload ad for better user experience
    this.adMobService.preloadAd();
  }

  ngOnDestroy(): void {
    if (this.statusSubscription) {
      this.statusSubscription.unsubscribe();
    }
  }

  openCurrentLink(): void {
    if (this.status?.currentLink) {
      window.open(this.status.currentLink, '_blank');
    }
  }

  onContinueClicked(): void {
    this.isProcessing = true;
    
    // Simulate processing time
    setTimeout(() => {
      this.linkShortenerService.confirmLinkShortened();
      this.isProcessing = false;
    }, 1000);
  }

  onLinkNotWorking(): void {
    this.linkShortenerService.reportLinkProblem();
  }

  onProblemOccurred(): void {
    this.linkShortenerService.reportGeneralProblem();
  }

  async onWatchAd(): Promise<void> {
    this.isLoadingAd = true;
    this.adError = '';

    try {
      // Load ad if not already loaded
      if (!this.adMobService.isAdLoaded()) {
        const loaded = await this.adMobService.loadRewardedAd();
        if (!loaded) {
          throw new Error('فشل في تحميل الإعلان');
        }
      }

      // Show the ad
      const adResult$ = await this.adMobService.showRewardedAd();

      adResult$.subscribe({
        next: (result: AdResult) => {
          this.isLoadingAd = false;

          if (result.success && result.rewardEarned) {
            // User watched the ad successfully
            this.linkShortenerService.confirmAdWatched();
          } else {
            this.adError = result.error || 'لم يتم إكمال مشاهدة الإعلان';
          }
        },
        error: (error) => {
          this.isLoadingAd = false;
          this.adError = 'حدث خطأ أثناء عرض الإعلان';
          console.error('Ad error:', error);
        }
      });

    } catch (error) {
      this.isLoadingAd = false;
      this.adError = 'فشل في تحميل الإعلان. يرجى المحاولة مرة أخرى.';
      console.error('Ad loading error:', error);
    }
  }

  getTimeRemainingText(): string {
    if (!this.status) return '';
    
    if (this.status.adTimeRemaining > 0) {
      return `الوقت المتبقي من مشاهدة الإعلان: ${this.linkShortenerService.formatTimeRemaining(this.status.adTimeRemaining)}`;
    }
    
    if (this.status.timeRemaining > 0) {
      return `الوقت المتبقي: ${this.linkShortenerService.formatTimeRemaining(this.status.timeRemaining)}`;
    }
    
    return 'يجب اختصار رابط جديد';
  }

  openSupportEmail(): void {
    const email = '<EMAIL>';
    const subject = 'مشكلة في اختصار الروابط - مولد مقاطع القرآن الكريم';
    const body = `السلام عليكم ورحمة الله وبركاته،

أواجه مشكلة في اختصار الروابط:

الرابط الحالي: ${this.status?.currentLink || 'غير متوفر'}
آخر اختصار: ${this.status?.lastShortenedAt?.toLocaleString('ar') || 'لم يتم'}

يرجى المساعدة في حل هذه المشكلة.

شكراً لكم`;
    
    const mailtoLink = `mailto:${email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.open(mailtoLink, '_blank');
  }

  refreshStatus(): void {
    this.linkShortenerService.forceRefreshStatus();
  }
}
