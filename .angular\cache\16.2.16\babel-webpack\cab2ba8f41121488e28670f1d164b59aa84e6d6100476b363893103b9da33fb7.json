{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nimport { identity } from '../util/identity';\nimport { noop } from '../util/noop';\nimport { popResultSelector } from '../util/args';\nexport function withLatestFrom(...inputs) {\n  const project = popResultSelector(inputs);\n  return operate((source, subscriber) => {\n    const len = inputs.length;\n    const otherValues = new Array(len);\n    let hasValue = inputs.map(() => false);\n    let ready = false;\n    for (let i = 0; i < len; i++) {\n      innerFrom(inputs[i]).subscribe(createOperatorSubscriber(subscriber, value => {\n        otherValues[i] = value;\n        if (!ready && !hasValue[i]) {\n          hasValue[i] = true;\n          (ready = hasValue.every(identity)) && (hasValue = null);\n        }\n      }, noop));\n    }\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      if (ready) {\n        const values = [value, ...otherValues];\n        subscriber.next(project ? project(...values) : values);\n      }\n    }));\n  });\n}\n//# sourceMappingURL=withLatestFrom.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}