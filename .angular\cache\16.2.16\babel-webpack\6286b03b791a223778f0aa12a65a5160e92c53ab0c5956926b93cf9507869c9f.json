{"ast": null, "code": "import { forkJoin, map } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./call-server.service\";\nimport * as i2 from \"@angular/common/http\";\nexport let QuranService = /*#__PURE__*/(() => {\n  class QuranService {\n    constructor(callServerService, http) {\n      this.callServerService = callServerService;\n      this.http = http;\n    }\n    GetSurah(surahNumber) {\n      return this.callServerService.Get(`https://quranapi.pages.dev/api/${surahNumber}.json`);\n    }\n    GetAyah(surahNumber, AyahNumber) {\n      return this.callServerService.Get(`https://quranapi.pages.dev/api/${surahNumber}/${AyahNumber}.json`);\n    }\n    GetAyatTexts(surahNumber, AyahStartNumber, AyahEndNumber, language) {\n      let observables = [];\n      for (let i = AyahStartNumber; i <= AyahEndNumber; i++) {\n        observables.push(this.GetAyah(surahNumber, i));\n      }\n      return forkJoin(observables).pipe(map(ayahs => {\n        let text = [];\n        ayahs.forEach(ayah => {\n          text.push(language == 'arabic' ? ayah.arabic2 : ayah.english);\n        });\n        return text;\n      }));\n    }\n    GetReciters() {\n      return this.callServerService.Get(`https://quranapi.pages.dev/api/reciters.json`).pipe(map(value => {\n        return Object.keys(value).map(key => {\n          return {\n            id: key,\n            name: value[key]\n          };\n        });\n      }));\n    }\n    GetAllSuras() {\n      return this.callServerService.Get('https://quranapi.pages.dev/api/surah.json');\n    }\n    GetAyahAudio(reciterId, surahNumber, ayahNumber) {\n      return this.http.get(`https://quranaudio.pages.dev/${reciterId}/${surahNumber}_${ayahNumber}.mp3`, {\n        responseType: 'blob'\n      });\n    }\n    GetAyahsAudio(reciterId, surahNumber, startAyah, endAyah) {\n      const observables = [];\n      for (let ayahNumber = startAyah; ayahNumber <= endAyah; ayahNumber++) {\n        const observable = this.GetAyahAudio(reciterId, surahNumber, ayahNumber);\n        observables.push(observable);\n      }\n      return forkJoin(observables);\n    }\n    static {\n      this.ɵfac = function QuranService_Factory(t) {\n        return new (t || QuranService)(i0.ɵɵinject(i1.CallServerService), i0.ɵɵinject(i2.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: QuranService,\n        factory: QuranService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return QuranService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}