{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/\\u062A\\u0637\\u0628\\u064A\\u0642 \\u0646\\u0627\\u0634\\u0631 \\u0627\\u064A\\u0627\\u062A \\u0642\\u0626\\u0627\\u0646\\u064A\\u0629/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./supabase.service\";\nexport let SecurityService = /*#__PURE__*/(() => {\n  class SecurityService {\n    constructor(supabaseService) {\n      this.supabaseService = supabaseService;\n      this.securityStatusSubject = new BehaviorSubject({\n        isVpnDetected: false,\n        isAdBlockerDetected: false,\n        isDnsBlocked: false,\n        allowAccess: true\n      });\n      this.securityStatus$ = this.securityStatusSubject.asObservable();\n      this.settings = {\n        enableVpnDetection: true,\n        enableAdBlockerDetection: true,\n        enableDnsDetection: true,\n        strictMode: true\n      };\n      // Known VPN and proxy indicators\n      this.vpnIndicators = ['vpn', 'proxy', 'tor', 'tunnel', 'anonymizer', 'hide', 'mask', 'secure', 'private', 'shield'];\n      // Known ad blocker extensions\n      this.adBlockerIndicators = ['adblock', 'ublock', 'ghostery', 'disconnect', 'privacy', 'blocker', 'guard', 'shield'];\n      // Blocked DNS servers\n      this.blockedDnsServers = ['dns.adguard.com', '************', '************', '***************', '***************', '*******', '*******', '*******', '*******'];\n      this.initializeSecurityChecks();\n    }\n    initializeSecurityChecks() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        // Run initial security checks\n        yield _this.performSecurityCheck();\n        // Set up periodic checks\n        setInterval(() => {\n          _this.performSecurityCheck();\n        }, 30000); // Check every 30 seconds\n      })();\n    }\n\n    performSecurityCheck() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        const result = {\n          isVpnDetected: false,\n          isAdBlockerDetected: false,\n          isDnsBlocked: false,\n          allowAccess: true\n        };\n        try {\n          // Check for VPN/Proxy\n          if (_this2.settings.enableVpnDetection) {\n            result.isVpnDetected = yield _this2.detectVpn();\n          }\n          // Check for Ad Blockers\n          if (_this2.settings.enableAdBlockerDetection) {\n            result.isAdBlockerDetected = yield _this2.detectAdBlocker();\n          }\n          // Check for blocked DNS\n          if (_this2.settings.enableDnsDetection) {\n            result.isDnsBlocked = yield _this2.detectBlockedDns();\n          }\n          // Determine if access should be allowed\n          result.allowAccess = _this2.shouldAllowAccess(result);\n          if (!result.allowAccess) {\n            result.blockedReason = _this2.getBlockedReason(result);\n            _this2.logSecurityEvent(result);\n          }\n        } catch (error) {\n          console.error('Security check failed:', error);\n          // In case of error, allow access but log the issue\n          result.allowAccess = !_this2.settings.strictMode;\n        }\n        _this2.securityStatusSubject.next(result);\n        return result;\n      })();\n    }\n    detectVpn() {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          // Method 1: Check user agent for VPN indicators\n          const userAgent = navigator.userAgent.toLowerCase();\n          const hasVpnIndicator = _this3.vpnIndicators.some(indicator => userAgent.includes(indicator));\n          if (hasVpnIndicator) {\n            return true;\n          }\n          // Method 2: Check for WebRTC IP leaks\n          const hasVpnIp = yield _this3.checkWebRtcIps();\n          if (hasVpnIp) {\n            return true;\n          }\n          // Method 3: Check timezone vs expected location\n          const hasVpnTimezone = _this3.checkTimezoneAnomaly();\n          if (hasVpnTimezone) {\n            return true;\n          }\n          return false;\n        } catch (error) {\n          console.error('VPN detection failed:', error);\n          return false;\n        }\n      })();\n    }\n    detectAdBlocker() {\n      var _this4 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          // Method 1: Try to load a test ad element\n          const adTestResult = yield _this4.testAdElement();\n          if (adTestResult) {\n            return true;\n          }\n          // Method 2: Check for ad blocker extensions\n          const hasAdBlockerExtension = _this4.checkAdBlockerExtensions();\n          if (hasAdBlockerExtension) {\n            return true;\n          }\n          // Method 3: Test blocked requests\n          const hasBlockedRequests = yield _this4.testBlockedRequests();\n          if (hasBlockedRequests) {\n            return true;\n          }\n          return false;\n        } catch (error) {\n          console.error('Ad blocker detection failed:', error);\n          return false;\n        }\n      })();\n    }\n    detectBlockedDns() {\n      var _this5 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          // Check if we can resolve known ad domains\n          const testDomains = ['googleads.g.doubleclick.net', 'googlesyndication.com', 'google-analytics.com'];\n          for (const domain of testDomains) {\n            const isBlocked = yield _this5.testDomainResolution(domain);\n            if (isBlocked) {\n              return true;\n            }\n          }\n          return false;\n        } catch (error) {\n          console.error('DNS detection failed:', error);\n          return false;\n        }\n      })();\n    }\n    checkWebRtcIps() {\n      var _this6 = this;\n      return _asyncToGenerator(function* () {\n        return new Promise(resolve => {\n          try {\n            const rtc = new RTCPeerConnection({\n              iceServers: [{\n                urls: 'stun:stun.l.google.com:19302'\n              }]\n            });\n            rtc.createDataChannel('');\n            rtc.createOffer().then(offer => rtc.setLocalDescription(offer));\n            rtc.onicecandidate = event => {\n              if (event.candidate) {\n                const ip = event.candidate.candidate.split(' ')[4];\n                // Check for VPN/proxy IP patterns\n                if (_this6.isVpnIp(ip)) {\n                  resolve(true);\n                  return;\n                }\n              }\n            };\n            // Timeout after 3 seconds\n            setTimeout(() => {\n              rtc.close();\n              resolve(false);\n            }, 3000);\n          } catch (error) {\n            resolve(false);\n          }\n        });\n      })();\n    }\n    checkTimezoneAnomaly() {\n      try {\n        const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;\n        const language = navigator.language;\n        // Simple check: if timezone doesn't match expected region for language\n        // This is a basic implementation and can be enhanced\n        if (language.startsWith('ar') && !timezone.includes('Asia') && !timezone.includes('Africa')) {\n          return true;\n        }\n        return false;\n      } catch (error) {\n        return false;\n      }\n    }\n    testAdElement() {\n      return _asyncToGenerator(function* () {\n        return new Promise(resolve => {\n          try {\n            const testAd = document.createElement('div');\n            testAd.innerHTML = '&nbsp;';\n            testAd.className = 'adsbox';\n            testAd.style.position = 'absolute';\n            testAd.style.left = '-10000px';\n            testAd.style.width = '1px';\n            testAd.style.height = '1px';\n            document.body.appendChild(testAd);\n            setTimeout(() => {\n              const isBlocked = testAd.offsetHeight === 0 || testAd.offsetWidth === 0 || testAd.style.display === 'none' || testAd.style.visibility === 'hidden';\n              document.body.removeChild(testAd);\n              resolve(isBlocked);\n            }, 100);\n          } catch (error) {\n            resolve(false);\n          }\n        });\n      })();\n    }\n    checkAdBlockerExtensions() {\n      try {\n        // Check for common ad blocker extension indicators\n        const extensionIndicators = ['chrome-extension://gighmmpiobklfepjocnamgkkbiglidom', 'chrome-extension://cjpalhdlnbpafiamejdnhcphjbkeiagm' // uBlock Origin\n        ];\n\n        return extensionIndicators.some(indicator => {\n          try {\n            const img = new Image();\n            img.src = indicator + '/icon.png';\n            return true;\n          } catch {\n            return false;\n          }\n        });\n      } catch (error) {\n        return false;\n      }\n    }\n    testBlockedRequests() {\n      return _asyncToGenerator(function* () {\n        try {\n          const testUrl = 'https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js';\n          const response = yield fetch(testUrl, {\n            method: 'HEAD',\n            mode: 'no-cors'\n          });\n          return false; // If we get here, request wasn't blocked\n        } catch (error) {\n          return true; // Request was likely blocked\n        }\n      })();\n    }\n\n    testDomainResolution(domain) {\n      return _asyncToGenerator(function* () {\n        try {\n          const testUrl = `https://${domain}/test`;\n          yield fetch(testUrl, {\n            method: 'HEAD',\n            mode: 'no-cors'\n          });\n          return false; // Domain resolves\n        } catch (error) {\n          return true; // Domain likely blocked\n        }\n      })();\n    }\n\n    isVpnIp(ip) {\n      // Check for common VPN IP patterns\n      const vpnPatterns = [/^10\\./, /^172\\.(1[6-9]|2[0-9]|3[0-1])\\./, /^192\\.168\\./, /^169\\.254\\./ // Link-local addresses\n      ];\n\n      return vpnPatterns.some(pattern => pattern.test(ip));\n    }\n    shouldAllowAccess(result) {\n      if (this.settings.strictMode) {\n        return !result.isVpnDetected && !result.isAdBlockerDetected && !result.isDnsBlocked;\n      } else {\n        // In non-strict mode, allow access but warn\n        return true;\n      }\n    }\n    getBlockedReason(result) {\n      const reasons = [];\n      if (result.isVpnDetected) {\n        reasons.push('تم اكتشاف استخدام VPN أو بروكسي');\n      }\n      if (result.isAdBlockerDetected) {\n        reasons.push('تم اكتشاف أداة حظر الإعلانات');\n      }\n      if (result.isDnsBlocked) {\n        reasons.push('تم اكتشاف DNS محجوب للإعلانات');\n      }\n      return reasons.join(', ');\n    }\n    updateSettings(newSettings) {\n      this.settings = {\n        ...this.settings,\n        ...newSettings\n      };\n      this.performSecurityCheck(); // Re-check with new settings\n    }\n\n    getSettings() {\n      return {\n        ...this.settings\n      };\n    }\n    forceSecurityCheck() {\n      var _this7 = this;\n      return _asyncToGenerator(function* () {\n        return yield _this7.performSecurityCheck();\n      })();\n    }\n    logSecurityEvent(result) {\n      if (this.supabaseService.isConnectedToSupabase()) {\n        const userId = this.supabaseService.getCurrentUserId();\n        let eventType = 'access_denied';\n        if (result.isVpnDetected) {\n          eventType = 'vpn_detected';\n        } else if (result.isAdBlockerDetected) {\n          eventType = 'adblocker_detected';\n        } else if (result.isDnsBlocked) {\n          eventType = 'dns_blocked';\n        }\n        this.supabaseService.logSecurityEvent({\n          user_id: userId,\n          event_type: eventType,\n          details: {\n            vpn_detected: result.isVpnDetected,\n            adblocker_detected: result.isAdBlockerDetected,\n            dns_blocked: result.isDnsBlocked,\n            blocked_reason: result.blockedReason,\n            timestamp: new Date().toISOString()\n          },\n          ip_address: this.getCurrentIP(),\n          user_agent: navigator.userAgent\n        }).subscribe({\n          next: log => {\n            console.log('Security event logged:', log);\n          },\n          error: error => {\n            console.error('Error logging security event:', error);\n          }\n        });\n      }\n    }\n    getCurrentIP() {\n      // This would need to be implemented with an external service\n      // For now, return a placeholder\n      return '0.0.0.0';\n    }\n    static {\n      this.ɵfac = function SecurityService_Factory(t) {\n        return new (t || SecurityService)(i0.ɵɵinject(i1.SupabaseService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: SecurityService,\n        factory: SecurityService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return SecurityService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}