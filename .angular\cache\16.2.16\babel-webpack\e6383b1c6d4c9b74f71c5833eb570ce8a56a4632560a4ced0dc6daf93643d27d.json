{"ast": null, "code": "export var FFFSType = /*#__PURE__*/function (FFFSType) {\n  FFFSType[\"MEMFS\"] = \"MEMFS\";\n  FFFSType[\"NODEFS\"] = \"NODEFS\";\n  FFFSType[\"NODERAWFS\"] = \"NODERAWFS\";\n  FFFSType[\"IDBFS\"] = \"IDBFS\";\n  FFFSType[\"WORKERFS\"] = \"WORKERFS\";\n  FFFSType[\"PROXYFS\"] = \"PROXYFS\";\n  return FFFSType;\n}(FFFSType || {});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}