{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Input, NgModule } from '@angular/core';\n\n/**\n * ProgressBar is a process status indicator.\n * @group Components\n */\nfunction ProgressBar_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"display\", ctx_r2.value != null && ctx_r2.value !== 0 ? \"flex\" : \"none\");\n    i0.ɵɵattribute(\"data-pc-section\", \"label\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.value, \"\", ctx_r2.unit, \"\");\n  }\n}\nfunction ProgressBar_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵtemplate(1, ProgressBar_div_1_div_1_Template, 2, 5, \"div\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"width\", ctx_r0.value + \"%\")(\"background\", ctx_r0.color);\n    i0.ɵɵattribute(\"data-pc-section\", \"value\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showValue);\n  }\n}\nfunction ProgressBar_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵelement(1, \"div\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"data-pc-section\", \"container\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"background\", ctx_r1.color);\n    i0.ɵɵattribute(\"data-pc-section\", \"value\");\n  }\n}\nconst _c0 = function (a1, a2) {\n  return {\n    \"p-progressbar p-component\": true,\n    \"p-progressbar-determinate\": a1,\n    \"p-progressbar-indeterminate\": a2\n  };\n};\nlet ProgressBar = /*#__PURE__*/(() => {\n  class ProgressBar {\n    /**\n     * Current value of the progress.\n     * @group Props\n     */\n    value;\n    /**\n     * Whether to display the progress bar value.\n     * @group Props\n     */\n    showValue = true;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    style;\n    /**\n     * Unit sign appended to the value.\n     * @group Props\n     */\n    unit = '%';\n    /**\n     * Defines the mode of the progress\n     * @group Props\n     */\n    mode = 'determinate';\n    /**\n     * Color for the background of the progress.\n     * @group Props\n     */\n    color;\n    static ɵfac = function ProgressBar_Factory(t) {\n      return new (t || ProgressBar)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: ProgressBar,\n      selectors: [[\"p-progressBar\"]],\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        value: \"value\",\n        showValue: \"showValue\",\n        styleClass: \"styleClass\",\n        style: \"style\",\n        unit: \"unit\",\n        mode: \"mode\",\n        color: \"color\"\n      },\n      decls: 3,\n      vars: 14,\n      consts: [[\"role\", \"progressbar\", 3, \"ngStyle\", \"ngClass\"], [\"class\", \"p-progressbar-value p-progressbar-value-animate\", \"style\", \"display:flex\", 3, \"width\", \"background\", 4, \"ngIf\"], [\"class\", \"p-progressbar-indeterminate-container\", 4, \"ngIf\"], [1, \"p-progressbar-value\", \"p-progressbar-value-animate\", 2, \"display\", \"flex\"], [\"class\", \"p-progressbar-label\", 3, \"display\", 4, \"ngIf\"], [1, \"p-progressbar-label\"], [1, \"p-progressbar-indeterminate-container\"], [1, \"p-progressbar-value\", \"p-progressbar-value-animate\"]],\n      template: function ProgressBar_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, ProgressBar_div_1_Template, 2, 6, \"div\", 1);\n          i0.ɵɵtemplate(2, ProgressBar_div_2_Template, 2, 4, \"div\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.styleClass);\n          i0.ɵɵproperty(\"ngStyle\", ctx.style)(\"ngClass\", i0.ɵɵpureFunction2(11, _c0, ctx.mode === \"determinate\", ctx.mode === \"indeterminate\"));\n          i0.ɵɵattribute(\"aria-valuemin\", 0)(\"aria-valuenow\", ctx.value)(\"aria-valuemax\", 100)(\"data-pc-name\", \"progressbar\")(\"data-pc-section\", \"root\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.mode === \"determinate\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.mode === \"indeterminate\");\n        }\n      },\n      dependencies: [i1.NgClass, i1.NgIf, i1.NgStyle],\n      styles: [\"@layer primeng{.p-progressbar{position:relative;overflow:hidden}.p-progressbar-determinate .p-progressbar-value{height:100%;width:0%;position:absolute;display:none;border:0 none;display:flex;align-items:center;justify-content:center;overflow:hidden}.p-progressbar-determinate .p-progressbar-label{display:inline-flex}.p-progressbar-determinate .p-progressbar-value-animate{transition:width 1s ease-in-out}.p-progressbar-indeterminate .p-progressbar-value:before{content:\\\"\\\";position:absolute;background-color:inherit;top:0;left:0;bottom:0;will-change:left,right;animation:p-progressbar-indeterminate-anim 2.1s cubic-bezier(.65,.815,.735,.395) infinite}.p-progressbar-indeterminate .p-progressbar-value:after{content:\\\"\\\";position:absolute;background-color:inherit;top:0;left:0;bottom:0;will-change:left,right;animation:p-progressbar-indeterminate-anim-short 2.1s cubic-bezier(.165,.84,.44,1) infinite;animation-delay:1.15s}}@keyframes p-progressbar-indeterminate-anim{0%{left:-35%;right:100%}60%{left:100%;right:-90%}to{left:100%;right:-90%}}@keyframes p-progressbar-indeterminate-anim-short{0%{left:-200%;right:100%}60%{left:107%;right:-8%}to{left:107%;right:-8%}}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return ProgressBar;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet ProgressBarModule = /*#__PURE__*/(() => {\n  class ProgressBarModule {\n    static ɵfac = function ProgressBarModule_Factory(t) {\n      return new (t || ProgressBarModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: ProgressBarModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n  return ProgressBarModule;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ProgressBar, ProgressBarModule };\n//# sourceMappingURL=primeng-progressbar.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}