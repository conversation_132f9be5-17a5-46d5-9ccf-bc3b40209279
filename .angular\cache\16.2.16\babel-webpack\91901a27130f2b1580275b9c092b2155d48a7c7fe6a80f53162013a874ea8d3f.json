{"ast": null, "code": "import { FFMessageType } from \"./const.js\";\nimport { getMessageID } from \"./utils.js\";\nimport { ERROR_TERMINATED, ERROR_NOT_LOADED } from \"./errors.js\";\n/**\n * Provides APIs to interact with ffmpeg web worker.\n *\n * @example\n * ```ts\n * const ffmpeg = new FFmpeg();\n * ```\n */\nexport class FFmpeg {\n  #worker = null;\n  /**\n   * #resolves and #rejects tracks Promise resolves and rejects to\n   * be called when we receive message from web worker.\n   */\n  #resolves = {};\n  #rejects = {};\n  #logEventCallbacks = [];\n  #progressEventCallbacks = [];\n  loaded = false;\n  /**\n   * register worker message event handlers.\n   */\n  #registerHandlers = () => {\n    if (this.#worker) {\n      this.#worker.onmessage = ({\n        data: {\n          id,\n          type,\n          data\n        }\n      }) => {\n        switch (type) {\n          case FFMessageType.LOAD:\n            this.loaded = true;\n            this.#resolves[id](data);\n            break;\n          case FFMessageType.MOUNT:\n          case FFMessageType.UNMOUNT:\n          case FFMessageType.EXEC:\n          case FFMessageType.FFPROBE:\n          case FFMessageType.WRITE_FILE:\n          case FFMessageType.READ_FILE:\n          case FFMessageType.DELETE_FILE:\n          case FFMessageType.RENAME:\n          case FFMessageType.CREATE_DIR:\n          case FFMessageType.LIST_DIR:\n          case FFMessageType.DELETE_DIR:\n            this.#resolves[id](data);\n            break;\n          case FFMessageType.LOG:\n            this.#logEventCallbacks.forEach(f => f(data));\n            break;\n          case FFMessageType.PROGRESS:\n            this.#progressEventCallbacks.forEach(f => f(data));\n            break;\n          case FFMessageType.ERROR:\n            this.#rejects[id](data);\n            break;\n        }\n        delete this.#resolves[id];\n        delete this.#rejects[id];\n      };\n    }\n  };\n  /**\n   * Generic function to send messages to web worker.\n   */\n  #send = ({\n    type,\n    data\n  }, trans = [], signal) => {\n    if (!this.#worker) {\n      return Promise.reject(ERROR_NOT_LOADED);\n    }\n    return new Promise((resolve, reject) => {\n      const id = getMessageID();\n      this.#worker && this.#worker.postMessage({\n        id,\n        type,\n        data\n      }, trans);\n      this.#resolves[id] = resolve;\n      this.#rejects[id] = reject;\n      signal?.addEventListener(\"abort\", () => {\n        reject(new DOMException(`Message # ${id} was aborted`, \"AbortError\"));\n      }, {\n        once: true\n      });\n    });\n  };\n  on(event, callback) {\n    if (event === \"log\") {\n      this.#logEventCallbacks.push(callback);\n    } else if (event === \"progress\") {\n      this.#progressEventCallbacks.push(callback);\n    }\n  }\n  off(event, callback) {\n    if (event === \"log\") {\n      this.#logEventCallbacks = this.#logEventCallbacks.filter(f => f !== callback);\n    } else if (event === \"progress\") {\n      this.#progressEventCallbacks = this.#progressEventCallbacks.filter(f => f !== callback);\n    }\n  }\n  /**\n   * Loads ffmpeg-core inside web worker. It is required to call this method first\n   * as it initializes WebAssembly and other essential variables.\n   *\n   * @category FFmpeg\n   * @returns `true` if ffmpeg core is loaded for the first time.\n   */\n  load = ({\n    classWorkerURL,\n    ...config\n  } = {}, {\n    signal\n  } = {}) => {\n    if (!this.#worker) {\n      this.#worker = classWorkerURL ? new Worker(new URL(classWorkerURL, import.meta.url), {\n        type: \"module\"\n      }) :\n      // We need to duplicated the code here to enable webpack\n      // to bundle worekr.js here.\n      new Worker(new URL(\"./worker.js\", import.meta.url), {\n        type: \"module\"\n      });\n      this.#registerHandlers();\n    }\n    return this.#send({\n      type: FFMessageType.LOAD,\n      data: config\n    }, undefined, signal);\n  };\n  /**\n   * Execute ffmpeg command.\n   *\n   * @remarks\n   * To avoid common I/O issues, [\"-nostdin\", \"-y\"] are prepended to the args\n   * by default.\n   *\n   * @example\n   * ```ts\n   * const ffmpeg = new FFmpeg();\n   * await ffmpeg.load();\n   * await ffmpeg.writeFile(\"video.avi\", ...);\n   * // ffmpeg -i video.avi video.mp4\n   * await ffmpeg.exec([\"-i\", \"video.avi\", \"video.mp4\"]);\n   * const data = ffmpeg.readFile(\"video.mp4\");\n   * ```\n   *\n   * @returns `0` if no error, `!= 0` if timeout (1) or error.\n   * @category FFmpeg\n   */\n  exec = ( /** ffmpeg command line args */\n  args,\n  /**\n   * milliseconds to wait before stopping the command execution.\n   *\n   * @defaultValue -1\n   */\n  timeout = -1, {\n    signal\n  } = {}) => this.#send({\n    type: FFMessageType.EXEC,\n    data: {\n      args,\n      timeout\n    }\n  }, undefined, signal);\n  /**\n   * Execute ffprobe command.\n   *\n   * @example\n   * ```ts\n   * const ffmpeg = new FFmpeg();\n   * await ffmpeg.load();\n   * await ffmpeg.writeFile(\"video.avi\", ...);\n   * // Getting duration of a video in seconds: ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 video.avi -o output.txt\n   * await ffmpeg.ffprobe([\"-v\", \"error\", \"-show_entries\", \"format=duration\", \"-of\", \"default=noprint_wrappers=1:nokey=1\", \"video.avi\", \"-o\", \"output.txt\"]);\n   * const data = ffmpeg.readFile(\"output.txt\");\n   * ```\n   *\n   * @returns `0` if no error, `!= 0` if timeout (1) or error.\n   * @category FFmpeg\n   */\n  ffprobe = ( /** ffprobe command line args */\n  args,\n  /**\n   * milliseconds to wait before stopping the command execution.\n   *\n   * @defaultValue -1\n   */\n  timeout = -1, {\n    signal\n  } = {}) => this.#send({\n    type: FFMessageType.FFPROBE,\n    data: {\n      args,\n      timeout\n    }\n  }, undefined, signal);\n  /**\n   * Terminate all ongoing API calls and terminate web worker.\n   * `FFmpeg.load()` must be called again before calling any other APIs.\n   *\n   * @category FFmpeg\n   */\n  terminate = () => {\n    const ids = Object.keys(this.#rejects);\n    // rejects all incomplete Promises.\n    for (const id of ids) {\n      this.#rejects[id](ERROR_TERMINATED);\n      delete this.#rejects[id];\n      delete this.#resolves[id];\n    }\n    if (this.#worker) {\n      this.#worker.terminate();\n      this.#worker = null;\n      this.loaded = false;\n    }\n  };\n  /**\n   * Write data to ffmpeg.wasm.\n   *\n   * @example\n   * ```ts\n   * const ffmpeg = new FFmpeg();\n   * await ffmpeg.load();\n   * await ffmpeg.writeFile(\"video.avi\", await fetchFile(\"../video.avi\"));\n   * await ffmpeg.writeFile(\"text.txt\", \"hello world\");\n   * ```\n   *\n   * @category File System\n   */\n  writeFile = (path, data, {\n    signal\n  } = {}) => {\n    const trans = [];\n    if (data instanceof Uint8Array) {\n      trans.push(data.buffer);\n    }\n    return this.#send({\n      type: FFMessageType.WRITE_FILE,\n      data: {\n        path,\n        data\n      }\n    }, trans, signal);\n  };\n  mount = (fsType, options, mountPoint) => {\n    const trans = [];\n    return this.#send({\n      type: FFMessageType.MOUNT,\n      data: {\n        fsType,\n        options,\n        mountPoint\n      }\n    }, trans);\n  };\n  unmount = mountPoint => {\n    const trans = [];\n    return this.#send({\n      type: FFMessageType.UNMOUNT,\n      data: {\n        mountPoint\n      }\n    }, trans);\n  };\n  /**\n   * Read data from ffmpeg.wasm.\n   *\n   * @example\n   * ```ts\n   * const ffmpeg = new FFmpeg();\n   * await ffmpeg.load();\n   * const data = await ffmpeg.readFile(\"video.mp4\");\n   * ```\n   *\n   * @category File System\n   */\n  readFile = (path,\n  /**\n   * File content encoding, supports two encodings:\n   * - utf8: read file as text file, return data in string type.\n   * - binary: read file as binary file, return data in Uint8Array type.\n   *\n   * @defaultValue binary\n   */\n  encoding = \"binary\", {\n    signal\n  } = {}) => this.#send({\n    type: FFMessageType.READ_FILE,\n    data: {\n      path,\n      encoding\n    }\n  }, undefined, signal);\n  /**\n   * Delete a file.\n   *\n   * @category File System\n   */\n  deleteFile = (path, {\n    signal\n  } = {}) => this.#send({\n    type: FFMessageType.DELETE_FILE,\n    data: {\n      path\n    }\n  }, undefined, signal);\n  /**\n   * Rename a file or directory.\n   *\n   * @category File System\n   */\n  rename = (oldPath, newPath, {\n    signal\n  } = {}) => this.#send({\n    type: FFMessageType.RENAME,\n    data: {\n      oldPath,\n      newPath\n    }\n  }, undefined, signal);\n  /**\n   * Create a directory.\n   *\n   * @category File System\n   */\n  createDir = (path, {\n    signal\n  } = {}) => this.#send({\n    type: FFMessageType.CREATE_DIR,\n    data: {\n      path\n    }\n  }, undefined, signal);\n  /**\n   * List directory contents.\n   *\n   * @category File System\n   */\n  listDir = (path, {\n    signal\n  } = {}) => this.#send({\n    type: FFMessageType.LIST_DIR,\n    data: {\n      path\n    }\n  }, undefined, signal);\n  /**\n   * Delete an empty directory.\n   *\n   * @category File System\n   */\n  deleteDir = (path, {\n    signal\n  } = {}) => this.#send({\n    type: FFMessageType.DELETE_DIR,\n    data: {\n      path\n    }\n  }, undefined, signal);\n}", "map": {"version": 3, "names": ["FFMessageType", "getMessageID", "ERROR_TERMINATED", "ERROR_NOT_LOADED", "FFmpeg", "worker", "resolves", "rejects", "logEventCallbacks", "progressEventCallbacks", "loaded", "registerHandlers", "#registerHandlers", "onmessage", "data", "id", "type", "LOAD", "MOUNT", "UNMOUNT", "EXEC", "FFPROBE", "WRITE_FILE", "READ_FILE", "DELETE_FILE", "RENAME", "CREATE_DIR", "LIST_DIR", "DELETE_DIR", "LOG", "for<PERSON>ach", "f", "PROGRESS", "ERROR", "send", "#send", "trans", "signal", "Promise", "reject", "resolve", "postMessage", "addEventListener", "DOMException", "once", "on", "event", "callback", "push", "off", "filter", "load", "classWorkerURL", "config", "Worker", "URL", "import", "meta", "url", "undefined", "exec", "args", "timeout", "ffprobe", "terminate", "ids", "Object", "keys", "writeFile", "path", "Uint8Array", "buffer", "mount", "fsType", "options", "mountPoint", "unmount", "readFile", "encoding", "deleteFile", "rename", "old<PERSON><PERSON>", "newPath", "createDir", "listDir", "deleteDir"], "sources": ["C:/Users/<USER>/Desktop/تطبيق ناشر ايات قئانية/QuranVidGen/node_modules/@ffmpeg/ffmpeg/dist/esm/classes.js"], "sourcesContent": ["import { FFMessageType } from \"./const.js\";\nimport { getMessageID } from \"./utils.js\";\nimport { ERROR_TERMINATED, ERROR_NOT_LOADED } from \"./errors.js\";\n/**\n * Provides APIs to interact with ffmpeg web worker.\n *\n * @example\n * ```ts\n * const ffmpeg = new FFmpeg();\n * ```\n */\nexport class FFmpeg {\n    #worker = null;\n    /**\n     * #resolves and #rejects tracks Promise resolves and rejects to\n     * be called when we receive message from web worker.\n     */\n    #resolves = {};\n    #rejects = {};\n    #logEventCallbacks = [];\n    #progressEventCallbacks = [];\n    loaded = false;\n    /**\n     * register worker message event handlers.\n     */\n    #registerHandlers = () => {\n        if (this.#worker) {\n            this.#worker.onmessage = ({ data: { id, type, data }, }) => {\n                switch (type) {\n                    case FFMessageType.LOAD:\n                        this.loaded = true;\n                        this.#resolves[id](data);\n                        break;\n                    case FFMessageType.MOUNT:\n                    case FFMessageType.UNMOUNT:\n                    case FFMessageType.EXEC:\n                    case FFMessageType.FFPROBE:\n                    case FFMessageType.WRITE_FILE:\n                    case FFMessageType.READ_FILE:\n                    case FFMessageType.DELETE_FILE:\n                    case FFMessageType.RENAME:\n                    case FFMessageType.CREATE_DIR:\n                    case FFMessageType.LIST_DIR:\n                    case FFMessageType.DELETE_DIR:\n                        this.#resolves[id](data);\n                        break;\n                    case FFMessageType.LOG:\n                        this.#logEventCallbacks.forEach((f) => f(data));\n                        break;\n                    case FFMessageType.PROGRESS:\n                        this.#progressEventCallbacks.forEach((f) => f(data));\n                        break;\n                    case FFMessageType.ERROR:\n                        this.#rejects[id](data);\n                        break;\n                }\n                delete this.#resolves[id];\n                delete this.#rejects[id];\n            };\n        }\n    };\n    /**\n     * Generic function to send messages to web worker.\n     */\n    #send = ({ type, data }, trans = [], signal) => {\n        if (!this.#worker) {\n            return Promise.reject(ERROR_NOT_LOADED);\n        }\n        return new Promise((resolve, reject) => {\n            const id = getMessageID();\n            this.#worker && this.#worker.postMessage({ id, type, data }, trans);\n            this.#resolves[id] = resolve;\n            this.#rejects[id] = reject;\n            signal?.addEventListener(\"abort\", () => {\n                reject(new DOMException(`Message # ${id} was aborted`, \"AbortError\"));\n            }, { once: true });\n        });\n    };\n    on(event, callback) {\n        if (event === \"log\") {\n            this.#logEventCallbacks.push(callback);\n        }\n        else if (event === \"progress\") {\n            this.#progressEventCallbacks.push(callback);\n        }\n    }\n    off(event, callback) {\n        if (event === \"log\") {\n            this.#logEventCallbacks = this.#logEventCallbacks.filter((f) => f !== callback);\n        }\n        else if (event === \"progress\") {\n            this.#progressEventCallbacks = this.#progressEventCallbacks.filter((f) => f !== callback);\n        }\n    }\n    /**\n     * Loads ffmpeg-core inside web worker. It is required to call this method first\n     * as it initializes WebAssembly and other essential variables.\n     *\n     * @category FFmpeg\n     * @returns `true` if ffmpeg core is loaded for the first time.\n     */\n    load = ({ classWorkerURL, ...config } = {}, { signal } = {}) => {\n        if (!this.#worker) {\n            this.#worker = classWorkerURL ?\n                new Worker(new URL(classWorkerURL, import.meta.url), {\n                    type: \"module\",\n                }) :\n                // We need to duplicated the code here to enable webpack\n                // to bundle worekr.js here.\n                new Worker(new URL(\"./worker.js\", import.meta.url), {\n                    type: \"module\",\n                });\n            this.#registerHandlers();\n        }\n        return this.#send({\n            type: FFMessageType.LOAD,\n            data: config,\n        }, undefined, signal);\n    };\n    /**\n     * Execute ffmpeg command.\n     *\n     * @remarks\n     * To avoid common I/O issues, [\"-nostdin\", \"-y\"] are prepended to the args\n     * by default.\n     *\n     * @example\n     * ```ts\n     * const ffmpeg = new FFmpeg();\n     * await ffmpeg.load();\n     * await ffmpeg.writeFile(\"video.avi\", ...);\n     * // ffmpeg -i video.avi video.mp4\n     * await ffmpeg.exec([\"-i\", \"video.avi\", \"video.mp4\"]);\n     * const data = ffmpeg.readFile(\"video.mp4\");\n     * ```\n     *\n     * @returns `0` if no error, `!= 0` if timeout (1) or error.\n     * @category FFmpeg\n     */\n    exec = (\n    /** ffmpeg command line args */\n    args, \n    /**\n     * milliseconds to wait before stopping the command execution.\n     *\n     * @defaultValue -1\n     */\n    timeout = -1, { signal } = {}) => this.#send({\n        type: FFMessageType.EXEC,\n        data: { args, timeout },\n    }, undefined, signal);\n    /**\n     * Execute ffprobe command.\n     *\n     * @example\n     * ```ts\n     * const ffmpeg = new FFmpeg();\n     * await ffmpeg.load();\n     * await ffmpeg.writeFile(\"video.avi\", ...);\n     * // Getting duration of a video in seconds: ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 video.avi -o output.txt\n     * await ffmpeg.ffprobe([\"-v\", \"error\", \"-show_entries\", \"format=duration\", \"-of\", \"default=noprint_wrappers=1:nokey=1\", \"video.avi\", \"-o\", \"output.txt\"]);\n     * const data = ffmpeg.readFile(\"output.txt\");\n     * ```\n     *\n     * @returns `0` if no error, `!= 0` if timeout (1) or error.\n     * @category FFmpeg\n     */\n    ffprobe = (\n    /** ffprobe command line args */\n    args, \n    /**\n     * milliseconds to wait before stopping the command execution.\n     *\n     * @defaultValue -1\n     */\n    timeout = -1, { signal } = {}) => this.#send({\n        type: FFMessageType.FFPROBE,\n        data: { args, timeout },\n    }, undefined, signal);\n    /**\n     * Terminate all ongoing API calls and terminate web worker.\n     * `FFmpeg.load()` must be called again before calling any other APIs.\n     *\n     * @category FFmpeg\n     */\n    terminate = () => {\n        const ids = Object.keys(this.#rejects);\n        // rejects all incomplete Promises.\n        for (const id of ids) {\n            this.#rejects[id](ERROR_TERMINATED);\n            delete this.#rejects[id];\n            delete this.#resolves[id];\n        }\n        if (this.#worker) {\n            this.#worker.terminate();\n            this.#worker = null;\n            this.loaded = false;\n        }\n    };\n    /**\n     * Write data to ffmpeg.wasm.\n     *\n     * @example\n     * ```ts\n     * const ffmpeg = new FFmpeg();\n     * await ffmpeg.load();\n     * await ffmpeg.writeFile(\"video.avi\", await fetchFile(\"../video.avi\"));\n     * await ffmpeg.writeFile(\"text.txt\", \"hello world\");\n     * ```\n     *\n     * @category File System\n     */\n    writeFile = (path, data, { signal } = {}) => {\n        const trans = [];\n        if (data instanceof Uint8Array) {\n            trans.push(data.buffer);\n        }\n        return this.#send({\n            type: FFMessageType.WRITE_FILE,\n            data: { path, data },\n        }, trans, signal);\n    };\n    mount = (fsType, options, mountPoint) => {\n        const trans = [];\n        return this.#send({\n            type: FFMessageType.MOUNT,\n            data: { fsType, options, mountPoint },\n        }, trans);\n    };\n    unmount = (mountPoint) => {\n        const trans = [];\n        return this.#send({\n            type: FFMessageType.UNMOUNT,\n            data: { mountPoint },\n        }, trans);\n    };\n    /**\n     * Read data from ffmpeg.wasm.\n     *\n     * @example\n     * ```ts\n     * const ffmpeg = new FFmpeg();\n     * await ffmpeg.load();\n     * const data = await ffmpeg.readFile(\"video.mp4\");\n     * ```\n     *\n     * @category File System\n     */\n    readFile = (path, \n    /**\n     * File content encoding, supports two encodings:\n     * - utf8: read file as text file, return data in string type.\n     * - binary: read file as binary file, return data in Uint8Array type.\n     *\n     * @defaultValue binary\n     */\n    encoding = \"binary\", { signal } = {}) => this.#send({\n        type: FFMessageType.READ_FILE,\n        data: { path, encoding },\n    }, undefined, signal);\n    /**\n     * Delete a file.\n     *\n     * @category File System\n     */\n    deleteFile = (path, { signal } = {}) => this.#send({\n        type: FFMessageType.DELETE_FILE,\n        data: { path },\n    }, undefined, signal);\n    /**\n     * Rename a file or directory.\n     *\n     * @category File System\n     */\n    rename = (oldPath, newPath, { signal } = {}) => this.#send({\n        type: FFMessageType.RENAME,\n        data: { oldPath, newPath },\n    }, undefined, signal);\n    /**\n     * Create a directory.\n     *\n     * @category File System\n     */\n    createDir = (path, { signal } = {}) => this.#send({\n        type: FFMessageType.CREATE_DIR,\n        data: { path },\n    }, undefined, signal);\n    /**\n     * List directory contents.\n     *\n     * @category File System\n     */\n    listDir = (path, { signal } = {}) => this.#send({\n        type: FFMessageType.LIST_DIR,\n        data: { path },\n    }, undefined, signal);\n    /**\n     * Delete an empty directory.\n     *\n     * @category File System\n     */\n    deleteDir = (path, { signal } = {}) => this.#send({\n        type: FFMessageType.DELETE_DIR,\n        data: { path },\n    }, undefined, signal);\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,YAAY;AAC1C,SAASC,YAAY,QAAQ,YAAY;AACzC,SAASC,gBAAgB,EAAEC,gBAAgB,QAAQ,aAAa;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,MAAM,CAAC;EAChB,CAACC,MAAM,GAAG,IAAI;EACd;AACJ;AACA;AACA;EACI,CAACC,QAAQ,GAAG,CAAC,CAAC;EACd,CAACC,OAAO,GAAG,CAAC,CAAC;EACb,CAACC,iBAAiB,GAAG,EAAE;EACvB,CAACC,sBAAsB,GAAG,EAAE;EAC5BC,MAAM,GAAG,KAAK;EACd;AACJ;AACA;EACI,CAACC,gBAAgB,GAAGC,CAAA,KAAM;IACtB,IAAI,IAAI,CAAC,CAACP,MAAM,EAAE;MACd,IAAI,CAAC,CAACA,MAAM,CAACQ,SAAS,GAAG,CAAC;QAAEC,IAAI,EAAE;UAAEC,EAAE;UAAEC,IAAI;UAAEF;QAAK;MAAG,CAAC,KAAK;QACxD,QAAQE,IAAI;UACR,KAAKhB,aAAa,CAACiB,IAAI;YACnB,IAAI,CAACP,MAAM,GAAG,IAAI;YAClB,IAAI,CAAC,CAACJ,QAAQ,CAACS,EAAE,CAAC,CAACD,IAAI,CAAC;YACxB;UACJ,KAAKd,aAAa,CAACkB,KAAK;UACxB,KAAKlB,aAAa,CAACmB,OAAO;UAC1B,KAAKnB,aAAa,CAACoB,IAAI;UACvB,KAAKpB,aAAa,CAACqB,OAAO;UAC1B,KAAKrB,aAAa,CAACsB,UAAU;UAC7B,KAAKtB,aAAa,CAACuB,SAAS;UAC5B,KAAKvB,aAAa,CAACwB,WAAW;UAC9B,KAAKxB,aAAa,CAACyB,MAAM;UACzB,KAAKzB,aAAa,CAAC0B,UAAU;UAC7B,KAAK1B,aAAa,CAAC2B,QAAQ;UAC3B,KAAK3B,aAAa,CAAC4B,UAAU;YACzB,IAAI,CAAC,CAACtB,QAAQ,CAACS,EAAE,CAAC,CAACD,IAAI,CAAC;YACxB;UACJ,KAAKd,aAAa,CAAC6B,GAAG;YAClB,IAAI,CAAC,CAACrB,iBAAiB,CAACsB,OAAO,CAAEC,CAAC,IAAKA,CAAC,CAACjB,IAAI,CAAC,CAAC;YAC/C;UACJ,KAAKd,aAAa,CAACgC,QAAQ;YACvB,IAAI,CAAC,CAACvB,sBAAsB,CAACqB,OAAO,CAAEC,CAAC,IAAKA,CAAC,CAACjB,IAAI,CAAC,CAAC;YACpD;UACJ,KAAKd,aAAa,CAACiC,KAAK;YACpB,IAAI,CAAC,CAAC1B,OAAO,CAACQ,EAAE,CAAC,CAACD,IAAI,CAAC;YACvB;QACR;QACA,OAAO,IAAI,CAAC,CAACR,QAAQ,CAACS,EAAE,CAAC;QACzB,OAAO,IAAI,CAAC,CAACR,OAAO,CAACQ,EAAE,CAAC;MAC5B,CAAC;IACL;EACJ,CAAC;EACD;AACJ;AACA;EACI,CAACmB,IAAI,GAAGC,CAAC;IAAEnB,IAAI;IAAEF;EAAK,CAAC,EAAEsB,KAAK,GAAG,EAAE,EAAEC,MAAM,KAAK;IAC5C,IAAI,CAAC,IAAI,CAAC,CAAChC,MAAM,EAAE;MACf,OAAOiC,OAAO,CAACC,MAAM,CAACpC,gBAAgB,CAAC;IAC3C;IACA,OAAO,IAAImC,OAAO,CAAC,CAACE,OAAO,EAAED,MAAM,KAAK;MACpC,MAAMxB,EAAE,GAAGd,YAAY,CAAC,CAAC;MACzB,IAAI,CAAC,CAACI,MAAM,IAAI,IAAI,CAAC,CAACA,MAAM,CAACoC,WAAW,CAAC;QAAE1B,EAAE;QAAEC,IAAI;QAAEF;MAAK,CAAC,EAAEsB,KAAK,CAAC;MACnE,IAAI,CAAC,CAAC9B,QAAQ,CAACS,EAAE,CAAC,GAAGyB,OAAO;MAC5B,IAAI,CAAC,CAACjC,OAAO,CAACQ,EAAE,CAAC,GAAGwB,MAAM;MAC1BF,MAAM,EAAEK,gBAAgB,CAAC,OAAO,EAAE,MAAM;QACpCH,MAAM,CAAC,IAAII,YAAY,CAAE,aAAY5B,EAAG,cAAa,EAAE,YAAY,CAAC,CAAC;MACzE,CAAC,EAAE;QAAE6B,IAAI,EAAE;MAAK,CAAC,CAAC;IACtB,CAAC,CAAC;EACN,CAAC;EACDC,EAAEA,CAACC,KAAK,EAAEC,QAAQ,EAAE;IAChB,IAAID,KAAK,KAAK,KAAK,EAAE;MACjB,IAAI,CAAC,CAACtC,iBAAiB,CAACwC,IAAI,CAACD,QAAQ,CAAC;IAC1C,CAAC,MACI,IAAID,KAAK,KAAK,UAAU,EAAE;MAC3B,IAAI,CAAC,CAACrC,sBAAsB,CAACuC,IAAI,CAACD,QAAQ,CAAC;IAC/C;EACJ;EACAE,GAAGA,CAACH,KAAK,EAAEC,QAAQ,EAAE;IACjB,IAAID,KAAK,KAAK,KAAK,EAAE;MACjB,IAAI,CAAC,CAACtC,iBAAiB,GAAG,IAAI,CAAC,CAACA,iBAAiB,CAAC0C,MAAM,CAAEnB,CAAC,IAAKA,CAAC,KAAKgB,QAAQ,CAAC;IACnF,CAAC,MACI,IAAID,KAAK,KAAK,UAAU,EAAE;MAC3B,IAAI,CAAC,CAACrC,sBAAsB,GAAG,IAAI,CAAC,CAACA,sBAAsB,CAACyC,MAAM,CAAEnB,CAAC,IAAKA,CAAC,KAAKgB,QAAQ,CAAC;IAC7F;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACII,IAAI,GAAGA,CAAC;IAAEC,cAAc;IAAE,GAAGC;EAAO,CAAC,GAAG,CAAC,CAAC,EAAE;IAAEhB;EAAO,CAAC,GAAG,CAAC,CAAC,KAAK;IAC5D,IAAI,CAAC,IAAI,CAAC,CAAChC,MAAM,EAAE;MACf,IAAI,CAAC,CAACA,MAAM,GAAG+C,cAAc,GACzB,IAAIE,MAAM,CAAC,IAAIC,GAAG,CAACH,cAAc,EAAEI,MAAM,CAACC,IAAI,CAACC,GAAG,CAAC,EAAE;QACjD1C,IAAI,EAAE;MACV,CAAC,CAAC;MACF;MACA;MACA,IAAIsC,MAAM,CAAC,IAAIC,GAAG,CAAC,aAAa,EAAEC,MAAM,CAACC,IAAI,CAACC,GAAG,CAAC,EAAE;QAChD1C,IAAI,EAAE;MACV,CAAC,CAAC;MACN,IAAI,CAAC,CAACL,gBAAgB,CAAC,CAAC;IAC5B;IACA,OAAO,IAAI,CAAC,CAACuB,IAAI,CAAC;MACdlB,IAAI,EAAEhB,aAAa,CAACiB,IAAI;MACxBH,IAAI,EAAEuC;IACV,CAAC,EAAEM,SAAS,EAAEtB,MAAM,CAAC;EACzB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIuB,IAAI,GAAGA,CAAA,CACP;EACAC,IAAI;EACJ;AACJ;AACA;AACA;AACA;EACIC,OAAO,GAAG,CAAC,CAAC,EAAE;IAAEzB;EAAO,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,CAACH,IAAI,CAAC;IACzClB,IAAI,EAAEhB,aAAa,CAACoB,IAAI;IACxBN,IAAI,EAAE;MAAE+C,IAAI;MAAEC;IAAQ;EAC1B,CAAC,EAAEH,SAAS,EAAEtB,MAAM,CAAC;EACrB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI0B,OAAO,GAAGA,CAAA,CACV;EACAF,IAAI;EACJ;AACJ;AACA;AACA;AACA;EACIC,OAAO,GAAG,CAAC,CAAC,EAAE;IAAEzB;EAAO,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,CAACH,IAAI,CAAC;IACzClB,IAAI,EAAEhB,aAAa,CAACqB,OAAO;IAC3BP,IAAI,EAAE;MAAE+C,IAAI;MAAEC;IAAQ;EAC1B,CAAC,EAAEH,SAAS,EAAEtB,MAAM,CAAC;EACrB;AACJ;AACA;AACA;AACA;AACA;EACI2B,SAAS,GAAGA,CAAA,KAAM;IACd,MAAMC,GAAG,GAAGC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC5D,OAAO,CAAC;IACtC;IACA,KAAK,MAAMQ,EAAE,IAAIkD,GAAG,EAAE;MAClB,IAAI,CAAC,CAAC1D,OAAO,CAACQ,EAAE,CAAC,CAACb,gBAAgB,CAAC;MACnC,OAAO,IAAI,CAAC,CAACK,OAAO,CAACQ,EAAE,CAAC;MACxB,OAAO,IAAI,CAAC,CAACT,QAAQ,CAACS,EAAE,CAAC;IAC7B;IACA,IAAI,IAAI,CAAC,CAACV,MAAM,EAAE;MACd,IAAI,CAAC,CAACA,MAAM,CAAC2D,SAAS,CAAC,CAAC;MACxB,IAAI,CAAC,CAAC3D,MAAM,GAAG,IAAI;MACnB,IAAI,CAACK,MAAM,GAAG,KAAK;IACvB;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI0D,SAAS,GAAGA,CAACC,IAAI,EAAEvD,IAAI,EAAE;IAAEuB;EAAO,CAAC,GAAG,CAAC,CAAC,KAAK;IACzC,MAAMD,KAAK,GAAG,EAAE;IAChB,IAAItB,IAAI,YAAYwD,UAAU,EAAE;MAC5BlC,KAAK,CAACY,IAAI,CAAClC,IAAI,CAACyD,MAAM,CAAC;IAC3B;IACA,OAAO,IAAI,CAAC,CAACrC,IAAI,CAAC;MACdlB,IAAI,EAAEhB,aAAa,CAACsB,UAAU;MAC9BR,IAAI,EAAE;QAAEuD,IAAI;QAAEvD;MAAK;IACvB,CAAC,EAAEsB,KAAK,EAAEC,MAAM,CAAC;EACrB,CAAC;EACDmC,KAAK,GAAGA,CAACC,MAAM,EAAEC,OAAO,EAAEC,UAAU,KAAK;IACrC,MAAMvC,KAAK,GAAG,EAAE;IAChB,OAAO,IAAI,CAAC,CAACF,IAAI,CAAC;MACdlB,IAAI,EAAEhB,aAAa,CAACkB,KAAK;MACzBJ,IAAI,EAAE;QAAE2D,MAAM;QAAEC,OAAO;QAAEC;MAAW;IACxC,CAAC,EAAEvC,KAAK,CAAC;EACb,CAAC;EACDwC,OAAO,GAAID,UAAU,IAAK;IACtB,MAAMvC,KAAK,GAAG,EAAE;IAChB,OAAO,IAAI,CAAC,CAACF,IAAI,CAAC;MACdlB,IAAI,EAAEhB,aAAa,CAACmB,OAAO;MAC3BL,IAAI,EAAE;QAAE6D;MAAW;IACvB,CAAC,EAAEvC,KAAK,CAAC;EACb,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIyC,QAAQ,GAAGA,CAACR,IAAI;EAChB;AACJ;AACA;AACA;AACA;AACA;AACA;EACIS,QAAQ,GAAG,QAAQ,EAAE;IAAEzC;EAAO,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,CAACH,IAAI,CAAC;IAChDlB,IAAI,EAAEhB,aAAa,CAACuB,SAAS;IAC7BT,IAAI,EAAE;MAAEuD,IAAI;MAAES;IAAS;EAC3B,CAAC,EAAEnB,SAAS,EAAEtB,MAAM,CAAC;EACrB;AACJ;AACA;AACA;AACA;EACI0C,UAAU,GAAGA,CAACV,IAAI,EAAE;IAAEhC;EAAO,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,CAACH,IAAI,CAAC;IAC/ClB,IAAI,EAAEhB,aAAa,CAACwB,WAAW;IAC/BV,IAAI,EAAE;MAAEuD;IAAK;EACjB,CAAC,EAAEV,SAAS,EAAEtB,MAAM,CAAC;EACrB;AACJ;AACA;AACA;AACA;EACI2C,MAAM,GAAGA,CAACC,OAAO,EAAEC,OAAO,EAAE;IAAE7C;EAAO,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,CAACH,IAAI,CAAC;IACvDlB,IAAI,EAAEhB,aAAa,CAACyB,MAAM;IAC1BX,IAAI,EAAE;MAAEmE,OAAO;MAAEC;IAAQ;EAC7B,CAAC,EAAEvB,SAAS,EAAEtB,MAAM,CAAC;EACrB;AACJ;AACA;AACA;AACA;EACI8C,SAAS,GAAGA,CAACd,IAAI,EAAE;IAAEhC;EAAO,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,CAACH,IAAI,CAAC;IAC9ClB,IAAI,EAAEhB,aAAa,CAAC0B,UAAU;IAC9BZ,IAAI,EAAE;MAAEuD;IAAK;EACjB,CAAC,EAAEV,SAAS,EAAEtB,MAAM,CAAC;EACrB;AACJ;AACA;AACA;AACA;EACI+C,OAAO,GAAGA,CAACf,IAAI,EAAE;IAAEhC;EAAO,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,CAACH,IAAI,CAAC;IAC5ClB,IAAI,EAAEhB,aAAa,CAAC2B,QAAQ;IAC5Bb,IAAI,EAAE;MAAEuD;IAAK;EACjB,CAAC,EAAEV,SAAS,EAAEtB,MAAM,CAAC;EACrB;AACJ;AACA;AACA;AACA;EACIgD,SAAS,GAAGA,CAAChB,IAAI,EAAE;IAAEhC;EAAO,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,CAACH,IAAI,CAAC;IAC9ClB,IAAI,EAAEhB,aAAa,CAAC4B,UAAU;IAC9Bd,IAAI,EAAE;MAAEuD;IAAK;EACjB,CAAC,EAAEV,SAAS,EAAEtB,MAAM,CAAC;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}