{"ast": null, "code": "\"use strict\";\n\nvar __importDefault = this && this.__importDefault || function (mod) {\n  return mod && mod.__esModule ? mod : {\n    \"default\": mod\n  };\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.PostgrestError = exports.PostgrestBuilder = exports.PostgrestTransformBuilder = exports.PostgrestFilterBuilder = exports.PostgrestQueryBuilder = exports.PostgrestClient = void 0;\n// Always update wrapper.mjs when updating this file.\nconst PostgrestClient_1 = __importDefault(require(\"./PostgrestClient\"));\nexports.PostgrestClient = PostgrestClient_1.default;\nconst PostgrestQueryBuilder_1 = __importDefault(require(\"./PostgrestQueryBuilder\"));\nexports.PostgrestQueryBuilder = PostgrestQueryBuilder_1.default;\nconst PostgrestFilterBuilder_1 = __importDefault(require(\"./PostgrestFilterBuilder\"));\nexports.PostgrestFilterBuilder = PostgrestFilterBuilder_1.default;\nconst PostgrestTransformBuilder_1 = __importDefault(require(\"./PostgrestTransformBuilder\"));\nexports.PostgrestTransformBuilder = PostgrestTransformBuilder_1.default;\nconst PostgrestBuilder_1 = __importDefault(require(\"./PostgrestBuilder\"));\nexports.PostgrestBuilder = PostgrestBuilder_1.default;\nconst PostgrestError_1 = __importDefault(require(\"./PostgrestError\"));\nexports.PostgrestError = PostgrestError_1.default;\nexports.default = {\n  PostgrestClient: PostgrestClient_1.default,\n  PostgrestQueryBuilder: PostgrestQueryBuilder_1.default,\n  PostgrestFilterBuilder: PostgrestFilterBuilder_1.default,\n  PostgrestTransformBuilder: PostgrestTransformBuilder_1.default,\n  PostgrestBuilder: PostgrestBuilder_1.default,\n  PostgrestError: PostgrestError_1.default\n};\n//# sourceMappingURL=index.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}