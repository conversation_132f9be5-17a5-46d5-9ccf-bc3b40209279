Write-Host "Downloading and Setting up Gradle..." -ForegroundColor Green

# Create Gradle directory
$gradleDir = "C:\gradle"
$gradleVersion = "gradle-7.6.4"
$gradleHome = "$gradleDir\$gradleVersion"

Write-Host "Creating Gradle directory..." -ForegroundColor Yellow
New-Item -ItemType Directory -Force -Path $gradleDir | Out-Null

# Download URLs
$downloadUrls = @(
    "https://services.gradle.org/distributions/gradle-7.6.4-all.zip",
    "https://downloads.gradle-dn.com/distributions/gradle-7.6.4-all.zip"
)

$zipFile = "$gradleDir\gradle-7.6.4-all.zip"
$downloaded = $false

foreach ($url in $downloadUrls) {
    Write-Host "Trying to download from: $url" -ForegroundColor Cyan
    
    try {
        Invoke-WebRequest -Uri $url -OutFile $zipFile -TimeoutSec 300
        
        if (Test-Path $zipFile) {
            $fileSize = (Get-Item $zipFile).Length / 1MB
            Write-Host "Downloaded successfully! File size: $([math]::Round($fileSize, 2)) MB" -ForegroundColor Green
            $downloaded = $true
            break
        }
    }
    catch {
        Write-Host "Failed to download from this URL: $($_.Exception.Message)" -ForegroundColor Red
        continue
    }
}

if (-not $downloaded) {
    Write-Host "Failed to download Gradle from all sources" -ForegroundColor Red
    Write-Host "Alternative solutions:" -ForegroundColor Yellow
    Write-Host "1. Use Android Studio" -ForegroundColor White
    Write-Host "2. Download manually from gradle.org" -ForegroundColor White
    Write-Host "3. Use VPN and try again" -ForegroundColor White
    Read-Host "Press Enter to exit..."
    exit 1
}

# Extract file
Write-Host "Extracting Gradle..." -ForegroundColor Yellow

try {
    if (Test-Path $gradleHome) {
        Remove-Item $gradleHome -Recurse -Force
    }
    
    Expand-Archive -Path $zipFile -DestinationPath $gradleDir -Force
    
    if (Test-Path $gradleHome) {
        Write-Host "Gradle extracted successfully" -ForegroundColor Green
    } else {
        throw "Failed to extract file"
    }
}
catch {
    Write-Host "Error extracting Gradle: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "Press Enter to exit..."
    exit 1
}

# Set environment variables
Write-Host "Setting up environment variables..." -ForegroundColor Yellow

$env:GRADLE_HOME = $gradleHome
$env:PATH = "$gradleHome\bin;$env:PATH"
$env:JAVA_HOME = "C:\Program Files\Microsoft\jdk-*********-hotspot"

Write-Host "GRADLE_HOME: $env:GRADLE_HOME" -ForegroundColor Cyan
Write-Host "JAVA_HOME: $env:JAVA_HOME" -ForegroundColor Cyan

# Test installation
Write-Host "Testing Gradle installation..." -ForegroundColor Yellow

try {
    $gradleVersion = & "$gradleHome\bin\gradle.bat" --version 2>&1
    Write-Host "Gradle installed successfully:" -ForegroundColor Green
    Write-Host $gradleVersion -ForegroundColor White
}
catch {
    Write-Host "Error running Gradle: $($_.Exception.Message)" -ForegroundColor Red
}

# Clean up
Write-Host "Cleaning up temporary files..." -ForegroundColor Yellow
Remove-Item $zipFile -Force -ErrorAction SilentlyContinue

Write-Host ""
Write-Host "Gradle setup completed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Open new PowerShell" -ForegroundColor White
Write-Host "2. Navigate to android folder" -ForegroundColor White
Write-Host "3. Run: gradle assembleDebug" -ForegroundColor White

Read-Host "Press Enter to continue..."
