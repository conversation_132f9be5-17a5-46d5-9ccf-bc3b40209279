{"ast": null, "code": "import { animation, style, animate, trigger, transition, useAnimation } from '@angular/animations';\nimport * as i2 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, PLATFORM_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ContentChild, ContentChildren, ViewChild, NgModule } from '@angular/core';\nimport * as i1 from 'primeng/api';\nimport { Header, Footer, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { <PERSON><PERSON>and<PERSON> } from 'primeng/dom';\nimport * as i3 from 'primeng/focustrap';\nimport { FocusTrapModule } from 'primeng/focustrap';\nimport { TimesIcon } from 'primeng/icons/times';\nimport { WindowMaximizeIcon } from 'primeng/icons/windowmaximize';\nimport { WindowMinimizeIcon } from 'primeng/icons/windowminimize';\nimport * as i4 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport { UniqueComponentId, ZIndexUtils } from 'primeng/utils';\nconst _c0 = [\"titlebar\"];\nconst _c1 = [\"content\"];\nconst _c2 = [\"footer\"];\nfunction Dialog_div_0_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵlistener(\"mousedown\", function Dialog_div_0_div_1_div_2_Template_div_mousedown_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r8.initResize($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction Dialog_div_0_div_1_div_3_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 18);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"id\", ctx_r11.getAriaLabelledBy());\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r11.header);\n  }\n}\nfunction Dialog_div_0_div_1_div_3_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 18);\n    i0.ɵɵprojection(1, 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"id\", ctx_r12.getAriaLabelledBy());\n  }\n}\nfunction Dialog_div_0_div_1_div_3_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Dialog_div_0_div_1_div_3_button_6_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 22);\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(5);\n    i0.ɵɵproperty(\"ngClass\", ctx_r16.maximized ? ctx_r16.minimizeIcon : ctx_r16.maximizeIcon);\n  }\n}\nfunction Dialog_div_0_div_1_div_3_button_6_ng_container_2_WindowMaximizeIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"WindowMaximizeIcon\", 24);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-dialog-header-maximize-icon\");\n  }\n}\nfunction Dialog_div_0_div_1_div_3_button_6_ng_container_2_WindowMinimizeIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"WindowMinimizeIcon\", 24);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-dialog-header-maximize-icon\");\n  }\n}\nfunction Dialog_div_0_div_1_div_3_button_6_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Dialog_div_0_div_1_div_3_button_6_ng_container_2_WindowMaximizeIcon_1_Template, 1, 1, \"WindowMaximizeIcon\", 23);\n    i0.ɵɵtemplate(2, Dialog_div_0_div_1_div_3_button_6_ng_container_2_WindowMinimizeIcon_2_Template, 1, 1, \"WindowMinimizeIcon\", 23);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r17.maximized && !ctx_r17.maximizeIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r17.maximized && !ctx_r17.minimizeIconTemplate);\n  }\n}\nfunction Dialog_div_0_div_1_div_3_button_6_ng_container_3_1_ng_template_0_Template(rf, ctx) {}\nfunction Dialog_div_0_div_1_div_3_button_6_ng_container_3_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Dialog_div_0_div_1_div_3_button_6_ng_container_3_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Dialog_div_0_div_1_div_3_button_6_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Dialog_div_0_div_1_div_3_button_6_ng_container_3_1_Template, 1, 0, null, 9);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r18.maximizeIconTemplate);\n  }\n}\nfunction Dialog_div_0_div_1_div_3_button_6_ng_container_4_1_ng_template_0_Template(rf, ctx) {}\nfunction Dialog_div_0_div_1_div_3_button_6_ng_container_4_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Dialog_div_0_div_1_div_3_button_6_ng_container_4_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Dialog_div_0_div_1_div_3_button_6_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Dialog_div_0_div_1_div_3_button_6_ng_container_4_1_Template, 1, 0, null, 9);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r19.minimizeIconTemplate);\n  }\n}\nconst _c3 = function () {\n  return {\n    \"p-dialog-header-icon p-dialog-header-maximize p-link\": true\n  };\n};\nfunction Dialog_div_0_div_1_div_3_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function Dialog_div_0_div_1_div_3_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r26.maximize());\n    })(\"keydown.enter\", function Dialog_div_0_div_1_div_3_button_6_Template_button_keydown_enter_0_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r28 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r28.maximize());\n    });\n    i0.ɵɵtemplate(1, Dialog_div_0_div_1_div_3_button_6_span_1_Template, 1, 1, \"span\", 20);\n    i0.ɵɵtemplate(2, Dialog_div_0_div_1_div_3_button_6_ng_container_2_Template, 3, 2, \"ng-container\", 21);\n    i0.ɵɵtemplate(3, Dialog_div_0_div_1_div_3_button_6_ng_container_3_Template, 2, 1, \"ng-container\", 21);\n    i0.ɵɵtemplate(4, Dialog_div_0_div_1_div_3_button_6_ng_container_4_Template, 2, 1, \"ng-container\", 21);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction0(5, _c3));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.maximizeIcon && !ctx_r14.maximizeIconTemplate && !ctx_r14.minimizeIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r14.maximizeIcon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r14.maximized);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.maximized);\n  }\n}\nfunction Dialog_div_0_div_1_div_3_button_7_ng_container_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 27);\n  }\n  if (rf & 2) {\n    const ctx_r31 = i0.ɵɵnextContext(6);\n    i0.ɵɵproperty(\"ngClass\", ctx_r31.closeIcon);\n  }\n}\nfunction Dialog_div_0_div_1_div_3_button_7_ng_container_1_TimesIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"TimesIcon\", 24);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-dialog-header-close-icon\");\n  }\n}\nfunction Dialog_div_0_div_1_div_3_button_7_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Dialog_div_0_div_1_div_3_button_7_ng_container_1_span_1_Template, 1, 1, \"span\", 26);\n    i0.ɵɵtemplate(2, Dialog_div_0_div_1_div_3_button_7_ng_container_1_TimesIcon_2_Template, 1, 1, \"TimesIcon\", 23);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r29 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r29.closeIcon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r29.closeIcon);\n  }\n}\nfunction Dialog_div_0_div_1_div_3_button_7_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction Dialog_div_0_div_1_div_3_button_7_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Dialog_div_0_div_1_div_3_button_7_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Dialog_div_0_div_1_div_3_button_7_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtemplate(1, Dialog_div_0_div_1_div_3_button_7_span_2_1_Template, 1, 0, null, 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r30 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r30.closeIconTemplate);\n  }\n}\nconst _c4 = function () {\n  return {\n    \"p-dialog-header-icon p-dialog-header-close p-link\": true\n  };\n};\nfunction Dialog_div_0_div_1_div_3_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r36 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function Dialog_div_0_div_1_div_3_button_7_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r35 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r35.close($event));\n    })(\"keydown.enter\", function Dialog_div_0_div_1_div_3_button_7_Template_button_keydown_enter_0_listener($event) {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r37 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r37.close($event));\n    });\n    i0.ɵɵtemplate(1, Dialog_div_0_div_1_div_3_button_7_ng_container_1_Template, 3, 2, \"ng-container\", 21);\n    i0.ɵɵtemplate(2, Dialog_div_0_div_1_div_3_button_7_span_2_Template, 2, 1, \"span\", 21);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction0(5, _c4));\n    i0.ɵɵattribute(\"aria-label\", ctx_r15.closeAriaLabel)(\"tabindex\", ctx_r15.closeTabindex);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r15.closeIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r15.closeIconTemplate);\n  }\n}\nfunction Dialog_div_0_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12, 13);\n    i0.ɵɵlistener(\"mousedown\", function Dialog_div_0_div_1_div_3_Template_div_mousedown_0_listener($event) {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r38 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r38.initDrag($event));\n    });\n    i0.ɵɵtemplate(2, Dialog_div_0_div_1_div_3_span_2_Template, 2, 2, \"span\", 14);\n    i0.ɵɵtemplate(3, Dialog_div_0_div_1_div_3_span_3_Template, 2, 1, \"span\", 14);\n    i0.ɵɵtemplate(4, Dialog_div_0_div_1_div_3_ng_container_4_Template, 1, 0, \"ng-container\", 9);\n    i0.ɵɵelementStart(5, \"div\", 15);\n    i0.ɵɵtemplate(6, Dialog_div_0_div_1_div_3_button_6_Template, 5, 6, \"button\", 16);\n    i0.ɵɵtemplate(7, Dialog_div_0_div_1_div_3_button_7_Template, 3, 6, \"button\", 17);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.headerFacet && !ctx_r4.headerTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.headerFacet);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r4.headerTemplate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.maximizable);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.closable);\n  }\n}\nfunction Dialog_div_0_div_1_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Dialog_div_0_div_1_div_8_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Dialog_div_0_div_1_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28, 29);\n    i0.ɵɵprojection(2, 2);\n    i0.ɵɵtemplate(3, Dialog_div_0_div_1_div_8_ng_container_3_Template, 1, 0, \"ng-container\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r7.footerTemplate);\n  }\n}\nconst _c5 = function (a1, a2, a3, a4) {\n  return {\n    \"p-dialog p-component\": true,\n    \"p-dialog-rtl\": a1,\n    \"p-dialog-draggable\": a2,\n    \"p-dialog-resizable\": a3,\n    \"p-dialog-maximized\": a4\n  };\n};\nconst _c6 = function (a0, a1) {\n  return {\n    transform: a0,\n    transition: a1\n  };\n};\nconst _c7 = function (a1) {\n  return {\n    value: \"visible\",\n    params: a1\n  };\n};\nfunction Dialog_div_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r43 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3, 4);\n    i0.ɵɵlistener(\"@animation.start\", function Dialog_div_0_div_1_Template_div_animation_animation_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r43);\n      const ctx_r42 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r42.onAnimationStart($event));\n    })(\"@animation.done\", function Dialog_div_0_div_1_Template_div_animation_animation_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r43);\n      const ctx_r44 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r44.onAnimationEnd($event));\n    });\n    i0.ɵɵtemplate(2, Dialog_div_0_div_1_div_2_Template, 1, 0, \"div\", 5);\n    i0.ɵɵtemplate(3, Dialog_div_0_div_1_div_3_Template, 8, 5, \"div\", 6);\n    i0.ɵɵelementStart(4, \"div\", 7, 8);\n    i0.ɵɵprojection(6);\n    i0.ɵɵtemplate(7, Dialog_div_0_div_1_ng_container_7_Template, 1, 0, \"ng-container\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, Dialog_div_0_div_1_div_8_Template, 4, 1, \"div\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r1.styleClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(16, _c5, ctx_r1.rtl, ctx_r1.draggable, ctx_r1.resizable, ctx_r1.maximized))(\"ngStyle\", ctx_r1.style)(\"pFocusTrapDisabled\", ctx_r1.focusTrap === false)(\"@animation\", i0.ɵɵpureFunction1(24, _c7, i0.ɵɵpureFunction2(21, _c6, ctx_r1.transformOptions, ctx_r1.transitionOptions)));\n    i0.ɵɵattribute(\"aria-labelledby\", ctx_r1.ariaLabelledBy)(\"aria-modal\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.resizable);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showHeader);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r1.contentStyleClass);\n    i0.ɵɵproperty(\"ngClass\", \"p-dialog-content\")(\"ngStyle\", ctx_r1.contentStyle);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.contentTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.footerFacet || ctx_r1.footerTemplate);\n  }\n}\nconst _c8 = function (a1, a2, a3, a4, a5, a6, a7, a8, a9, a10) {\n  return {\n    \"p-dialog-mask\": true,\n    \"p-component-overlay p-component-overlay-enter\": a1,\n    \"p-dialog-mask-scrollblocker\": a2,\n    \"p-dialog-left\": a3,\n    \"p-dialog-right\": a4,\n    \"p-dialog-top\": a5,\n    \"p-dialog-top-left\": a6,\n    \"p-dialog-top-right\": a7,\n    \"p-dialog-bottom\": a8,\n    \"p-dialog-bottom-left\": a9,\n    \"p-dialog-bottom-right\": a10\n  };\n};\nfunction Dialog_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵtemplate(1, Dialog_div_0_div_1_Template, 9, 26, \"div\", 2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r0.maskStyleClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunctionV(4, _c8, [ctx_r0.modal, ctx_r0.modal || ctx_r0.blockScroll, ctx_r0.position === \"left\", ctx_r0.position === \"right\", ctx_r0.position === \"top\", ctx_r0.position === \"topleft\" || ctx_r0.position === \"top-left\", ctx_r0.position === \"topright\" || ctx_r0.position === \"top-right\", ctx_r0.position === \"bottom\", ctx_r0.position === \"bottomleft\" || ctx_r0.position === \"bottom-left\", ctx_r0.position === \"bottomright\" || ctx_r0.position === \"bottom-right\"]));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.visible);\n  }\n}\nconst _c9 = [\"*\", [[\"p-header\"]], [[\"p-footer\"]]];\nconst _c10 = [\"*\", \"p-header\", \"p-footer\"];\nconst showAnimation = animation([style({\n  transform: '{{transform}}',\n  opacity: 0\n}), animate('{{transition}}')]);\nconst hideAnimation = animation([animate('{{transition}}', style({\n  transform: '{{transform}}',\n  opacity: 0\n}))]);\n/**\n * Dialog is a container to display content in an overlay window.\n * @group Components\n */\nlet Dialog = /*#__PURE__*/(() => {\n  class Dialog {\n    document;\n    platformId;\n    el;\n    renderer;\n    zone;\n    cd;\n    config;\n    /**\n     * Title text of the dialog.\n     * @group Props\n     */\n    header;\n    /**\n     * Enables dragging to change the position using header.\n     * @group Props\n     */\n    draggable = true;\n    /**\n     * Enables resizing of the content.\n     * @group Props\n     */\n    resizable = true;\n    /**\n     * Defines the left offset of dialog.\n     * @group Props\n     * @deprecated positionLeft property is deprecated.\n     */\n    get positionLeft() {\n      return 0;\n    }\n    set positionLeft(_positionLeft) {\n      console.log('positionLeft property is deprecated.');\n    }\n    /**\n     * Defines the top offset of dialog.\n     * @group Props\n     * @deprecated positionTop property is deprecated.\n     */\n    get positionTop() {\n      return 0;\n    }\n    set positionTop(_positionTop) {\n      console.log('positionTop property is deprecated.');\n    }\n    /**\n     * Style of the content section.\n     * @group Props\n     */\n    contentStyle;\n    /**\n     * Style class of the content.\n     * @group Props\n     */\n    contentStyleClass;\n    /**\n     * Defines if background should be blocked when dialog is displayed.\n     * @group Props\n     */\n    modal = false;\n    /**\n     * Specifies if pressing escape key should hide the dialog.\n     * @group Props\n     */\n    closeOnEscape = true;\n    /**\n     * Specifies if clicking the modal background should hide the dialog.\n     * @group Props\n     */\n    dismissableMask = false;\n    /**\n     * When enabled dialog is displayed in RTL direction.\n     * @group Props\n     */\n    rtl = false;\n    /**\n     * Adds a close icon to the header to hide the dialog.\n     * @group Props\n     */\n    closable = true;\n    /**\n     * Defines if the component is responsive.\n     * @group Props\n     * @deprecated Responsive property is deprecated.\n     */\n    get responsive() {\n      return false;\n    }\n    set responsive(_responsive) {\n      console.log('Responsive property is deprecated.');\n    }\n    /**\n     * Target element to attach the dialog, valid values are \"body\" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n     * @group Props\n     */\n    appendTo;\n    /**\n     * Object literal to define widths per screen size.\n     * @group Props\n     */\n    breakpoints;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Style class of the mask.\n     * @group Props\n     */\n    maskStyleClass;\n    /**\n     * Whether to show the header or not.\n     * @group Props\n     */\n    showHeader = true;\n    /**\n     * Defines the breakpoint of the component responsive.\n     * @group Props\n     * @deprecated Breakpoint property is not utilized and deprecated. Use breakpoints or CSS media queries instead.\n     */\n    get breakpoint() {\n      return 649;\n    }\n    set breakpoint(_breakpoint) {\n      console.log('Breakpoint property is not utilized and deprecated, use breakpoints or CSS media queries instead.');\n    }\n    /**\n     * Whether background scroll should be blocked when dialog is visible.\n     * @group Props\n     */\n    blockScroll = false;\n    /**\n     * Whether to automatically manage layering.\n     * @group Props\n     */\n    autoZIndex = true;\n    /**\n     * Base zIndex value to use in layering.\n     * @group Props\n     */\n    baseZIndex = 0;\n    /**\n     * Minimum value for the left coordinate of dialog in dragging.\n     * @group Props\n     */\n    minX = 0;\n    /**\n     * Minimum value for the top coordinate of dialog in dragging.\n     * @group Props\n     */\n    minY = 0;\n    /**\n     * When enabled, first button receives focus on show.\n     * @group Props\n     */\n    focusOnShow = true;\n    /**\n     * Whether the dialog can be displayed full screen.\n     * @group Props\n     */\n    maximizable = false;\n    /**\n     * Keeps dialog in the viewport.\n     * @group Props\n     */\n    keepInViewport = true;\n    /**\n     * When enabled, can only focus on elements inside the dialog.\n     * @group Props\n     */\n    focusTrap = true;\n    /**\n     * Transition options of the animation.\n     * @group Props\n     */\n    transitionOptions = '150ms cubic-bezier(0, 0, 0.2, 1)';\n    /**\n     * Name of the close icon.\n     * @group Props\n     */\n    closeIcon;\n    /**\n     * Defines a string that labels the close button for accessibility.\n     * @group Props\n     */\n    closeAriaLabel;\n    /**\n     * Index of the close button in tabbing order.\n     * @group Props\n     */\n    closeTabindex = '-1';\n    /**\n     * Name of the minimize icon.\n     * @group Props\n     */\n    minimizeIcon;\n    /**\n     * Name of the maximize icon.\n     * @group Props\n     */\n    maximizeIcon;\n    /**\n     * Specifies the visibility of the dialog.\n     * @group Props\n     */\n    get visible() {\n      return this._visible;\n    }\n    set visible(value) {\n      this._visible = value;\n      if (this._visible && !this.maskVisible) {\n        this.maskVisible = true;\n      }\n    }\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    get style() {\n      return this._style;\n    }\n    set style(value) {\n      if (value) {\n        this._style = {\n          ...value\n        };\n        this.originalStyle = value;\n      }\n    }\n    /**\n     * Position of the dialog.\n     * @group Props\n     */\n    get position() {\n      return this._position;\n    }\n    set position(value) {\n      this._position = value;\n      switch (value) {\n        case 'topleft':\n        case 'bottomleft':\n        case 'left':\n          this.transformOptions = 'translate3d(-100%, 0px, 0px)';\n          break;\n        case 'topright':\n        case 'bottomright':\n        case 'right':\n          this.transformOptions = 'translate3d(100%, 0px, 0px)';\n          break;\n        case 'bottom':\n          this.transformOptions = 'translate3d(0px, 100%, 0px)';\n          break;\n        case 'top':\n          this.transformOptions = 'translate3d(0px, -100%, 0px)';\n          break;\n        default:\n          this.transformOptions = 'scale(0.7)';\n          break;\n      }\n    }\n    /**\n     * Callback to invoke when dialog is shown.\n     * @group Emits\n     */\n    onShow = new EventEmitter();\n    /**\n     * Callback to invoke when dialog is hidden.\n     * @group Emits\n     */\n    onHide = new EventEmitter();\n    /**\n     * This EventEmitter is used to notify changes in the visibility state of a component.\n     * @param {boolean} value - New value.\n     * @group Emits\n     */\n    visibleChange = new EventEmitter();\n    /**\n     * Callback to invoke when dialog resizing is initiated.\n     * @param {MouseEvent} event - Mouse event.\n     * @group Emits\n     */\n    onResizeInit = new EventEmitter();\n    /**\n     * Callback to invoke when dialog resizing is completed.\n     * @param {MouseEvent} event - Mouse event.\n     * @group Emits\n     */\n    onResizeEnd = new EventEmitter();\n    /**\n     * Callback to invoke when dialog dragging is completed.\n     * @param {DragEvent} event - Drag event.\n     * @group Emits\n     */\n    onDragEnd = new EventEmitter();\n    /**\n     * Callback to invoke when dialog maximized or unmaximized.\n     * @group Emits\n     */\n    onMaximize = new EventEmitter();\n    headerFacet;\n    footerFacet;\n    templates;\n    headerViewChild;\n    contentViewChild;\n    footerViewChild;\n    headerTemplate;\n    contentTemplate;\n    footerTemplate;\n    maximizeIconTemplate;\n    closeIconTemplate;\n    minimizeIconTemplate;\n    _visible = false;\n    maskVisible;\n    container;\n    wrapper;\n    dragging;\n    ariaLabelledBy;\n    documentDragListener;\n    documentDragEndListener;\n    resizing;\n    documentResizeListener;\n    documentResizeEndListener;\n    documentEscapeListener;\n    maskClickListener;\n    lastPageX;\n    lastPageY;\n    preventVisibleChangePropagation;\n    maximized;\n    preMaximizeContentHeight;\n    preMaximizeContainerWidth;\n    preMaximizeContainerHeight;\n    preMaximizePageX;\n    preMaximizePageY;\n    id = UniqueComponentId();\n    _style = {};\n    _position = 'center';\n    originalStyle;\n    transformOptions = 'scale(0.7)';\n    styleElement;\n    window;\n    constructor(document, platformId, el, renderer, zone, cd, config) {\n      this.document = document;\n      this.platformId = platformId;\n      this.el = el;\n      this.renderer = renderer;\n      this.zone = zone;\n      this.cd = cd;\n      this.config = config;\n      this.window = this.document.defaultView;\n    }\n    ngAfterContentInit() {\n      this.templates?.forEach(item => {\n        switch (item.getType()) {\n          case 'header':\n            this.headerTemplate = item.template;\n            break;\n          case 'content':\n            this.contentTemplate = item.template;\n            break;\n          case 'footer':\n            this.footerTemplate = item.template;\n            break;\n          case 'closeicon':\n            this.closeIconTemplate = item.template;\n            break;\n          case 'maximizeicon':\n            this.maximizeIconTemplate = item.template;\n            break;\n          case 'minimizeicon':\n            this.minimizeIconTemplate = item.template;\n            break;\n          default:\n            this.contentTemplate = item.template;\n            break;\n        }\n      });\n    }\n    ngOnInit() {\n      if (this.breakpoints) {\n        this.createStyle();\n      }\n    }\n    getAriaLabelledBy() {\n      return this.header !== null ? UniqueComponentId() + '_header' : null;\n    }\n    focus() {\n      let focusable = DomHandler.findSingle(this.container, '[autofocus]');\n      if (focusable) {\n        this.zone.runOutsideAngular(() => {\n          setTimeout(() => focusable.focus(), 5);\n        });\n      }\n    }\n    close(event) {\n      this.visibleChange.emit(false);\n      event.preventDefault();\n    }\n    enableModality() {\n      if (this.closable && this.dismissableMask) {\n        this.maskClickListener = this.renderer.listen(this.wrapper, 'mousedown', event => {\n          if (this.wrapper && this.wrapper.isSameNode(event.target)) {\n            this.close(event);\n          }\n        });\n      }\n      if (this.modal) {\n        DomHandler.blockBodyScroll();\n      }\n    }\n    disableModality() {\n      if (this.wrapper) {\n        if (this.dismissableMask) {\n          this.unbindMaskClickListener();\n        }\n        if (this.modal) {\n          DomHandler.unblockBodyScroll();\n        }\n        if (!this.cd.destroyed) {\n          this.cd.detectChanges();\n        }\n      }\n    }\n    maximize() {\n      this.maximized = !this.maximized;\n      if (!this.modal && !this.blockScroll) {\n        if (this.maximized) {\n          DomHandler.blockBodyScroll();\n        } else {\n          DomHandler.unblockBodyScroll();\n        }\n      }\n      this.onMaximize.emit({\n        maximized: this.maximized\n      });\n    }\n    unbindMaskClickListener() {\n      if (this.maskClickListener) {\n        this.maskClickListener();\n        this.maskClickListener = null;\n      }\n    }\n    moveOnTop() {\n      if (this.autoZIndex) {\n        ZIndexUtils.set('modal', this.container, this.baseZIndex + this.config.zIndex.modal);\n        this.wrapper.style.zIndex = String(parseInt(this.container.style.zIndex, 10) - 1);\n      }\n    }\n    createStyle() {\n      if (isPlatformBrowser(this.platformId)) {\n        if (!this.styleElement) {\n          this.styleElement = this.renderer.createElement('style');\n          this.styleElement.type = 'text/css';\n          this.renderer.appendChild(this.document.head, this.styleElement);\n          let innerHTML = '';\n          for (let breakpoint in this.breakpoints) {\n            innerHTML += `\n                        @media screen and (max-width: ${breakpoint}) {\n                            .p-dialog[${this.id}]:not(.p-dialog-maximized) {\n                                width: ${this.breakpoints[breakpoint]} !important;\n                            }\n                        }\n                    `;\n          }\n          this.renderer.setProperty(this.styleElement, 'innerHTML', innerHTML);\n        }\n      }\n    }\n    initDrag(event) {\n      if (DomHandler.hasClass(event.target, 'p-dialog-header-icon') || DomHandler.hasClass(event.target.parentElement, 'p-dialog-header-icon')) {\n        return;\n      }\n      if (this.draggable) {\n        this.dragging = true;\n        this.lastPageX = event.pageX;\n        this.lastPageY = event.pageY;\n        this.container.style.margin = '0';\n        DomHandler.addClass(this.document.body, 'p-unselectable-text');\n      }\n    }\n    onKeydown(event) {\n      if (this.focusTrap) {\n        if (event.which === 9) {\n          event.preventDefault();\n          let focusableElements = DomHandler.getFocusableElements(this.container);\n          if (focusableElements && focusableElements.length > 0) {\n            if (!focusableElements[0].ownerDocument.activeElement) {\n              focusableElements[0].focus();\n            } else {\n              let focusedIndex = focusableElements.indexOf(focusableElements[0].ownerDocument.activeElement);\n              if (event.shiftKey) {\n                if (focusedIndex == -1 || focusedIndex === 0) focusableElements[focusableElements.length - 1].focus();else focusableElements[focusedIndex - 1].focus();\n              } else {\n                if (focusedIndex == -1 || focusedIndex === focusableElements.length - 1) focusableElements[0].focus();else focusableElements[focusedIndex + 1].focus();\n              }\n            }\n          }\n        }\n      }\n    }\n    onDrag(event) {\n      if (this.dragging) {\n        const containerWidth = DomHandler.getOuterWidth(this.container);\n        const containerHeight = DomHandler.getOuterHeight(this.container);\n        const deltaX = event.pageX - this.lastPageX;\n        const deltaY = event.pageY - this.lastPageY;\n        const offset = this.container.getBoundingClientRect();\n        const containerComputedStyle = getComputedStyle(this.container);\n        const leftMargin = parseFloat(containerComputedStyle.marginLeft);\n        const topMargin = parseFloat(containerComputedStyle.marginTop);\n        const leftPos = offset.left + deltaX - leftMargin;\n        const topPos = offset.top + deltaY - topMargin;\n        const viewport = DomHandler.getViewport();\n        this.container.style.position = 'fixed';\n        if (this.keepInViewport) {\n          if (leftPos >= this.minX && leftPos + containerWidth < viewport.width) {\n            this._style.left = `${leftPos}px`;\n            this.lastPageX = event.pageX;\n            this.container.style.left = `${leftPos}px`;\n          }\n          if (topPos >= this.minY && topPos + containerHeight < viewport.height) {\n            this._style.top = `${topPos}px`;\n            this.lastPageY = event.pageY;\n            this.container.style.top = `${topPos}px`;\n          }\n        } else {\n          this.lastPageX = event.pageX;\n          this.container.style.left = `${leftPos}px`;\n          this.lastPageY = event.pageY;\n          this.container.style.top = `${topPos}px`;\n        }\n      }\n    }\n    endDrag(event) {\n      if (this.dragging) {\n        this.dragging = false;\n        DomHandler.removeClass(this.document.body, 'p-unselectable-text');\n        this.cd.detectChanges();\n        this.onDragEnd.emit(event);\n      }\n    }\n    resetPosition() {\n      this.container.style.position = '';\n      this.container.style.left = '';\n      this.container.style.top = '';\n      this.container.style.margin = '';\n    }\n    //backward compatibility\n    center() {\n      this.resetPosition();\n    }\n    initResize(event) {\n      if (this.resizable) {\n        this.resizing = true;\n        this.lastPageX = event.pageX;\n        this.lastPageY = event.pageY;\n        DomHandler.addClass(this.document.body, 'p-unselectable-text');\n        this.onResizeInit.emit(event);\n      }\n    }\n    onResize(event) {\n      if (this.resizing) {\n        let deltaX = event.pageX - this.lastPageX;\n        let deltaY = event.pageY - this.lastPageY;\n        let containerWidth = DomHandler.getOuterWidth(this.container);\n        let containerHeight = DomHandler.getOuterHeight(this.container);\n        let contentHeight = DomHandler.getOuterHeight(this.contentViewChild?.nativeElement);\n        let newWidth = containerWidth + deltaX;\n        let newHeight = containerHeight + deltaY;\n        let minWidth = this.container.style.minWidth;\n        let minHeight = this.container.style.minHeight;\n        let offset = this.container.getBoundingClientRect();\n        let viewport = DomHandler.getViewport();\n        let hasBeenDragged = !parseInt(this.container.style.top) || !parseInt(this.container.style.left);\n        if (hasBeenDragged) {\n          newWidth += deltaX;\n          newHeight += deltaY;\n        }\n        if ((!minWidth || newWidth > parseInt(minWidth)) && offset.left + newWidth < viewport.width) {\n          this._style.width = newWidth + 'px';\n          this.container.style.width = this._style.width;\n        }\n        if ((!minHeight || newHeight > parseInt(minHeight)) && offset.top + newHeight < viewport.height) {\n          this.contentViewChild.nativeElement.style.height = contentHeight + newHeight - containerHeight + 'px';\n          if (this._style.height) {\n            this._style.height = newHeight + 'px';\n            this.container.style.height = this._style.height;\n          }\n        }\n        this.lastPageX = event.pageX;\n        this.lastPageY = event.pageY;\n      }\n    }\n    resizeEnd(event) {\n      if (this.resizing) {\n        this.resizing = false;\n        DomHandler.removeClass(this.document.body, 'p-unselectable-text');\n        this.onResizeEnd.emit(event);\n      }\n    }\n    bindGlobalListeners() {\n      if (this.draggable) {\n        this.bindDocumentDragListener();\n        this.bindDocumentDragEndListener();\n      }\n      if (this.resizable) {\n        this.bindDocumentResizeListeners();\n      }\n      if (this.closeOnEscape && this.closable) {\n        this.bindDocumentEscapeListener();\n      }\n    }\n    unbindGlobalListeners() {\n      this.unbindDocumentDragListener();\n      this.unbindDocumentDragEndListener();\n      this.unbindDocumentResizeListeners();\n      this.unbindDocumentEscapeListener();\n    }\n    bindDocumentDragListener() {\n      if (!this.documentDragListener) {\n        this.zone.runOutsideAngular(() => {\n          this.documentDragListener = this.renderer.listen(this.window, 'mousemove', this.onDrag.bind(this));\n        });\n      }\n    }\n    unbindDocumentDragListener() {\n      if (this.documentDragListener) {\n        this.documentDragListener();\n        this.documentDragListener = null;\n      }\n    }\n    bindDocumentDragEndListener() {\n      if (!this.documentDragEndListener) {\n        this.zone.runOutsideAngular(() => {\n          this.documentDragEndListener = this.renderer.listen(this.window, 'mouseup', this.endDrag.bind(this));\n        });\n      }\n    }\n    unbindDocumentDragEndListener() {\n      if (this.documentDragEndListener) {\n        this.documentDragEndListener();\n        this.documentDragEndListener = null;\n      }\n    }\n    bindDocumentResizeListeners() {\n      if (!this.documentResizeListener && !this.documentResizeEndListener) {\n        this.zone.runOutsideAngular(() => {\n          this.documentResizeListener = this.renderer.listen(this.window, 'mousemove', this.onResize.bind(this));\n          this.documentResizeEndListener = this.renderer.listen(this.window, 'mouseup', this.resizeEnd.bind(this));\n        });\n      }\n    }\n    unbindDocumentResizeListeners() {\n      if (this.documentResizeListener && this.documentResizeEndListener) {\n        this.documentResizeListener();\n        this.documentResizeEndListener();\n        this.documentResizeListener = null;\n        this.documentResizeEndListener = null;\n      }\n    }\n    bindDocumentEscapeListener() {\n      const documentTarget = this.el ? this.el.nativeElement.ownerDocument : 'document';\n      this.documentEscapeListener = this.renderer.listen(documentTarget, 'keydown', event => {\n        if (event.which == 27) {\n          this.close(event);\n        }\n      });\n    }\n    unbindDocumentEscapeListener() {\n      if (this.documentEscapeListener) {\n        this.documentEscapeListener();\n        this.documentEscapeListener = null;\n      }\n    }\n    appendContainer() {\n      if (this.appendTo) {\n        if (this.appendTo === 'body') this.renderer.appendChild(this.document.body, this.wrapper);else DomHandler.appendChild(this.wrapper, this.appendTo);\n      }\n    }\n    restoreAppend() {\n      if (this.container && this.appendTo) {\n        this.renderer.appendChild(this.el.nativeElement, this.wrapper);\n      }\n    }\n    onAnimationStart(event) {\n      switch (event.toState) {\n        case 'visible':\n          this.container = event.element;\n          this.wrapper = this.container?.parentElement;\n          this.appendContainer();\n          this.moveOnTop();\n          this.bindGlobalListeners();\n          this.container?.setAttribute(this.id, '');\n          if (this.modal) {\n            this.enableModality();\n          }\n          if (!this.modal && this.blockScroll) {\n            DomHandler.addClass(this.document.body, 'p-overflow-hidden');\n          }\n          if (this.focusOnShow) {\n            this.focus();\n          }\n          break;\n        case 'void':\n          if (this.wrapper && this.modal) {\n            DomHandler.addClass(this.wrapper, 'p-component-overlay-leave');\n          }\n          break;\n      }\n    }\n    onAnimationEnd(event) {\n      switch (event.toState) {\n        case 'void':\n          this.onContainerDestroy();\n          this.onHide.emit({});\n          this.cd.markForCheck();\n          break;\n        case 'visible':\n          this.onShow.emit({});\n          break;\n      }\n    }\n    onContainerDestroy() {\n      this.unbindGlobalListeners();\n      this.dragging = false;\n      this.maskVisible = false;\n      if (this.maximized) {\n        DomHandler.removeClass(this.document.body, 'p-overflow-hidden');\n        this.document.body.style.removeProperty('--scrollbar-width');\n        this.maximized = false;\n      }\n      if (this.modal) {\n        this.disableModality();\n      }\n      if (this.blockScroll) {\n        DomHandler.removeClass(this.document.body, 'p-overflow-hidden');\n      }\n      if (this.container && this.autoZIndex) {\n        ZIndexUtils.clear(this.container);\n      }\n      this.container = null;\n      this.wrapper = null;\n      this._style = this.originalStyle ? {\n        ...this.originalStyle\n      } : {};\n    }\n    destroyStyle() {\n      if (this.styleElement) {\n        this.renderer.removeChild(this.document.head, this.styleElement);\n        this.styleElement = null;\n      }\n    }\n    ngOnDestroy() {\n      if (this.container) {\n        this.restoreAppend();\n        this.onContainerDestroy();\n      }\n      this.destroyStyle();\n    }\n    static ɵfac = function Dialog_Factory(t) {\n      return new (t || Dialog)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.PrimeNGConfig));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: Dialog,\n      selectors: [[\"p-dialog\"]],\n      contentQueries: function Dialog_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, Header, 5);\n          i0.ɵɵcontentQuery(dirIndex, Footer, 5);\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerFacet = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.footerFacet = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      viewQuery: function Dialog_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.contentViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.footerViewChild = _t.first);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        header: \"header\",\n        draggable: \"draggable\",\n        resizable: \"resizable\",\n        positionLeft: \"positionLeft\",\n        positionTop: \"positionTop\",\n        contentStyle: \"contentStyle\",\n        contentStyleClass: \"contentStyleClass\",\n        modal: \"modal\",\n        closeOnEscape: \"closeOnEscape\",\n        dismissableMask: \"dismissableMask\",\n        rtl: \"rtl\",\n        closable: \"closable\",\n        responsive: \"responsive\",\n        appendTo: \"appendTo\",\n        breakpoints: \"breakpoints\",\n        styleClass: \"styleClass\",\n        maskStyleClass: \"maskStyleClass\",\n        showHeader: \"showHeader\",\n        breakpoint: \"breakpoint\",\n        blockScroll: \"blockScroll\",\n        autoZIndex: \"autoZIndex\",\n        baseZIndex: \"baseZIndex\",\n        minX: \"minX\",\n        minY: \"minY\",\n        focusOnShow: \"focusOnShow\",\n        maximizable: \"maximizable\",\n        keepInViewport: \"keepInViewport\",\n        focusTrap: \"focusTrap\",\n        transitionOptions: \"transitionOptions\",\n        closeIcon: \"closeIcon\",\n        closeAriaLabel: \"closeAriaLabel\",\n        closeTabindex: \"closeTabindex\",\n        minimizeIcon: \"minimizeIcon\",\n        maximizeIcon: \"maximizeIcon\",\n        visible: \"visible\",\n        style: \"style\",\n        position: \"position\"\n      },\n      outputs: {\n        onShow: \"onShow\",\n        onHide: \"onHide\",\n        visibleChange: \"visibleChange\",\n        onResizeInit: \"onResizeInit\",\n        onResizeEnd: \"onResizeEnd\",\n        onDragEnd: \"onDragEnd\",\n        onMaximize: \"onMaximize\"\n      },\n      ngContentSelectors: _c10,\n      decls: 1,\n      vars: 1,\n      consts: [[3, \"class\", \"ngClass\", 4, \"ngIf\"], [3, \"ngClass\"], [\"pFocusTrap\", \"\", \"role\", \"dialog\", 3, \"ngClass\", \"ngStyle\", \"class\", \"pFocusTrapDisabled\", 4, \"ngIf\"], [\"pFocusTrap\", \"\", \"role\", \"dialog\", 3, \"ngClass\", \"ngStyle\", \"pFocusTrapDisabled\"], [\"container\", \"\"], [\"class\", \"p-resizable-handle\", \"style\", \"z-index: 90;\", 3, \"mousedown\", 4, \"ngIf\"], [\"class\", \"p-dialog-header\", 3, \"mousedown\", 4, \"ngIf\"], [3, \"ngClass\", \"ngStyle\"], [\"content\", \"\"], [4, \"ngTemplateOutlet\"], [\"class\", \"p-dialog-footer\", 4, \"ngIf\"], [1, \"p-resizable-handle\", 2, \"z-index\", \"90\", 3, \"mousedown\"], [1, \"p-dialog-header\", 3, \"mousedown\"], [\"titlebar\", \"\"], [\"class\", \"p-dialog-title\", 3, \"id\", 4, \"ngIf\"], [1, \"p-dialog-header-icons\"], [\"role\", \"button\", \"type\", \"button\", \"tabindex\", \"-1\", \"pRipple\", \"\", 3, \"ngClass\", \"click\", \"keydown.enter\", 4, \"ngIf\"], [\"type\", \"button\", \"pRipple\", \"\", 3, \"ngClass\", \"click\", \"keydown.enter\", 4, \"ngIf\"], [1, \"p-dialog-title\", 3, \"id\"], [\"role\", \"button\", \"type\", \"button\", \"tabindex\", \"-1\", \"pRipple\", \"\", 3, \"ngClass\", \"click\", \"keydown.enter\"], [\"class\", \"p-dialog-header-maximize-icon\", 3, \"ngClass\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"p-dialog-header-maximize-icon\", 3, \"ngClass\"], [3, \"styleClass\", 4, \"ngIf\"], [3, \"styleClass\"], [\"type\", \"button\", \"pRipple\", \"\", 3, \"ngClass\", \"click\", \"keydown.enter\"], [\"class\", \"p-dialog-header-close-icon\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"p-dialog-header-close-icon\", 3, \"ngClass\"], [1, \"p-dialog-footer\"], [\"footer\", \"\"]],\n      template: function Dialog_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c9);\n          i0.ɵɵtemplate(0, Dialog_div_0_Template, 2, 15, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.maskVisible);\n        }\n      },\n      dependencies: function () {\n        return [i2.NgClass, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i3.FocusTrap, i4.Ripple, TimesIcon, WindowMaximizeIcon, WindowMinimizeIcon];\n      },\n      styles: [\"@layer primeng{.p-dialog-mask{position:fixed;top:0;left:0;width:100%;height:100%;display:flex;justify-content:center;align-items:center;pointer-events:none}.p-dialog-mask.p-component-overlay{pointer-events:auto}.p-dialog{display:flex;flex-direction:column;pointer-events:auto;max-height:90%;transform:scale(1);position:relative}.p-dialog-content{overflow-y:auto;flex-grow:1}.p-dialog-header{display:flex;align-items:center;justify-content:space-between;flex-shrink:0}.p-dialog-draggable .p-dialog-header{cursor:move}.p-dialog-footer{flex-shrink:0}.p-dialog .p-dialog-header-icons{display:flex;align-items:center}.p-dialog .p-dialog-header-icon{display:flex;align-items:center;justify-content:center;overflow:hidden;position:relative}.p-fluid .p-dialog-footer .p-button{width:auto}.p-dialog-top .p-dialog,.p-dialog-bottom .p-dialog,.p-dialog-left .p-dialog,.p-dialog-right .p-dialog,.p-dialog-top-left .p-dialog,.p-dialog-top-right .p-dialog,.p-dialog-bottom-left .p-dialog,.p-dialog-bottom-right .p-dialog{margin:.75rem;transform:translateZ(0)}.p-dialog-maximized{transition:none;transform:none;width:100vw!important;height:100vh!important;top:0!important;left:0!important;max-height:100%;height:100%}.p-dialog-maximized .p-dialog-content{flex-grow:1}.p-dialog-left{justify-content:flex-start}.p-dialog-right{justify-content:flex-end}.p-dialog-top{align-items:flex-start}.p-dialog-top-left{justify-content:flex-start;align-items:flex-start}.p-dialog-top-right{justify-content:flex-end;align-items:flex-start}.p-dialog-bottom{align-items:flex-end}.p-dialog-bottom-left{justify-content:flex-start;align-items:flex-end}.p-dialog-bottom-right{justify-content:flex-end;align-items:flex-end}.p-dialog .p-resizable-handle{position:absolute;font-size:.1px;display:block;cursor:se-resize;width:12px;height:12px;right:1px;bottom:1px}.p-confirm-dialog .p-dialog-content{display:flex;align-items:center}}\\n\"],\n      encapsulation: 2,\n      data: {\n        animation: [trigger('animation', [transition('void => visible', [useAnimation(showAnimation)]), transition('visible => void', [useAnimation(hideAnimation)])])]\n      },\n      changeDetection: 0\n    });\n  }\n  return Dialog;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet DialogModule = /*#__PURE__*/(() => {\n  class DialogModule {\n    static ɵfac = function DialogModule_Factory(t) {\n      return new (t || DialogModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: DialogModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, FocusTrapModule, RippleModule, TimesIcon, WindowMaximizeIcon, WindowMinimizeIcon, SharedModule]\n    });\n  }\n  return DialogModule;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Dialog, DialogModule };\n//# sourceMappingURL=primeng-dialog.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}