{"ast": null, "code": "import { catchError, of } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class CallServerService {\n  constructor(httpClient) {\n    this.httpClient = httpClient;\n  }\n  Get(url) {\n    try {\n      return this.httpClient.get(url).pipe(catchError(error => of([])));\n    } catch (ex) {\n      throw ex?.error?.Description;\n    }\n  }\n  static {\n    this.ɵfac = function CallServerService_Factory(t) {\n      return new (t || CallServerService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CallServerService,\n      factory: CallServerService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["catchError", "of", "CallServerService", "constructor", "httpClient", "Get", "url", "get", "pipe", "error", "ex", "Description", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\تطبيق ناشر ايات قئانية\\QuranVidGen\\src\\app\\Services\\call-server.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Observable, catchError, of } from 'rxjs';\r\nimport {HttpClient} from '@angular/common/http'\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class CallServerService {\r\n\r\n  constructor(private httpClient:HttpClient) { }\r\n  Get(url:string):Observable<any>\r\n  {\r\n    try{\r\n    return this.httpClient.get<any>(url).pipe(catchError(error => of([])));\r\n\r\n    }catch(ex:any){\r\n      throw (ex?.error?.Description);\r\n    }\r\n  }\r\n}\r\n"], "mappings": "AACA,SAAqBA,UAAU,EAAEC,EAAE,QAAQ,MAAM;;;AAMjD,OAAM,MAAOC,iBAAiB;EAE5BC,YAAoBC,UAAqB;IAArB,KAAAA,UAAU,GAAVA,UAAU;EAAe;EAC7CC,GAAGA,CAACC,GAAU;IAEZ,IAAG;MACH,OAAO,IAAI,CAACF,UAAU,CAACG,GAAG,CAAMD,GAAG,CAAC,CAACE,IAAI,CAACR,UAAU,CAACS,KAAK,IAAIR,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;KAErE,QAAMS,EAAM,EAAC;MACZ,MAAOA,EAAE,EAAED,KAAK,EAAEE,WAAW;;EAEjC;;;uBAXWT,iBAAiB,EAAAU,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAjBb,iBAAiB;MAAAc,OAAA,EAAjBd,iBAAiB,CAAAe,IAAA;MAAAC,UAAA,EAFhB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}