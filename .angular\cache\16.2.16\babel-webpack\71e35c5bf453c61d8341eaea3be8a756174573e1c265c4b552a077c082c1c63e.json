{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { GeneratorComponent } from './GeneratorPage/generator/generator.component';\nimport { AdminComponent } from './admin/admin.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: GeneratorComponent\n}, {\n  path: 'admin',\n  component: AdminComponent\n}];\nexport let AppRoutingModule = /*#__PURE__*/(() => {\n  class AppRoutingModule {\n    static {\n      this.ɵfac = function AppRoutingModule_Factory(t) {\n        return new (t || AppRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: AppRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forRoot(routes), RouterModule]\n      });\n    }\n  }\n  return AppRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}