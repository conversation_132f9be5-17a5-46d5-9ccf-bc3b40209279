import { Injectable } from '@angular/core';
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { Observable, from, BehaviorSubject } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

// Database interfaces
export interface ShortenerLink {
  id?: number;
  site_name: string;
  site_url: string;
  link_template: string;
  is_active: boolean;
  description?: string;
  created_at?: string;
  updated_at?: string;
}

export interface UserSession {
  id?: number;
  user_id: string;
  session_type: 'link_shortened' | 'ad_watched';
  expires_at: string;
  created_at?: string;
  metadata?: any;
}

export interface SecurityLog {
  id?: number;
  user_id: string;
  event_type: 'vpn_detected' | 'adblocker_detected' | 'dns_blocked' | 'access_denied';
  details?: any;
  ip_address?: string;
  user_agent?: string;
  created_at?: string;
}

export interface AppSettings {
  id?: number;
  setting_key: string;
  setting_value: any;
  description?: string;
  updated_at?: string;
}

@Injectable({
  providedIn: 'root'
})
export class SupabaseService {
  private supabase: SupabaseClient;
  private isConnected = new BehaviorSubject<boolean>(false);
  public connectionStatus$ = this.isConnected.asObservable();

  constructor() {
    // Initialize with placeholder values - these should be set from environment or config
    const supabaseUrl = 'YOUR_SUPABASE_URL';
    const supabaseKey = 'YOUR_SUPABASE_ANON_KEY';
    
    this.supabase = createClient(supabaseUrl, supabaseKey);
    this.checkConnection();
  }

  // Initialize Supabase with actual credentials
  public initializeSupabase(url: string, key: string): void {
    this.supabase = createClient(url, key);
    this.checkConnection();
  }

  private async checkConnection(): Promise<void> {
    try {
      const { data, error } = await this.supabase.from('app_settings').select('count').limit(1);
      this.isConnected.next(!error);
    } catch (error) {
      this.isConnected.next(false);
    }
  }

  // Shortener Links Management
  public getShortenerLinks(): Observable<ShortenerLink[]> {
    return from(
      this.supabase
        .from('shortener_links')
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: false })
    ).pipe(
      map(response => {
        if (response.error) throw response.error;
        return response.data || [];
      }),
      catchError(error => {
        console.error('Error fetching shortener links:', error);
        return [];
      })
    );
  }

  public addShortenerLink(link: Omit<ShortenerLink, 'id' | 'created_at' | 'updated_at'>): Observable<ShortenerLink> {
    return from(
      this.supabase
        .from('shortener_links')
        .insert([link])
        .select()
        .single()
    ).pipe(
      map(response => {
        if (response.error) throw response.error;
        return response.data;
      })
    );
  }

  public updateShortenerLink(id: number, updates: Partial<ShortenerLink>): Observable<ShortenerLink> {
    return from(
      this.supabase
        .from('shortener_links')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', id)
        .select()
        .single()
    ).pipe(
      map(response => {
        if (response.error) throw response.error;
        return response.data;
      })
    );
  }

  public deleteShortenerLink(id: number): Observable<boolean> {
    return from(
      this.supabase
        .from('shortener_links')
        .delete()
        .eq('id', id)
    ).pipe(
      map(response => {
        if (response.error) throw response.error;
        return true;
      })
    );
  }

  // User Sessions Management
  public createUserSession(session: Omit<UserSession, 'id' | 'created_at'>): Observable<UserSession> {
    return from(
      this.supabase
        .from('user_sessions')
        .insert([session])
        .select()
        .single()
    ).pipe(
      map(response => {
        if (response.error) throw response.error;
        return response.data;
      })
    );
  }

  public getUserActiveSessions(userId: string): Observable<UserSession[]> {
    const now = new Date().toISOString();
    
    return from(
      this.supabase
        .from('user_sessions')
        .select('*')
        .eq('user_id', userId)
        .gt('expires_at', now)
        .order('created_at', { ascending: false })
    ).pipe(
      map(response => {
        if (response.error) throw response.error;
        return response.data || [];
      })
    );
  }

  public cleanupExpiredSessions(): Observable<boolean> {
    const now = new Date().toISOString();
    
    return from(
      this.supabase
        .from('user_sessions')
        .delete()
        .lt('expires_at', now)
    ).pipe(
      map(response => {
        if (response.error) throw response.error;
        return true;
      })
    );
  }

  // Security Logs
  public logSecurityEvent(log: Omit<SecurityLog, 'id' | 'created_at'>): Observable<SecurityLog> {
    return from(
      this.supabase
        .from('security_logs')
        .insert([log])
        .select()
        .single()
    ).pipe(
      map(response => {
        if (response.error) throw response.error;
        return response.data;
      })
    );
  }

  public getSecurityLogs(userId?: string, limit: number = 100): Observable<SecurityLog[]> {
    let query = this.supabase
      .from('security_logs')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(limit);

    if (userId) {
      query = query.eq('user_id', userId);
    }

    return from(query).pipe(
      map(response => {
        if (response.error) throw response.error;
        return response.data || [];
      })
    );
  }

  // App Settings Management
  public getAppSetting(key: string): Observable<any> {
    return from(
      this.supabase
        .from('app_settings')
        .select('setting_value')
        .eq('setting_key', key)
        .single()
    ).pipe(
      map(response => {
        if (response.error) throw response.error;
        return response.data?.setting_value;
      })
    );
  }

  public setAppSetting(key: string, value: any, description?: string): Observable<AppSettings> {
    const setting: Omit<AppSettings, 'id'> = {
      setting_key: key,
      setting_value: value,
      description,
      updated_at: new Date().toISOString()
    };

    return from(
      this.supabase
        .from('app_settings')
        .upsert([setting], { onConflict: 'setting_key' })
        .select()
        .single()
    ).pipe(
      map(response => {
        if (response.error) throw response.error;
        return response.data;
      })
    );
  }

  public getAllAppSettings(): Observable<AppSettings[]> {
    return from(
      this.supabase
        .from('app_settings')
        .select('*')
        .order('setting_key')
    ).pipe(
      map(response => {
        if (response.error) throw response.error;
        return response.data || [];
      })
    );
  }

  // Analytics and Statistics
  public getUserStats(userId: string): Observable<any> {
    return from(
      this.supabase
        .rpc('get_user_stats', { user_id: userId })
    ).pipe(
      map(response => {
        if (response.error) throw response.error;
        return response.data;
      })
    );
  }

  public getAppStats(): Observable<any> {
    return from(
      this.supabase
        .rpc('get_app_stats')
    ).pipe(
      map(response => {
        if (response.error) throw response.error;
        return response.data;
      })
    );
  }

  // Utility methods
  public isConnectedToSupabase(): boolean {
    return this.isConnected.value;
  }

  public async testConnection(): Promise<boolean> {
    try {
      const { data, error } = await this.supabase.from('app_settings').select('count').limit(1);
      const connected = !error;
      this.isConnected.next(connected);
      return connected;
    } catch (error) {
      this.isConnected.next(false);
      return false;
    }
  }

  // Generate unique user ID for anonymous users
  public generateUserId(): string {
    const stored = localStorage.getItem('quran_app_user_id');
    if (stored) {
      return stored;
    }

    const newId = 'user_' + Date.now() + '_' + Math.random().toString(36).substring(2, 15);
    localStorage.setItem('quran_app_user_id', newId);
    return newId;
  }

  public getCurrentUserId(): string {
    return this.generateUserId();
  }
}
