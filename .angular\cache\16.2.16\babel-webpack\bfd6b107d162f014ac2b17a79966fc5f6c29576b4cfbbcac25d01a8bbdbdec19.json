{"ast": null, "code": "import { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, ContentChild, ContentChildren, NgModule } from '@angular/core';\nimport { Footer, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { MinusIcon } from 'primeng/icons/minus';\nimport { PlusIcon } from 'primeng/icons/plus';\nimport * as i2 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport { UniqueComponentId } from 'primeng/utils';\n\n/**\n * Panel is a container with the optional content toggle feature.\n * @group Components\n */\nfunction Panel_div_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"id\", ctx_r3.id + \"_header\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r3.header);\n  }\n}\nfunction Panel_div_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Panel_div_1_5_ng_template_0_Template(rf, ctx) {}\nfunction Panel_div_1_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Panel_div_1_5_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Panel_div_1_button_6_ng_container_1_ng_container_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 16);\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(5);\n    i0.ɵɵclassMap(ctx_r12.expandIcon);\n    i0.ɵɵproperty(\"ngClass\", ctx_r12.iconClass);\n  }\n}\nfunction Panel_div_1_button_6_ng_container_1_ng_container_1_MinusIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"MinusIcon\", 17);\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(5);\n    i0.ɵɵproperty(\"styleClass\", ctx_r13.iconClass);\n  }\n}\nfunction Panel_div_1_button_6_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Panel_div_1_button_6_ng_container_1_ng_container_1_span_1_Template, 1, 3, \"span\", 14);\n    i0.ɵɵtemplate(2, Panel_div_1_button_6_ng_container_1_ng_container_1_MinusIcon_2_Template, 1, 1, \"MinusIcon\", 15);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.expandIcon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.expandIcon);\n  }\n}\nfunction Panel_div_1_button_6_ng_container_1_ng_container_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 16);\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(5);\n    i0.ɵɵclassMap(ctx_r14.collapseIcon);\n    i0.ɵɵproperty(\"ngClass\", ctx_r14.iconClass);\n  }\n}\nfunction Panel_div_1_button_6_ng_container_1_ng_container_2_PlusIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"PlusIcon\", 17);\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(5);\n    i0.ɵɵproperty(\"styleClass\", ctx_r15.iconClass);\n  }\n}\nfunction Panel_div_1_button_6_ng_container_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Panel_div_1_button_6_ng_container_1_ng_container_2_span_1_Template, 1, 3, \"span\", 14);\n    i0.ɵɵtemplate(2, Panel_div_1_button_6_ng_container_1_ng_container_2_PlusIcon_2_Template, 1, 1, \"PlusIcon\", 15);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.collapseIcon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r11.collapseIcon);\n  }\n}\nfunction Panel_div_1_button_6_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Panel_div_1_button_6_ng_container_1_ng_container_1_Template, 3, 2, \"ng-container\", 12);\n    i0.ɵɵtemplate(2, Panel_div_1_button_6_ng_container_1_ng_container_2_Template, 3, 2, \"ng-container\", 12);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.collapsed);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.collapsed);\n  }\n}\nfunction Panel_div_1_button_6_2_ng_template_0_Template(rf, ctx) {}\nfunction Panel_div_1_button_6_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Panel_div_1_button_6_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    $implicit: a0\n  };\n};\nfunction Panel_div_1_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function Panel_div_1_button_6_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r17.onIconClick($event));\n    })(\"keydown\", function Panel_div_1_button_6_Template_button_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r19 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r19.onKeyDown($event));\n    });\n    i0.ɵɵtemplate(1, Panel_div_1_button_6_ng_container_1_Template, 3, 2, \"ng-container\", 12);\n    i0.ɵɵtemplate(2, Panel_div_1_button_6_2_Template, 1, 0, null, 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"id\", ctx_r6.id + \"_header\")(\"aria-label\", ctx_r6.buttonAriaLabel)(\"aria-controls\", ctx_r6.id + \"_content\")(\"aria-expanded\", !ctx_r6.collapsed);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.headerIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r6.headerIconTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(7, _c0, ctx_r6.collapsed));\n  }\n}\nconst _c1 = function (a0, a1, a2) {\n  return {\n    \"p-panel-icons-start\": a0,\n    \"p-panel-icons-end\": a1,\n    \"p-panel-icons-center\": a2\n  };\n};\nfunction Panel_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵlistener(\"click\", function Panel_div_1_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.onHeaderClick($event));\n    });\n    i0.ɵɵtemplate(1, Panel_div_1_span_1_Template, 2, 2, \"span\", 7);\n    i0.ɵɵprojection(2, 1);\n    i0.ɵɵtemplate(3, Panel_div_1_ng_container_3_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵelementStart(4, \"div\", 8);\n    i0.ɵɵtemplate(5, Panel_div_1_5_Template, 1, 0, null, 4);\n    i0.ɵɵtemplate(6, Panel_div_1_button_6_Template, 3, 9, \"button\", 9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"id\", ctx_r0.id + \"-titlebar\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.header);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.headerTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(6, _c1, ctx_r0.iconPos === \"start\", ctx_r0.iconPos === \"end\", ctx_r0.iconPos === \"center\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.iconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.toggleable);\n  }\n}\nfunction Panel_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Panel_div_6_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Panel_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵprojection(1, 2);\n    i0.ɵɵtemplate(2, Panel_div_6_ng_container_2_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.footerTemplate);\n  }\n}\nconst _c2 = [\"*\", [[\"p-header\"]], [[\"p-footer\"]]];\nconst _c3 = function (a1, a2) {\n  return {\n    \"p-panel p-component\": true,\n    \"p-panel-toggleable\": a1,\n    \"p-panel-expanded\": a2\n  };\n};\nconst _c4 = function (a0) {\n  return {\n    transitionParams: a0,\n    height: \"0\",\n    opacity: \"0\"\n  };\n};\nconst _c5 = function (a1) {\n  return {\n    value: \"hidden\",\n    params: a1\n  };\n};\nconst _c6 = function (a0) {\n  return {\n    transitionParams: a0,\n    height: \"*\",\n    opacity: \"1\"\n  };\n};\nconst _c7 = function (a1) {\n  return {\n    value: \"visible\",\n    params: a1\n  };\n};\nconst _c8 = [\"*\", \"p-header\", \"p-footer\"];\nlet Panel = /*#__PURE__*/(() => {\n  class Panel {\n    el;\n    /**\n     * Defines if content of panel can be expanded and collapsed.\n     * @group Props\n     */\n    toggleable;\n    /**\n     * Header text of the panel.\n     * @group Props\n     */\n    header;\n    /**\n     * Defines the initial state of panel content, supports one or two-way binding as well.\n     * @group Props\n     */\n    collapsed;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    style;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Position of the icons.\n     * @group Props\n     */\n    iconPos = 'end';\n    /**\n     * Expand icon of the toggle button.\n     * @group Props\n     * @deprecated since v15.4.2, use `headericons` template instead.\n     */\n    expandIcon;\n    /**\n     * Collapse icon of the toggle button.\n     * @group Props\n     * @deprecated since v15.4.2, use `headericons` template instead.\n     */\n    collapseIcon;\n    /**\n     * Specifies if header of panel cannot be displayed.\n     * @group Props\n     * @deprecated since v15.4.2, use `headericons` template instead.\n     */\n    showHeader = true;\n    /**\n     * Specifies the toggler element to toggle the panel content.\n     * @group Props\n     */\n    toggler = 'icon';\n    /**\n     * Transition options of the animation.\n     * @group Props\n     */\n    transitionOptions = '400ms cubic-bezier(0.86, 0, 0.07, 1)';\n    /**\n     * Emitted when the collapsed changes.\n     * @param {boolean} value - New Value.\n     * @group Emits\n     */\n    collapsedChange = new EventEmitter();\n    /**\n     * Callback to invoke before panel toggle.\n     * @param {PanelBeforeToggleEvent} event - Custom panel toggle event\n     * @group Emits\n     */\n    onBeforeToggle = new EventEmitter();\n    /**\n     * Callback to invoke after panel toggle.\n     * @param {PanelAfterToggleEvent} event - Custom panel toggle event\n     * @group Emits\n     */\n    onAfterToggle = new EventEmitter();\n    footerFacet;\n    templates;\n    iconTemplate;\n    animating;\n    headerTemplate;\n    contentTemplate;\n    footerTemplate;\n    headerIconTemplate;\n    get id() {\n      return UniqueComponentId();\n    }\n    get buttonAriaLabel() {\n      return this.header;\n    }\n    constructor(el) {\n      this.el = el;\n    }\n    ngAfterContentInit() {\n      this.templates.forEach(item => {\n        switch (item.getType()) {\n          case 'header':\n            this.headerTemplate = item.template;\n            break;\n          case 'content':\n            this.contentTemplate = item.template;\n            break;\n          case 'footer':\n            this.footerTemplate = item.template;\n            break;\n          case 'icons':\n            this.iconTemplate = item.template;\n            break;\n          case 'headericons':\n            this.headerIconTemplate = item.template;\n            break;\n          default:\n            this.contentTemplate = item.template;\n            break;\n        }\n      });\n    }\n    onHeaderClick(event) {\n      if (this.toggler === 'header') {\n        this.toggle(event);\n      }\n    }\n    onIconClick(event) {\n      if (this.toggler === 'icon') {\n        this.toggle(event);\n      }\n    }\n    toggle(event) {\n      if (this.animating) {\n        return false;\n      }\n      this.animating = true;\n      this.onBeforeToggle.emit({\n        originalEvent: event,\n        collapsed: this.collapsed\n      });\n      if (this.toggleable) {\n        if (this.collapsed) this.expand();else this.collapse();\n      }\n      event.preventDefault();\n    }\n    expand() {\n      this.collapsed = false;\n      this.collapsedChange.emit(this.collapsed);\n    }\n    collapse() {\n      this.collapsed = true;\n      this.collapsedChange.emit(this.collapsed);\n    }\n    getBlockableElement() {\n      return this.el.nativeElement.children[0];\n    }\n    onKeyDown(event) {\n      if (event.code === 'Enter' || event.code === 'Space') {\n        this.toggle(event);\n        event.preventDefault();\n      }\n    }\n    onToggleDone(event) {\n      this.animating = false;\n      this.onAfterToggle.emit({\n        originalEvent: event,\n        collapsed: this.collapsed\n      });\n    }\n    static ɵfac = function Panel_Factory(t) {\n      return new (t || Panel)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: Panel,\n      selectors: [[\"p-panel\"]],\n      contentQueries: function Panel_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, Footer, 5);\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.footerFacet = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        toggleable: \"toggleable\",\n        header: \"header\",\n        collapsed: \"collapsed\",\n        style: \"style\",\n        styleClass: \"styleClass\",\n        iconPos: \"iconPos\",\n        expandIcon: \"expandIcon\",\n        collapseIcon: \"collapseIcon\",\n        showHeader: \"showHeader\",\n        toggler: \"toggler\",\n        transitionOptions: \"transitionOptions\"\n      },\n      outputs: {\n        collapsedChange: \"collapsedChange\",\n        onBeforeToggle: \"onBeforeToggle\",\n        onAfterToggle: \"onAfterToggle\"\n      },\n      ngContentSelectors: _c8,\n      decls: 7,\n      vars: 25,\n      consts: [[3, \"ngClass\", \"ngStyle\"], [\"class\", \"p-panel-header\", 3, \"click\", 4, \"ngIf\"], [\"role\", \"region\", 1, \"p-toggleable-content\", 3, \"id\"], [1, \"p-panel-content\"], [4, \"ngTemplateOutlet\"], [\"class\", \"p-panel-footer\", 4, \"ngIf\"], [1, \"p-panel-header\", 3, \"click\"], [\"class\", \"p-panel-title\", 4, \"ngIf\"], [1, \"p-panel-icons\", 3, \"ngClass\"], [\"pRipple\", \"\", \"type\", \"button\", \"role\", \"button\", \"class\", \"p-panel-header-icon p-panel-toggler p-link\", 3, \"click\", \"keydown\", 4, \"ngIf\"], [1, \"p-panel-title\"], [\"pRipple\", \"\", \"type\", \"button\", \"role\", \"button\", 1, \"p-panel-header-icon\", \"p-panel-toggler\", \"p-link\", 3, \"click\", \"keydown\"], [4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [3, \"class\", \"ngClass\", 4, \"ngIf\"], [3, \"styleClass\", 4, \"ngIf\"], [3, \"ngClass\"], [3, \"styleClass\"], [1, \"p-panel-footer\"]],\n      template: function Panel_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c2);\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, Panel_div_1_Template, 7, 10, \"div\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2);\n          i0.ɵɵlistener(\"@panelContent.done\", function Panel_Template_div_animation_panelContent_done_2_listener($event) {\n            return ctx.onToggleDone($event);\n          });\n          i0.ɵɵelementStart(3, \"div\", 3);\n          i0.ɵɵprojection(4);\n          i0.ɵɵtemplate(5, Panel_ng_container_5_Template, 1, 0, \"ng-container\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(6, Panel_div_6_Template, 3, 1, \"div\", 5);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.styleClass);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(14, _c3, ctx.toggleable, !ctx.collapsed && ctx.toggleable))(\"ngStyle\", ctx.style);\n          i0.ɵɵattribute(\"id\", ctx.id)(\"data-pc-name\", \"panel\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showHeader);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"id\", ctx.id + \"_content\")(\"@panelContent\", ctx.collapsed ? i0.ɵɵpureFunction1(19, _c5, i0.ɵɵpureFunction1(17, _c4, ctx.animating ? ctx.transitionOptions : \"0ms\")) : i0.ɵɵpureFunction1(23, _c7, i0.ɵɵpureFunction1(21, _c6, ctx.animating ? ctx.transitionOptions : \"0ms\")));\n          i0.ɵɵattribute(\"aria-labelledby\", ctx.id + \"_header\")(\"aria-hidden\", ctx.collapsed)(\"tabindex\", ctx.collapsed ? \"-1\" : undefined);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.contentTemplate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.footerFacet || ctx.footerTemplate);\n        }\n      },\n      dependencies: function () {\n        return [i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, i2.Ripple, PlusIcon, MinusIcon];\n      },\n      styles: [\"@layer primeng{.p-panel-header{display:flex;align-items:center}.p-panel-title{line-height:1;order:1}.p-panel-header-icon{display:inline-flex;justify-content:center;align-items:center;cursor:pointer;text-decoration:none;overflow:hidden;position:relative}.p-panel-toggleable.p-panel-expanded>.p-toggleable-content:not(.ng-animating){overflow:visible}.p-panel-toggleable .p-toggleable-content{overflow:hidden}}\\n\"],\n      encapsulation: 2,\n      data: {\n        animation: [trigger('panelContent', [state('hidden', style({\n          height: '0'\n        })), state('void', style({\n          height: '{{height}}'\n        }), {\n          params: {\n            height: '0'\n          }\n        }), state('visible', style({\n          height: '*'\n        })), transition('visible <=> hidden', [animate('{{transitionParams}}')]), transition('void => hidden', animate('{{transitionParams}}')), transition('void => visible', animate('{{transitionParams}}'))])]\n      },\n      changeDetection: 0\n    });\n  }\n  return Panel;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet PanelModule = /*#__PURE__*/(() => {\n  class PanelModule {\n    static ɵfac = function PanelModule_Factory(t) {\n      return new (t || PanelModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: PanelModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, SharedModule, RippleModule, PlusIcon, MinusIcon, SharedModule]\n    });\n  }\n  return PanelModule;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Panel, PanelModule };\n//# sourceMappingURL=primeng-panel.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}