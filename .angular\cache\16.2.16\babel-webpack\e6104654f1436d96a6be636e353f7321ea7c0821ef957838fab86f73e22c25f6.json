{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, Injectable, EventEmitter, Component, ChangeDetectionStrategy, Input, Output, ViewChild, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR, NgControl } from '@angular/forms';\nconst _c0 = [\"input\"];\nconst _c1 = function (a1, a2, a3) {\n  return {\n    \"p-radiobutton-label\": true,\n    \"p-radiobutton-label-active\": a1,\n    \"p-disabled\": a2,\n    \"p-radiobutton-label-focus\": a3\n  };\n};\nfunction RadioButton_label_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 7);\n    i0.ɵɵlistener(\"click\", function RadioButton_label_6_Template_label_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.select($event));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    const _r0 = i0.ɵɵreference(3);\n    i0.ɵɵclassMap(ctx_r1.labelStyleClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(6, _c1, _r0.checked, ctx_r1.disabled, ctx_r1.focused));\n    i0.ɵɵattribute(\"for\", ctx_r1.inputId)(\"data-pc-section\", \"label\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r1.label);\n  }\n}\nconst _c2 = function (a1, a2, a3) {\n  return {\n    \"p-radiobutton p-component\": true,\n    \"p-radiobutton-checked\": a1,\n    \"p-radiobutton-disabled\": a2,\n    \"p-radiobutton-focused\": a3\n  };\n};\nconst _c3 = function (a1, a2, a3) {\n  return {\n    \"p-radiobutton-box\": true,\n    \"p-highlight\": a1,\n    \"p-disabled\": a2,\n    \"p-focus\": a3\n  };\n};\nconst RADIO_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => RadioButton),\n  multi: true\n};\nlet RadioControlRegistry = /*#__PURE__*/(() => {\n  class RadioControlRegistry {\n    accessors = [];\n    add(control, accessor) {\n      this.accessors.push([control, accessor]);\n    }\n    remove(accessor) {\n      this.accessors = this.accessors.filter(c => {\n        return c[1] !== accessor;\n      });\n    }\n    select(accessor) {\n      this.accessors.forEach(c => {\n        if (this.isSameGroup(c, accessor) && c[1] !== accessor) {\n          c[1].writeValue(accessor.value);\n        }\n      });\n    }\n    isSameGroup(controlPair, accessor) {\n      if (!controlPair[0].control) {\n        return false;\n      }\n      return controlPair[0].control.root === accessor.control.control.root && controlPair[1].name === accessor.name;\n    }\n    static ɵfac = function RadioControlRegistry_Factory(t) {\n      return new (t || RadioControlRegistry)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: RadioControlRegistry,\n      factory: RadioControlRegistry.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return RadioControlRegistry;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * RadioButton is an extension to standard radio button element with theming.\n * @group Components\n */\nlet RadioButton = /*#__PURE__*/(() => {\n  class RadioButton {\n    cd;\n    injector;\n    registry;\n    /**\n     * Value of the radiobutton.\n     * @group Props\n     */\n    value;\n    /**\n     * The name of the form control.\n     * @group Props\n     */\n    formControlName;\n    /**\n     * Name of the radiobutton group.\n     * @group Props\n     */\n    name;\n    /**\n     * When present, it specifies that the element should be disabled.\n     * @group Props\n     */\n    disabled;\n    /**\n     * Label of the radiobutton.\n     * @group Props\n     */\n    label;\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    tabindex;\n    /**\n     * Identifier of the focus input to match a label defined for the component.\n     * @group Props\n     */\n    inputId;\n    /**\n     * Establishes relationships between the component and label(s) where its value should be one or more element IDs.\n     * @group Props\n     */\n    ariaLabelledBy;\n    /**\n     * Used to define a string that labels the input element.\n     * @group Props\n     */\n    ariaLabel;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    style;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Style class of the label.\n     * @group Props\n     */\n    labelStyleClass;\n    /**\n     * Callback to invoke on radio button click.\n     * @param {RadioButtonClickEvent} event - Custom click event.\n     * @group Emits\n     */\n    onClick = new EventEmitter();\n    /**\n     * Callback to invoke when the receives focus.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    onFocus = new EventEmitter();\n    /**\n     * Callback to invoke when the loses focus.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    onBlur = new EventEmitter();\n    inputViewChild;\n    onModelChange = () => {};\n    onModelTouched = () => {};\n    checked;\n    focused;\n    control;\n    constructor(cd, injector, registry) {\n      this.cd = cd;\n      this.injector = injector;\n      this.registry = registry;\n    }\n    ngOnInit() {\n      this.control = this.injector.get(NgControl);\n      this.checkName();\n      this.registry.add(this.control, this);\n    }\n    handleClick(event, radioButton, focus) {\n      event.preventDefault();\n      if (this.disabled) {\n        return;\n      }\n      this.select(event);\n      if (focus) {\n        radioButton.focus();\n      }\n    }\n    select(event) {\n      if (!this.disabled) {\n        this.inputViewChild.nativeElement.checked = true;\n        this.checked = true;\n        this.onModelChange(this.value);\n        this.registry.select(this);\n        this.onClick.emit({\n          originalEvent: event,\n          value: this.value\n        });\n      }\n    }\n    writeValue(value) {\n      this.checked = value == this.value;\n      if (this.inputViewChild && this.inputViewChild.nativeElement) {\n        this.inputViewChild.nativeElement.checked = this.checked;\n      }\n      this.cd.markForCheck();\n    }\n    registerOnChange(fn) {\n      this.onModelChange = fn;\n    }\n    registerOnTouched(fn) {\n      this.onModelTouched = fn;\n    }\n    setDisabledState(val) {\n      this.disabled = val;\n      this.cd.markForCheck();\n    }\n    onInputFocus(event) {\n      this.focused = true;\n      this.onFocus.emit(event);\n    }\n    onInputBlur(event) {\n      this.focused = false;\n      this.onModelTouched();\n      this.onBlur.emit(event);\n    }\n    /**\n     * Applies focus to input field.\n     * @group Method\n     */\n    focus() {\n      this.inputViewChild.nativeElement.focus();\n    }\n    ngOnDestroy() {\n      this.registry.remove(this);\n    }\n    checkName() {\n      if (this.name && this.formControlName && this.name !== this.formControlName) {\n        this.throwNameError();\n      }\n      if (!this.name && this.formControlName) {\n        this.name = this.formControlName;\n      }\n    }\n    throwNameError() {\n      throw new Error(`\n          If you define both a name and a formControlName attribute on your radio button, their values\n          must match. Ex: <p-radioButton formControlName=\"food\" name=\"food\"></p-radioButton>\n        `);\n    }\n    static ɵfac = function RadioButton_Factory(t) {\n      return new (t || RadioButton)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(RadioControlRegistry));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: RadioButton,\n      selectors: [[\"p-radioButton\"]],\n      viewQuery: function RadioButton_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.inputViewChild = _t.first);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        value: \"value\",\n        formControlName: \"formControlName\",\n        name: \"name\",\n        disabled: \"disabled\",\n        label: \"label\",\n        tabindex: \"tabindex\",\n        inputId: \"inputId\",\n        ariaLabelledBy: \"ariaLabelledBy\",\n        ariaLabel: \"ariaLabel\",\n        style: \"style\",\n        styleClass: \"styleClass\",\n        labelStyleClass: \"labelStyleClass\"\n      },\n      outputs: {\n        onClick: \"onClick\",\n        onFocus: \"onFocus\",\n        onBlur: \"onBlur\"\n      },\n      features: [i0.ɵɵProvidersFeature([RADIO_VALUE_ACCESSOR])],\n      decls: 7,\n      vars: 29,\n      consts: [[3, \"ngStyle\", \"ngClass\", \"click\"], [1, \"p-hidden-accessible\"], [\"type\", \"radio\", 3, \"checked\", \"disabled\", \"value\", \"focus\", \"blur\"], [\"input\", \"\"], [3, \"ngClass\"], [1, \"p-radiobutton-icon\"], [3, \"class\", \"ngClass\", \"click\", 4, \"ngIf\"], [3, \"ngClass\", \"click\"]],\n      template: function RadioButton_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r4 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵlistener(\"click\", function RadioButton_Template_div_click_0_listener($event) {\n            i0.ɵɵrestoreView(_r4);\n            const _r0 = i0.ɵɵreference(3);\n            return i0.ɵɵresetView(ctx.handleClick($event, _r0, true));\n          });\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"input\", 2, 3);\n          i0.ɵɵlistener(\"focus\", function RadioButton_Template_input_focus_2_listener($event) {\n            return ctx.onInputFocus($event);\n          })(\"blur\", function RadioButton_Template_input_blur_2_listener($event) {\n            return ctx.onInputBlur($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"div\", 4);\n          i0.ɵɵelement(5, \"span\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(6, RadioButton_label_6_Template, 2, 10, \"label\", 6);\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.styleClass);\n          i0.ɵɵproperty(\"ngStyle\", ctx.style)(\"ngClass\", i0.ɵɵpureFunction3(21, _c2, ctx.checked, ctx.disabled, ctx.focused));\n          i0.ɵɵattribute(\"data-pc-name\", \"radiobutton\")(\"data-pc-section\", \"root\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵattribute(\"data-pc-section\", \"hiddenInputWrapper\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"checked\", ctx.checked)(\"disabled\", ctx.disabled)(\"value\", ctx.value);\n          i0.ɵɵattribute(\"id\", ctx.inputId)(\"name\", ctx.name)(\"aria-labelledby\", ctx.ariaLabelledBy)(\"aria-label\", ctx.ariaLabel)(\"tabindex\", ctx.tabindex)(\"aria-checked\", ctx.checked)(\"data-pc-section\", \"hiddenInput\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(25, _c3, ctx.checked, ctx.disabled, ctx.focused));\n          i0.ɵɵattribute(\"data-pc-section\", \"input\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵattribute(\"data-pc-section\", \"icon\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.label);\n        }\n      },\n      dependencies: [i1.NgClass, i1.NgIf, i1.NgStyle],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return RadioButton;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet RadioButtonModule = /*#__PURE__*/(() => {\n  class RadioButtonModule {\n    static ɵfac = function RadioButtonModule_Factory(t) {\n      return new (t || RadioButtonModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: RadioButtonModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n  return RadioButtonModule;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { RADIO_VALUE_ACCESSOR, RadioButton, RadioButtonModule, RadioControlRegistry };\n//# sourceMappingURL=primeng-radiobutton.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}