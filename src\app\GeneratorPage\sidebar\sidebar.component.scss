::ng-deep .custom-sidebar {
  width: 320px !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  
  .p-sidebar-header {
    background: rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 1.5rem;
  }
  
  .p-sidebar-content {
    padding: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
}

.sidebar-header {
  text-align: center;
  
  .sidebar-title {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: white;
  }
}

.sidebar-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 1rem 0;
}

.menu-items {
  flex: 1;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(-5px);
  }
  
  .menu-icon {
    font-size: 1.2rem;
    margin-left: 1rem;
    color: #ffd700;
  }
  
  .menu-label {
    flex: 1;
    font-size: 1.1rem;
    font-weight: 500;
  }
  
  .menu-arrow {
    font-size: 0.9rem;
    opacity: 0.7;
  }
}

.sidebar-footer {
  padding: 1.5rem;
  text-align: center;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  
  .app-info {
    margin: 0 0 0.5rem 0;
    font-size: 0.9rem;
    opacity: 0.8;
    
    i {
      margin-left: 0.5rem;
    }
  }
  
  .version {
    margin: 0;
    font-size: 0.8rem;
    opacity: 0.6;
  }
}
