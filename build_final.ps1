Write-Host "========================================" -ForegroundColor Green
Write-Host "    بناء APK - مولد مقاطع القرآن الكريم" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# تعيين متغيرات البيئة
$env:GRADLE_HOME = "C:\gradle\gradle-7.6.4"
$env:PATH = "$env:GRADLE_HOME\bin;$env:PATH"
$env:JAVA_HOME = "C:\Program Files\Microsoft\jdk-*********-hotspot"

Write-Host "تعيين متغيرات البيئة..." -ForegroundColor Yellow
Write-Host "GRADLE_HOME: $env:GRADLE_HOME" -ForegroundColor Cyan
Write-Host "JAVA_HOME: $env:JAVA_HOME" -ForegroundColor Cyan

# التحقق من المتطلبات
Write-Host ""
Write-Host "التحقق من المتطلبات..." -ForegroundColor Yellow

# التحقق من Java
$javaCheck = java -version 2>&1
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Java متوفر" -ForegroundColor Green
} else {
    Write-Host "❌ Java غير متوفر" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج..."
    exit 1
}

# التحقق من Node.js
$nodeCheck = node --version 2>&1
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Node.js متوفر" -ForegroundColor Green
} else {
    Write-Host "❌ Node.js غير متوفر" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج..."
    exit 1
}

# بناء مشروع Angular
Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "بناء مشروع Angular..." -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

npm run build
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ تم بناء Angular بنجاح" -ForegroundColor Green
} else {
    Write-Host "❌ خطأ في بناء Angular" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج..."
    exit 1
}

# التأكد من وجود index.html
if (-not (Test-Path "dist\quran-vid-gen\index.html")) {
    Write-Host "نسخ ملف index.html..." -ForegroundColor Yellow
    Copy-Item "src\index.html" "dist\quran-vid-gen\index.html"
}

# بناء APK باستخدام Capacitor
Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "بناء APK باستخدام Capacitor..." -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

npx cap run android --no-open
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ تم بناء APK بنجاح" -ForegroundColor Green
} else {
    Write-Host "❌ خطأ في بناء APK" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج..."
    exit 1
}

# البحث عن ملف APK ونسخه
Write-Host ""
Write-Host "البحث عن ملف APK..." -ForegroundColor Yellow

$apkPaths = @(
    "android\app\build\outputs\apk\debug\app-debug.apk",
    "android\app\build\outputs\apk\release\app-release.apk"
)

$apkFound = $false
$apkPath = ""

foreach ($path in $apkPaths) {
    if (Test-Path $path) {
        $apkPath = $path
        $apkFound = $true
        break
    }
}

if ($apkFound) {
    Write-Host "✅ تم العثور على APK: $apkPath" -ForegroundColor Green
    
    # معلومات الملف
    $fileInfo = Get-Item $apkPath
    $fileSize = [math]::Round($fileInfo.Length / 1MB, 2)
    Write-Host "حجم الملف: $fileSize MB" -ForegroundColor Cyan
    
    # إنشاء اسم ملف مع التاريخ والوقت
    $timestamp = Get-Date -Format "yyyy-MM-dd_HH-mm"
    $desktopPath = "$env:USERPROFILE\Desktop\QuranVidGen_$timestamp.apk"
    
    try {
        Copy-Item $apkPath $desktopPath -Force
        Write-Host "✅ تم نسخ APK إلى سطح المكتب" -ForegroundColor Green
        
        Write-Host ""
        Write-Host "========================================" -ForegroundColor Green
        Write-Host "تم بناء APK بنجاح!" -ForegroundColor Green
        Write-Host "========================================" -ForegroundColor Green
        Write-Host "اسم الملف: QuranVidGen_$timestamp.apk" -ForegroundColor White
        Write-Host "حجم الملف: $fileSize MB" -ForegroundColor White
        Write-Host "مسار الملف: $desktopPath" -ForegroundColor White
        
    } catch {
        Write-Host "❌ خطأ في نسخ الملف: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "مسار APK الأصلي: $apkPath" -ForegroundColor Yellow
    }
    
} else {
    Write-Host "❌ لم يتم العثور على ملف APK" -ForegroundColor Red
}

Write-Host ""
Write-Host "انتهت العملية!" -ForegroundColor Green
Read-Host "اضغط Enter للخروج..."
