{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Directive, Inject, Input, EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Output, ContentChildren, NgModule } from '@angular/core';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { <PERSON><PERSON>andler } from 'primeng/dom';\nimport { SpinnerIcon } from 'primeng/icons/spinner';\nimport * as i2 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport { ObjectUtils } from 'primeng/utils';\nfunction Button_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Button_ng_container_3_ng_container_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassMap(\"p-button-loading-icon pi-spin \" + ctx_r7.loadingIcon);\n    i0.ɵɵproperty(\"ngClass\", ctx_r7.iconClass());\n    i0.ɵɵattribute(\"aria-hidden\", true)(\"data-pc-section\", \"loadingicon\");\n  }\n}\nfunction Button_ng_container_3_ng_container_1_SpinnerIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"SpinnerIcon\", 9);\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"styleClass\", ctx_r8.spinnerIconClass())(\"spin\", true);\n    i0.ɵɵattribute(\"aria-hidden\", true)(\"data-pc-section\", \"loadingicon\");\n  }\n}\nfunction Button_ng_container_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Button_ng_container_3_ng_container_1_span_1_Template, 1, 5, \"span\", 6);\n    i0.ɵɵtemplate(2, Button_ng_container_3_ng_container_1_SpinnerIcon_2_Template, 1, 4, \"SpinnerIcon\", 7);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.loadingIcon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.loadingIcon);\n  }\n}\nfunction Button_ng_container_3_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction Button_ng_container_3_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Button_ng_container_3_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Button_ng_container_3_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 10);\n    i0.ɵɵtemplate(1, Button_ng_container_3_span_2_1_Template, 1, 0, null, 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r6.iconClass());\n    i0.ɵɵattribute(\"aria-hidden\", true)(\"data-pc-section\", \"loadingicon\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r6.loadingIconTemplate);\n  }\n}\nfunction Button_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Button_ng_container_3_ng_container_1_Template, 3, 2, \"ng-container\", 2);\n    i0.ɵɵtemplate(2, Button_ng_container_3_span_2_Template, 2, 4, \"span\", 5);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loadingIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loadingIconTemplate);\n  }\n}\nfunction Button_ng_container_4_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r11.icon);\n    i0.ɵɵproperty(\"ngClass\", ctx_r11.iconClass());\n    i0.ɵɵattribute(\"data-pc-section\", \"icon\");\n  }\n}\nfunction Button_ng_container_4_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction Button_ng_container_4_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Button_ng_container_4_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\", 12);\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r13.icon);\n  }\n}\nfunction Button_ng_container_4_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 8);\n    i0.ɵɵtemplate(1, Button_ng_container_4_span_2_1_Template, 1, 1, null, 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r12.iconClass());\n    i0.ɵɵattribute(\"data-pc-section\", \"icon\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r12.iconTemplate);\n  }\n}\nfunction Button_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Button_ng_container_4_span_1_Template, 1, 4, \"span\", 6);\n    i0.ɵɵtemplate(2, Button_ng_container_4_span_2_Template, 2, 3, \"span\", 11);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.icon && !ctx_r2.iconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.icon && ctx_r2.iconTemplate);\n  }\n}\nfunction Button_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"aria-hidden\", ctx_r3.icon && !ctx_r3.label)(\"data-pc-section\", \"label\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r3.label);\n  }\n}\nfunction Button_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r4.badgeClass);\n    i0.ɵɵproperty(\"ngClass\", ctx_r4.badgeStyleClass());\n    i0.ɵɵattribute(\"data-pc-section\", \"badge\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r4.badge);\n  }\n}\nconst _c0 = [\"*\"];\nconst INTERNAL_BUTTON_CLASSES = {\n  button: 'p-button',\n  component: 'p-component',\n  iconOnly: 'p-button-icon-only',\n  disabled: 'p-disabled',\n  loading: 'p-button-loading',\n  labelOnly: 'p-button-loading-label-only'\n};\n/**\n * Button directive is an extension to button component.\n * @group Components\n */\nlet ButtonDirective = /*#__PURE__*/(() => {\n  class ButtonDirective {\n    el;\n    document;\n    /**\n     * Position of the icon.\n     * @group Props\n     */\n    iconPos = 'left';\n    /**\n     * Uses to pass attributes to the loading icon's DOM element.\n     * @group Props\n     */\n    loadingIcon;\n    /**\n     * Text of the button.\n     * @group Props\n     */\n    get label() {\n      return this._label;\n    }\n    set label(val) {\n      this._label = val;\n      if (this.initialized) {\n        this.updateLabel();\n        this.updateIcon();\n        this.setStyleClass();\n      }\n    }\n    /**\n     * Name of the icon.\n     * @group Props\n     */\n    get icon() {\n      return this._icon;\n    }\n    set icon(val) {\n      this._icon = val;\n      if (this.initialized) {\n        this.updateIcon();\n        this.setStyleClass();\n      }\n    }\n    /**\n     * Whether the button is in loading state.\n     * @group Props\n     */\n    get loading() {\n      return this._loading;\n    }\n    set loading(val) {\n      this._loading = val;\n      if (this.initialized) {\n        this.updateIcon();\n        this.setStyleClass();\n      }\n    }\n    _label;\n    _icon;\n    _loading = false;\n    initialized;\n    get htmlElement() {\n      return this.el.nativeElement;\n    }\n    _internalClasses = Object.values(INTERNAL_BUTTON_CLASSES);\n    spinnerIcon = `<svg width=\"14\" height=\"14\" viewBox=\"0 0 14 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" class=\"p-icon-spin\">\n        <g clip-path=\"url(#clip0_417_21408)\">\n            <path\n                d=\"M6.99701 14C5.85441 13.999 4.72939 13.7186 3.72012 13.1832C2.71084 12.6478 1.84795 11.8737 1.20673 10.9284C0.565504 9.98305 0.165424 8.89526 0.041387 7.75989C-0.0826496 6.62453 0.073125 5.47607 0.495122 4.4147C0.917119 3.35333 1.59252 2.4113 2.46241 1.67077C3.33229 0.930247 4.37024 0.413729 5.4857 0.166275C6.60117 -0.0811796 7.76026 -0.0520535 8.86188 0.251112C9.9635 0.554278 10.9742 1.12227 11.8057 1.90555C11.915 2.01493 11.9764 2.16319 11.9764 2.31778C11.9764 2.47236 11.915 2.62062 11.8057 2.73C11.7521 2.78503 11.688 2.82877 11.6171 2.85864C11.5463 2.8885 11.4702 2.90389 11.3933 2.90389C11.3165 2.90389 11.2404 2.8885 11.1695 2.85864C11.0987 2.82877 11.0346 2.78503 10.9809 2.73C9.9998 1.81273 8.73246 1.26138 7.39226 1.16876C6.05206 1.07615 4.72086 1.44794 3.62279 2.22152C2.52471 2.99511 1.72683 4.12325 1.36345 5.41602C1.00008 6.70879 1.09342 8.08723 1.62775 9.31926C2.16209 10.5513 3.10478 11.5617 4.29713 12.1803C5.48947 12.7989 6.85865 12.988 8.17414 12.7157C9.48963 12.4435 10.6711 11.7264 11.5196 10.6854C12.3681 9.64432 12.8319 8.34282 12.8328 7C12.8328 6.84529 12.8943 6.69692 13.0038 6.58752C13.1132 6.47812 13.2616 6.41667 13.4164 6.41667C13.5712 6.41667 13.7196 6.47812 13.8291 6.58752C13.9385 6.69692 14 6.84529 14 7C14 8.85651 13.2622 10.637 11.9489 11.9497C10.6356 13.2625 8.85432 14 6.99701 14Z\"\n                fill=\"currentColor\"\n            />\n        </g>\n        <defs>\n            <clipPath id=\"clip0_417_21408\">\n                <rect width=\"14\" height=\"14\" fill=\"white\" />\n            </clipPath>\n        </defs>\n    </svg>`;\n    constructor(el, document) {\n      this.el = el;\n      this.document = document;\n    }\n    ngAfterViewInit() {\n      DomHandler.addMultipleClasses(this.htmlElement, this.getStyleClass().join(' '));\n      this.createIcon();\n      this.createLabel();\n      this.initialized = true;\n    }\n    getStyleClass() {\n      const styleClass = [INTERNAL_BUTTON_CLASSES.button, INTERNAL_BUTTON_CLASSES.component];\n      if (this.icon && !this.label && ObjectUtils.isEmpty(this.htmlElement.textContent)) {\n        styleClass.push(INTERNAL_BUTTON_CLASSES.iconOnly);\n      }\n      if (this.loading) {\n        styleClass.push(INTERNAL_BUTTON_CLASSES.disabled, INTERNAL_BUTTON_CLASSES.loading);\n        if (!this.icon && this.label) {\n          styleClass.push(INTERNAL_BUTTON_CLASSES.labelOnly);\n        }\n        if (this.icon && !this.label && !ObjectUtils.isEmpty(this.htmlElement.textContent)) {\n          styleClass.push(INTERNAL_BUTTON_CLASSES.iconOnly);\n        }\n      }\n      return styleClass;\n    }\n    setStyleClass() {\n      const styleClass = this.getStyleClass();\n      this.htmlElement.classList.remove(...this._internalClasses);\n      this.htmlElement.classList.add(...styleClass);\n    }\n    createLabel() {\n      if (this.label) {\n        let labelElement = this.document.createElement('span');\n        if (this.icon && !this.label) {\n          labelElement.setAttribute('aria-hidden', 'true');\n        }\n        labelElement.className = 'p-button-label';\n        labelElement.appendChild(this.document.createTextNode(this.label));\n        this.htmlElement.appendChild(labelElement);\n      }\n    }\n    createIcon() {\n      if (this.icon || this.loading) {\n        let iconElement = this.document.createElement('span');\n        iconElement.className = 'p-button-icon';\n        iconElement.setAttribute('aria-hidden', 'true');\n        let iconPosClass = this.label ? 'p-button-icon-' + this.iconPos : null;\n        if (iconPosClass) {\n          DomHandler.addClass(iconElement, iconPosClass);\n        }\n        let iconClass = this.getIconClass();\n        if (iconClass) {\n          DomHandler.addMultipleClasses(iconElement, iconClass);\n        }\n        if (!this.loadingIcon && this.loading) {\n          iconElement.innerHTML = this.spinnerIcon;\n        }\n        this.htmlElement.insertBefore(iconElement, this.htmlElement.firstChild);\n      }\n    }\n    updateLabel() {\n      let labelElement = DomHandler.findSingle(this.htmlElement, '.p-button-label');\n      if (!this.label) {\n        labelElement && this.htmlElement.removeChild(labelElement);\n        return;\n      }\n      labelElement ? labelElement.textContent = this.label : this.createLabel();\n    }\n    updateIcon() {\n      let iconElement = DomHandler.findSingle(this.htmlElement, '.p-button-icon');\n      let labelElement = DomHandler.findSingle(this.htmlElement, '.p-button-label');\n      if (this.loading && !this.loadingIcon && iconElement) {\n        iconElement.innerHTML = this.spinnerIcon;\n      } else if (iconElement?.innerHTML) {\n        iconElement.innerHTML = '';\n      }\n      if (iconElement) {\n        if (this.iconPos) {\n          iconElement.className = 'p-button-icon ' + (labelElement ? 'p-button-icon-' + this.iconPos : '') + ' ' + this.getIconClass();\n        } else {\n          iconElement.className = 'p-button-icon ' + this.getIconClass();\n        }\n      } else {\n        this.createIcon();\n      }\n    }\n    getIconClass() {\n      return this.loading ? 'p-button-loading-icon ' + (this.loadingIcon ? this.loadingIcon : 'p-icon') : this.icon || 'p-hidden';\n    }\n    ngOnDestroy() {\n      this.initialized = false;\n    }\n    static ɵfac = function ButtonDirective_Factory(t) {\n      return new (t || ButtonDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(DOCUMENT));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: ButtonDirective,\n      selectors: [[\"\", \"pButton\", \"\"]],\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        iconPos: \"iconPos\",\n        loadingIcon: \"loadingIcon\",\n        label: \"label\",\n        icon: \"icon\",\n        loading: \"loading\"\n      }\n    });\n  }\n  return ButtonDirective;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Button is an extension to standard button element with icons and theming.\n * @group Components\n */\nlet Button = /*#__PURE__*/(() => {\n  class Button {\n    /**\n     * Type of the button.\n     * @group Props\n     */\n    type = 'button';\n    /**\n     * Position of the icon.\n     * @group Props\n     */\n    iconPos = 'left';\n    /**\n     * Name of the icon.\n     * @group Props\n     */\n    icon;\n    /**\n     * Value of the badge.\n     * @group Props\n     */\n    badge;\n    /**\n     * Uses to pass attributes to the label's DOM element.\n     * @group Props\n     */\n    label;\n    /**\n     * When present, it specifies that the component should be disabled.\n     * @group Props\n     */\n    disabled;\n    /**\n     * Whether the button is in loading state.\n     * @group Props\n     */\n    loading = false;\n    /**\n     * Icon to display in loading state.\n     * @group Props\n     */\n    loadingIcon;\n    /**\n     * Add a shadow to indicate elevation.\n     * @group Props\n     */\n    raised = false;\n    /**\n     * Add a circular border radius to the button.\n     * @group Props\n     */\n    rounded = false;\n    /**\n     * Add a textual class to the button without a background initially.\n     * @group Props\n     */\n    text = false;\n    /**\n     * Add a plain textual class to the button without a background initially.\n     * @group Props\n     */\n    plain = false;\n    /**\n     * Defines the style of the button.\n     * @group Props\n     */\n    severity;\n    /**\n     * Add a border class without a background initially.\n     * @group Props\n     */\n    outlined = false;\n    /**\n     *  Add a link style to the button.\n     * @group Props\n     */\n    link = false;\n    /**\n     * Defines the size of the button.\n     * @group Props\n     */\n    size;\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    style;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Style class of the badge.\n     * @group Props\n     */\n    badgeClass;\n    /**\n     * Used to define a string that autocomplete attribute the current element.\n     * @group Props\n     */\n    ariaLabel;\n    /**\n     * Callback to execute when button is clicked.\n     * This event is intended to be used with the <p-button> component. Using a regular <button> element, use (click).\n     * @param {MouseEvent} event - Mouse event.\n     * @group Emits\n     */\n    onClick = new EventEmitter();\n    /**\n     * Callback to execute when button is focused.\n     * This event is intended to be used with the <p-button> component. Using a regular <button> element, use (focus).\n     * @param {FocusEvent} event - Focus event.\n     * @group Emits\n     */\n    onFocus = new EventEmitter();\n    /**\n     * Callback to execute when button loses focus.\n     * This event is intended to be used with the <p-button> component. Using a regular <button> element, use (blur).\n     * @param {FocusEvent} event - Focus event.\n     * @group Emits\n     */\n    onBlur = new EventEmitter();\n    contentTemplate;\n    loadingIconTemplate;\n    iconTemplate;\n    templates;\n    spinnerIconClass() {\n      return Object.entries(this.iconClass()).filter(([, value]) => !!value).reduce((acc, [key]) => acc + ` ${key}`, 'p-button-loading-icon');\n    }\n    iconClass() {\n      return {\n        'p-button-icon': true,\n        'p-button-icon-left': this.iconPos === 'left' && this.label,\n        'p-button-icon-right': this.iconPos === 'right' && this.label,\n        'p-button-icon-top': this.iconPos === 'top' && this.label,\n        'p-button-icon-bottom': this.iconPos === 'bottom' && this.label\n      };\n    }\n    buttonClass() {\n      return {\n        'p-button p-component': true,\n        'p-button-icon-only': (this.icon || this.iconTemplate || this.loadingIcon || this.loadingIconTemplate) && !this.label,\n        'p-button-vertical': (this.iconPos === 'top' || this.iconPos === 'bottom') && this.label,\n        'p-disabled': this.disabled || this.loading,\n        'p-button-loading': this.loading,\n        'p-button-loading-label-only': this.loading && !this.icon && this.label && !this.loadingIcon && this.iconPos === 'left',\n        'p-button-link': this.link,\n        [`p-button-${this.severity}`]: this.severity,\n        'p-button-raised': this.raised,\n        'p-button-rounded': this.rounded,\n        'p-button-text': this.text,\n        'p-button-outlined': this.outlined,\n        'p-button-sm': this.size === 'small',\n        'p-button-lg': this.size === 'large',\n        'p-button-plain': this.plain\n      };\n    }\n    ngAfterContentInit() {\n      this.templates?.forEach(item => {\n        switch (item.getType()) {\n          case 'content':\n            this.contentTemplate = item.template;\n            break;\n          case 'icon':\n            this.iconTemplate = item.template;\n            break;\n          case 'loadingicon':\n            this.loadingIconTemplate = item.template;\n            break;\n          default:\n            this.contentTemplate = item.template;\n            break;\n        }\n      });\n    }\n    badgeStyleClass() {\n      return {\n        'p-badge p-component': true,\n        'p-badge-no-gutter': this.badge && String(this.badge).length === 1\n      };\n    }\n    static ɵfac = function Button_Factory(t) {\n      return new (t || Button)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: Button,\n      selectors: [[\"p-button\"]],\n      contentQueries: function Button_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      hostVars: 2,\n      hostBindings: function Button_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"p-disabled\", ctx.disabled);\n        }\n      },\n      inputs: {\n        type: \"type\",\n        iconPos: \"iconPos\",\n        icon: \"icon\",\n        badge: \"badge\",\n        label: \"label\",\n        disabled: \"disabled\",\n        loading: \"loading\",\n        loadingIcon: \"loadingIcon\",\n        raised: \"raised\",\n        rounded: \"rounded\",\n        text: \"text\",\n        plain: \"plain\",\n        severity: \"severity\",\n        outlined: \"outlined\",\n        link: \"link\",\n        size: \"size\",\n        style: \"style\",\n        styleClass: \"styleClass\",\n        badgeClass: \"badgeClass\",\n        ariaLabel: \"ariaLabel\"\n      },\n      outputs: {\n        onClick: \"onClick\",\n        onFocus: \"onFocus\",\n        onBlur: \"onBlur\"\n      },\n      ngContentSelectors: _c0,\n      decls: 7,\n      vars: 14,\n      consts: [[\"pRipple\", \"\", 3, \"ngStyle\", \"disabled\", \"ngClass\", \"click\", \"focus\", \"blur\"], [4, \"ngTemplateOutlet\"], [4, \"ngIf\"], [\"class\", \"p-button-label\", 4, \"ngIf\"], [3, \"ngClass\", \"class\", 4, \"ngIf\"], [\"class\", \"p-button-loading-icon\", 3, \"ngClass\", 4, \"ngIf\"], [3, \"class\", \"ngClass\", 4, \"ngIf\"], [3, \"styleClass\", \"spin\", 4, \"ngIf\"], [3, \"ngClass\"], [3, \"styleClass\", \"spin\"], [1, \"p-button-loading-icon\", 3, \"ngClass\"], [3, \"ngClass\", 4, \"ngIf\"], [3, \"ngIf\"], [1, \"p-button-label\"]],\n      template: function Button_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"button\", 0);\n          i0.ɵɵlistener(\"click\", function Button_Template_button_click_0_listener($event) {\n            return ctx.onClick.emit($event);\n          })(\"focus\", function Button_Template_button_focus_0_listener($event) {\n            return ctx.onFocus.emit($event);\n          })(\"blur\", function Button_Template_button_blur_0_listener($event) {\n            return ctx.onBlur.emit($event);\n          });\n          i0.ɵɵprojection(1);\n          i0.ɵɵtemplate(2, Button_ng_container_2_Template, 1, 0, \"ng-container\", 1);\n          i0.ɵɵtemplate(3, Button_ng_container_3_Template, 3, 2, \"ng-container\", 2);\n          i0.ɵɵtemplate(4, Button_ng_container_4_Template, 3, 2, \"ng-container\", 2);\n          i0.ɵɵtemplate(5, Button_span_5_Template, 2, 3, \"span\", 3);\n          i0.ɵɵtemplate(6, Button_span_6_Template, 2, 5, \"span\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.styleClass);\n          i0.ɵɵproperty(\"ngStyle\", ctx.style)(\"disabled\", ctx.disabled || ctx.loading)(\"ngClass\", ctx.buttonClass());\n          i0.ɵɵattribute(\"type\", ctx.type)(\"aria-label\", ctx.ariaLabel)(\"data-pc-name\", \"button\")(\"data-pc-section\", \"root\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.contentTemplate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.contentTemplate && ctx.label);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.contentTemplate && ctx.badge);\n        }\n      },\n      dependencies: function () {\n        return [i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, i2.Ripple, SpinnerIcon];\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return Button;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet ButtonModule = /*#__PURE__*/(() => {\n  class ButtonModule {\n    static ɵfac = function ButtonModule_Factory(t) {\n      return new (t || ButtonModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: ButtonModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, RippleModule, SharedModule, SpinnerIcon, SharedModule]\n    });\n  }\n  return ButtonModule;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Button, ButtonDirective, ButtonModule };\n//# sourceMappingURL=primeng-button.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}