"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2022 Google LLC. https://angular.io/
 * License: MIT
 */class TaskTrackingZoneSpec{constructor(){this.name="TaskTrackingZone",this.microTasks=[],this.macroTasks=[],this.eventTasks=[],this.properties={TaskTrackingZone:this}}static get(){return Zone.current.get("TaskTrackingZone")}getTasksFor(e){switch(e){case"microTask":return this.microTasks;case"macroTask":return this.macroTasks;case"eventTask":return this.eventTasks}throw new Error("Unknown task format: "+e)}onScheduleTask(e,s,t,r){return r.creationLocation=new Error(`Task '${r.type}' from '${r.source}'.`),this.getTasksFor(r.type).push(r),e.scheduleTask(t,r)}onCancelTask(e,s,t,r){const a=this.getTasksFor(r.type);for(let e=0;e<a.length;e++)if(a[e]==r){a.splice(e,1);break}return e.cancelTask(t,r)}onInvokeTask(e,s,t,r,a,n){if("eventTask"===r.type||r.data?.isPeriodic)return e.invokeTask(t,r,a,n);const k=this.getTasksFor(r.type);for(let e=0;e<k.length;e++)if(k[e]==r){k.splice(e,1);break}return e.invokeTask(t,r,a,n)}clearEvents(){for(;this.eventTasks.length;)Zone.current.cancelTask(this.eventTasks[0])}}Zone.TaskTrackingZoneSpec=TaskTrackingZoneSpec;