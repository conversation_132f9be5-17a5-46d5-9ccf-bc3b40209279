{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, ViewChild, ContentChild, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport * as i2 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport { ObjectUtils } from 'primeng/utils';\nconst _c0 = [\"container\"];\nfunction SelectButton_div_2_ng_container_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 8);\n  }\n  if (rf & 2) {\n    const option_r2 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵclassMap(option_r2.icon);\n    i0.ɵɵproperty(\"ngClass\", \"p-button-icon p-button-icon-left\");\n    i0.ɵɵattribute(\"data-pc-section\", \"icon\");\n  }\n}\nfunction SelectButton_div_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, SelectButton_div_2_ng_container_1_span_1_Template, 1, 4, \"span\", 6);\n    i0.ɵɵelementStart(2, \"span\", 7);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const option_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", option_r2.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵattribute(\"data-pc-section\", \"label\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r4.getOptionLabel(option_r2));\n  }\n}\nfunction SelectButton_div_2_ng_template_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c1 = function (a0, a1) {\n  return {\n    $implicit: a0,\n    index: a1\n  };\n};\nfunction SelectButton_div_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, SelectButton_div_2_ng_template_2_ng_container_0_Template, 1, 0, \"ng-container\", 9);\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    const option_r2 = ctx_r11.$implicit;\n    const i_r3 = ctx_r11.index;\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r6.selectButtonTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c1, option_r2, i_r3));\n  }\n}\nconst _c2 = function (a0, a1, a2) {\n  return {\n    \"p-highlight\": a0,\n    \"p-disabled\": a1,\n    \"p-button-icon-only\": a2\n  };\n};\nfunction SelectButton_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵlistener(\"click\", function SelectButton_div_2_Template_div_click_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r13);\n      const option_r2 = restoredCtx.$implicit;\n      const i_r3 = restoredCtx.index;\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.onOptionSelect($event, option_r2, i_r3));\n    })(\"keydown\", function SelectButton_div_2_Template_div_keydown_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r13);\n      const option_r2 = restoredCtx.$implicit;\n      const i_r3 = restoredCtx.index;\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.onKeyDown($event, option_r2, i_r3));\n    })(\"focus\", function SelectButton_div_2_Template_div_focus_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r13);\n      const i_r3 = restoredCtx.index;\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.onFocus($event, i_r3));\n    })(\"blur\", function SelectButton_div_2_Template_div_blur_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.onBlur());\n    });\n    i0.ɵɵtemplate(1, SelectButton_div_2_ng_container_1_Template, 4, 3, \"ng-container\", 4);\n    i0.ɵɵtemplate(2, SelectButton_div_2_ng_template_2_Template, 1, 5, \"ng-template\", null, 5, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r2 = ctx.$implicit;\n    const i_r3 = ctx.index;\n    const _r5 = i0.ɵɵreference(3);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(option_r2.styleClass);\n    i0.ɵɵproperty(\"role\", ctx_r1.multiple ? \"checkbox\" : \"radio\")(\"ngClass\", i0.ɵɵpureFunction3(14, _c2, ctx_r1.isSelected(option_r2), ctx_r1.disabled || ctx_r1.isOptionDisabled(option_r2), option_r2.icon && !ctx_r1.getOptionLabel(option_r2)));\n    i0.ɵɵattribute(\"tabindex\", i_r3 === ctx_r1.focusedIndex ? \"0\" : \"-1\")(\"aria-label\", option_r2.label)(\"aria-checked\", ctx_r1.isSelected(option_r2))(\"aria-disabled\", ctx_r1.optionDisabled)(\"aria-pressed\", ctx_r1.isSelected(option_r2))(\"title\", option_r2.title)(\"aria-labelledby\", ctx_r1.getOptionLabel(option_r2))(\"data-pc-section\", \"button\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.itemTemplate)(\"ngIfElse\", _r5);\n  }\n}\nconst SELECTBUTTON_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => SelectButton),\n  multi: true\n};\n/**\n * SelectButton is used to choose single or multiple items from a list using buttons.\n * @group Components\n */\nlet SelectButton = /*#__PURE__*/(() => {\n  class SelectButton {\n    cd;\n    /**\n     * An array of selectitems to display as the available options.\n     * @group Props\n     */\n    options;\n    /**\n     * Name of the label field of an option.\n     * @group Props\n     */\n    optionLabel;\n    /**\n     * Name of the value field of an option.\n     * @group Props\n     */\n    optionValue;\n    /**\n     * Name of the disabled field of an option.\n     * @group Props\n     */\n    optionDisabled;\n    /**\n     * Whether selection can be cleared.\n     * @group Props\n     */\n    unselectable = false;\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    tabindex = 0;\n    /**\n     * When specified, allows selecting multiple values.\n     * @group Props\n     */\n    multiple;\n    /**\n     * Whether selection can not be cleared.\n     * @group Props\n     */\n    allowEmpty = true;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    style;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Establishes relationships between the component and label(s) where its value should be one or more element IDs.\n     * @group Props\n     */\n    ariaLabelledBy;\n    /**\n     * When present, it specifies that the element should be disabled.\n     * @group Props\n     */\n    disabled;\n    /**\n     * A property to uniquely identify a value in options.\n     * @group Props\n     */\n    dataKey;\n    /**\n     * Callback to invoke on input click.\n     * @param {SelectButtonOptionClickEvent} event - Custom click event.\n     * @group Emits\n     */\n    onOptionClick = new EventEmitter();\n    /**\n     * Callback to invoke on selection change.\n     * @param {SelectButtonChangeEvent} event - Custom change event.\n     * @group Emits\n     */\n    onChange = new EventEmitter();\n    container;\n    itemTemplate;\n    get selectButtonTemplate() {\n      return this.itemTemplate?.template;\n    }\n    get equalityKey() {\n      return this.optionValue ? null : this.dataKey;\n    }\n    value;\n    onModelChange = () => {};\n    onModelTouched = () => {};\n    focusedIndex = 0;\n    constructor(cd) {\n      this.cd = cd;\n    }\n    getOptionLabel(option) {\n      return this.optionLabel ? ObjectUtils.resolveFieldData(option, this.optionLabel) : option.label != undefined ? option.label : option;\n    }\n    getOptionValue(option) {\n      return this.optionValue ? ObjectUtils.resolveFieldData(option, this.optionValue) : this.optionLabel || option.value === undefined ? option : option.value;\n    }\n    isOptionDisabled(option) {\n      return this.optionDisabled ? ObjectUtils.resolveFieldData(option, this.optionDisabled) : option.disabled !== undefined ? option.disabled : false;\n    }\n    writeValue(value) {\n      this.value = value;\n      this.cd.markForCheck();\n    }\n    registerOnChange(fn) {\n      this.onModelChange = fn;\n    }\n    registerOnTouched(fn) {\n      this.onModelTouched = fn;\n    }\n    setDisabledState(val) {\n      this.disabled = val;\n      this.cd.markForCheck();\n    }\n    onOptionSelect(event, option, index) {\n      if (this.disabled || this.isOptionDisabled(option)) {\n        return;\n      }\n      let selected = this.isSelected(option);\n      if (selected && this.unselectable) {\n        return;\n      }\n      let optionValue = this.getOptionValue(option);\n      let newValue;\n      if (this.multiple) {\n        if (selected) newValue = this.value.filter(val => !ObjectUtils.equals(val, optionValue, this.equalityKey));else newValue = this.value ? [...this.value, optionValue] : [optionValue];\n      } else {\n        if (selected && !this.allowEmpty) {\n          return;\n        }\n        newValue = selected ? null : optionValue;\n      }\n      this.focusedIndex = index;\n      this.value = newValue;\n      this.onModelChange(this.value);\n      this.onChange.emit({\n        originalEvent: event,\n        value: this.value\n      });\n      this.onOptionClick.emit({\n        originalEvent: event,\n        option: option,\n        index: index\n      });\n    }\n    onKeyDown(event, option, index) {\n      switch (event.code) {\n        case 'Space':\n          {\n            this.onOptionSelect(event, option, index);\n            event.preventDefault();\n            break;\n          }\n        case 'ArrowDown':\n        case 'ArrowRight':\n          {\n            this.changeTabIndexes(event, 'next');\n            event.preventDefault();\n            break;\n          }\n        case 'ArrowUp':\n        case 'ArrowLeft':\n          {\n            this.changeTabIndexes(event, 'prev');\n            event.preventDefault();\n            break;\n          }\n        default:\n          //no op\n          break;\n      }\n    }\n    changeTabIndexes(event, direction) {\n      let firstTabableChild, index;\n      for (let i = 0; i <= this.container.nativeElement.children.length - 1; i++) {\n        if (this.container.nativeElement.children[i].getAttribute('tabindex') === '0') firstTabableChild = {\n          elem: this.container.nativeElement.children[i],\n          index: i\n        };\n      }\n      if (direction === 'prev') {\n        if (firstTabableChild.index === 0) index = this.container.nativeElement.children.length - 1;else index = firstTabableChild.index - 1;\n      } else {\n        if (firstTabableChild.index === this.container.nativeElement.children.length - 1) index = 0;else index = firstTabableChild.index + 1;\n      }\n      this.focusedIndex = index;\n      this.container.nativeElement.children[index].focus();\n    }\n    onFocus(event, index) {\n      this.focusedIndex = index;\n    }\n    onBlur() {\n      this.onModelTouched();\n    }\n    removeOption(option) {\n      this.value = this.value.filter(val => !ObjectUtils.equals(val, this.getOptionValue(option), this.dataKey));\n    }\n    isSelected(option) {\n      let selected = false;\n      const optionValue = this.getOptionValue(option);\n      if (this.multiple) {\n        if (this.value && Array.isArray(this.value)) {\n          for (let val of this.value) {\n            if (ObjectUtils.equals(val, optionValue, this.dataKey)) {\n              selected = true;\n              break;\n            }\n          }\n        }\n      } else {\n        selected = ObjectUtils.equals(this.getOptionValue(option), this.value, this.equalityKey);\n      }\n      return selected;\n    }\n    static ɵfac = function SelectButton_Factory(t) {\n      return new (t || SelectButton)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: SelectButton,\n      selectors: [[\"p-selectButton\"]],\n      contentQueries: function SelectButton_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.itemTemplate = _t.first);\n        }\n      },\n      viewQuery: function SelectButton_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.container = _t.first);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        options: \"options\",\n        optionLabel: \"optionLabel\",\n        optionValue: \"optionValue\",\n        optionDisabled: \"optionDisabled\",\n        unselectable: \"unselectable\",\n        tabindex: \"tabindex\",\n        multiple: \"multiple\",\n        allowEmpty: \"allowEmpty\",\n        style: \"style\",\n        styleClass: \"styleClass\",\n        ariaLabelledBy: \"ariaLabelledBy\",\n        disabled: \"disabled\",\n        dataKey: \"dataKey\"\n      },\n      outputs: {\n        onOptionClick: \"onOptionClick\",\n        onChange: \"onChange\"\n      },\n      features: [i0.ɵɵProvidersFeature([SELECTBUTTON_VALUE_ACCESSOR])],\n      decls: 3,\n      vars: 8,\n      consts: [[\"role\", \"group\", 3, \"ngClass\", \"ngStyle\"], [\"container\", \"\"], [\"pRipple\", \"\", \"class\", \"p-button p-component\", 3, \"role\", \"class\", \"ngClass\", \"click\", \"keydown\", \"focus\", \"blur\", 4, \"ngFor\", \"ngForOf\"], [\"pRipple\", \"\", 1, \"p-button\", \"p-component\", 3, \"role\", \"ngClass\", \"click\", \"keydown\", \"focus\", \"blur\"], [4, \"ngIf\", \"ngIfElse\"], [\"customcontent\", \"\"], [3, \"ngClass\", \"class\", 4, \"ngIf\"], [1, \"p-button-label\"], [3, \"ngClass\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"]],\n      template: function SelectButton_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0, 1);\n          i0.ɵɵtemplate(2, SelectButton_div_2_Template, 4, 18, \"div\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.styleClass);\n          i0.ɵɵproperty(\"ngClass\", \"p-selectbutton p-buttonset p-component\")(\"ngStyle\", ctx.style);\n          i0.ɵɵattribute(\"aria-labelledby\", ctx.ariaLabelledBy)(\"data-pc-name\", \"selectbutton\")(\"data-pc-section\", \"root\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.options);\n        }\n      },\n      dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, i2.Ripple],\n      styles: [\"@layer primeng{.p-button{margin:0;display:inline-flex;cursor:pointer;-webkit-user-select:none;user-select:none;align-items:center;vertical-align:bottom;text-align:center;overflow:hidden;position:relative}.p-button-label{flex:1 1 auto}.p-button-icon-right{order:1}.p-button:disabled{cursor:default;pointer-events:none}.p-button-icon-only{justify-content:center}.p-button-icon-only:after{content:\\\"p\\\";visibility:hidden;clip:rect(0 0 0 0);width:0}.p-button-vertical{flex-direction:column}.p-button-icon-bottom{order:2}.p-buttonset .p-button{margin:0}.p-buttonset .p-button:not(:last-child){border-right:0 none}.p-buttonset .p-button:not(:first-of-type):not(:last-of-type){border-radius:0}.p-buttonset .p-button:first-of-type{border-top-right-radius:0;border-bottom-right-radius:0}.p-buttonset .p-button:last-of-type{border-top-left-radius:0;border-bottom-left-radius:0}.p-buttonset .p-button:focus{position:relative;z-index:1}p-button[iconpos=right] spinnericon{order:1}}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return SelectButton;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet SelectButtonModule = /*#__PURE__*/(() => {\n  class SelectButtonModule {\n    static ɵfac = function SelectButtonModule_Factory(t) {\n      return new (t || SelectButtonModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: SelectButtonModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, RippleModule, SharedModule, SharedModule]\n    });\n  }\n  return SelectButtonModule;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { SELECTBUTTON_VALUE_ACCESSOR, SelectButton, SelectButtonModule };\n//# sourceMappingURL=primeng-selectbutton.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}