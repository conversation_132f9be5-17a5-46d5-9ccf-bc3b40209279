{"ast": null, "code": "import { animation, style, animate, trigger, transition, useAnimation } from '@angular/animations';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, ContentChildren, Output, NgModule } from '@angular/core';\nimport * as i1 from 'primeng/api';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { <PERSON><PERSON>and<PERSON> } from 'primeng/dom';\nimport { TimesIcon } from 'primeng/icons/times';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport { ZIndexUtils } from 'primeng/utils';\nfunction Sidebar_div_0_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Sidebar_div_0_button_4_TimesIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"TimesIcon\", 11);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-sidebar-close-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"closeicon\");\n  }\n}\nfunction Sidebar_div_0_button_4_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction Sidebar_div_0_button_4_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Sidebar_div_0_button_4_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Sidebar_div_0_button_4_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 12);\n    i0.ɵɵtemplate(1, Sidebar_div_0_button_4_span_2_1_Template, 1, 0, null, 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(3);\n    i0.ɵɵattribute(\"data-pc-section\", \"closeicon\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r7.closeIconTemplate);\n  }\n}\nfunction Sidebar_div_0_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function Sidebar_div_0_button_4_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.close($event));\n    })(\"keydown.enter\", function Sidebar_div_0_button_4_Template_button_keydown_enter_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r12.close($event));\n    });\n    i0.ɵɵtemplate(1, Sidebar_div_0_button_4_TimesIcon_1_Template, 1, 2, \"TimesIcon\", 9);\n    i0.ɵɵtemplate(2, Sidebar_div_0_button_4_span_2_Template, 2, 2, \"span\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"aria-label\", ctx_r3.ariaCloseLabel)(\"data-pc-section\", \"closebutton\")(\"data-pc-group-section\", \"iconcontainer\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.closeIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.closeIconTemplate);\n  }\n}\nfunction Sidebar_div_0_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Sidebar_div_0_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c0 = function (a1, a2, a3, a4, a5, a6) {\n  return {\n    \"p-sidebar\": true,\n    \"p-sidebar-active\": a1,\n    \"p-sidebar-left\": a2,\n    \"p-sidebar-right\": a3,\n    \"p-sidebar-top\": a4,\n    \"p-sidebar-bottom\": a5,\n    \"p-sidebar-full\": a6\n  };\n};\nconst _c1 = function (a0, a1) {\n  return {\n    transform: a0,\n    transition: a1\n  };\n};\nconst _c2 = function (a1) {\n  return {\n    value: \"visible\",\n    params: a1\n  };\n};\nfunction Sidebar_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1, 2);\n    i0.ɵɵlistener(\"@panelState.start\", function Sidebar_div_0_Template_div_animation_panelState_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.onAnimationStart($event));\n    })(\"@panelState.done\", function Sidebar_div_0_Template_div_animation_panelState_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.onAnimationEnd($event));\n    })(\"keydown\", function Sidebar_div_0_Template_div_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.onKeyDown($event));\n    });\n    i0.ɵɵelementStart(2, \"div\", 3);\n    i0.ɵɵtemplate(3, Sidebar_div_0_ng_container_3_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵtemplate(4, Sidebar_div_0_button_4_Template, 3, 5, \"button\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 6);\n    i0.ɵɵprojection(6);\n    i0.ɵɵtemplate(7, Sidebar_div_0_ng_container_7_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 7);\n    i0.ɵɵtemplate(9, Sidebar_div_0_ng_container_9_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r0.styleClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction6(15, _c0, ctx_r0.visible, ctx_r0.position === \"left\" && !ctx_r0.fullScreen, ctx_r0.position === \"right\" && !ctx_r0.fullScreen, ctx_r0.position === \"top\" && !ctx_r0.fullScreen, ctx_r0.position === \"bottom\" && !ctx_r0.fullScreen, ctx_r0.fullScreen))(\"@panelState\", i0.ɵɵpureFunction1(25, _c2, i0.ɵɵpureFunction2(22, _c1, ctx_r0.transformOptions, ctx_r0.transitionOptions)))(\"ngStyle\", ctx_r0.style);\n    i0.ɵɵattribute(\"data-pc-name\", \"sidebar\")(\"data-pc-section\", \"root\")(\"aria-modal\", ctx_r0.modal);\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"data-pc-section\", \"header\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.headerTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showCloseIcon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵattribute(\"data-pc-section\", \"content\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.contentTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵattribute(\"data-pc-section\", \"footer\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.footerTemplate);\n  }\n}\nconst _c3 = [\"*\"];\nconst showAnimation = animation([style({\n  transform: '{{transform}}',\n  opacity: 0\n}), animate('{{transition}}')]);\nconst hideAnimation = animation([animate('{{transition}}', style({\n  transform: '{{transform}}',\n  opacity: 0\n}))]);\n/**\n * Sidebar is a panel component displayed as an overlay at the edges of the screen.\n * @group Components\n */\nlet Sidebar = /*#__PURE__*/(() => {\n  class Sidebar {\n    document;\n    el;\n    renderer;\n    cd;\n    config;\n    /**\n     *  Target element to attach the dialog, valid values are \"body\" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n     * @group Props\n     */\n    appendTo;\n    /**\n     * Whether to block scrolling of the document when sidebar is active.\n     * @group Props\n     */\n    blockScroll = false;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    style;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Aria label of the close icon.\n     * @group Props\n     */\n    ariaCloseLabel;\n    /**\n     * Whether to automatically manage layering.\n     * @group Props\n     */\n    autoZIndex = true;\n    /**\n     * Base zIndex value to use in layering.\n     * @group Props\n     */\n    baseZIndex = 0;\n    /**\n     * Whether an overlay mask is displayed behind the sidebar.\n     * @group Props\n     */\n    modal = true;\n    /**\n     * Whether to dismiss sidebar on click of the mask.\n     * @group Props\n     */\n    dismissible = true;\n    /**\n     * Whether to display the close icon.\n     * @group Props\n     */\n    showCloseIcon = true;\n    /**\n     * Specifies if pressing escape key should hide the sidebar.\n     * @group Props\n     */\n    closeOnEscape = true;\n    /**\n     * Transition options of the animation.\n     * @group Props\n     */\n    transitionOptions = '150ms cubic-bezier(0, 0, 0.2, 1)';\n    /**\n     * Specifies the visibility of the dialog.\n     * @group Props\n     */\n    get visible() {\n      return this._visible;\n    }\n    set visible(val) {\n      this._visible = val;\n    }\n    /**\n     * Specifies the position of the sidebar, valid values are \"left\", \"right\", \"bottom\" and \"top\".\n     * @group Props\n     */\n    get position() {\n      return this._position;\n    }\n    set position(value) {\n      this._position = value;\n      switch (value) {\n        case 'left':\n          this.transformOptions = 'translate3d(-100%, 0px, 0px)';\n          break;\n        case 'right':\n          this.transformOptions = 'translate3d(100%, 0px, 0px)';\n          break;\n        case 'bottom':\n          this.transformOptions = 'translate3d(0px, 100%, 0px)';\n          break;\n        case 'top':\n          this.transformOptions = 'translate3d(0px, -100%, 0px)';\n          break;\n      }\n    }\n    /**\n     * Adds a close icon to the header to hide the dialog.\n     * @group Props\n     */\n    get fullScreen() {\n      return this._fullScreen;\n    }\n    set fullScreen(value) {\n      this._fullScreen = value;\n      if (value) this.transformOptions = 'none';\n    }\n    templates;\n    /**\n     * Callback to invoke when dialog is shown.\n     * @group Emits\n     */\n    onShow = new EventEmitter();\n    /**\n     * Callback to invoke when dialog is hidden.\n     * @group Emits\n     */\n    onHide = new EventEmitter();\n    /**\n     * Callback to invoke when dialog visibility is changed.\n     * @param {boolean} value - Visible value.\n     * @group Emits\n     */\n    visibleChange = new EventEmitter();\n    initialized;\n    _visible;\n    _position = 'left';\n    _fullScreen = false;\n    container;\n    transformOptions = 'translate3d(-100%, 0px, 0px)';\n    mask;\n    maskClickListener;\n    documentEscapeListener;\n    animationEndListener;\n    contentTemplate;\n    headerTemplate;\n    footerTemplate;\n    closeIconTemplate;\n    constructor(document, el, renderer, cd, config) {\n      this.document = document;\n      this.el = el;\n      this.renderer = renderer;\n      this.cd = cd;\n      this.config = config;\n    }\n    ngAfterViewInit() {\n      this.initialized = true;\n    }\n    ngAfterContentInit() {\n      this.templates?.forEach(item => {\n        switch (item.getType()) {\n          case 'content':\n            this.contentTemplate = item.template;\n            break;\n          case 'header':\n            this.headerTemplate = item.template;\n            break;\n          case 'footer':\n            this.footerTemplate = item.template;\n            break;\n          case 'closeicon':\n            this.closeIconTemplate = item.template;\n            break;\n          default:\n            this.contentTemplate = item.template;\n            break;\n        }\n      });\n    }\n    onKeyDown(event) {\n      if (event.code === 'Escape') {\n        this.hide(false);\n      }\n    }\n    show() {\n      if (this.autoZIndex) {\n        ZIndexUtils.set('modal', this.container, this.baseZIndex || this.config.zIndex.modal);\n      }\n      if (this.modal) {\n        this.enableModality();\n      }\n      this.onShow.emit({});\n      this.visibleChange.emit(true);\n    }\n    hide(emit = true) {\n      if (emit) {\n        this.onHide.emit({});\n      }\n      if (this.modal) {\n        this.disableModality();\n      }\n    }\n    close(event) {\n      this.hide(false);\n      this.visibleChange.emit(false);\n      event.preventDefault();\n    }\n    enableModality() {\n      if (!this.mask) {\n        this.mask = this.renderer.createElement('div');\n        this.renderer.setStyle(this.mask, 'zIndex', String(parseInt(this.container.style.zIndex) - 1));\n        DomHandler.addMultipleClasses(this.mask, 'p-component-overlay p-sidebar-mask p-component-overlay p-component-overlay-enter');\n        if (this.dismissible) {\n          this.maskClickListener = this.renderer.listen(this.mask, 'click', event => {\n            if (this.dismissible) {\n              this.close(event);\n            }\n          });\n        }\n        this.renderer.appendChild(this.document.body, this.mask);\n        if (this.blockScroll) {\n          DomHandler.blockBodyScroll();\n        }\n      }\n    }\n    disableModality() {\n      if (this.mask) {\n        DomHandler.addClass(this.mask, 'p-component-overlay-leave');\n        this.animationEndListener = this.renderer.listen(this.mask, 'animationend', this.destroyModal.bind(this));\n      }\n    }\n    destroyModal() {\n      this.unbindMaskClickListener();\n      if (this.mask) {\n        this.renderer.removeChild(this.document.body, this.mask);\n      }\n      if (this.blockScroll) {\n        DomHandler.unblockBodyScroll();\n      }\n      this.unbindAnimationEndListener();\n      this.mask = null;\n    }\n    onAnimationStart(event) {\n      switch (event.toState) {\n        case 'visible':\n          this.container = event.element;\n          this.appendContainer();\n          this.show();\n          if (this.closeOnEscape) {\n            this.bindDocumentEscapeListener();\n          }\n          break;\n      }\n    }\n    onAnimationEnd(event) {\n      switch (event.toState) {\n        case 'void':\n          this.hide();\n          ZIndexUtils.clear(this.container);\n          this.unbindGlobalListeners();\n          break;\n      }\n    }\n    appendContainer() {\n      if (this.appendTo) {\n        if (this.appendTo === 'body') this.renderer.appendChild(this.document.body, this.container);else DomHandler.appendChild(this.container, this.appendTo);\n      }\n    }\n    bindDocumentEscapeListener() {\n      const documentTarget = this.el ? this.el.nativeElement.ownerDocument : this.document;\n      this.documentEscapeListener = this.renderer.listen(documentTarget, 'keydown', event => {\n        if (event.which == 27) {\n          if (parseInt(this.container.style.zIndex) === ZIndexUtils.get(this.container)) {\n            this.close(event);\n          }\n        }\n      });\n    }\n    unbindDocumentEscapeListener() {\n      if (this.documentEscapeListener) {\n        this.documentEscapeListener();\n        this.documentEscapeListener = null;\n      }\n    }\n    unbindMaskClickListener() {\n      if (this.maskClickListener) {\n        this.maskClickListener();\n        this.maskClickListener = null;\n      }\n    }\n    unbindGlobalListeners() {\n      this.unbindMaskClickListener();\n      this.unbindDocumentEscapeListener();\n    }\n    unbindAnimationEndListener() {\n      if (this.animationEndListener && this.mask) {\n        this.animationEndListener();\n        this.animationEndListener = null;\n      }\n    }\n    ngOnDestroy() {\n      this.initialized = false;\n      if (this.visible && this.modal) {\n        this.destroyModal();\n      }\n      if (this.appendTo && this.container) {\n        this.renderer.appendChild(this.el.nativeElement, this.container);\n      }\n      if (this.container && this.autoZIndex) {\n        ZIndexUtils.clear(this.container);\n      }\n      this.container = null;\n      this.unbindGlobalListeners();\n      this.unbindAnimationEndListener();\n    }\n    static ɵfac = function Sidebar_Factory(t) {\n      return new (t || Sidebar)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.PrimeNGConfig));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: Sidebar,\n      selectors: [[\"p-sidebar\"]],\n      contentQueries: function Sidebar_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        appendTo: \"appendTo\",\n        blockScroll: \"blockScroll\",\n        style: \"style\",\n        styleClass: \"styleClass\",\n        ariaCloseLabel: \"ariaCloseLabel\",\n        autoZIndex: \"autoZIndex\",\n        baseZIndex: \"baseZIndex\",\n        modal: \"modal\",\n        dismissible: \"dismissible\",\n        showCloseIcon: \"showCloseIcon\",\n        closeOnEscape: \"closeOnEscape\",\n        transitionOptions: \"transitionOptions\",\n        visible: \"visible\",\n        position: \"position\",\n        fullScreen: \"fullScreen\"\n      },\n      outputs: {\n        onShow: \"onShow\",\n        onHide: \"onHide\",\n        visibleChange: \"visibleChange\"\n      },\n      ngContentSelectors: _c3,\n      decls: 1,\n      vars: 1,\n      consts: [[\"role\", \"complementary\", 3, \"ngClass\", \"ngStyle\", \"class\", \"keydown\", 4, \"ngIf\"], [\"role\", \"complementary\", 3, \"ngClass\", \"ngStyle\", \"keydown\"], [\"container\", \"\"], [1, \"p-sidebar-header\"], [4, \"ngTemplateOutlet\"], [\"type\", \"button\", \"class\", \"p-sidebar-close p-sidebar-icon p-link\", \"pRipple\", \"\", 3, \"click\", \"keydown.enter\", 4, \"ngIf\"], [1, \"p-sidebar-content\"], [1, \"p-sidebar-footer\"], [\"type\", \"button\", \"pRipple\", \"\", 1, \"p-sidebar-close\", \"p-sidebar-icon\", \"p-link\", 3, \"click\", \"keydown.enter\"], [3, \"styleClass\", 4, \"ngIf\"], [\"class\", \"p-sidebar-close-icon\", 4, \"ngIf\"], [3, \"styleClass\"], [1, \"p-sidebar-close-icon\"]],\n      template: function Sidebar_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, Sidebar_div_0_Template, 10, 27, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.visible);\n        }\n      },\n      dependencies: function () {\n        return [i2.NgClass, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i3.Ripple, TimesIcon];\n      },\n      styles: [\"@layer primeng{.p-sidebar{position:fixed;transition:transform .3s;display:flex;flex-direction:column}.p-sidebar-content{position:relative;overflow-y:auto;flex-grow:1}.p-sidebar-header{display:flex;align-items:center}.p-sidebar-footer{margin-top:auto}.p-sidebar-icon{display:flex;align-items:center;justify-content:center;margin-left:auto}.p-sidebar-left{top:0;left:0;width:20rem;height:100%}.p-sidebar-right{top:0;right:0;width:20rem;height:100%}.p-sidebar-top{top:0;left:0;width:100%;height:10rem}.p-sidebar-bottom{bottom:0;left:0;width:100%;height:10rem}.p-sidebar-full{width:100%;height:100%;top:0;left:0;transition:none}.p-sidebar-left.p-sidebar-sm,.p-sidebar-right.p-sidebar-sm{width:20rem}.p-sidebar-left.p-sidebar-md,.p-sidebar-right.p-sidebar-md{width:40rem}.p-sidebar-left.p-sidebar-lg,.p-sidebar-right.p-sidebar-lg{width:60rem}.p-sidebar-top.p-sidebar-sm,.p-sidebar-bottom.p-sidebar-sm{height:10rem}.p-sidebar-top.p-sidebar-md,.p-sidebar-bottom.p-sidebar-md{height:20rem}.p-sidebar-top.p-sidebar-lg,.p-sidebar-bottom.p-sidebar-lg{height:30rem}@media screen and (max-width: 64em){.p-sidebar-left.p-sidebar-lg,.p-sidebar-left.p-sidebar-md,.p-sidebar-right.p-sidebar-lg,.p-sidebar-right.p-sidebar-md{width:20rem}}}\\n\"],\n      encapsulation: 2,\n      data: {\n        animation: [trigger('panelState', [transition('void => visible', [useAnimation(showAnimation)]), transition('visible => void', [useAnimation(hideAnimation)])])]\n      },\n      changeDetection: 0\n    });\n  }\n  return Sidebar;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet SidebarModule = /*#__PURE__*/(() => {\n  class SidebarModule {\n    static ɵfac = function SidebarModule_Factory(t) {\n      return new (t || SidebarModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: SidebarModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, RippleModule, SharedModule, TimesIcon, SharedModule]\n    });\n  }\n  return SidebarModule;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Sidebar, SidebarModule };\n//# sourceMappingURL=primeng-sidebar.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}