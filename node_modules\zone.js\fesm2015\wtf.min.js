"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2022 Google LLC. https://angular.io/
 * License: MIT
 */!function(e){let n=null,t=null;const c=function(){const c=e.wtf;return!(!c||(n=c.trace,!n)||(t=n.events,0))}();class o{constructor(){this.name="WTF"}static{this.forkInstance=c?t.createInstance("Zone:fork(ascii zone, ascii newZone)"):null}static{this.scheduleInstance={}}static{this.cancelInstance={}}static{this.invokeScope={}}static{this.invokeTaskScope={}}onFork(e,n,t,c){const s=e.fork(t,c);return o.forkInstance(a(t),s.name),s}onInvoke(e,c,s,r,i,l,u){const k=u||"unknown";let f=o.invokeScope[k];return f||(f=o.invokeScope[k]=t.createScope(`Zone:invoke:${u}(ascii zone)`)),n.leaveScope(f(a(s)),e.invoke(s,r,i,l,u))}onHandleError(e,n,t,c){return e.handleError(t,c)}onScheduleTask(e,n,c,r){const i=r.type+":"+r.source;let l=o.scheduleInstance[i];l||(l=o.scheduleInstance[i]=t.createInstance(`Zone:schedule:${i}(ascii zone, any data)`));const u=e.scheduleTask(c,r);return l(a(c),s(r.data,2)),u}onInvokeTask(e,c,s,r,i,l){const u=r.source;let k=o.invokeTaskScope[u];return k||(k=o.invokeTaskScope[u]=t.createScope(`Zone:invokeTask:${u}(ascii zone)`)),n.leaveScope(k(a(s)),e.invokeTask(s,r,i,l))}onCancelTask(e,n,c,r){const i=r.source;let l=o.cancelInstance[i];l||(l=o.cancelInstance[i]=t.createInstance(`Zone:cancel:${i}(ascii zone, any options)`));const u=e.cancelTask(c,r);return l(a(c),s(r.data,2)),u}}function s(e,n){if(!e||!n)return null;const t={};for(const c in e)if(e.hasOwnProperty(c)){let o=e[c];switch(typeof o){case"object":const e=o&&o.constructor&&o.constructor.name;o=e==Object.name?s(o,n-1):e;break;case"function":o=o.name||void 0}t[c]=o}return t}function a(e){let n=e.name,t=e.parent;for(;null!=t;)n=t.name+"::"+n,t=t.parent;return n}Zone.wtfZoneSpec=c?new o:null}("object"==typeof window&&window||"object"==typeof self&&self||global);