"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2022 Google LLC. https://angular.io/
 * License: MIT
 */!function(e){class n{static{this.symbolParentUnresolved=Zone.__symbol__("parentUnresolved")}constructor(n,s,t){this.finishCallback=n,this.failCallback=s,this._pendingMicroTasks=!1,this._pendingMacroTasks=!1,this._alreadyErrored=!1,this._isSync=!1,this._existingFinishTimer=null,this.entryFunction=null,this.runZone=Zone.current,this.unresolvedChainedPromiseCount=0,this.supportWaitUnresolvedChainedPromise=!1,this.name="asyncTestZone for "+t,this.properties={AsyncTestZoneSpec:this},this.supportWaitUnresolvedChainedPromise=!0===e[Zone.__symbol__("supportWaitUnResolvedChainedPromise")]}isUnresolvedChainedPromisePending(){return this.unresolvedChainedPromiseCount>0}_finishCallbackIfDone(){null!==this._existingFinishTimer&&(clearTimeout(this._existingFinishTimer),this._existingFinishTimer=null),this._pendingMicroTasks||this._pendingMacroTasks||this.supportWaitUnresolvedChainedPromise&&this.isUnresolvedChainedPromisePending()||this.runZone.run((()=>{this._existingFinishTimer=setTimeout((()=>{this._alreadyErrored||this._pendingMicroTasks||this._pendingMacroTasks||this.finishCallback()}),0)}))}patchPromiseForTest(){if(!this.supportWaitUnresolvedChainedPromise)return;const e=Promise[Zone.__symbol__("patchPromiseForTest")];e&&e()}unPatchPromiseForTest(){if(!this.supportWaitUnresolvedChainedPromise)return;const e=Promise[Zone.__symbol__("unPatchPromiseForTest")];e&&e()}onScheduleTask(e,s,t,i){return"eventTask"!==i.type&&(this._isSync=!1),"microTask"===i.type&&i.data&&i.data instanceof Promise&&!0===i.data[n.symbolParentUnresolved]&&this.unresolvedChainedPromiseCount--,e.scheduleTask(t,i)}onInvokeTask(e,n,s,t,i,r){return"eventTask"!==t.type&&(this._isSync=!1),e.invokeTask(s,t,i,r)}onCancelTask(e,n,s,t){return"eventTask"!==t.type&&(this._isSync=!1),e.cancelTask(s,t)}onInvoke(e,n,s,t,i,r,o){this.entryFunction||(this.entryFunction=t);try{return this._isSync=!0,e.invoke(s,t,i,r,o)}finally{this._isSync&&this.entryFunction===t&&this._finishCallbackIfDone()}}onHandleError(e,n,s,t){return e.handleError(s,t)&&(this.failCallback(t),this._alreadyErrored=!0),!1}onHasTask(e,n,s,t){e.hasTask(s,t),n===s&&("microTask"==t.change?(this._pendingMicroTasks=t.microTask,this._finishCallbackIfDone()):"macroTask"==t.change&&(this._pendingMacroTasks=t.macroTask,this._finishCallbackIfDone()))}}Zone.AsyncTestZoneSpec=n}("undefined"!=typeof window&&window||"undefined"!=typeof self&&self||global),Zone.__load_patch("asynctest",((e,n,s)=>{function t(e,s,t,i){const r=n.current,o=n.AsyncTestZoneSpec;if(void 0===o)throw new Error("AsyncTestZoneSpec is needed for the async() test helper but could not be found. Please make sure that your environment includes zone.js/plugins/async-test");const a=n.ProxyZoneSpec;if(!a)throw new Error("ProxyZoneSpec is needed for the async() test helper but could not be found. Please make sure that your environment includes zone.js/plugins/proxy");const h=a.get();a.assertPresent();const c=n.current.getZoneWith("ProxyZoneSpec"),l=h.getDelegate();return c.parent.run((()=>{const e=new o((()=>{h.getDelegate()==e&&h.setDelegate(l),e.unPatchPromiseForTest(),r.run((()=>{t()}))}),(n=>{h.getDelegate()==e&&h.setDelegate(l),e.unPatchPromiseForTest(),r.run((()=>{i(n)}))}),"test");h.setDelegate(e),e.patchPromiseForTest()})),n.current.runGuarded(e,s)}n[s.symbol("asyncTest")]=function n(s){return e.jasmine?function(e){e||((e=function(){}).fail=function(e){throw e}),t(s,this,e,(n=>{if("string"==typeof n)return e.fail(new Error(n));e.fail(n)}))}:function(){return new Promise(((e,n)=>{t(s,this,e,n)}))}}}));