{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { RouterOutlet } from '@angular/router';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { HttpClientModule } from '@angular/common/http';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { GeneratorComponent } from './GeneratorPage/generator/generator.component';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport { ButtonModule } from 'primeng/button';\nimport { ProgressBarModule } from 'primeng/progressbar';\nimport { ToastModule } from 'primeng/toast';\nimport { MessageService } from 'primeng/api';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { VideosDialogComponent } from './GeneratorPage/videos-dialog/videos-dialog.component';\nimport { DialogModule } from 'primeng/dialog';\nimport { AuthDialogComponent } from './GeneratorPage/auth-dialog/auth-dialog.component';\nimport { UploadDialogComponent } from './GeneratorPage/upload-dialog/upload-dialog.component';\nimport { SidebarComponent } from './GeneratorPage/sidebar/sidebar.component';\nimport { VideoSettingsComponent } from './GeneratorPage/video-settings/video-settings.component';\nimport { TermsDialogComponent } from './GeneratorPage/terms-dialog/terms-dialog.component';\nimport { PrivacyDialogComponent } from './GeneratorPage/privacy-dialog/privacy-dialog.component';\nimport { SecurityWarningComponent } from './GeneratorPage/security-warning/security-warning.component';\nimport { LinkShortenerComponent } from './GeneratorPage/link-shortener/link-shortener.component';\nimport { AdminComponent } from './admin/admin.component';\nimport { AdMobSettingsComponent } from './admin/admob-settings/admob-settings.component';\nimport { InputTextareaModule } from 'primeng/inputtextarea';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { SidebarModule } from 'primeng/sidebar';\nimport { MenuModule } from 'primeng/menu';\nimport { OverlayPanelModule } from 'primeng/overlaypanel';\nimport { PanelModule } from 'primeng/panel';\nimport { CardModule } from 'primeng/card';\nimport { DividerModule } from 'primeng/divider';\nimport { TabViewModule } from 'primeng/tabview';\nimport { SelectButtonModule } from 'primeng/selectbutton';\nimport { SliderModule } from 'primeng/slider';\nimport { FileUploadModule } from 'primeng/fileupload';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { ConfirmationService } from 'primeng/api';\nexport let AppModule = class AppModule {};\nAppModule = __decorate([NgModule({\n  declarations: [AppComponent, GeneratorComponent, VideosDialogComponent, AuthDialogComponent, UploadDialogComponent, SidebarComponent, VideoSettingsComponent, TermsDialogComponent, PrivacyDialogComponent, SecurityWarningComponent, LinkShortenerComponent, AdminComponent, AdMobSettingsComponent],\n  imports: [BrowserModule, AppRoutingModule, HttpClientModule, BrowserAnimationsModule, RouterOutlet, InputTextModule, CheckboxModule, RadioButtonModule, ButtonModule, ProgressBarModule, ToastModule, DropdownModule, FormsModule, ReactiveFormsModule, DialogModule, InputTextareaModule, TooltipModule, SidebarModule, MenuModule, OverlayPanelModule, PanelModule, CardModule, DividerModule, TabViewModule, SelectButtonModule, SliderModule, FileUploadModule, ConfirmDialogModule, CheckboxModule],\n  providers: [MessageService, ConfirmationService],\n  bootstrap: [AppComponent]\n})], AppModule);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}