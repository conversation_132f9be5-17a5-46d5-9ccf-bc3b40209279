"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2022 Google LLC. https://angular.io/
 * License: MIT
 */Zone.__load_patch("mocha",((t,n)=>{const e=t.Mocha;if(void 0===e)return;if(void 0===n)throw new Error("Missing Zone.js");const r=n.ProxyZoneSpec,i=n.SyncTestZoneSpec;if(!r)throw new Error("Missing ProxyZoneSpec");if(e.__zone_patch__)throw new Error('"Mocha" has already been patched with "Zone".');e.__zone_patch__=!0;const o=n.current,u=o.fork(new i("Mocha.describe"));let c=null;const s=o.fork(new r),f={after:t.after,afterEach:t.afterEach,before:t.before,beforeEach:t.beforeEach,describe:t.describe,it:t.it};function a(t,n,e){for(let r=0;r<t.length;r++){let i=t[r];"function"==typeof i&&(t[r]=0===i.length?n(i):e(i),t[r].toString=function(){return i.toString()})}return t}function p(t){return a(t,(function(t){return function(){return u.run(t,this,arguments)}}))}function h(t){return a(t,(function(t){return function(){return c.run(t,this)}}),(function(t){return function(n){return c.run(t,this,[n])}}))}function l(t){return a(t,(function(t){return function(){return s.run(t,this)}}),(function(t){return function(n){return s.run(t,this,[n])}}))}var y,d;t.describe=t.suite=function(){return f.describe.apply(this,p(arguments))},t.xdescribe=t.suite.skip=t.describe.skip=function(){return f.describe.skip.apply(this,p(arguments))},t.describe.only=t.suite.only=function(){return f.describe.only.apply(this,p(arguments))},t.it=t.specify=t.test=function(){return f.it.apply(this,h(arguments))},t.xit=t.xspecify=t.it.skip=function(){return f.it.skip.apply(this,h(arguments))},t.it.only=t.test.only=function(){return f.it.only.apply(this,h(arguments))},t.after=t.suiteTeardown=function(){return f.after.apply(this,l(arguments))},t.afterEach=t.teardown=function(){return f.afterEach.apply(this,h(arguments))},t.before=t.suiteSetup=function(){return f.before.apply(this,l(arguments))},t.beforeEach=t.setup=function(){return f.beforeEach.apply(this,h(arguments))},y=e.Runner.prototype.runTest,d=e.Runner.prototype.run,e.Runner.prototype.runTest=function(t){n.current.scheduleMicroTask("mocha.forceTask",(()=>{y.call(this,t)}))},e.Runner.prototype.run=function(t){return this.on("test",(t=>{c=o.fork(new r)})),this.on("fail",((t,n)=>{const e=c&&c.get("ProxyZoneSpec");if(e&&n)try{n.message+=e.getAndClearPendingTasksInfo()}catch(t){}})),d.call(this,t)}}));