{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/\\u062A\\u0637\\u0628\\u064A\\u0642 \\u0646\\u0627\\u0634\\u0631 \\u0627\\u064A\\u0627\\u062A \\u0642\\u0626\\u0627\\u0646\\u064A\\u0629/QuranVidGen/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*! Capacitor: https://capacitorjs.com/ - MIT License */\nconst createCapacitorPlatforms = win => {\n  const defaultPlatformMap = new Map();\n  defaultPlatformMap.set('web', {\n    name: 'web'\n  });\n  const capPlatforms = win.CapacitorPlatforms || {\n    currentPlatform: {\n      name: 'web'\n    },\n    platforms: defaultPlatformMap\n  };\n  const addPlatform = (name, platform) => {\n    capPlatforms.platforms.set(name, platform);\n  };\n  const setPlatform = name => {\n    if (capPlatforms.platforms.has(name)) {\n      capPlatforms.currentPlatform = capPlatforms.platforms.get(name);\n    }\n  };\n  capPlatforms.addPlatform = addPlatform;\n  capPlatforms.setPlatform = setPlatform;\n  return capPlatforms;\n};\nconst initPlatforms = win => win.CapacitorPlatforms = createCapacitorPlatforms(win);\n/**\n * @deprecated Set `CapacitorCustomPlatform` on the window object prior to runtime executing in the web app instead\n */\nconst CapacitorPlatforms = /*#__PURE__*/initPlatforms(typeof globalThis !== 'undefined' ? globalThis : typeof self !== 'undefined' ? self : typeof window !== 'undefined' ? window : typeof global !== 'undefined' ? global : {});\n/**\n * @deprecated Set `CapacitorCustomPlatform` on the window object prior to runtime executing in the web app instead\n */\nconst addPlatform = CapacitorPlatforms.addPlatform;\n/**\n * @deprecated Set `CapacitorCustomPlatform` on the window object prior to runtime executing in the web app instead\n */\nconst setPlatform = CapacitorPlatforms.setPlatform;\nconst legacyRegisterWebPlugin = (cap, webPlugin) => {\n  var _a;\n  const config = webPlugin.config;\n  const Plugins = cap.Plugins;\n  if (!(config === null || config === void 0 ? void 0 : config.name)) {\n    // TODO: add link to upgrade guide\n    throw new Error(`Capacitor WebPlugin is using the deprecated \"registerWebPlugin()\" function, but without the config. Please use \"registerPlugin()\" instead to register this web plugin.\"`);\n  }\n  // TODO: add link to upgrade guide\n  console.warn(`Capacitor plugin \"${config.name}\" is using the deprecated \"registerWebPlugin()\" function`);\n  if (!Plugins[config.name] || ((_a = config === null || config === void 0 ? void 0 : config.platforms) === null || _a === void 0 ? void 0 : _a.includes(cap.getPlatform()))) {\n    // Add the web plugin into the plugins registry if there already isn't\n    // an existing one. If it doesn't already exist, that means\n    // there's no existing native implementation for it.\n    // - OR -\n    // If we already have a plugin registered (meaning it was defined in the native layer),\n    // then we should only overwrite it if the corresponding web plugin activates on\n    // a certain platform. For example: Geolocation uses the WebPlugin on Android but not iOS\n    Plugins[config.name] = webPlugin;\n  }\n};\nvar ExceptionCode;\n(function (ExceptionCode) {\n  /**\n   * API is not implemented.\n   *\n   * This usually means the API can't be used because it is not implemented for\n   * the current platform.\n   */\n  ExceptionCode[\"Unimplemented\"] = \"UNIMPLEMENTED\";\n  /**\n   * API is not available.\n   *\n   * This means the API can't be used right now because:\n   *   - it is currently missing a prerequisite, such as network connectivity\n   *   - it requires a particular platform or browser version\n   */\n  ExceptionCode[\"Unavailable\"] = \"UNAVAILABLE\";\n})(ExceptionCode || (ExceptionCode = {}));\nclass CapacitorException extends Error {\n  constructor(message, code, data) {\n    super(message);\n    this.message = message;\n    this.code = code;\n    this.data = data;\n  }\n}\nconst getPlatformId = win => {\n  var _a, _b;\n  if (win === null || win === void 0 ? void 0 : win.androidBridge) {\n    return 'android';\n  } else if ((_b = (_a = win === null || win === void 0 ? void 0 : win.webkit) === null || _a === void 0 ? void 0 : _a.messageHandlers) === null || _b === void 0 ? void 0 : _b.bridge) {\n    return 'ios';\n  } else {\n    return 'web';\n  }\n};\nconst createCapacitor = win => {\n  var _a, _b, _c, _d, _e;\n  const capCustomPlatform = win.CapacitorCustomPlatform || null;\n  const cap = win.Capacitor || {};\n  const Plugins = cap.Plugins = cap.Plugins || {};\n  /**\n   * @deprecated Use `capCustomPlatform` instead, default functions like registerPlugin will function with the new object.\n   */\n  const capPlatforms = win.CapacitorPlatforms;\n  const defaultGetPlatform = () => {\n    return capCustomPlatform !== null ? capCustomPlatform.name : getPlatformId(win);\n  };\n  const getPlatform = ((_a = capPlatforms === null || capPlatforms === void 0 ? void 0 : capPlatforms.currentPlatform) === null || _a === void 0 ? void 0 : _a.getPlatform) || defaultGetPlatform;\n  const defaultIsNativePlatform = () => getPlatform() !== 'web';\n  const isNativePlatform = ((_b = capPlatforms === null || capPlatforms === void 0 ? void 0 : capPlatforms.currentPlatform) === null || _b === void 0 ? void 0 : _b.isNativePlatform) || defaultIsNativePlatform;\n  const defaultIsPluginAvailable = pluginName => {\n    const plugin = registeredPlugins.get(pluginName);\n    if (plugin === null || plugin === void 0 ? void 0 : plugin.platforms.has(getPlatform())) {\n      // JS implementation available for the current platform.\n      return true;\n    }\n    if (getPluginHeader(pluginName)) {\n      // Native implementation available.\n      return true;\n    }\n    return false;\n  };\n  const isPluginAvailable = ((_c = capPlatforms === null || capPlatforms === void 0 ? void 0 : capPlatforms.currentPlatform) === null || _c === void 0 ? void 0 : _c.isPluginAvailable) || defaultIsPluginAvailable;\n  const defaultGetPluginHeader = pluginName => {\n    var _a;\n    return (_a = cap.PluginHeaders) === null || _a === void 0 ? void 0 : _a.find(h => h.name === pluginName);\n  };\n  const getPluginHeader = ((_d = capPlatforms === null || capPlatforms === void 0 ? void 0 : capPlatforms.currentPlatform) === null || _d === void 0 ? void 0 : _d.getPluginHeader) || defaultGetPluginHeader;\n  const handleError = err => win.console.error(err);\n  const pluginMethodNoop = (_target, prop, pluginName) => {\n    return Promise.reject(`${pluginName} does not have an implementation of \"${prop}\".`);\n  };\n  const registeredPlugins = new Map();\n  const defaultRegisterPlugin = (pluginName, jsImplementations = {}) => {\n    const registeredPlugin = registeredPlugins.get(pluginName);\n    if (registeredPlugin) {\n      console.warn(`Capacitor plugin \"${pluginName}\" already registered. Cannot register plugins twice.`);\n      return registeredPlugin.proxy;\n    }\n    const platform = getPlatform();\n    const pluginHeader = getPluginHeader(pluginName);\n    let jsImplementation;\n    const loadPluginImplementation = /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* () {\n        if (!jsImplementation && platform in jsImplementations) {\n          jsImplementation = typeof jsImplementations[platform] === 'function' ? jsImplementation = yield jsImplementations[platform]() : jsImplementation = jsImplementations[platform];\n        } else if (capCustomPlatform !== null && !jsImplementation && 'web' in jsImplementations) {\n          jsImplementation = typeof jsImplementations['web'] === 'function' ? jsImplementation = yield jsImplementations['web']() : jsImplementation = jsImplementations['web'];\n        }\n        return jsImplementation;\n      });\n      return function loadPluginImplementation() {\n        return _ref.apply(this, arguments);\n      };\n    }();\n    const createPluginMethod = (impl, prop) => {\n      var _a, _b;\n      if (pluginHeader) {\n        const methodHeader = pluginHeader === null || pluginHeader === void 0 ? void 0 : pluginHeader.methods.find(m => prop === m.name);\n        if (methodHeader) {\n          if (methodHeader.rtype === 'promise') {\n            return options => cap.nativePromise(pluginName, prop.toString(), options);\n          } else {\n            return (options, callback) => cap.nativeCallback(pluginName, prop.toString(), options, callback);\n          }\n        } else if (impl) {\n          return (_a = impl[prop]) === null || _a === void 0 ? void 0 : _a.bind(impl);\n        }\n      } else if (impl) {\n        return (_b = impl[prop]) === null || _b === void 0 ? void 0 : _b.bind(impl);\n      } else {\n        throw new CapacitorException(`\"${pluginName}\" plugin is not implemented on ${platform}`, ExceptionCode.Unimplemented);\n      }\n    };\n    const createPluginMethodWrapper = prop => {\n      let remove;\n      const wrapper = (...args) => {\n        const p = loadPluginImplementation().then(impl => {\n          const fn = createPluginMethod(impl, prop);\n          if (fn) {\n            const p = fn(...args);\n            remove = p === null || p === void 0 ? void 0 : p.remove;\n            return p;\n          } else {\n            throw new CapacitorException(`\"${pluginName}.${prop}()\" is not implemented on ${platform}`, ExceptionCode.Unimplemented);\n          }\n        });\n        if (prop === 'addListener') {\n          p.remove = /*#__PURE__*/_asyncToGenerator(function* () {\n            return remove();\n          });\n        }\n        return p;\n      };\n      // Some flair ✨\n      wrapper.toString = () => `${prop.toString()}() { [capacitor code] }`;\n      Object.defineProperty(wrapper, 'name', {\n        value: prop,\n        writable: false,\n        configurable: false\n      });\n      return wrapper;\n    };\n    const addListener = createPluginMethodWrapper('addListener');\n    const removeListener = createPluginMethodWrapper('removeListener');\n    const addListenerNative = (eventName, callback) => {\n      const call = addListener({\n        eventName\n      }, callback);\n      const remove = /*#__PURE__*/function () {\n        var _ref3 = _asyncToGenerator(function* () {\n          const callbackId = yield call;\n          removeListener({\n            eventName,\n            callbackId\n          }, callback);\n        });\n        return function remove() {\n          return _ref3.apply(this, arguments);\n        };\n      }();\n      const p = new Promise(resolve => call.then(() => resolve({\n        remove\n      })));\n      p.remove = /*#__PURE__*/_asyncToGenerator(function* () {\n        console.warn(`Using addListener() without 'await' is deprecated.`);\n        yield remove();\n      });\n      return p;\n    };\n    const proxy = new Proxy({}, {\n      get(_, prop) {\n        switch (prop) {\n          // https://github.com/facebook/react/issues/20030\n          case '$$typeof':\n            return undefined;\n          case 'toJSON':\n            return () => ({});\n          case 'addListener':\n            return pluginHeader ? addListenerNative : addListener;\n          case 'removeListener':\n            return removeListener;\n          default:\n            return createPluginMethodWrapper(prop);\n        }\n      }\n    });\n    Plugins[pluginName] = proxy;\n    registeredPlugins.set(pluginName, {\n      name: pluginName,\n      proxy,\n      platforms: new Set([...Object.keys(jsImplementations), ...(pluginHeader ? [platform] : [])])\n    });\n    return proxy;\n  };\n  const registerPlugin = ((_e = capPlatforms === null || capPlatforms === void 0 ? void 0 : capPlatforms.currentPlatform) === null || _e === void 0 ? void 0 : _e.registerPlugin) || defaultRegisterPlugin;\n  // Add in convertFileSrc for web, it will already be available in native context\n  if (!cap.convertFileSrc) {\n    cap.convertFileSrc = filePath => filePath;\n  }\n  cap.getPlatform = getPlatform;\n  cap.handleError = handleError;\n  cap.isNativePlatform = isNativePlatform;\n  cap.isPluginAvailable = isPluginAvailable;\n  cap.pluginMethodNoop = pluginMethodNoop;\n  cap.registerPlugin = registerPlugin;\n  cap.Exception = CapacitorException;\n  cap.DEBUG = !!cap.DEBUG;\n  cap.isLoggingEnabled = !!cap.isLoggingEnabled;\n  // Deprecated props\n  cap.platform = cap.getPlatform();\n  cap.isNative = cap.isNativePlatform();\n  return cap;\n};\nconst initCapacitorGlobal = win => win.Capacitor = createCapacitor(win);\nconst Capacitor = /*#__PURE__*/initCapacitorGlobal(typeof globalThis !== 'undefined' ? globalThis : typeof self !== 'undefined' ? self : typeof window !== 'undefined' ? window : typeof global !== 'undefined' ? global : {});\nconst registerPlugin = Capacitor.registerPlugin;\n/**\n * @deprecated Provided for backwards compatibility for Capacitor v2 plugins.\n * Capacitor v3 plugins should import the plugin directly. This \"Plugins\"\n * export is deprecated in v3, and will be removed in v4.\n */\nconst Plugins = Capacitor.Plugins;\n/**\n * Provided for backwards compatibility. Use the registerPlugin() API\n * instead, and provide the web plugin as the \"web\" implmenetation.\n * For example\n *\n * export const Example = registerPlugin('Example', {\n *   web: () => import('./web').then(m => new m.Example())\n * })\n *\n * @deprecated Deprecated in v3, will be removed from v4.\n */\nconst registerWebPlugin = plugin => legacyRegisterWebPlugin(Capacitor, plugin);\n\n/**\n * Base class web plugins should extend.\n */\nclass WebPlugin {\n  constructor(config) {\n    this.listeners = {};\n    this.retainedEventArguments = {};\n    this.windowListeners = {};\n    if (config) {\n      // TODO: add link to upgrade guide\n      console.warn(`Capacitor WebPlugin \"${config.name}\" config object was deprecated in v3 and will be removed in v4.`);\n      this.config = config;\n    }\n  }\n  addListener(eventName, listenerFunc) {\n    var _this = this;\n    let firstListener = false;\n    const listeners = this.listeners[eventName];\n    if (!listeners) {\n      this.listeners[eventName] = [];\n      firstListener = true;\n    }\n    this.listeners[eventName].push(listenerFunc);\n    // If we haven't added a window listener for this event and it requires one,\n    // go ahead and add it\n    const windowListener = this.windowListeners[eventName];\n    if (windowListener && !windowListener.registered) {\n      this.addWindowListener(windowListener);\n    }\n    if (firstListener) {\n      this.sendRetainedArgumentsForEvent(eventName);\n    }\n    const remove = /*#__PURE__*/function () {\n      var _ref5 = _asyncToGenerator(function* () {\n        return _this.removeListener(eventName, listenerFunc);\n      });\n      return function remove() {\n        return _ref5.apply(this, arguments);\n      };\n    }();\n    const p = Promise.resolve({\n      remove\n    });\n    return p;\n  }\n  removeAllListeners() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.listeners = {};\n      for (const listener in _this2.windowListeners) {\n        _this2.removeWindowListener(_this2.windowListeners[listener]);\n      }\n      _this2.windowListeners = {};\n    })();\n  }\n  notifyListeners(eventName, data, retainUntilConsumed) {\n    const listeners = this.listeners[eventName];\n    if (!listeners) {\n      if (retainUntilConsumed) {\n        let args = this.retainedEventArguments[eventName];\n        if (!args) {\n          args = [];\n        }\n        args.push(data);\n        this.retainedEventArguments[eventName] = args;\n      }\n      return;\n    }\n    listeners.forEach(listener => listener(data));\n  }\n  hasListeners(eventName) {\n    return !!this.listeners[eventName].length;\n  }\n  registerWindowListener(windowEventName, pluginEventName) {\n    this.windowListeners[pluginEventName] = {\n      registered: false,\n      windowEventName,\n      pluginEventName,\n      handler: event => {\n        this.notifyListeners(pluginEventName, event);\n      }\n    };\n  }\n  unimplemented(msg = 'not implemented') {\n    return new Capacitor.Exception(msg, ExceptionCode.Unimplemented);\n  }\n  unavailable(msg = 'not available') {\n    return new Capacitor.Exception(msg, ExceptionCode.Unavailable);\n  }\n  removeListener(eventName, listenerFunc) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const listeners = _this3.listeners[eventName];\n      if (!listeners) {\n        return;\n      }\n      const index = listeners.indexOf(listenerFunc);\n      _this3.listeners[eventName].splice(index, 1);\n      // If there are no more listeners for this type of event,\n      // remove the window listener\n      if (!_this3.listeners[eventName].length) {\n        _this3.removeWindowListener(_this3.windowListeners[eventName]);\n      }\n    })();\n  }\n  addWindowListener(handle) {\n    window.addEventListener(handle.windowEventName, handle.handler);\n    handle.registered = true;\n  }\n  removeWindowListener(handle) {\n    if (!handle) {\n      return;\n    }\n    window.removeEventListener(handle.windowEventName, handle.handler);\n    handle.registered = false;\n  }\n  sendRetainedArgumentsForEvent(eventName) {\n    const args = this.retainedEventArguments[eventName];\n    if (!args) {\n      return;\n    }\n    delete this.retainedEventArguments[eventName];\n    args.forEach(arg => {\n      this.notifyListeners(eventName, arg);\n    });\n  }\n}\nconst WebView = /*#__PURE__*/registerPlugin('WebView');\n/******** END WEB VIEW PLUGIN ********/\n/******** COOKIES PLUGIN ********/\n/**\n * Safely web encode a string value (inspired by js-cookie)\n * @param str The string value to encode\n */\nconst encode = str => encodeURIComponent(str).replace(/%(2[346B]|5E|60|7C)/g, decodeURIComponent).replace(/[()]/g, escape);\n/**\n * Safely web decode a string value (inspired by js-cookie)\n * @param str The string value to decode\n */\nconst decode = str => str.replace(/(%[\\dA-F]{2})+/gi, decodeURIComponent);\nclass CapacitorCookiesPluginWeb extends WebPlugin {\n  getCookies() {\n    return _asyncToGenerator(function* () {\n      const cookies = document.cookie;\n      const cookieMap = {};\n      cookies.split(';').forEach(cookie => {\n        if (cookie.length <= 0) return;\n        // Replace first \"=\" with CAP_COOKIE to prevent splitting on additional \"=\"\n        let [key, value] = cookie.replace(/=/, 'CAP_COOKIE').split('CAP_COOKIE');\n        key = decode(key).trim();\n        value = decode(value).trim();\n        cookieMap[key] = value;\n      });\n      return cookieMap;\n    })();\n  }\n  setCookie(options) {\n    return _asyncToGenerator(function* () {\n      try {\n        // Safely Encoded Key/Value\n        const encodedKey = encode(options.key);\n        const encodedValue = encode(options.value);\n        // Clean & sanitize options\n        const expires = `; expires=${(options.expires || '').replace('expires=', '')}`; // Default is \"; expires=\"\n        const path = (options.path || '/').replace('path=', ''); // Default is \"path=/\"\n        const domain = options.url != null && options.url.length > 0 ? `domain=${options.url}` : '';\n        document.cookie = `${encodedKey}=${encodedValue || ''}${expires}; path=${path}; ${domain};`;\n      } catch (error) {\n        return Promise.reject(error);\n      }\n    })();\n  }\n  deleteCookie(options) {\n    return _asyncToGenerator(function* () {\n      try {\n        document.cookie = `${options.key}=; Max-Age=0`;\n      } catch (error) {\n        return Promise.reject(error);\n      }\n    })();\n  }\n  clearCookies() {\n    return _asyncToGenerator(function* () {\n      try {\n        const cookies = document.cookie.split(';') || [];\n        for (const cookie of cookies) {\n          document.cookie = cookie.replace(/^ +/, '').replace(/=.*/, `=;expires=${new Date().toUTCString()};path=/`);\n        }\n      } catch (error) {\n        return Promise.reject(error);\n      }\n    })();\n  }\n  clearAllCookies() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        yield _this4.clearCookies();\n      } catch (error) {\n        return Promise.reject(error);\n      }\n    })();\n  }\n}\nconst CapacitorCookies = registerPlugin('CapacitorCookies', {\n  web: () => new CapacitorCookiesPluginWeb()\n});\n// UTILITY FUNCTIONS\n/**\n * Read in a Blob value and return it as a base64 string\n * @param blob The blob value to convert to a base64 string\n */\nconst readBlobAsBase64 = /*#__PURE__*/function () {\n  var _ref6 = _asyncToGenerator(function* (blob) {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.onload = () => {\n        const base64String = reader.result;\n        // remove prefix \"data:application/pdf;base64,\"\n        resolve(base64String.indexOf(',') >= 0 ? base64String.split(',')[1] : base64String);\n      };\n      reader.onerror = error => reject(error);\n      reader.readAsDataURL(blob);\n    });\n  });\n  return function readBlobAsBase64(_x) {\n    return _ref6.apply(this, arguments);\n  };\n}();\n/**\n * Normalize an HttpHeaders map by lowercasing all of the values\n * @param headers The HttpHeaders object to normalize\n */\nconst normalizeHttpHeaders = (headers = {}) => {\n  const originalKeys = Object.keys(headers);\n  const loweredKeys = Object.keys(headers).map(k => k.toLocaleLowerCase());\n  const normalized = loweredKeys.reduce((acc, key, index) => {\n    acc[key] = headers[originalKeys[index]];\n    return acc;\n  }, {});\n  return normalized;\n};\n/**\n * Builds a string of url parameters that\n * @param params A map of url parameters\n * @param shouldEncode true if you should encodeURIComponent() the values (true by default)\n */\nconst buildUrlParams = (params, shouldEncode = true) => {\n  if (!params) return null;\n  const output = Object.entries(params).reduce((accumulator, entry) => {\n    const [key, value] = entry;\n    let encodedValue;\n    let item;\n    if (Array.isArray(value)) {\n      item = '';\n      value.forEach(str => {\n        encodedValue = shouldEncode ? encodeURIComponent(str) : str;\n        item += `${key}=${encodedValue}&`;\n      });\n      // last character will always be \"&\" so slice it off\n      item.slice(0, -1);\n    } else {\n      encodedValue = shouldEncode ? encodeURIComponent(value) : value;\n      item = `${key}=${encodedValue}`;\n    }\n    return `${accumulator}&${item}`;\n  }, '');\n  // Remove initial \"&\" from the reduce\n  return output.substr(1);\n};\n/**\n * Build the RequestInit object based on the options passed into the initial request\n * @param options The Http plugin options\n * @param extra Any extra RequestInit values\n */\nconst buildRequestInit = (options, extra = {}) => {\n  const output = Object.assign({\n    method: options.method || 'GET',\n    headers: options.headers\n  }, extra);\n  // Get the content-type\n  const headers = normalizeHttpHeaders(options.headers);\n  const type = headers['content-type'] || '';\n  // If body is already a string, then pass it through as-is.\n  if (typeof options.data === 'string') {\n    output.body = options.data;\n  }\n  // Build request initializers based off of content-type\n  else if (type.includes('application/x-www-form-urlencoded')) {\n    const params = new URLSearchParams();\n    for (const [key, value] of Object.entries(options.data || {})) {\n      params.set(key, value);\n    }\n    output.body = params.toString();\n  } else if (type.includes('multipart/form-data') || options.data instanceof FormData) {\n    const form = new FormData();\n    if (options.data instanceof FormData) {\n      options.data.forEach((value, key) => {\n        form.append(key, value);\n      });\n    } else {\n      for (const key of Object.keys(options.data)) {\n        form.append(key, options.data[key]);\n      }\n    }\n    output.body = form;\n    const headers = new Headers(output.headers);\n    headers.delete('content-type'); // content-type will be set by `window.fetch` to includy boundary\n    output.headers = headers;\n  } else if (type.includes('application/json') || typeof options.data === 'object') {\n    output.body = JSON.stringify(options.data);\n  }\n  return output;\n};\n// WEB IMPLEMENTATION\nclass CapacitorHttpPluginWeb extends WebPlugin {\n  /**\n   * Perform an Http request given a set of options\n   * @param options Options to build the HTTP request\n   */\n  request(options) {\n    return _asyncToGenerator(function* () {\n      const requestInit = buildRequestInit(options, options.webFetchExtra);\n      const urlParams = buildUrlParams(options.params, options.shouldEncodeUrlParams);\n      const url = urlParams ? `${options.url}?${urlParams}` : options.url;\n      const response = yield fetch(url, requestInit);\n      const contentType = response.headers.get('content-type') || '';\n      // Default to 'text' responseType so no parsing happens\n      let {\n        responseType = 'text'\n      } = response.ok ? options : {};\n      // If the response content-type is json, force the response to be json\n      if (contentType.includes('application/json')) {\n        responseType = 'json';\n      }\n      let data;\n      let blob;\n      switch (responseType) {\n        case 'arraybuffer':\n        case 'blob':\n          blob = yield response.blob();\n          data = yield readBlobAsBase64(blob);\n          break;\n        case 'json':\n          data = yield response.json();\n          break;\n        case 'document':\n        case 'text':\n        default:\n          data = yield response.text();\n      }\n      // Convert fetch headers to Capacitor HttpHeaders\n      const headers = {};\n      response.headers.forEach((value, key) => {\n        headers[key] = value;\n      });\n      return {\n        data,\n        headers,\n        status: response.status,\n        url: response.url\n      };\n    })();\n  }\n  /**\n   * Perform an Http GET request given a set of options\n   * @param options Options to build the HTTP request\n   */\n  get(options) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      return _this5.request(Object.assign(Object.assign({}, options), {\n        method: 'GET'\n      }));\n    })();\n  }\n  /**\n   * Perform an Http POST request given a set of options\n   * @param options Options to build the HTTP request\n   */\n  post(options) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      return _this6.request(Object.assign(Object.assign({}, options), {\n        method: 'POST'\n      }));\n    })();\n  }\n  /**\n   * Perform an Http PUT request given a set of options\n   * @param options Options to build the HTTP request\n   */\n  put(options) {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      return _this7.request(Object.assign(Object.assign({}, options), {\n        method: 'PUT'\n      }));\n    })();\n  }\n  /**\n   * Perform an Http PATCH request given a set of options\n   * @param options Options to build the HTTP request\n   */\n  patch(options) {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      return _this8.request(Object.assign(Object.assign({}, options), {\n        method: 'PATCH'\n      }));\n    })();\n  }\n  /**\n   * Perform an Http DELETE request given a set of options\n   * @param options Options to build the HTTP request\n   */\n  delete(options) {\n    var _this9 = this;\n    return _asyncToGenerator(function* () {\n      return _this9.request(Object.assign(Object.assign({}, options), {\n        method: 'DELETE'\n      }));\n    })();\n  }\n}\nconst CapacitorHttp = registerPlugin('CapacitorHttp', {\n  web: () => new CapacitorHttpPluginWeb()\n});\n/******** END HTTP PLUGIN ********/\n\nexport { Capacitor, CapacitorCookies, CapacitorException, CapacitorHttp, CapacitorPlatforms, ExceptionCode, Plugins, WebPlugin, WebView, addPlatform, buildRequestInit, registerPlugin, registerWebPlugin, setPlatform };", "map": {"version": 3, "names": ["createCapacitorPlatforms", "win", "defaultPlatformMap", "Map", "set", "name", "capPlatforms", "CapacitorPlatforms", "currentPlatform", "platforms", "addPlatform", "platform", "setPlatform", "has", "get", "initPlatforms", "globalThis", "self", "window", "global", "legacyRegisterWebPlugin", "cap", "webPlugin", "_a", "config", "Plugins", "Error", "console", "warn", "includes", "getPlatform", "ExceptionCode", "CapacitorException", "constructor", "message", "code", "data", "getPlatformId", "_b", "androidBridge", "webkit", "messageHandlers", "bridge", "createCapacitor", "_c", "_d", "_e", "capCustomPlatform", "CapacitorCustomPlatform", "Capacitor", "defaultGetPlatform", "defaultIsNativePlatform", "isNativePlatform", "defaultIsPluginAvailable", "pluginName", "plugin", "registeredPlugins", "getPluginHeader", "isPluginAvailable", "defaultGetPluginHeader", "PluginHeaders", "find", "h", "handleError", "err", "error", "pluginMethodNoop", "_target", "prop", "Promise", "reject", "defaultRegisterPlugin", "jsImplementations", "registeredPlugin", "proxy", "pluginHeader", "jsImplementation", "loadPluginImplementation", "_ref", "_asyncToGenerator", "apply", "arguments", "createPluginMethod", "impl", "methodHeader", "methods", "m", "rtype", "options", "nativePromise", "toString", "callback", "nativeCallback", "bind", "Unimplemented", "createPluginMethodWrapper", "remove", "wrapper", "args", "p", "then", "fn", "Object", "defineProperty", "value", "writable", "configurable", "addListener", "removeListener", "addListenerNative", "eventName", "call", "_ref3", "callbackId", "resolve", "Proxy", "_", "undefined", "Set", "keys", "registerPlugin", "convertFileSrc", "filePath", "Exception", "DEBUG", "isLoggingEnabled", "isNative", "initCapacitorGlobal", "registerWebPlugin", "WebPlugin", "listeners", "retainedEventArguments", "windowListeners", "listenerFunc", "_this", "firstListener", "push", "windowListener", "registered", "addWindowListener", "sendRetainedArgumentsForEvent", "_ref5", "removeAllListeners", "_this2", "listener", "removeWindowListener", "notifyListeners", "retainUntilConsumed", "for<PERSON>ach", "hasListeners", "length", "registerWindowListener", "windowEventName", "pluginEventName", "handler", "event", "unimplemented", "msg", "unavailable", "Unavailable", "_this3", "index", "indexOf", "splice", "handle", "addEventListener", "removeEventListener", "arg", "WebView", "encode", "str", "encodeURIComponent", "replace", "decodeURIComponent", "escape", "decode", "CapacitorCookiesPluginWeb", "getCookies", "cookies", "document", "cookie", "cookieMap", "split", "key", "trim", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "encodedValue", "expires", "path", "domain", "url", "deleteC<PERSON>ie", "clearCookies", "Date", "toUTCString", "clearAllCookies", "_this4", "CapacitorCookies", "web", "readBlobAsBase64", "_ref6", "blob", "reader", "FileReader", "onload", "base64String", "result", "onerror", "readAsDataURL", "_x", "normalizeHttpHeaders", "headers", "originalKeys", "<PERSON><PERSON><PERSON><PERSON>", "map", "k", "toLocaleLowerCase", "normalized", "reduce", "acc", "buildUrlParams", "params", "shouldEncode", "output", "entries", "accumulator", "entry", "item", "Array", "isArray", "slice", "substr", "buildRequestInit", "extra", "assign", "method", "type", "body", "URLSearchParams", "FormData", "form", "append", "Headers", "delete", "JSON", "stringify", "CapacitorHttpPluginWeb", "request", "requestInit", "webFetchExtra", "urlParams", "shouldEncodeUrlParams", "response", "fetch", "contentType", "responseType", "ok", "json", "text", "status", "_this5", "post", "_this6", "put", "_this7", "patch", "_this8", "_this9", "CapacitorHttp"], "sources": ["C:/Users/<USER>/Desktop/تطبيق ناشر ايات قئانية/QuranVidGen/node_modules/@capacitor/core/dist/index.js"], "sourcesContent": ["/*! Capacitor: https://capacitorjs.com/ - MIT License */\nconst createCapacitorPlatforms = (win) => {\n    const defaultPlatformMap = new Map();\n    defaultPlatformMap.set('web', { name: 'web' });\n    const capPlatforms = win.CapacitorPlatforms || {\n        currentPlatform: { name: 'web' },\n        platforms: defaultPlatformMap,\n    };\n    const addPlatform = (name, platform) => {\n        capPlatforms.platforms.set(name, platform);\n    };\n    const setPlatform = (name) => {\n        if (capPlatforms.platforms.has(name)) {\n            capPlatforms.currentPlatform = capPlatforms.platforms.get(name);\n        }\n    };\n    capPlatforms.addPlatform = addPlatform;\n    capPlatforms.setPlatform = setPlatform;\n    return capPlatforms;\n};\nconst initPlatforms = (win) => (win.CapacitorPlatforms = createCapacitorPlatforms(win));\n/**\n * @deprecated Set `CapacitorCustomPlatform` on the window object prior to runtime executing in the web app instead\n */\nconst CapacitorPlatforms = /*#__PURE__*/ initPlatforms((typeof globalThis !== 'undefined'\n    ? globalThis\n    : typeof self !== 'undefined'\n        ? self\n        : typeof window !== 'undefined'\n            ? window\n            : typeof global !== 'undefined'\n                ? global\n                : {}));\n/**\n * @deprecated Set `CapacitorCustomPlatform` on the window object prior to runtime executing in the web app instead\n */\nconst addPlatform = CapacitorPlatforms.addPlatform;\n/**\n * @deprecated Set `CapacitorCustomPlatform` on the window object prior to runtime executing in the web app instead\n */\nconst setPlatform = CapacitorPlatforms.setPlatform;\n\nconst legacyRegisterWebPlugin = (cap, webPlugin) => {\n    var _a;\n    const config = webPlugin.config;\n    const Plugins = cap.Plugins;\n    if (!(config === null || config === void 0 ? void 0 : config.name)) {\n        // TODO: add link to upgrade guide\n        throw new Error(`Capacitor WebPlugin is using the deprecated \"registerWebPlugin()\" function, but without the config. Please use \"registerPlugin()\" instead to register this web plugin.\"`);\n    }\n    // TODO: add link to upgrade guide\n    console.warn(`Capacitor plugin \"${config.name}\" is using the deprecated \"registerWebPlugin()\" function`);\n    if (!Plugins[config.name] || ((_a = config === null || config === void 0 ? void 0 : config.platforms) === null || _a === void 0 ? void 0 : _a.includes(cap.getPlatform()))) {\n        // Add the web plugin into the plugins registry if there already isn't\n        // an existing one. If it doesn't already exist, that means\n        // there's no existing native implementation for it.\n        // - OR -\n        // If we already have a plugin registered (meaning it was defined in the native layer),\n        // then we should only overwrite it if the corresponding web plugin activates on\n        // a certain platform. For example: Geolocation uses the WebPlugin on Android but not iOS\n        Plugins[config.name] = webPlugin;\n    }\n};\n\nvar ExceptionCode;\n(function (ExceptionCode) {\n    /**\n     * API is not implemented.\n     *\n     * This usually means the API can't be used because it is not implemented for\n     * the current platform.\n     */\n    ExceptionCode[\"Unimplemented\"] = \"UNIMPLEMENTED\";\n    /**\n     * API is not available.\n     *\n     * This means the API can't be used right now because:\n     *   - it is currently missing a prerequisite, such as network connectivity\n     *   - it requires a particular platform or browser version\n     */\n    ExceptionCode[\"Unavailable\"] = \"UNAVAILABLE\";\n})(ExceptionCode || (ExceptionCode = {}));\nclass CapacitorException extends Error {\n    constructor(message, code, data) {\n        super(message);\n        this.message = message;\n        this.code = code;\n        this.data = data;\n    }\n}\nconst getPlatformId = (win) => {\n    var _a, _b;\n    if (win === null || win === void 0 ? void 0 : win.androidBridge) {\n        return 'android';\n    }\n    else if ((_b = (_a = win === null || win === void 0 ? void 0 : win.webkit) === null || _a === void 0 ? void 0 : _a.messageHandlers) === null || _b === void 0 ? void 0 : _b.bridge) {\n        return 'ios';\n    }\n    else {\n        return 'web';\n    }\n};\n\nconst createCapacitor = (win) => {\n    var _a, _b, _c, _d, _e;\n    const capCustomPlatform = win.CapacitorCustomPlatform || null;\n    const cap = win.Capacitor || {};\n    const Plugins = (cap.Plugins = cap.Plugins || {});\n    /**\n     * @deprecated Use `capCustomPlatform` instead, default functions like registerPlugin will function with the new object.\n     */\n    const capPlatforms = win.CapacitorPlatforms;\n    const defaultGetPlatform = () => {\n        return capCustomPlatform !== null\n            ? capCustomPlatform.name\n            : getPlatformId(win);\n    };\n    const getPlatform = ((_a = capPlatforms === null || capPlatforms === void 0 ? void 0 : capPlatforms.currentPlatform) === null || _a === void 0 ? void 0 : _a.getPlatform) || defaultGetPlatform;\n    const defaultIsNativePlatform = () => getPlatform() !== 'web';\n    const isNativePlatform = ((_b = capPlatforms === null || capPlatforms === void 0 ? void 0 : capPlatforms.currentPlatform) === null || _b === void 0 ? void 0 : _b.isNativePlatform) || defaultIsNativePlatform;\n    const defaultIsPluginAvailable = (pluginName) => {\n        const plugin = registeredPlugins.get(pluginName);\n        if (plugin === null || plugin === void 0 ? void 0 : plugin.platforms.has(getPlatform())) {\n            // JS implementation available for the current platform.\n            return true;\n        }\n        if (getPluginHeader(pluginName)) {\n            // Native implementation available.\n            return true;\n        }\n        return false;\n    };\n    const isPluginAvailable = ((_c = capPlatforms === null || capPlatforms === void 0 ? void 0 : capPlatforms.currentPlatform) === null || _c === void 0 ? void 0 : _c.isPluginAvailable) ||\n        defaultIsPluginAvailable;\n    const defaultGetPluginHeader = (pluginName) => { var _a; return (_a = cap.PluginHeaders) === null || _a === void 0 ? void 0 : _a.find(h => h.name === pluginName); };\n    const getPluginHeader = ((_d = capPlatforms === null || capPlatforms === void 0 ? void 0 : capPlatforms.currentPlatform) === null || _d === void 0 ? void 0 : _d.getPluginHeader) || defaultGetPluginHeader;\n    const handleError = (err) => win.console.error(err);\n    const pluginMethodNoop = (_target, prop, pluginName) => {\n        return Promise.reject(`${pluginName} does not have an implementation of \"${prop}\".`);\n    };\n    const registeredPlugins = new Map();\n    const defaultRegisterPlugin = (pluginName, jsImplementations = {}) => {\n        const registeredPlugin = registeredPlugins.get(pluginName);\n        if (registeredPlugin) {\n            console.warn(`Capacitor plugin \"${pluginName}\" already registered. Cannot register plugins twice.`);\n            return registeredPlugin.proxy;\n        }\n        const platform = getPlatform();\n        const pluginHeader = getPluginHeader(pluginName);\n        let jsImplementation;\n        const loadPluginImplementation = async () => {\n            if (!jsImplementation && platform in jsImplementations) {\n                jsImplementation =\n                    typeof jsImplementations[platform] === 'function'\n                        ? (jsImplementation = await jsImplementations[platform]())\n                        : (jsImplementation = jsImplementations[platform]);\n            }\n            else if (capCustomPlatform !== null &&\n                !jsImplementation &&\n                'web' in jsImplementations) {\n                jsImplementation =\n                    typeof jsImplementations['web'] === 'function'\n                        ? (jsImplementation = await jsImplementations['web']())\n                        : (jsImplementation = jsImplementations['web']);\n            }\n            return jsImplementation;\n        };\n        const createPluginMethod = (impl, prop) => {\n            var _a, _b;\n            if (pluginHeader) {\n                const methodHeader = pluginHeader === null || pluginHeader === void 0 ? void 0 : pluginHeader.methods.find(m => prop === m.name);\n                if (methodHeader) {\n                    if (methodHeader.rtype === 'promise') {\n                        return (options) => cap.nativePromise(pluginName, prop.toString(), options);\n                    }\n                    else {\n                        return (options, callback) => cap.nativeCallback(pluginName, prop.toString(), options, callback);\n                    }\n                }\n                else if (impl) {\n                    return (_a = impl[prop]) === null || _a === void 0 ? void 0 : _a.bind(impl);\n                }\n            }\n            else if (impl) {\n                return (_b = impl[prop]) === null || _b === void 0 ? void 0 : _b.bind(impl);\n            }\n            else {\n                throw new CapacitorException(`\"${pluginName}\" plugin is not implemented on ${platform}`, ExceptionCode.Unimplemented);\n            }\n        };\n        const createPluginMethodWrapper = (prop) => {\n            let remove;\n            const wrapper = (...args) => {\n                const p = loadPluginImplementation().then(impl => {\n                    const fn = createPluginMethod(impl, prop);\n                    if (fn) {\n                        const p = fn(...args);\n                        remove = p === null || p === void 0 ? void 0 : p.remove;\n                        return p;\n                    }\n                    else {\n                        throw new CapacitorException(`\"${pluginName}.${prop}()\" is not implemented on ${platform}`, ExceptionCode.Unimplemented);\n                    }\n                });\n                if (prop === 'addListener') {\n                    p.remove = async () => remove();\n                }\n                return p;\n            };\n            // Some flair ✨\n            wrapper.toString = () => `${prop.toString()}() { [capacitor code] }`;\n            Object.defineProperty(wrapper, 'name', {\n                value: prop,\n                writable: false,\n                configurable: false,\n            });\n            return wrapper;\n        };\n        const addListener = createPluginMethodWrapper('addListener');\n        const removeListener = createPluginMethodWrapper('removeListener');\n        const addListenerNative = (eventName, callback) => {\n            const call = addListener({ eventName }, callback);\n            const remove = async () => {\n                const callbackId = await call;\n                removeListener({\n                    eventName,\n                    callbackId,\n                }, callback);\n            };\n            const p = new Promise(resolve => call.then(() => resolve({ remove })));\n            p.remove = async () => {\n                console.warn(`Using addListener() without 'await' is deprecated.`);\n                await remove();\n            };\n            return p;\n        };\n        const proxy = new Proxy({}, {\n            get(_, prop) {\n                switch (prop) {\n                    // https://github.com/facebook/react/issues/20030\n                    case '$$typeof':\n                        return undefined;\n                    case 'toJSON':\n                        return () => ({});\n                    case 'addListener':\n                        return pluginHeader ? addListenerNative : addListener;\n                    case 'removeListener':\n                        return removeListener;\n                    default:\n                        return createPluginMethodWrapper(prop);\n                }\n            },\n        });\n        Plugins[pluginName] = proxy;\n        registeredPlugins.set(pluginName, {\n            name: pluginName,\n            proxy,\n            platforms: new Set([\n                ...Object.keys(jsImplementations),\n                ...(pluginHeader ? [platform] : []),\n            ]),\n        });\n        return proxy;\n    };\n    const registerPlugin = ((_e = capPlatforms === null || capPlatforms === void 0 ? void 0 : capPlatforms.currentPlatform) === null || _e === void 0 ? void 0 : _e.registerPlugin) || defaultRegisterPlugin;\n    // Add in convertFileSrc for web, it will already be available in native context\n    if (!cap.convertFileSrc) {\n        cap.convertFileSrc = filePath => filePath;\n    }\n    cap.getPlatform = getPlatform;\n    cap.handleError = handleError;\n    cap.isNativePlatform = isNativePlatform;\n    cap.isPluginAvailable = isPluginAvailable;\n    cap.pluginMethodNoop = pluginMethodNoop;\n    cap.registerPlugin = registerPlugin;\n    cap.Exception = CapacitorException;\n    cap.DEBUG = !!cap.DEBUG;\n    cap.isLoggingEnabled = !!cap.isLoggingEnabled;\n    // Deprecated props\n    cap.platform = cap.getPlatform();\n    cap.isNative = cap.isNativePlatform();\n    return cap;\n};\nconst initCapacitorGlobal = (win) => (win.Capacitor = createCapacitor(win));\n\nconst Capacitor = /*#__PURE__*/ initCapacitorGlobal(typeof globalThis !== 'undefined'\n    ? globalThis\n    : typeof self !== 'undefined'\n        ? self\n        : typeof window !== 'undefined'\n            ? window\n            : typeof global !== 'undefined'\n                ? global\n                : {});\nconst registerPlugin = Capacitor.registerPlugin;\n/**\n * @deprecated Provided for backwards compatibility for Capacitor v2 plugins.\n * Capacitor v3 plugins should import the plugin directly. This \"Plugins\"\n * export is deprecated in v3, and will be removed in v4.\n */\nconst Plugins = Capacitor.Plugins;\n/**\n * Provided for backwards compatibility. Use the registerPlugin() API\n * instead, and provide the web plugin as the \"web\" implmenetation.\n * For example\n *\n * export const Example = registerPlugin('Example', {\n *   web: () => import('./web').then(m => new m.Example())\n * })\n *\n * @deprecated Deprecated in v3, will be removed from v4.\n */\nconst registerWebPlugin = (plugin) => legacyRegisterWebPlugin(Capacitor, plugin);\n\n/**\n * Base class web plugins should extend.\n */\nclass WebPlugin {\n    constructor(config) {\n        this.listeners = {};\n        this.retainedEventArguments = {};\n        this.windowListeners = {};\n        if (config) {\n            // TODO: add link to upgrade guide\n            console.warn(`Capacitor WebPlugin \"${config.name}\" config object was deprecated in v3 and will be removed in v4.`);\n            this.config = config;\n        }\n    }\n    addListener(eventName, listenerFunc) {\n        let firstListener = false;\n        const listeners = this.listeners[eventName];\n        if (!listeners) {\n            this.listeners[eventName] = [];\n            firstListener = true;\n        }\n        this.listeners[eventName].push(listenerFunc);\n        // If we haven't added a window listener for this event and it requires one,\n        // go ahead and add it\n        const windowListener = this.windowListeners[eventName];\n        if (windowListener && !windowListener.registered) {\n            this.addWindowListener(windowListener);\n        }\n        if (firstListener) {\n            this.sendRetainedArgumentsForEvent(eventName);\n        }\n        const remove = async () => this.removeListener(eventName, listenerFunc);\n        const p = Promise.resolve({ remove });\n        return p;\n    }\n    async removeAllListeners() {\n        this.listeners = {};\n        for (const listener in this.windowListeners) {\n            this.removeWindowListener(this.windowListeners[listener]);\n        }\n        this.windowListeners = {};\n    }\n    notifyListeners(eventName, data, retainUntilConsumed) {\n        const listeners = this.listeners[eventName];\n        if (!listeners) {\n            if (retainUntilConsumed) {\n                let args = this.retainedEventArguments[eventName];\n                if (!args) {\n                    args = [];\n                }\n                args.push(data);\n                this.retainedEventArguments[eventName] = args;\n            }\n            return;\n        }\n        listeners.forEach(listener => listener(data));\n    }\n    hasListeners(eventName) {\n        return !!this.listeners[eventName].length;\n    }\n    registerWindowListener(windowEventName, pluginEventName) {\n        this.windowListeners[pluginEventName] = {\n            registered: false,\n            windowEventName,\n            pluginEventName,\n            handler: event => {\n                this.notifyListeners(pluginEventName, event);\n            },\n        };\n    }\n    unimplemented(msg = 'not implemented') {\n        return new Capacitor.Exception(msg, ExceptionCode.Unimplemented);\n    }\n    unavailable(msg = 'not available') {\n        return new Capacitor.Exception(msg, ExceptionCode.Unavailable);\n    }\n    async removeListener(eventName, listenerFunc) {\n        const listeners = this.listeners[eventName];\n        if (!listeners) {\n            return;\n        }\n        const index = listeners.indexOf(listenerFunc);\n        this.listeners[eventName].splice(index, 1);\n        // If there are no more listeners for this type of event,\n        // remove the window listener\n        if (!this.listeners[eventName].length) {\n            this.removeWindowListener(this.windowListeners[eventName]);\n        }\n    }\n    addWindowListener(handle) {\n        window.addEventListener(handle.windowEventName, handle.handler);\n        handle.registered = true;\n    }\n    removeWindowListener(handle) {\n        if (!handle) {\n            return;\n        }\n        window.removeEventListener(handle.windowEventName, handle.handler);\n        handle.registered = false;\n    }\n    sendRetainedArgumentsForEvent(eventName) {\n        const args = this.retainedEventArguments[eventName];\n        if (!args) {\n            return;\n        }\n        delete this.retainedEventArguments[eventName];\n        args.forEach(arg => {\n            this.notifyListeners(eventName, arg);\n        });\n    }\n}\n\nconst WebView = /*#__PURE__*/ registerPlugin('WebView');\n/******** END WEB VIEW PLUGIN ********/\n/******** COOKIES PLUGIN ********/\n/**\n * Safely web encode a string value (inspired by js-cookie)\n * @param str The string value to encode\n */\nconst encode = (str) => encodeURIComponent(str)\n    .replace(/%(2[346B]|5E|60|7C)/g, decodeURIComponent)\n    .replace(/[()]/g, escape);\n/**\n * Safely web decode a string value (inspired by js-cookie)\n * @param str The string value to decode\n */\nconst decode = (str) => str.replace(/(%[\\dA-F]{2})+/gi, decodeURIComponent);\nclass CapacitorCookiesPluginWeb extends WebPlugin {\n    async getCookies() {\n        const cookies = document.cookie;\n        const cookieMap = {};\n        cookies.split(';').forEach(cookie => {\n            if (cookie.length <= 0)\n                return;\n            // Replace first \"=\" with CAP_COOKIE to prevent splitting on additional \"=\"\n            let [key, value] = cookie.replace(/=/, 'CAP_COOKIE').split('CAP_COOKIE');\n            key = decode(key).trim();\n            value = decode(value).trim();\n            cookieMap[key] = value;\n        });\n        return cookieMap;\n    }\n    async setCookie(options) {\n        try {\n            // Safely Encoded Key/Value\n            const encodedKey = encode(options.key);\n            const encodedValue = encode(options.value);\n            // Clean & sanitize options\n            const expires = `; expires=${(options.expires || '').replace('expires=', '')}`; // Default is \"; expires=\"\n            const path = (options.path || '/').replace('path=', ''); // Default is \"path=/\"\n            const domain = options.url != null && options.url.length > 0\n                ? `domain=${options.url}`\n                : '';\n            document.cookie = `${encodedKey}=${encodedValue || ''}${expires}; path=${path}; ${domain};`;\n        }\n        catch (error) {\n            return Promise.reject(error);\n        }\n    }\n    async deleteCookie(options) {\n        try {\n            document.cookie = `${options.key}=; Max-Age=0`;\n        }\n        catch (error) {\n            return Promise.reject(error);\n        }\n    }\n    async clearCookies() {\n        try {\n            const cookies = document.cookie.split(';') || [];\n            for (const cookie of cookies) {\n                document.cookie = cookie\n                    .replace(/^ +/, '')\n                    .replace(/=.*/, `=;expires=${new Date().toUTCString()};path=/`);\n            }\n        }\n        catch (error) {\n            return Promise.reject(error);\n        }\n    }\n    async clearAllCookies() {\n        try {\n            await this.clearCookies();\n        }\n        catch (error) {\n            return Promise.reject(error);\n        }\n    }\n}\nconst CapacitorCookies = registerPlugin('CapacitorCookies', {\n    web: () => new CapacitorCookiesPluginWeb(),\n});\n// UTILITY FUNCTIONS\n/**\n * Read in a Blob value and return it as a base64 string\n * @param blob The blob value to convert to a base64 string\n */\nconst readBlobAsBase64 = async (blob) => new Promise((resolve, reject) => {\n    const reader = new FileReader();\n    reader.onload = () => {\n        const base64String = reader.result;\n        // remove prefix \"data:application/pdf;base64,\"\n        resolve(base64String.indexOf(',') >= 0\n            ? base64String.split(',')[1]\n            : base64String);\n    };\n    reader.onerror = (error) => reject(error);\n    reader.readAsDataURL(blob);\n});\n/**\n * Normalize an HttpHeaders map by lowercasing all of the values\n * @param headers The HttpHeaders object to normalize\n */\nconst normalizeHttpHeaders = (headers = {}) => {\n    const originalKeys = Object.keys(headers);\n    const loweredKeys = Object.keys(headers).map(k => k.toLocaleLowerCase());\n    const normalized = loweredKeys.reduce((acc, key, index) => {\n        acc[key] = headers[originalKeys[index]];\n        return acc;\n    }, {});\n    return normalized;\n};\n/**\n * Builds a string of url parameters that\n * @param params A map of url parameters\n * @param shouldEncode true if you should encodeURIComponent() the values (true by default)\n */\nconst buildUrlParams = (params, shouldEncode = true) => {\n    if (!params)\n        return null;\n    const output = Object.entries(params).reduce((accumulator, entry) => {\n        const [key, value] = entry;\n        let encodedValue;\n        let item;\n        if (Array.isArray(value)) {\n            item = '';\n            value.forEach(str => {\n                encodedValue = shouldEncode ? encodeURIComponent(str) : str;\n                item += `${key}=${encodedValue}&`;\n            });\n            // last character will always be \"&\" so slice it off\n            item.slice(0, -1);\n        }\n        else {\n            encodedValue = shouldEncode ? encodeURIComponent(value) : value;\n            item = `${key}=${encodedValue}`;\n        }\n        return `${accumulator}&${item}`;\n    }, '');\n    // Remove initial \"&\" from the reduce\n    return output.substr(1);\n};\n/**\n * Build the RequestInit object based on the options passed into the initial request\n * @param options The Http plugin options\n * @param extra Any extra RequestInit values\n */\nconst buildRequestInit = (options, extra = {}) => {\n    const output = Object.assign({ method: options.method || 'GET', headers: options.headers }, extra);\n    // Get the content-type\n    const headers = normalizeHttpHeaders(options.headers);\n    const type = headers['content-type'] || '';\n    // If body is already a string, then pass it through as-is.\n    if (typeof options.data === 'string') {\n        output.body = options.data;\n    }\n    // Build request initializers based off of content-type\n    else if (type.includes('application/x-www-form-urlencoded')) {\n        const params = new URLSearchParams();\n        for (const [key, value] of Object.entries(options.data || {})) {\n            params.set(key, value);\n        }\n        output.body = params.toString();\n    }\n    else if (type.includes('multipart/form-data') ||\n        options.data instanceof FormData) {\n        const form = new FormData();\n        if (options.data instanceof FormData) {\n            options.data.forEach((value, key) => {\n                form.append(key, value);\n            });\n        }\n        else {\n            for (const key of Object.keys(options.data)) {\n                form.append(key, options.data[key]);\n            }\n        }\n        output.body = form;\n        const headers = new Headers(output.headers);\n        headers.delete('content-type'); // content-type will be set by `window.fetch` to includy boundary\n        output.headers = headers;\n    }\n    else if (type.includes('application/json') ||\n        typeof options.data === 'object') {\n        output.body = JSON.stringify(options.data);\n    }\n    return output;\n};\n// WEB IMPLEMENTATION\nclass CapacitorHttpPluginWeb extends WebPlugin {\n    /**\n     * Perform an Http request given a set of options\n     * @param options Options to build the HTTP request\n     */\n    async request(options) {\n        const requestInit = buildRequestInit(options, options.webFetchExtra);\n        const urlParams = buildUrlParams(options.params, options.shouldEncodeUrlParams);\n        const url = urlParams ? `${options.url}?${urlParams}` : options.url;\n        const response = await fetch(url, requestInit);\n        const contentType = response.headers.get('content-type') || '';\n        // Default to 'text' responseType so no parsing happens\n        let { responseType = 'text' } = response.ok ? options : {};\n        // If the response content-type is json, force the response to be json\n        if (contentType.includes('application/json')) {\n            responseType = 'json';\n        }\n        let data;\n        let blob;\n        switch (responseType) {\n            case 'arraybuffer':\n            case 'blob':\n                blob = await response.blob();\n                data = await readBlobAsBase64(blob);\n                break;\n            case 'json':\n                data = await response.json();\n                break;\n            case 'document':\n            case 'text':\n            default:\n                data = await response.text();\n        }\n        // Convert fetch headers to Capacitor HttpHeaders\n        const headers = {};\n        response.headers.forEach((value, key) => {\n            headers[key] = value;\n        });\n        return {\n            data,\n            headers,\n            status: response.status,\n            url: response.url,\n        };\n    }\n    /**\n     * Perform an Http GET request given a set of options\n     * @param options Options to build the HTTP request\n     */\n    async get(options) {\n        return this.request(Object.assign(Object.assign({}, options), { method: 'GET' }));\n    }\n    /**\n     * Perform an Http POST request given a set of options\n     * @param options Options to build the HTTP request\n     */\n    async post(options) {\n        return this.request(Object.assign(Object.assign({}, options), { method: 'POST' }));\n    }\n    /**\n     * Perform an Http PUT request given a set of options\n     * @param options Options to build the HTTP request\n     */\n    async put(options) {\n        return this.request(Object.assign(Object.assign({}, options), { method: 'PUT' }));\n    }\n    /**\n     * Perform an Http PATCH request given a set of options\n     * @param options Options to build the HTTP request\n     */\n    async patch(options) {\n        return this.request(Object.assign(Object.assign({}, options), { method: 'PATCH' }));\n    }\n    /**\n     * Perform an Http DELETE request given a set of options\n     * @param options Options to build the HTTP request\n     */\n    async delete(options) {\n        return this.request(Object.assign(Object.assign({}, options), { method: 'DELETE' }));\n    }\n}\nconst CapacitorHttp = registerPlugin('CapacitorHttp', {\n    web: () => new CapacitorHttpPluginWeb(),\n});\n/******** END HTTP PLUGIN ********/\n\nexport { Capacitor, CapacitorCookies, CapacitorException, CapacitorHttp, CapacitorPlatforms, ExceptionCode, Plugins, WebPlugin, WebView, addPlatform, buildRequestInit, registerPlugin, registerWebPlugin, setPlatform };\n"], "mappings": ";AAAA;AACA,MAAMA,wBAAwB,GAAIC,GAAG,IAAK;EACtC,MAAMC,kBAAkB,GAAG,IAAIC,GAAG,CAAC,CAAC;EACpCD,kBAAkB,CAACE,GAAG,CAAC,KAAK,EAAE;IAAEC,IAAI,EAAE;EAAM,CAAC,CAAC;EAC9C,MAAMC,YAAY,GAAGL,GAAG,CAACM,kBAAkB,IAAI;IAC3CC,eAAe,EAAE;MAAEH,IAAI,EAAE;IAAM,CAAC;IAChCI,SAAS,EAAEP;EACf,CAAC;EACD,MAAMQ,WAAW,GAAGA,CAACL,IAAI,EAAEM,QAAQ,KAAK;IACpCL,YAAY,CAACG,SAAS,CAACL,GAAG,CAACC,IAAI,EAAEM,QAAQ,CAAC;EAC9C,CAAC;EACD,MAAMC,WAAW,GAAIP,IAAI,IAAK;IAC1B,IAAIC,YAAY,CAACG,SAAS,CAACI,GAAG,CAACR,IAAI,CAAC,EAAE;MAClCC,YAAY,CAACE,eAAe,GAAGF,YAAY,CAACG,SAAS,CAACK,GAAG,CAACT,IAAI,CAAC;IACnE;EACJ,CAAC;EACDC,YAAY,CAACI,WAAW,GAAGA,WAAW;EACtCJ,YAAY,CAACM,WAAW,GAAGA,WAAW;EACtC,OAAON,YAAY;AACvB,CAAC;AACD,MAAMS,aAAa,GAAId,GAAG,IAAMA,GAAG,CAACM,kBAAkB,GAAGP,wBAAwB,CAACC,GAAG,CAAE;AACvF;AACA;AACA;AACA,MAAMM,kBAAkB,GAAG,aAAcQ,aAAa,CAAE,OAAOC,UAAU,KAAK,WAAW,GACnFA,UAAU,GACV,OAAOC,IAAI,KAAK,WAAW,GACvBA,IAAI,GACJ,OAAOC,MAAM,KAAK,WAAW,GACzBA,MAAM,GACN,OAAOC,MAAM,KAAK,WAAW,GACzBA,MAAM,GACN,CAAC,CAAE,CAAC;AACtB;AACA;AACA;AACA,MAAMT,WAAW,GAAGH,kBAAkB,CAACG,WAAW;AAClD;AACA;AACA;AACA,MAAME,WAAW,GAAGL,kBAAkB,CAACK,WAAW;AAElD,MAAMQ,uBAAuB,GAAGA,CAACC,GAAG,EAAEC,SAAS,KAAK;EAChD,IAAIC,EAAE;EACN,MAAMC,MAAM,GAAGF,SAAS,CAACE,MAAM;EAC/B,MAAMC,OAAO,GAAGJ,GAAG,CAACI,OAAO;EAC3B,IAAI,EAAED,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACnB,IAAI,CAAC,EAAE;IAChE;IACA,MAAM,IAAIqB,KAAK,CAAE,yKAAwK,CAAC;EAC9L;EACA;EACAC,OAAO,CAACC,IAAI,CAAE,qBAAoBJ,MAAM,CAACnB,IAAK,0DAAyD,CAAC;EACxG,IAAI,CAACoB,OAAO,CAACD,MAAM,CAACnB,IAAI,CAAC,KAAK,CAACkB,EAAE,GAAGC,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACf,SAAS,MAAM,IAAI,IAAIc,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACM,QAAQ,CAACR,GAAG,CAACS,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;IACxK;IACA;IACA;IACA;IACA;IACA;IACA;IACAL,OAAO,CAACD,MAAM,CAACnB,IAAI,CAAC,GAAGiB,SAAS;EACpC;AACJ,CAAC;AAED,IAAIS,aAAa;AACjB,CAAC,UAAUA,aAAa,EAAE;EACtB;AACJ;AACA;AACA;AACA;AACA;EACIA,aAAa,CAAC,eAAe,CAAC,GAAG,eAAe;EAChD;AACJ;AACA;AACA;AACA;AACA;AACA;EACIA,aAAa,CAAC,aAAa,CAAC,GAAG,aAAa;AAChD,CAAC,EAAEA,aAAa,KAAKA,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC;AACzC,MAAMC,kBAAkB,SAASN,KAAK,CAAC;EACnCO,WAAWA,CAACC,OAAO,EAAEC,IAAI,EAAEC,IAAI,EAAE;IAC7B,KAAK,CAACF,OAAO,CAAC;IACd,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,IAAI,GAAGA,IAAI;EACpB;AACJ;AACA,MAAMC,aAAa,GAAIpC,GAAG,IAAK;EAC3B,IAAIsB,EAAE,EAAEe,EAAE;EACV,IAAIrC,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACsC,aAAa,EAAE;IAC7D,OAAO,SAAS;EACpB,CAAC,MACI,IAAI,CAACD,EAAE,GAAG,CAACf,EAAE,GAAGtB,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACuC,MAAM,MAAM,IAAI,IAAIjB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACkB,eAAe,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,MAAM,EAAE;IAChL,OAAO,KAAK;EAChB,CAAC,MACI;IACD,OAAO,KAAK;EAChB;AACJ,CAAC;AAED,MAAMC,eAAe,GAAI1C,GAAG,IAAK;EAC7B,IAAIsB,EAAE,EAAEe,EAAE,EAAEM,EAAE,EAAEC,EAAE,EAAEC,EAAE;EACtB,MAAMC,iBAAiB,GAAG9C,GAAG,CAAC+C,uBAAuB,IAAI,IAAI;EAC7D,MAAM3B,GAAG,GAAGpB,GAAG,CAACgD,SAAS,IAAI,CAAC,CAAC;EAC/B,MAAMxB,OAAO,GAAIJ,GAAG,CAACI,OAAO,GAAGJ,GAAG,CAACI,OAAO,IAAI,CAAC,CAAE;EACjD;AACJ;AACA;EACI,MAAMnB,YAAY,GAAGL,GAAG,CAACM,kBAAkB;EAC3C,MAAM2C,kBAAkB,GAAGA,CAAA,KAAM;IAC7B,OAAOH,iBAAiB,KAAK,IAAI,GAC3BA,iBAAiB,CAAC1C,IAAI,GACtBgC,aAAa,CAACpC,GAAG,CAAC;EAC5B,CAAC;EACD,MAAM6B,WAAW,GAAG,CAAC,CAACP,EAAE,GAAGjB,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACE,eAAe,MAAM,IAAI,IAAIe,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACO,WAAW,KAAKoB,kBAAkB;EAC/L,MAAMC,uBAAuB,GAAGA,CAAA,KAAMrB,WAAW,CAAC,CAAC,KAAK,KAAK;EAC7D,MAAMsB,gBAAgB,GAAG,CAAC,CAACd,EAAE,GAAGhC,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACE,eAAe,MAAM,IAAI,IAAI8B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACc,gBAAgB,KAAKD,uBAAuB;EAC9M,MAAME,wBAAwB,GAAIC,UAAU,IAAK;IAC7C,MAAMC,MAAM,GAAGC,iBAAiB,CAAC1C,GAAG,CAACwC,UAAU,CAAC;IAChD,IAAIC,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC9C,SAAS,CAACI,GAAG,CAACiB,WAAW,CAAC,CAAC,CAAC,EAAE;MACrF;MACA,OAAO,IAAI;IACf;IACA,IAAI2B,eAAe,CAACH,UAAU,CAAC,EAAE;MAC7B;MACA,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB,CAAC;EACD,MAAMI,iBAAiB,GAAG,CAAC,CAACd,EAAE,GAAGtC,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACE,eAAe,MAAM,IAAI,IAAIoC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACc,iBAAiB,KAChLL,wBAAwB;EAC5B,MAAMM,sBAAsB,GAAIL,UAAU,IAAK;IAAE,IAAI/B,EAAE;IAAE,OAAO,CAACA,EAAE,GAAGF,GAAG,CAACuC,aAAa,MAAM,IAAI,IAAIrC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACzD,IAAI,KAAKiD,UAAU,CAAC;EAAE,CAAC;EACpK,MAAMG,eAAe,GAAG,CAAC,CAACZ,EAAE,GAAGvC,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACE,eAAe,MAAM,IAAI,IAAIqC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACY,eAAe,KAAKE,sBAAsB;EAC3M,MAAMI,WAAW,GAAIC,GAAG,IAAK/D,GAAG,CAAC0B,OAAO,CAACsC,KAAK,CAACD,GAAG,CAAC;EACnD,MAAME,gBAAgB,GAAGA,CAACC,OAAO,EAAEC,IAAI,EAAEd,UAAU,KAAK;IACpD,OAAOe,OAAO,CAACC,MAAM,CAAE,GAAEhB,UAAW,wCAAuCc,IAAK,IAAG,CAAC;EACxF,CAAC;EACD,MAAMZ,iBAAiB,GAAG,IAAIrD,GAAG,CAAC,CAAC;EACnC,MAAMoE,qBAAqB,GAAGA,CAACjB,UAAU,EAAEkB,iBAAiB,GAAG,CAAC,CAAC,KAAK;IAClE,MAAMC,gBAAgB,GAAGjB,iBAAiB,CAAC1C,GAAG,CAACwC,UAAU,CAAC;IAC1D,IAAImB,gBAAgB,EAAE;MAClB9C,OAAO,CAACC,IAAI,CAAE,qBAAoB0B,UAAW,sDAAqD,CAAC;MACnG,OAAOmB,gBAAgB,CAACC,KAAK;IACjC;IACA,MAAM/D,QAAQ,GAAGmB,WAAW,CAAC,CAAC;IAC9B,MAAM6C,YAAY,GAAGlB,eAAe,CAACH,UAAU,CAAC;IAChD,IAAIsB,gBAAgB;IACpB,MAAMC,wBAAwB;MAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,aAAY;QACzC,IAAI,CAACH,gBAAgB,IAAIjE,QAAQ,IAAI6D,iBAAiB,EAAE;UACpDI,gBAAgB,GACZ,OAAOJ,iBAAiB,CAAC7D,QAAQ,CAAC,KAAK,UAAU,GAC1CiE,gBAAgB,SAASJ,iBAAiB,CAAC7D,QAAQ,CAAC,CAAC,CAAC,GACtDiE,gBAAgB,GAAGJ,iBAAiB,CAAC7D,QAAQ,CAAE;QAC9D,CAAC,MACI,IAAIoC,iBAAiB,KAAK,IAAI,IAC/B,CAAC6B,gBAAgB,IACjB,KAAK,IAAIJ,iBAAiB,EAAE;UAC5BI,gBAAgB,GACZ,OAAOJ,iBAAiB,CAAC,KAAK,CAAC,KAAK,UAAU,GACvCI,gBAAgB,SAASJ,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,GACnDI,gBAAgB,GAAGJ,iBAAiB,CAAC,KAAK,CAAE;QAC3D;QACA,OAAOI,gBAAgB;MAC3B,CAAC;MAAA,gBAhBKC,wBAAwBA,CAAA;QAAA,OAAAC,IAAA,CAAAE,KAAA,OAAAC,SAAA;MAAA;IAAA,GAgB7B;IACD,MAAMC,kBAAkB,GAAGA,CAACC,IAAI,EAAEf,IAAI,KAAK;MACvC,IAAI7C,EAAE,EAAEe,EAAE;MACV,IAAIqC,YAAY,EAAE;QACd,MAAMS,YAAY,GAAGT,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACU,OAAO,CAACxB,IAAI,CAACyB,CAAC,IAAIlB,IAAI,KAAKkB,CAAC,CAACjF,IAAI,CAAC;QAChI,IAAI+E,YAAY,EAAE;UACd,IAAIA,YAAY,CAACG,KAAK,KAAK,SAAS,EAAE;YAClC,OAAQC,OAAO,IAAKnE,GAAG,CAACoE,aAAa,CAACnC,UAAU,EAAEc,IAAI,CAACsB,QAAQ,CAAC,CAAC,EAAEF,OAAO,CAAC;UAC/E,CAAC,MACI;YACD,OAAO,CAACA,OAAO,EAAEG,QAAQ,KAAKtE,GAAG,CAACuE,cAAc,CAACtC,UAAU,EAAEc,IAAI,CAACsB,QAAQ,CAAC,CAAC,EAAEF,OAAO,EAAEG,QAAQ,CAAC;UACpG;QACJ,CAAC,MACI,IAAIR,IAAI,EAAE;UACX,OAAO,CAAC5D,EAAE,GAAG4D,IAAI,CAACf,IAAI,CAAC,MAAM,IAAI,IAAI7C,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsE,IAAI,CAACV,IAAI,CAAC;QAC/E;MACJ,CAAC,MACI,IAAIA,IAAI,EAAE;QACX,OAAO,CAAC7C,EAAE,GAAG6C,IAAI,CAACf,IAAI,CAAC,MAAM,IAAI,IAAI9B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuD,IAAI,CAACV,IAAI,CAAC;MAC/E,CAAC,MACI;QACD,MAAM,IAAInD,kBAAkB,CAAE,IAAGsB,UAAW,kCAAiC3C,QAAS,EAAC,EAAEoB,aAAa,CAAC+D,aAAa,CAAC;MACzH;IACJ,CAAC;IACD,MAAMC,yBAAyB,GAAI3B,IAAI,IAAK;MACxC,IAAI4B,MAAM;MACV,MAAMC,OAAO,GAAGA,CAAC,GAAGC,IAAI,KAAK;QACzB,MAAMC,CAAC,GAAGtB,wBAAwB,CAAC,CAAC,CAACuB,IAAI,CAACjB,IAAI,IAAI;UAC9C,MAAMkB,EAAE,GAAGnB,kBAAkB,CAACC,IAAI,EAAEf,IAAI,CAAC;UACzC,IAAIiC,EAAE,EAAE;YACJ,MAAMF,CAAC,GAAGE,EAAE,CAAC,GAAGH,IAAI,CAAC;YACrBF,MAAM,GAAGG,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,CAAC,CAACH,MAAM;YACvD,OAAOG,CAAC;UACZ,CAAC,MACI;YACD,MAAM,IAAInE,kBAAkB,CAAE,IAAGsB,UAAW,IAAGc,IAAK,6BAA4BzD,QAAS,EAAC,EAAEoB,aAAa,CAAC+D,aAAa,CAAC;UAC5H;QACJ,CAAC,CAAC;QACF,IAAI1B,IAAI,KAAK,aAAa,EAAE;UACxB+B,CAAC,CAACH,MAAM,gBAAAjB,iBAAA,CAAG;YAAA,OAAYiB,MAAM,CAAC,CAAC;UAAA;QACnC;QACA,OAAOG,CAAC;MACZ,CAAC;MACD;MACAF,OAAO,CAACP,QAAQ,GAAG,MAAO,GAAEtB,IAAI,CAACsB,QAAQ,CAAC,CAAE,yBAAwB;MACpEY,MAAM,CAACC,cAAc,CAACN,OAAO,EAAE,MAAM,EAAE;QACnCO,KAAK,EAAEpC,IAAI;QACXqC,QAAQ,EAAE,KAAK;QACfC,YAAY,EAAE;MAClB,CAAC,CAAC;MACF,OAAOT,OAAO;IAClB,CAAC;IACD,MAAMU,WAAW,GAAGZ,yBAAyB,CAAC,aAAa,CAAC;IAC5D,MAAMa,cAAc,GAAGb,yBAAyB,CAAC,gBAAgB,CAAC;IAClE,MAAMc,iBAAiB,GAAGA,CAACC,SAAS,EAAEnB,QAAQ,KAAK;MAC/C,MAAMoB,IAAI,GAAGJ,WAAW,CAAC;QAAEG;MAAU,CAAC,EAAEnB,QAAQ,CAAC;MACjD,MAAMK,MAAM;QAAA,IAAAgB,KAAA,GAAAjC,iBAAA,CAAG,aAAY;UACvB,MAAMkC,UAAU,SAASF,IAAI;UAC7BH,cAAc,CAAC;YACXE,SAAS;YACTG;UACJ,CAAC,EAAEtB,QAAQ,CAAC;QAChB,CAAC;QAAA,gBANKK,MAAMA,CAAA;UAAA,OAAAgB,KAAA,CAAAhC,KAAA,OAAAC,SAAA;QAAA;MAAA,GAMX;MACD,MAAMkB,CAAC,GAAG,IAAI9B,OAAO,CAAC6C,OAAO,IAAIH,IAAI,CAACX,IAAI,CAAC,MAAMc,OAAO,CAAC;QAAElB;MAAO,CAAC,CAAC,CAAC,CAAC;MACtEG,CAAC,CAACH,MAAM,gBAAAjB,iBAAA,CAAG,aAAY;QACnBpD,OAAO,CAACC,IAAI,CAAE,oDAAmD,CAAC;QAClE,MAAMoE,MAAM,CAAC,CAAC;MAClB,CAAC;MACD,OAAOG,CAAC;IACZ,CAAC;IACD,MAAMzB,KAAK,GAAG,IAAIyC,KAAK,CAAC,CAAC,CAAC,EAAE;MACxBrG,GAAGA,CAACsG,CAAC,EAAEhD,IAAI,EAAE;QACT,QAAQA,IAAI;UACR;UACA,KAAK,UAAU;YACX,OAAOiD,SAAS;UACpB,KAAK,QAAQ;YACT,OAAO,OAAO,CAAC,CAAC,CAAC;UACrB,KAAK,aAAa;YACd,OAAO1C,YAAY,GAAGkC,iBAAiB,GAAGF,WAAW;UACzD,KAAK,gBAAgB;YACjB,OAAOC,cAAc;UACzB;YACI,OAAOb,yBAAyB,CAAC3B,IAAI,CAAC;QAC9C;MACJ;IACJ,CAAC,CAAC;IACF3C,OAAO,CAAC6B,UAAU,CAAC,GAAGoB,KAAK;IAC3BlB,iBAAiB,CAACpD,GAAG,CAACkD,UAAU,EAAE;MAC9BjD,IAAI,EAAEiD,UAAU;MAChBoB,KAAK;MACLjE,SAAS,EAAE,IAAI6G,GAAG,CAAC,CACf,GAAGhB,MAAM,CAACiB,IAAI,CAAC/C,iBAAiB,CAAC,EACjC,IAAIG,YAAY,GAAG,CAAChE,QAAQ,CAAC,GAAG,EAAE,CAAC,CACtC;IACL,CAAC,CAAC;IACF,OAAO+D,KAAK;EAChB,CAAC;EACD,MAAM8C,cAAc,GAAG,CAAC,CAAC1E,EAAE,GAAGxC,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACE,eAAe,MAAM,IAAI,IAAIsC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC0E,cAAc,KAAKjD,qBAAqB;EACxM;EACA,IAAI,CAAClD,GAAG,CAACoG,cAAc,EAAE;IACrBpG,GAAG,CAACoG,cAAc,GAAGC,QAAQ,IAAIA,QAAQ;EAC7C;EACArG,GAAG,CAACS,WAAW,GAAGA,WAAW;EAC7BT,GAAG,CAAC0C,WAAW,GAAGA,WAAW;EAC7B1C,GAAG,CAAC+B,gBAAgB,GAAGA,gBAAgB;EACvC/B,GAAG,CAACqC,iBAAiB,GAAGA,iBAAiB;EACzCrC,GAAG,CAAC6C,gBAAgB,GAAGA,gBAAgB;EACvC7C,GAAG,CAACmG,cAAc,GAAGA,cAAc;EACnCnG,GAAG,CAACsG,SAAS,GAAG3F,kBAAkB;EAClCX,GAAG,CAACuG,KAAK,GAAG,CAAC,CAACvG,GAAG,CAACuG,KAAK;EACvBvG,GAAG,CAACwG,gBAAgB,GAAG,CAAC,CAACxG,GAAG,CAACwG,gBAAgB;EAC7C;EACAxG,GAAG,CAACV,QAAQ,GAAGU,GAAG,CAACS,WAAW,CAAC,CAAC;EAChCT,GAAG,CAACyG,QAAQ,GAAGzG,GAAG,CAAC+B,gBAAgB,CAAC,CAAC;EACrC,OAAO/B,GAAG;AACd,CAAC;AACD,MAAM0G,mBAAmB,GAAI9H,GAAG,IAAMA,GAAG,CAACgD,SAAS,GAAGN,eAAe,CAAC1C,GAAG,CAAE;AAE3E,MAAMgD,SAAS,GAAG,aAAc8E,mBAAmB,CAAC,OAAO/G,UAAU,KAAK,WAAW,GAC/EA,UAAU,GACV,OAAOC,IAAI,KAAK,WAAW,GACvBA,IAAI,GACJ,OAAOC,MAAM,KAAK,WAAW,GACzBA,MAAM,GACN,OAAOC,MAAM,KAAK,WAAW,GACzBA,MAAM,GACN,CAAC,CAAC,CAAC;AACrB,MAAMqG,cAAc,GAAGvE,SAAS,CAACuE,cAAc;AAC/C;AACA;AACA;AACA;AACA;AACA,MAAM/F,OAAO,GAAGwB,SAAS,CAACxB,OAAO;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuG,iBAAiB,GAAIzE,MAAM,IAAKnC,uBAAuB,CAAC6B,SAAS,EAAEM,MAAM,CAAC;;AAEhF;AACA;AACA;AACA,MAAM0E,SAAS,CAAC;EACZhG,WAAWA,CAACT,MAAM,EAAE;IAChB,IAAI,CAAC0G,SAAS,GAAG,CAAC,CAAC;IACnB,IAAI,CAACC,sBAAsB,GAAG,CAAC,CAAC;IAChC,IAAI,CAACC,eAAe,GAAG,CAAC,CAAC;IACzB,IAAI5G,MAAM,EAAE;MACR;MACAG,OAAO,CAACC,IAAI,CAAE,wBAAuBJ,MAAM,CAACnB,IAAK,iEAAgE,CAAC;MAClH,IAAI,CAACmB,MAAM,GAAGA,MAAM;IACxB;EACJ;EACAmF,WAAWA,CAACG,SAAS,EAAEuB,YAAY,EAAE;IAAA,IAAAC,KAAA;IACjC,IAAIC,aAAa,GAAG,KAAK;IACzB,MAAML,SAAS,GAAG,IAAI,CAACA,SAAS,CAACpB,SAAS,CAAC;IAC3C,IAAI,CAACoB,SAAS,EAAE;MACZ,IAAI,CAACA,SAAS,CAACpB,SAAS,CAAC,GAAG,EAAE;MAC9ByB,aAAa,GAAG,IAAI;IACxB;IACA,IAAI,CAACL,SAAS,CAACpB,SAAS,CAAC,CAAC0B,IAAI,CAACH,YAAY,CAAC;IAC5C;IACA;IACA,MAAMI,cAAc,GAAG,IAAI,CAACL,eAAe,CAACtB,SAAS,CAAC;IACtD,IAAI2B,cAAc,IAAI,CAACA,cAAc,CAACC,UAAU,EAAE;MAC9C,IAAI,CAACC,iBAAiB,CAACF,cAAc,CAAC;IAC1C;IACA,IAAIF,aAAa,EAAE;MACf,IAAI,CAACK,6BAA6B,CAAC9B,SAAS,CAAC;IACjD;IACA,MAAMd,MAAM;MAAA,IAAA6C,KAAA,GAAA9D,iBAAA,CAAG;QAAA,OAAYuD,KAAI,CAAC1B,cAAc,CAACE,SAAS,EAAEuB,YAAY,CAAC;MAAA;MAAA,gBAAjErC,MAAMA,CAAA;QAAA,OAAA6C,KAAA,CAAA7D,KAAA,OAAAC,SAAA;MAAA;IAAA,GAA2D;IACvE,MAAMkB,CAAC,GAAG9B,OAAO,CAAC6C,OAAO,CAAC;MAAElB;IAAO,CAAC,CAAC;IACrC,OAAOG,CAAC;EACZ;EACM2C,kBAAkBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAhE,iBAAA;MACvBgE,MAAI,CAACb,SAAS,GAAG,CAAC,CAAC;MACnB,KAAK,MAAMc,QAAQ,IAAID,MAAI,CAACX,eAAe,EAAE;QACzCW,MAAI,CAACE,oBAAoB,CAACF,MAAI,CAACX,eAAe,CAACY,QAAQ,CAAC,CAAC;MAC7D;MACAD,MAAI,CAACX,eAAe,GAAG,CAAC,CAAC;IAAC;EAC9B;EACAc,eAAeA,CAACpC,SAAS,EAAE1E,IAAI,EAAE+G,mBAAmB,EAAE;IAClD,MAAMjB,SAAS,GAAG,IAAI,CAACA,SAAS,CAACpB,SAAS,CAAC;IAC3C,IAAI,CAACoB,SAAS,EAAE;MACZ,IAAIiB,mBAAmB,EAAE;QACrB,IAAIjD,IAAI,GAAG,IAAI,CAACiC,sBAAsB,CAACrB,SAAS,CAAC;QACjD,IAAI,CAACZ,IAAI,EAAE;UACPA,IAAI,GAAG,EAAE;QACb;QACAA,IAAI,CAACsC,IAAI,CAACpG,IAAI,CAAC;QACf,IAAI,CAAC+F,sBAAsB,CAACrB,SAAS,CAAC,GAAGZ,IAAI;MACjD;MACA;IACJ;IACAgC,SAAS,CAACkB,OAAO,CAACJ,QAAQ,IAAIA,QAAQ,CAAC5G,IAAI,CAAC,CAAC;EACjD;EACAiH,YAAYA,CAACvC,SAAS,EAAE;IACpB,OAAO,CAAC,CAAC,IAAI,CAACoB,SAAS,CAACpB,SAAS,CAAC,CAACwC,MAAM;EAC7C;EACAC,sBAAsBA,CAACC,eAAe,EAAEC,eAAe,EAAE;IACrD,IAAI,CAACrB,eAAe,CAACqB,eAAe,CAAC,GAAG;MACpCf,UAAU,EAAE,KAAK;MACjBc,eAAe;MACfC,eAAe;MACfC,OAAO,EAAEC,KAAK,IAAI;QACd,IAAI,CAACT,eAAe,CAACO,eAAe,EAAEE,KAAK,CAAC;MAChD;IACJ,CAAC;EACL;EACAC,aAAaA,CAACC,GAAG,GAAG,iBAAiB,EAAE;IACnC,OAAO,IAAI5G,SAAS,CAAC0E,SAAS,CAACkC,GAAG,EAAE9H,aAAa,CAAC+D,aAAa,CAAC;EACpE;EACAgE,WAAWA,CAACD,GAAG,GAAG,eAAe,EAAE;IAC/B,OAAO,IAAI5G,SAAS,CAAC0E,SAAS,CAACkC,GAAG,EAAE9H,aAAa,CAACgI,WAAW,CAAC;EAClE;EACMnD,cAAcA,CAACE,SAAS,EAAEuB,YAAY,EAAE;IAAA,IAAA2B,MAAA;IAAA,OAAAjF,iBAAA;MAC1C,MAAMmD,SAAS,GAAG8B,MAAI,CAAC9B,SAAS,CAACpB,SAAS,CAAC;MAC3C,IAAI,CAACoB,SAAS,EAAE;QACZ;MACJ;MACA,MAAM+B,KAAK,GAAG/B,SAAS,CAACgC,OAAO,CAAC7B,YAAY,CAAC;MAC7C2B,MAAI,CAAC9B,SAAS,CAACpB,SAAS,CAAC,CAACqD,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAC1C;MACA;MACA,IAAI,CAACD,MAAI,CAAC9B,SAAS,CAACpB,SAAS,CAAC,CAACwC,MAAM,EAAE;QACnCU,MAAI,CAACf,oBAAoB,CAACe,MAAI,CAAC5B,eAAe,CAACtB,SAAS,CAAC,CAAC;MAC9D;IAAC;EACL;EACA6B,iBAAiBA,CAACyB,MAAM,EAAE;IACtBlJ,MAAM,CAACmJ,gBAAgB,CAACD,MAAM,CAACZ,eAAe,EAAEY,MAAM,CAACV,OAAO,CAAC;IAC/DU,MAAM,CAAC1B,UAAU,GAAG,IAAI;EAC5B;EACAO,oBAAoBA,CAACmB,MAAM,EAAE;IACzB,IAAI,CAACA,MAAM,EAAE;MACT;IACJ;IACAlJ,MAAM,CAACoJ,mBAAmB,CAACF,MAAM,CAACZ,eAAe,EAAEY,MAAM,CAACV,OAAO,CAAC;IAClEU,MAAM,CAAC1B,UAAU,GAAG,KAAK;EAC7B;EACAE,6BAA6BA,CAAC9B,SAAS,EAAE;IACrC,MAAMZ,IAAI,GAAG,IAAI,CAACiC,sBAAsB,CAACrB,SAAS,CAAC;IACnD,IAAI,CAACZ,IAAI,EAAE;MACP;IACJ;IACA,OAAO,IAAI,CAACiC,sBAAsB,CAACrB,SAAS,CAAC;IAC7CZ,IAAI,CAACkD,OAAO,CAACmB,GAAG,IAAI;MAChB,IAAI,CAACrB,eAAe,CAACpC,SAAS,EAAEyD,GAAG,CAAC;IACxC,CAAC,CAAC;EACN;AACJ;AAEA,MAAMC,OAAO,GAAG,aAAchD,cAAc,CAAC,SAAS,CAAC;AACvD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiD,MAAM,GAAIC,GAAG,IAAKC,kBAAkB,CAACD,GAAG,CAAC,CAC1CE,OAAO,CAAC,sBAAsB,EAAEC,kBAAkB,CAAC,CACnDD,OAAO,CAAC,OAAO,EAAEE,MAAM,CAAC;AAC7B;AACA;AACA;AACA;AACA,MAAMC,MAAM,GAAIL,GAAG,IAAKA,GAAG,CAACE,OAAO,CAAC,kBAAkB,EAAEC,kBAAkB,CAAC;AAC3E,MAAMG,yBAAyB,SAAS/C,SAAS,CAAC;EACxCgD,UAAUA,CAAA,EAAG;IAAA,OAAAlG,iBAAA;MACf,MAAMmG,OAAO,GAAGC,QAAQ,CAACC,MAAM;MAC/B,MAAMC,SAAS,GAAG,CAAC,CAAC;MACpBH,OAAO,CAACI,KAAK,CAAC,GAAG,CAAC,CAAClC,OAAO,CAACgC,MAAM,IAAI;QACjC,IAAIA,MAAM,CAAC9B,MAAM,IAAI,CAAC,EAClB;QACJ;QACA,IAAI,CAACiC,GAAG,EAAE/E,KAAK,CAAC,GAAG4E,MAAM,CAACR,OAAO,CAAC,GAAG,EAAE,YAAY,CAAC,CAACU,KAAK,CAAC,YAAY,CAAC;QACxEC,GAAG,GAAGR,MAAM,CAACQ,GAAG,CAAC,CAACC,IAAI,CAAC,CAAC;QACxBhF,KAAK,GAAGuE,MAAM,CAACvE,KAAK,CAAC,CAACgF,IAAI,CAAC,CAAC;QAC5BH,SAAS,CAACE,GAAG,CAAC,GAAG/E,KAAK;MAC1B,CAAC,CAAC;MACF,OAAO6E,SAAS;IAAC;EACrB;EACMI,SAASA,CAACjG,OAAO,EAAE;IAAA,OAAAT,iBAAA;MACrB,IAAI;QACA;QACA,MAAM2G,UAAU,GAAGjB,MAAM,CAACjF,OAAO,CAAC+F,GAAG,CAAC;QACtC,MAAMI,YAAY,GAAGlB,MAAM,CAACjF,OAAO,CAACgB,KAAK,CAAC;QAC1C;QACA,MAAMoF,OAAO,GAAI,aAAY,CAACpG,OAAO,CAACoG,OAAO,IAAI,EAAE,EAAEhB,OAAO,CAAC,UAAU,EAAE,EAAE,CAAE,EAAC,CAAC,CAAC;QAChF,MAAMiB,IAAI,GAAG,CAACrG,OAAO,CAACqG,IAAI,IAAI,GAAG,EAAEjB,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;QACzD,MAAMkB,MAAM,GAAGtG,OAAO,CAACuG,GAAG,IAAI,IAAI,IAAIvG,OAAO,CAACuG,GAAG,CAACzC,MAAM,GAAG,CAAC,GACrD,UAAS9D,OAAO,CAACuG,GAAI,EAAC,GACvB,EAAE;QACRZ,QAAQ,CAACC,MAAM,GAAI,GAAEM,UAAW,IAAGC,YAAY,IAAI,EAAG,GAAEC,OAAQ,UAASC,IAAK,KAAIC,MAAO,GAAE;MAC/F,CAAC,CACD,OAAO7H,KAAK,EAAE;QACV,OAAOI,OAAO,CAACC,MAAM,CAACL,KAAK,CAAC;MAChC;IAAC;EACL;EACM+H,YAAYA,CAACxG,OAAO,EAAE;IAAA,OAAAT,iBAAA;MACxB,IAAI;QACAoG,QAAQ,CAACC,MAAM,GAAI,GAAE5F,OAAO,CAAC+F,GAAI,cAAa;MAClD,CAAC,CACD,OAAOtH,KAAK,EAAE;QACV,OAAOI,OAAO,CAACC,MAAM,CAACL,KAAK,CAAC;MAChC;IAAC;EACL;EACMgI,YAAYA,CAAA,EAAG;IAAA,OAAAlH,iBAAA;MACjB,IAAI;QACA,MAAMmG,OAAO,GAAGC,QAAQ,CAACC,MAAM,CAACE,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE;QAChD,KAAK,MAAMF,MAAM,IAAIF,OAAO,EAAE;UAC1BC,QAAQ,CAACC,MAAM,GAAGA,MAAM,CACnBR,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAClBA,OAAO,CAAC,KAAK,EAAG,aAAY,IAAIsB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAE,SAAQ,CAAC;QACvE;MACJ,CAAC,CACD,OAAOlI,KAAK,EAAE;QACV,OAAOI,OAAO,CAACC,MAAM,CAACL,KAAK,CAAC;MAChC;IAAC;EACL;EACMmI,eAAeA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAtH,iBAAA;MACpB,IAAI;QACA,MAAMsH,MAAI,CAACJ,YAAY,CAAC,CAAC;MAC7B,CAAC,CACD,OAAOhI,KAAK,EAAE;QACV,OAAOI,OAAO,CAACC,MAAM,CAACL,KAAK,CAAC;MAChC;IAAC;EACL;AACJ;AACA,MAAMqI,gBAAgB,GAAG9E,cAAc,CAAC,kBAAkB,EAAE;EACxD+E,GAAG,EAAEA,CAAA,KAAM,IAAIvB,yBAAyB,CAAC;AAC7C,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,MAAMwB,gBAAgB;EAAA,IAAAC,KAAA,GAAA1H,iBAAA,CAAG,WAAO2H,IAAI;IAAA,OAAK,IAAIrI,OAAO,CAAC,CAAC6C,OAAO,EAAE5C,MAAM,KAAK;MACtE,MAAMqI,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAG,MAAM;QAClB,MAAMC,YAAY,GAAGH,MAAM,CAACI,MAAM;QAClC;QACA7F,OAAO,CAAC4F,YAAY,CAAC5C,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,GAChC4C,YAAY,CAACxB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAC1BwB,YAAY,CAAC;MACvB,CAAC;MACDH,MAAM,CAACK,OAAO,GAAI/I,KAAK,IAAKK,MAAM,CAACL,KAAK,CAAC;MACzC0I,MAAM,CAACM,aAAa,CAACP,IAAI,CAAC;IAC9B,CAAC,CAAC;EAAA;EAAA,gBAXIF,gBAAgBA,CAAAU,EAAA;IAAA,OAAAT,KAAA,CAAAzH,KAAA,OAAAC,SAAA;EAAA;AAAA,GAWpB;AACF;AACA;AACA;AACA;AACA,MAAMkI,oBAAoB,GAAGA,CAACC,OAAO,GAAG,CAAC,CAAC,KAAK;EAC3C,MAAMC,YAAY,GAAG/G,MAAM,CAACiB,IAAI,CAAC6F,OAAO,CAAC;EACzC,MAAME,WAAW,GAAGhH,MAAM,CAACiB,IAAI,CAAC6F,OAAO,CAAC,CAACG,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,iBAAiB,CAAC,CAAC,CAAC;EACxE,MAAMC,UAAU,GAAGJ,WAAW,CAACK,MAAM,CAAC,CAACC,GAAG,EAAErC,GAAG,EAAEtB,KAAK,KAAK;IACvD2D,GAAG,CAACrC,GAAG,CAAC,GAAG6B,OAAO,CAACC,YAAY,CAACpD,KAAK,CAAC,CAAC;IACvC,OAAO2D,GAAG;EACd,CAAC,EAAE,CAAC,CAAC,CAAC;EACN,OAAOF,UAAU;AACrB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMG,cAAc,GAAGA,CAACC,MAAM,EAAEC,YAAY,GAAG,IAAI,KAAK;EACpD,IAAI,CAACD,MAAM,EACP,OAAO,IAAI;EACf,MAAME,MAAM,GAAG1H,MAAM,CAAC2H,OAAO,CAACH,MAAM,CAAC,CAACH,MAAM,CAAC,CAACO,WAAW,EAAEC,KAAK,KAAK;IACjE,MAAM,CAAC5C,GAAG,EAAE/E,KAAK,CAAC,GAAG2H,KAAK;IAC1B,IAAIxC,YAAY;IAChB,IAAIyC,IAAI;IACR,IAAIC,KAAK,CAACC,OAAO,CAAC9H,KAAK,CAAC,EAAE;MACtB4H,IAAI,GAAG,EAAE;MACT5H,KAAK,CAAC4C,OAAO,CAACsB,GAAG,IAAI;QACjBiB,YAAY,GAAGoC,YAAY,GAAGpD,kBAAkB,CAACD,GAAG,CAAC,GAAGA,GAAG;QAC3D0D,IAAI,IAAK,GAAE7C,GAAI,IAAGI,YAAa,GAAE;MACrC,CAAC,CAAC;MACF;MACAyC,IAAI,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACrB,CAAC,MACI;MACD5C,YAAY,GAAGoC,YAAY,GAAGpD,kBAAkB,CAACnE,KAAK,CAAC,GAAGA,KAAK;MAC/D4H,IAAI,GAAI,GAAE7C,GAAI,IAAGI,YAAa,EAAC;IACnC;IACA,OAAQ,GAAEuC,WAAY,IAAGE,IAAK,EAAC;EACnC,CAAC,EAAE,EAAE,CAAC;EACN;EACA,OAAOJ,MAAM,CAACQ,MAAM,CAAC,CAAC,CAAC;AAC3B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,GAAGA,CAACjJ,OAAO,EAAEkJ,KAAK,GAAG,CAAC,CAAC,KAAK;EAC9C,MAAMV,MAAM,GAAG1H,MAAM,CAACqI,MAAM,CAAC;IAAEC,MAAM,EAAEpJ,OAAO,CAACoJ,MAAM,IAAI,KAAK;IAAExB,OAAO,EAAE5H,OAAO,CAAC4H;EAAQ,CAAC,EAAEsB,KAAK,CAAC;EAClG;EACA,MAAMtB,OAAO,GAAGD,oBAAoB,CAAC3H,OAAO,CAAC4H,OAAO,CAAC;EACrD,MAAMyB,IAAI,GAAGzB,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE;EAC1C;EACA,IAAI,OAAO5H,OAAO,CAACpD,IAAI,KAAK,QAAQ,EAAE;IAClC4L,MAAM,CAACc,IAAI,GAAGtJ,OAAO,CAACpD,IAAI;EAC9B;EACA;EAAA,KACK,IAAIyM,IAAI,CAAChN,QAAQ,CAAC,mCAAmC,CAAC,EAAE;IACzD,MAAMiM,MAAM,GAAG,IAAIiB,eAAe,CAAC,CAAC;IACpC,KAAK,MAAM,CAACxD,GAAG,EAAE/E,KAAK,CAAC,IAAIF,MAAM,CAAC2H,OAAO,CAACzI,OAAO,CAACpD,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE;MAC3D0L,MAAM,CAAC1N,GAAG,CAACmL,GAAG,EAAE/E,KAAK,CAAC;IAC1B;IACAwH,MAAM,CAACc,IAAI,GAAGhB,MAAM,CAACpI,QAAQ,CAAC,CAAC;EACnC,CAAC,MACI,IAAImJ,IAAI,CAAChN,QAAQ,CAAC,qBAAqB,CAAC,IACzC2D,OAAO,CAACpD,IAAI,YAAY4M,QAAQ,EAAE;IAClC,MAAMC,IAAI,GAAG,IAAID,QAAQ,CAAC,CAAC;IAC3B,IAAIxJ,OAAO,CAACpD,IAAI,YAAY4M,QAAQ,EAAE;MAClCxJ,OAAO,CAACpD,IAAI,CAACgH,OAAO,CAAC,CAAC5C,KAAK,EAAE+E,GAAG,KAAK;QACjC0D,IAAI,CAACC,MAAM,CAAC3D,GAAG,EAAE/E,KAAK,CAAC;MAC3B,CAAC,CAAC;IACN,CAAC,MACI;MACD,KAAK,MAAM+E,GAAG,IAAIjF,MAAM,CAACiB,IAAI,CAAC/B,OAAO,CAACpD,IAAI,CAAC,EAAE;QACzC6M,IAAI,CAACC,MAAM,CAAC3D,GAAG,EAAE/F,OAAO,CAACpD,IAAI,CAACmJ,GAAG,CAAC,CAAC;MACvC;IACJ;IACAyC,MAAM,CAACc,IAAI,GAAGG,IAAI;IAClB,MAAM7B,OAAO,GAAG,IAAI+B,OAAO,CAACnB,MAAM,CAACZ,OAAO,CAAC;IAC3CA,OAAO,CAACgC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC;IAChCpB,MAAM,CAACZ,OAAO,GAAGA,OAAO;EAC5B,CAAC,MACI,IAAIyB,IAAI,CAAChN,QAAQ,CAAC,kBAAkB,CAAC,IACtC,OAAO2D,OAAO,CAACpD,IAAI,KAAK,QAAQ,EAAE;IAClC4L,MAAM,CAACc,IAAI,GAAGO,IAAI,CAACC,SAAS,CAAC9J,OAAO,CAACpD,IAAI,CAAC;EAC9C;EACA,OAAO4L,MAAM;AACjB,CAAC;AACD;AACA,MAAMuB,sBAAsB,SAAStH,SAAS,CAAC;EAC3C;AACJ;AACA;AACA;EACUuH,OAAOA,CAAChK,OAAO,EAAE;IAAA,OAAAT,iBAAA;MACnB,MAAM0K,WAAW,GAAGhB,gBAAgB,CAACjJ,OAAO,EAAEA,OAAO,CAACkK,aAAa,CAAC;MACpE,MAAMC,SAAS,GAAG9B,cAAc,CAACrI,OAAO,CAACsI,MAAM,EAAEtI,OAAO,CAACoK,qBAAqB,CAAC;MAC/E,MAAM7D,GAAG,GAAG4D,SAAS,GAAI,GAAEnK,OAAO,CAACuG,GAAI,IAAG4D,SAAU,EAAC,GAAGnK,OAAO,CAACuG,GAAG;MACnE,MAAM8D,QAAQ,SAASC,KAAK,CAAC/D,GAAG,EAAE0D,WAAW,CAAC;MAC9C,MAAMM,WAAW,GAAGF,QAAQ,CAACzC,OAAO,CAACtM,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE;MAC9D;MACA,IAAI;QAAEkP,YAAY,GAAG;MAAO,CAAC,GAAGH,QAAQ,CAACI,EAAE,GAAGzK,OAAO,GAAG,CAAC,CAAC;MAC1D;MACA,IAAIuK,WAAW,CAAClO,QAAQ,CAAC,kBAAkB,CAAC,EAAE;QAC1CmO,YAAY,GAAG,MAAM;MACzB;MACA,IAAI5N,IAAI;MACR,IAAIsK,IAAI;MACR,QAAQsD,YAAY;QAChB,KAAK,aAAa;QAClB,KAAK,MAAM;UACPtD,IAAI,SAASmD,QAAQ,CAACnD,IAAI,CAAC,CAAC;UAC5BtK,IAAI,SAASoK,gBAAgB,CAACE,IAAI,CAAC;UACnC;QACJ,KAAK,MAAM;UACPtK,IAAI,SAASyN,QAAQ,CAACK,IAAI,CAAC,CAAC;UAC5B;QACJ,KAAK,UAAU;QACf,KAAK,MAAM;QACX;UACI9N,IAAI,SAASyN,QAAQ,CAACM,IAAI,CAAC,CAAC;MACpC;MACA;MACA,MAAM/C,OAAO,GAAG,CAAC,CAAC;MAClByC,QAAQ,CAACzC,OAAO,CAAChE,OAAO,CAAC,CAAC5C,KAAK,EAAE+E,GAAG,KAAK;QACrC6B,OAAO,CAAC7B,GAAG,CAAC,GAAG/E,KAAK;MACxB,CAAC,CAAC;MACF,OAAO;QACHpE,IAAI;QACJgL,OAAO;QACPgD,MAAM,EAAEP,QAAQ,CAACO,MAAM;QACvBrE,GAAG,EAAE8D,QAAQ,CAAC9D;MAClB,CAAC;IAAC;EACN;EACA;AACJ;AACA;AACA;EACUjL,GAAGA,CAAC0E,OAAO,EAAE;IAAA,IAAA6K,MAAA;IAAA,OAAAtL,iBAAA;MACf,OAAOsL,MAAI,CAACb,OAAO,CAAClJ,MAAM,CAACqI,MAAM,CAACrI,MAAM,CAACqI,MAAM,CAAC,CAAC,CAAC,EAAEnJ,OAAO,CAAC,EAAE;QAAEoJ,MAAM,EAAE;MAAM,CAAC,CAAC,CAAC;IAAC;EACtF;EACA;AACJ;AACA;AACA;EACU0B,IAAIA,CAAC9K,OAAO,EAAE;IAAA,IAAA+K,MAAA;IAAA,OAAAxL,iBAAA;MAChB,OAAOwL,MAAI,CAACf,OAAO,CAAClJ,MAAM,CAACqI,MAAM,CAACrI,MAAM,CAACqI,MAAM,CAAC,CAAC,CAAC,EAAEnJ,OAAO,CAAC,EAAE;QAAEoJ,MAAM,EAAE;MAAO,CAAC,CAAC,CAAC;IAAC;EACvF;EACA;AACJ;AACA;AACA;EACU4B,GAAGA,CAAChL,OAAO,EAAE;IAAA,IAAAiL,MAAA;IAAA,OAAA1L,iBAAA;MACf,OAAO0L,MAAI,CAACjB,OAAO,CAAClJ,MAAM,CAACqI,MAAM,CAACrI,MAAM,CAACqI,MAAM,CAAC,CAAC,CAAC,EAAEnJ,OAAO,CAAC,EAAE;QAAEoJ,MAAM,EAAE;MAAM,CAAC,CAAC,CAAC;IAAC;EACtF;EACA;AACJ;AACA;AACA;EACU8B,KAAKA,CAAClL,OAAO,EAAE;IAAA,IAAAmL,MAAA;IAAA,OAAA5L,iBAAA;MACjB,OAAO4L,MAAI,CAACnB,OAAO,CAAClJ,MAAM,CAACqI,MAAM,CAACrI,MAAM,CAACqI,MAAM,CAAC,CAAC,CAAC,EAAEnJ,OAAO,CAAC,EAAE;QAAEoJ,MAAM,EAAE;MAAQ,CAAC,CAAC,CAAC;IAAC;EACxF;EACA;AACJ;AACA;AACA;EACUQ,MAAMA,CAAC5J,OAAO,EAAE;IAAA,IAAAoL,MAAA;IAAA,OAAA7L,iBAAA;MAClB,OAAO6L,MAAI,CAACpB,OAAO,CAAClJ,MAAM,CAACqI,MAAM,CAACrI,MAAM,CAACqI,MAAM,CAAC,CAAC,CAAC,EAAEnJ,OAAO,CAAC,EAAE;QAAEoJ,MAAM,EAAE;MAAS,CAAC,CAAC,CAAC;IAAC;EACzF;AACJ;AACA,MAAMiC,aAAa,GAAGrJ,cAAc,CAAC,eAAe,EAAE;EAClD+E,GAAG,EAAEA,CAAA,KAAM,IAAIgD,sBAAsB,CAAC;AAC1C,CAAC,CAAC;AACF;;AAEA,SAAStM,SAAS,EAAEqJ,gBAAgB,EAAEtK,kBAAkB,EAAE6O,aAAa,EAAEtQ,kBAAkB,EAAEwB,aAAa,EAAEN,OAAO,EAAEwG,SAAS,EAAEuC,OAAO,EAAE9J,WAAW,EAAE+N,gBAAgB,EAAEjH,cAAc,EAAEQ,iBAAiB,EAAEpH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}