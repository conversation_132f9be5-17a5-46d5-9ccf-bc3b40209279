import { Component, EventEmitter, Input, Output } from '@angular/core';
import { YoutubeService, YouTubeAuthStatus } from '../../Services/youtube.service';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-auth-dialog',
  templateUrl: './auth-dialog.component.html',
  styleUrls: ['./auth-dialog.component.scss']
})
export class AuthDialogComponent {
  @Input() visible: boolean = false;
  @Output() visibleChange = new EventEmitter<boolean>();
  @Output() authSuccess = new EventEmitter<void>();

  authStatus$: Observable<YouTubeAuthStatus>;
  isSigningIn = false;
  errorMessage = '';

  constructor(private youtubeService: YoutubeService) {
    this.authStatus$ = this.youtubeService.authStatus$;
  }

  signIn(): void {
    this.isSigningIn = true;
    this.errorMessage = '';
    
    this.youtubeService.signIn().subscribe({
      next: (success) => {
        this.isSigningIn = false;
        if (success) {
          this.authSuccess.emit();
          this.closeDialog();
        }
      },
      error: (error) => {
        this.isSigningIn = false;
        this.errorMessage = 'فشل في تسجيل الدخول. يرجى المحاولة مرة أخرى.';
        console.error('Sign in error:', error);
      }
    });
  }

  signOut(): void {
    this.youtubeService.signOut().subscribe({
      next: () => {
        this.closeDialog();
      },
      error: (error) => {
        this.errorMessage = 'فشل في تسجيل الخروج.';
        console.error('Sign out error:', error);
      }
    });
  }

  closeDialog(): void {
    this.visible = false;
    this.visibleChange.emit(false);
    this.errorMessage = '';
  }
}
