{"ast": null, "code": "import { zip as zipStatic } from '../observable/zip';\nimport { operate } from '../util/lift';\nexport function zip(...sources) {\n  return operate((source, subscriber) => {\n    zipStatic(source, ...sources).subscribe(subscriber);\n  });\n}", "map": {"version": 3, "names": ["zip", "zipStatic", "operate", "sources", "source", "subscriber", "subscribe"], "sources": ["C:/Users/<USER>/Desktop/تطبيق ناشر ايات قئانية/QuranVidGen/node_modules/rxjs/dist/esm/internal/operators/zip.js"], "sourcesContent": ["import { zip as zipStatic } from '../observable/zip';\nimport { operate } from '../util/lift';\nexport function zip(...sources) {\n    return operate((source, subscriber) => {\n        zipStatic(source, ...sources).subscribe(subscriber);\n    });\n}\n"], "mappings": "AAAA,SAASA,GAAG,IAAIC,SAAS,QAAQ,mBAAmB;AACpD,SAASC,OAAO,QAAQ,cAAc;AACtC,OAAO,SAASF,GAAGA,CAAC,GAAGG,OAAO,EAAE;EAC5B,OAAOD,OAAO,CAAC,CAACE,MAAM,EAAEC,UAAU,KAAK;IACnCJ,SAAS,CAACG,MAAM,EAAE,GAAGD,OAAO,CAAC,CAACG,SAAS,CAACD,UAAU,CAAC;EACvD,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}