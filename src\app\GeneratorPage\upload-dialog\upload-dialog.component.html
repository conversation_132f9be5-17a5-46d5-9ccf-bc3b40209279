<p-dialog
  header="رفع إلى YouTube"
  [(visible)]="visible"
  [modal]="true"
  [closable]="true"
  [resizable]="false"
  styleClass="upload-dialog"
  [style]="{width: '500px'}"
  (onHide)="onHide()">

  <div class="upload-container">
    
    <!-- Upload Form -->
    <form [formGroup]="uploadForm" *ngIf="!isUploading && !isUploadComplete">
      <div class="form-group">
        <label for="title">عنوان الفيديو</label>
        <input
          id="title"
          type="text"
          formControlName="title"
          pInputText
          placeholder="أدخل عنوان الفيديو"
          class="w-100">
      </div>

      <div class="form-group">
        <label for="description">وصف الفيديو</label>
        <textarea
          id="description"
          formControlName="description"
          pInputTextarea
          rows="4"
          placeholder="أدخل وصف الفيديو"
          class="w-100">
        </textarea>
      </div>

      <div class="form-group">
        <label for="tags">الكلمات المفتاحية</label>
        <input
          id="tags"
          type="text"
          formControlName="tags"
          pInputText
          placeholder="قرآن، تلاوة، إسلام"
          class="w-100">
      </div>
    </form>

    <!-- Upload Progress -->
    <div class="upload-progress" *ngIf="isUploading">
      <div class="progress-icon">
        <i class="pi pi-spin pi-spinner"></i>
      </div>
      <h3>جاري رفع الفيديو...</h3>
      <p-progressBar [value]="uploadProgress"></p-progressBar>
      <p class="progress-text">{{ uploadProgress }}% مكتمل</p>
    </div>

    <!-- Upload Complete -->
    <div class="upload-complete" *ngIf="isUploadComplete">
      <div class="success-icon">
        <i class="pi pi-check-circle"></i>
      </div>
      <h3>تم رفع الفيديو بنجاح!</h3>
      <p>يمكنك الآن مشاهدة الفيديو على YouTube</p>
      <a [href]="videoUrl" target="_blank" class="video-link" *ngIf="videoUrl">
        مشاهدة الفيديو
      </a>
    </div>

  </div>

  <ng-template pTemplate="footer">
    <div class="dialog-footer">
      <p-button
        *ngIf="!isUploading && !isUploadComplete"
        label="رفع"
        icon="pi pi-upload"
        severity="primary"
        [disabled]="!uploadForm.valid"
        (onClick)="onUpload()">
      </p-button>

      <p-button
        *ngIf="!isUploading"
        label="إلغاء"
        icon="pi pi-times"
        severity="secondary"
        [outlined]="true"
        (onClick)="onHide()">
      </p-button>
    </div>
  </ng-template>
</p-dialog>
