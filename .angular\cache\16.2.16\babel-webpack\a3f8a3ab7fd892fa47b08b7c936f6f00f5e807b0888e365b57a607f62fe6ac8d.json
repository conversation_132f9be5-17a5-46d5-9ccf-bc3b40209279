{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../Services/youtube.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"primeng/button\";\nimport * as i4 from \"primeng/dialog\";\nfunction AuthDialogComponent_div_2_div_1_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.errorMessage, \" \");\n  }\n}\nfunction AuthDialogComponent_div_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 5);\n    i0.ɵɵelement(2, \"i\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\", 7);\n    i0.ɵɵtext(4, \"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644 \\u0625\\u0644\\u0649 YouTube\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 8);\n    i0.ɵɵtext(6, \" \\u064A\\u0631\\u062C\\u0649 \\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644 \\u0625\\u0644\\u0649 \\u062D\\u0633\\u0627\\u0628 Google \\u0627\\u0644\\u062E\\u0627\\u0635 \\u0628\\u0643 \\u0644\\u062A\\u062A\\u0645\\u0643\\u0646 \\u0645\\u0646 \\u0646\\u0634\\u0631 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\\u0647\\u0627\\u062A \\u0639\\u0644\\u0649 \\u0642\\u0646\\u0627\\u062A\\u0643 \\u0641\\u064A YouTube \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, AuthDialogComponent_div_2_div_1_div_7_Template, 2, 1, \"div\", 9);\n    i0.ɵɵelementStart(8, \"p-button\", 10);\n    i0.ɵɵlistener(\"onClick\", function AuthDialogComponent_div_2_div_1_Template_p_button_onClick_8_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.signIn());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 11)(10, \"small\", 12);\n    i0.ɵɵelement(11, \"i\", 13);\n    i0.ɵɵtext(12, \" \\u0633\\u064A\\u062A\\u0645 \\u0637\\u0644\\u0628 \\u0625\\u0630\\u0646 \\u0644\\u0644\\u0648\\u0635\\u0648\\u0644 \\u0625\\u0644\\u0649 \\u0642\\u0646\\u0627\\u0629 YouTube \\u0627\\u0644\\u062E\\u0627\\u0635\\u0629 \\u0628\\u0643 \\u0644\\u0631\\u0641\\u0639 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\\u0647\\u0627\\u062A \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.errorMessage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"loading\", ctx_r2.isSigningIn);\n  }\n}\nfunction AuthDialogComponent_div_2_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 7);\n    i0.ɵɵelement(2, \"img\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h5\", 16);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 17);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 18);\n    i0.ɵɵelement(8, \"i\", 19);\n    i0.ɵɵtext(9, \" \\u062A\\u0645 \\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644 \\u0628\\u0646\\u062C\\u0627\\u062D! \\u064A\\u0645\\u0643\\u0646\\u0643 \\u0627\\u0644\\u0622\\u0646 \\u0646\\u0634\\u0631 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\\u0647\\u0627\\u062A \\u0639\\u0644\\u0649 YouTube \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 20)(11, \"p-button\", 21);\n    i0.ɵɵlistener(\"onClick\", function AuthDialogComponent_div_2_div_2_Template_p_button_onClick_11_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r7.signOut());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p-button\", 22);\n    i0.ɵɵlistener(\"onClick\", function AuthDialogComponent_div_2_div_2_Template_p_button_onClick_12_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.closeDialog());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const authStatus_r1 = i0.ɵɵnextContext().ngIf;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", authStatus_r1.user.picture, i0.ɵɵsanitizeUrl)(\"alt\", authStatus_r1.user.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u0645\\u0631\\u062D\\u0628\\u0627\\u064B\\u060C \", authStatus_r1.user.name, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(authStatus_r1.user.email);\n  }\n}\nfunction AuthDialogComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, AuthDialogComponent_div_2_div_1_Template, 13, 2, \"div\", 3);\n    i0.ɵɵtemplate(2, AuthDialogComponent_div_2_div_2_Template, 13, 4, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const authStatus_r1 = ctx.ngIf;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !authStatus_r1.isAuthenticated);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", authStatus_r1.isAuthenticated && authStatus_r1.user);\n  }\n}\nconst _c0 = function () {\n  return {\n    width: \"450px\"\n  };\n};\nexport let AuthDialogComponent = /*#__PURE__*/(() => {\n  class AuthDialogComponent {\n    constructor(youtubeService) {\n      this.youtubeService = youtubeService;\n      this.visible = false;\n      this.visibleChange = new EventEmitter();\n      this.authSuccess = new EventEmitter();\n      this.isSigningIn = false;\n      this.errorMessage = '';\n      this.authStatus$ = this.youtubeService.authStatus$;\n    }\n    signIn() {\n      this.isSigningIn = true;\n      this.errorMessage = '';\n      this.youtubeService.signIn().subscribe({\n        next: success => {\n          this.isSigningIn = false;\n          if (success) {\n            this.authSuccess.emit();\n            this.closeDialog();\n          }\n        },\n        error: error => {\n          this.isSigningIn = false;\n          this.errorMessage = 'فشل في تسجيل الدخول. يرجى المحاولة مرة أخرى.';\n          console.error('Sign in error:', error);\n        }\n      });\n    }\n    signOut() {\n      this.youtubeService.signOut().subscribe({\n        next: () => {\n          this.closeDialog();\n        },\n        error: error => {\n          this.errorMessage = 'فشل في تسجيل الخروج.';\n          console.error('Sign out error:', error);\n        }\n      });\n    }\n    closeDialog() {\n      this.visible = false;\n      this.visibleChange.emit(false);\n      this.errorMessage = '';\n    }\n    static {\n      this.ɵfac = function AuthDialogComponent_Factory(t) {\n        return new (t || AuthDialogComponent)(i0.ɵɵdirectiveInject(i1.YoutubeService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AuthDialogComponent,\n        selectors: [[\"app-auth-dialog\"]],\n        inputs: {\n          visible: \"visible\"\n        },\n        outputs: {\n          visibleChange: \"visibleChange\",\n          authSuccess: \"authSuccess\"\n        },\n        decls: 4,\n        vars: 9,\n        consts: [[\"header\", \"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644 \\u0625\\u0644\\u0649 YouTube\", 3, \"visible\", \"modal\", \"closable\", \"visibleChange\", \"onHide\"], [1, \"auth-content\"], [4, \"ngIf\"], [\"class\", \"text-center\", 4, \"ngIf\"], [1, \"text-center\"], [1, \"mb-4\"], [1, \"pi\", \"pi-youtube\", 2, \"font-size\", \"3rem\", \"color\", \"#FF0000\"], [1, \"mb-3\"], [1, \"text-muted\", \"mb-4\"], [\"class\", \"alert alert-danger mb-3\", 4, \"ngIf\"], [\"label\", \"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644 \\u0628\\u0640 Google\", \"icon\", \"pi pi-google\", \"loadingIcon\", \"pi pi-spinner pi-spin\", \"styleClass\", \"p-button-danger w-100\", 3, \"loading\", \"onClick\"], [1, \"mt-3\"], [1, \"text-muted\"], [1, \"pi\", \"pi-info-circle\", \"me-1\"], [1, \"alert\", \"alert-danger\", \"mb-3\"], [1, \"rounded-circle\", 2, \"width\", \"80px\", \"height\", \"80px\", 3, \"src\", \"alt\"], [1, \"mb-2\"], [1, \"text-muted\", \"mb-3\"], [1, \"alert\", \"alert-success\", \"mb-3\"], [1, \"pi\", \"pi-check-circle\", \"me-2\"], [1, \"d-flex\", \"gap-2\"], [\"label\", \"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062E\\u0631\\u0648\\u062C\", \"icon\", \"pi pi-sign-out\", \"styleClass\", \"p-button-secondary flex-fill\", 3, \"onClick\"], [\"label\", \"\\u0645\\u062A\\u0627\\u0628\\u0639\\u0629\", \"icon\", \"pi pi-check\", \"styleClass\", \"p-button-success flex-fill\", 3, \"onClick\"]],\n        template: function AuthDialogComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"p-dialog\", 0);\n            i0.ɵɵlistener(\"visibleChange\", function AuthDialogComponent_Template_p_dialog_visibleChange_0_listener($event) {\n              return ctx.visible = $event;\n            })(\"onHide\", function AuthDialogComponent_Template_p_dialog_onHide_0_listener() {\n              return ctx.closeDialog();\n            });\n            i0.ɵɵelementStart(1, \"div\", 1);\n            i0.ɵɵtemplate(2, AuthDialogComponent_div_2_Template, 3, 2, \"div\", 2);\n            i0.ɵɵpipe(3, \"async\");\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵstyleMap(i0.ɵɵpureFunction0(8, _c0));\n            i0.ɵɵproperty(\"visible\", ctx.visible)(\"modal\", true)(\"closable\", true);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(3, 6, ctx.authStatus$));\n          }\n        },\n        dependencies: [i2.NgIf, i3.Button, i4.Dialog, i2.AsyncPipe],\n        styles: [\".auth-content[_ngcontent-%COMP%]{padding:1rem}.alert[_ngcontent-%COMP%]{padding:.75rem 1rem;border-radius:.375rem;border:1px solid transparent}.alert-danger[_ngcontent-%COMP%]{color:#721c24;background-color:#f8d7da;border-color:#f5c6cb}.alert-success[_ngcontent-%COMP%]{color:#155724;background-color:#d4edda;border-color:#c3e6cb}.text-muted[_ngcontent-%COMP%]{color:#6c757d!important}.rounded-circle[_ngcontent-%COMP%]{border-radius:50%!important}.d-flex[_ngcontent-%COMP%]{display:flex}.gap-2[_ngcontent-%COMP%]{gap:.5rem}.flex-fill[_ngcontent-%COMP%]{flex:1 1 auto}.w-100[_ngcontent-%COMP%]{width:100%!important}.mb-2[_ngcontent-%COMP%]{margin-bottom:.5rem}.mb-3[_ngcontent-%COMP%]{margin-bottom:1rem}.mb-4[_ngcontent-%COMP%]{margin-bottom:1.5rem}.mt-3[_ngcontent-%COMP%]{margin-top:1rem}.me-1[_ngcontent-%COMP%]{margin-right:.25rem}.me-2[_ngcontent-%COMP%]{margin-right:.5rem}.text-center[_ngcontent-%COMP%]{text-align:center}\"]\n      });\n    }\n  }\n  return AuthDialogComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}