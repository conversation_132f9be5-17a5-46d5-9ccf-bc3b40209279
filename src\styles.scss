/* You can add global styles to this file, and also import other style files */

/* Importing Bootstrap SCSS file. */
@import "./node_modules/bootstrap/scss/bootstrap";

/* Importing Datepicker SCSS file. */
@import "node_modules/ngx-bootstrap/datepicker/bs-datepicker";

@import "primeicons/primeicons.css";
@import url('https://fonts.googleapis.com/css2?family=Roboto&display=swap');

@font-face {
  font-family: 'quranFont';
  src: url("assets/fonts/quranFont.ttf") format('truetype');
  font-weight: normal;
  font-style: normal;
}
body{
  font-family: "Roboto", sans-serif;
  font-weight: 400;
  font-style: normal;
}

.quran-text {
  font-family: "quranFont", sans-serif;
}



