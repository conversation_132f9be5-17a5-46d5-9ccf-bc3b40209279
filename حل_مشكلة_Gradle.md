# حل مشكلة Gradle - Network is unreachable

## 🚨 المشكلة
```
Could not install Gradle distribution from 'https://services.gradle.org/distributions/gradle-7.6.4-all.zip'.
Reason: java.net.SocketException: Network is unreachable: getsockopt
```

## 🔧 الحلول المتاحة

### الحل الأول: تنزيل Gradle يدوياً (الأسرع)

#### 1. تنزيل Gradle
- اذهب إلى: https://gradle.org/releases/
- ابحث عن Gradle 7.6.4
- نزل "Binary-only" أو "Complete"
- أو استخدم هذا الرابط المباشر:
  https://services.gradle.org/distributions/gradle-7.6.4-all.zip

#### 2. إعداد Gradle يدوياً
```powershell
# إنشاء مجلد Gradle
New-Item -ItemType Directory -Force -Path "C:\gradle"

# استخراج الملف المنزل إلى C:\gradle
# ستحصل على مجلد: C:\gradle\gradle-7.6.4

# تعيين متغيرات البيئة
$env:GRADLE_HOME = "C:\gradle\gradle-7.6.4"
$env:PATH = "$env:GRADLE_HOME\bin;$env:PATH"
$env:JAVA_HOME = "C:\Program Files\Microsoft\jdk-*********-hotspot"

# التحقق من التثبيت
gradle --version
```

#### 3. بناء APK
```powershell
cd "C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android"
gradle assembleDebug
```

### الحل الثاني: استخدام Gradle Wrapper محلياً

#### 1. نسخ Gradle إلى مجلد المشروع
```powershell
# إنشاء مجلد gradle محلي
New-Item -ItemType Directory -Force -Path "android\gradle\wrapper\dists\gradle-7.6.4-all"

# نسخ ملف gradle-7.6.4-all.zip إلى:
# android\gradle\wrapper\dists\gradle-7.6.4-all\
```

#### 2. تعديل إعدادات Wrapper
```powershell
# تعديل ملف gradle-wrapper.properties
# تغيير distributionUrl إلى مسار محلي
```

### الحل الثالث: Android Studio (الأسهل)

#### 1. تنزيل Android Studio
- من: https://developer.android.com/studio
- Android Studio يأتي مع Gradle مدمج

#### 2. فتح المشروع
1. افتح Android Studio
2. اختر "Open an existing Android Studio project"
3. انتقل إلى: `android` (داخل مجلد المشروع)
4. Android Studio سيتولى تنزيل وإعداد Gradle تلقائياً

#### 3. بناء APK
- من القائمة: Build → Build Bundle(s) / APK(s) → Build APK(s)

### الحل الرابع: استخدام VPN أو تغيير DNS

#### 1. تغيير DNS
```powershell
# تغيير DNS إلى Google DNS
netsh interface ip set dns "Wi-Fi" static *******
netsh interface ip add dns "Wi-Fi" ******* index=2
```

#### 2. استخدام VPN
- جرب VPN مختلف للوصول إلى gradle.org

### الحل الخامس: استخدام Mirror مختلف

#### تعديل gradle-wrapper.properties
```properties
distributionBase=GRADLE_USER_HOME
distributionPath=wrapper/dists
distributionUrl=https\://downloads.gradle-dn.com/distributions/gradle-7.6.4-all.zip
networkTimeout=60000
validateDistributionUrl=false
zipStoreBase=GRADLE_USER_HOME
zipStorePath=wrapper/dists
```

## 🚀 الحل الموصى به

**استخدم Android Studio** - إنه الأسهل والأكثر موثوقية:

1. نزل Android Studio
2. افتح المشروع من مجلد `android`
3. دع Android Studio يتولى كل شيء
4. اضغط Build APK

## 📝 ملاحظات مهمة

- المشروع جاهز 100% والمشكلة فقط في تنزيل Gradle
- جميع الملفات والإعدادات صحيحة
- بمجرد حل مشكلة Gradle، ستحصل على APK فوراً

## 🎯 خطوات سريعة للحل

```powershell
# الطريقة السريعة
# 1. نزل gradle-7.6.4-all.zip يدوياً
# 2. استخرج إلى C:\gradle\gradle-7.6.4
# 3. شغل هذه الأوامر:

$env:GRADLE_HOME = "C:\gradle\gradle-7.6.4"
$env:PATH = "$env:GRADLE_HOME\bin;$env:PATH"
$env:JAVA_HOME = "C:\Program Files\Microsoft\jdk-*********-hotspot"

cd "C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android"
gradle assembleDebug

# نسخ APK إلى سطح المكتب
copy "app\build\outputs\apk\debug\app-debug.apk" "$env:USERPROFILE\Desktop\QuranVidGen.apk"
```

---

**💡 أفضل حل:** نزل Android Studio واتركه يتولى كل شيء!
