"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2022 Google LLC. https://angular.io/
 * License: MIT
 */Zone.__load_patch("fetch",((t,o,e)=>{let n=t.fetch;if("function"!=typeof n)return;const r=t[e.symbol("fetch")];r&&(n=r);const l=t.Promise,c=e.symbol("thenPatched"),a=e.symbol("fetchTaskScheduling"),s=e.symbol("fetchTaskAborting"),f=t.AbortController,i="function"==typeof f;let h=null;i&&(t.AbortController=function(){const t=new f;return t.signal.abortController=t,t},h=e.patchMethod(f.prototype,"abort",(t=>(o,e)=>o.task?o.task.zone.cancelTask(o.task):t.apply(o,e))));const u=function(){};t.fetch=function(){const t=Array.prototype.slice.call(arguments),r=t.length>1?t[1]:null,f=r&&r.signal;return new Promise(((r,b)=>{const p=o.current.scheduleMacroTask("fetch",u,{fetchArgs:t},(()=>{let s,f=o.current;try{f[a]=!0,s=n.apply(this,t)}catch(t){return void b(t)}finally{f[a]=!1}if(!(s instanceof l)){let t=s.constructor;t[c]||e.patchThen(t)}s.then((t=>{"notScheduled"!==p.state&&p.invoke(),r(t)}),(t=>{"notScheduled"!==p.state&&p.invoke(),b(t)}))}),(()=>{if(i)if(f&&f.abortController&&!f.aborted&&"function"==typeof f.abortController.abort&&h)try{o.current[s]=!0,h.call(f.abortController)}finally{o.current[s]=!1}else b("cancel fetch need a AbortController.signal");else b("No AbortController supported, can not cancel fetch")}));f&&f.abortController&&(f.abortController.task=p)}))}}));