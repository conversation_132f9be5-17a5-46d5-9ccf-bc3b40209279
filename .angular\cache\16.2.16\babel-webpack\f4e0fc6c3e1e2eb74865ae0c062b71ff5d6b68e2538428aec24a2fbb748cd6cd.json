{"ast": null, "code": "import { version } from './version';\nexport const DEFAULT_VERSION = `realtime-js/${version}`;\nexport const VSN = '1.0.0';\nexport const VERSION = version;\nexport const DEFAULT_TIMEOUT = 10000;\nexport const WS_CLOSE_NORMAL = 1000;\nexport var SOCKET_STATES = /*#__PURE__*/function (SOCKET_STATES) {\n  SOCKET_STATES[SOCKET_STATES[\"connecting\"] = 0] = \"connecting\";\n  SOCKET_STATES[SOCKET_STATES[\"open\"] = 1] = \"open\";\n  SOCKET_STATES[SOCKET_STATES[\"closing\"] = 2] = \"closing\";\n  SOCKET_STATES[SOCKET_STATES[\"closed\"] = 3] = \"closed\";\n  return SOCKET_STATES;\n}(SOCKET_STATES || {});\nexport var CHANNEL_STATES = /*#__PURE__*/function (CHANNEL_STATES) {\n  CHANNEL_STATES[\"closed\"] = \"closed\";\n  CHANNEL_STATES[\"errored\"] = \"errored\";\n  CHANNEL_STATES[\"joined\"] = \"joined\";\n  CHANNEL_STATES[\"joining\"] = \"joining\";\n  CHANNEL_STATES[\"leaving\"] = \"leaving\";\n  return CHANNEL_STATES;\n}(CHANNEL_STATES || {});\nexport var CHANNEL_EVENTS = /*#__PURE__*/function (CHANNEL_EVENTS) {\n  CHANNEL_EVENTS[\"close\"] = \"phx_close\";\n  CHANNEL_EVENTS[\"error\"] = \"phx_error\";\n  CHANNEL_EVENTS[\"join\"] = \"phx_join\";\n  CHANNEL_EVENTS[\"reply\"] = \"phx_reply\";\n  CHANNEL_EVENTS[\"leave\"] = \"phx_leave\";\n  CHANNEL_EVENTS[\"access_token\"] = \"access_token\";\n  return CHANNEL_EVENTS;\n}(CHANNEL_EVENTS || {});\nexport var TRANSPORTS = /*#__PURE__*/function (TRANSPORTS) {\n  TRANSPORTS[\"websocket\"] = \"websocket\";\n  return TRANSPORTS;\n}(TRANSPORTS || {});\nexport var CONNECTION_STATE = /*#__PURE__*/function (CONNECTION_STATE) {\n  CONNECTION_STATE[\"Connecting\"] = \"connecting\";\n  CONNECTION_STATE[\"Open\"] = \"open\";\n  CONNECTION_STATE[\"Closing\"] = \"closing\";\n  CONNECTION_STATE[\"Closed\"] = \"closed\";\n  return CONNECTION_STATE;\n}(CONNECTION_STATE || {});\n\n//# sourceMappingURL=constants.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}