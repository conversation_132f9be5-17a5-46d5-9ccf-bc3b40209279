{"ast": null, "code": "import { Observable } from './Observable';\nimport { Subscription, EMPTY_SUBSCRIPTION } from './Subscription';\nimport { ObjectUnsubscribedError } from './util/ObjectUnsubscribedError';\nimport { arrRemove } from './util/arrRemove';\nimport { errorContext } from './util/errorContext';\nexport let Subject = /*#__PURE__*/(() => {\n  class Subject extends Observable {\n    constructor() {\n      super();\n      this.closed = false;\n      this.currentObservers = null;\n      this.observers = [];\n      this.isStopped = false;\n      this.hasError = false;\n      this.thrownError = null;\n    }\n    lift(operator) {\n      const subject = new AnonymousSubject(this, this);\n      subject.operator = operator;\n      return subject;\n    }\n    _throwIfClosed() {\n      if (this.closed) {\n        throw new ObjectUnsubscribedError();\n      }\n    }\n    next(value) {\n      errorContext(() => {\n        this._throwIfClosed();\n        if (!this.isStopped) {\n          if (!this.currentObservers) {\n            this.currentObservers = Array.from(this.observers);\n          }\n          for (const observer of this.currentObservers) {\n            observer.next(value);\n          }\n        }\n      });\n    }\n    error(err) {\n      errorContext(() => {\n        this._throwIfClosed();\n        if (!this.isStopped) {\n          this.hasError = this.isStopped = true;\n          this.thrownError = err;\n          const {\n            observers\n          } = this;\n          while (observers.length) {\n            observers.shift().error(err);\n          }\n        }\n      });\n    }\n    complete() {\n      errorContext(() => {\n        this._throwIfClosed();\n        if (!this.isStopped) {\n          this.isStopped = true;\n          const {\n            observers\n          } = this;\n          while (observers.length) {\n            observers.shift().complete();\n          }\n        }\n      });\n    }\n    unsubscribe() {\n      this.isStopped = this.closed = true;\n      this.observers = this.currentObservers = null;\n    }\n    get observed() {\n      var _a;\n      return ((_a = this.observers) === null || _a === void 0 ? void 0 : _a.length) > 0;\n    }\n    _trySubscribe(subscriber) {\n      this._throwIfClosed();\n      return super._trySubscribe(subscriber);\n    }\n    _subscribe(subscriber) {\n      this._throwIfClosed();\n      this._checkFinalizedStatuses(subscriber);\n      return this._innerSubscribe(subscriber);\n    }\n    _innerSubscribe(subscriber) {\n      const {\n        hasError,\n        isStopped,\n        observers\n      } = this;\n      if (hasError || isStopped) {\n        return EMPTY_SUBSCRIPTION;\n      }\n      this.currentObservers = null;\n      observers.push(subscriber);\n      return new Subscription(() => {\n        this.currentObservers = null;\n        arrRemove(observers, subscriber);\n      });\n    }\n    _checkFinalizedStatuses(subscriber) {\n      const {\n        hasError,\n        thrownError,\n        isStopped\n      } = this;\n      if (hasError) {\n        subscriber.error(thrownError);\n      } else if (isStopped) {\n        subscriber.complete();\n      }\n    }\n    asObservable() {\n      const observable = new Observable();\n      observable.source = this;\n      return observable;\n    }\n  }\n  Subject.create = (destination, source) => {\n    return new AnonymousSubject(destination, source);\n  };\n  return Subject;\n})();\nexport class AnonymousSubject extends Subject {\n  constructor(destination, source) {\n    super();\n    this.destination = destination;\n    this.source = source;\n  }\n  next(value) {\n    var _a, _b;\n    (_b = (_a = this.destination) === null || _a === void 0 ? void 0 : _a.next) === null || _b === void 0 ? void 0 : _b.call(_a, value);\n  }\n  error(err) {\n    var _a, _b;\n    (_b = (_a = this.destination) === null || _a === void 0 ? void 0 : _a.error) === null || _b === void 0 ? void 0 : _b.call(_a, err);\n  }\n  complete() {\n    var _a, _b;\n    (_b = (_a = this.destination) === null || _a === void 0 ? void 0 : _a.complete) === null || _b === void 0 ? void 0 : _b.call(_a);\n  }\n  _subscribe(subscriber) {\n    var _a, _b;\n    return (_b = (_a = this.source) === null || _a === void 0 ? void 0 : _a.subscribe(subscriber)) !== null && _b !== void 0 ? _b : EMPTY_SUBSCRIPTION;\n  }\n}\n//# sourceMappingURL=Subject.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}