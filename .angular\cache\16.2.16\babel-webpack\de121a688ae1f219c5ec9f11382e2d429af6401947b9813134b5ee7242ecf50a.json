{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/\\u062A\\u0637\\u0628\\u064A\\u0642 \\u0646\\u0627\\u0634\\u0631 \\u0627\\u064A\\u0627\\u062A \\u0642\\u0626\\u0627\\u0646\\u064A\\u0629/QuranVidGen/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { signal } from '@angular/core';\nimport { FFmpeg } from '@ffmpeg/ffmpeg';\nimport { fetchFile, toBlobURL } from '@ffmpeg/util';\nimport { Directory, Filesystem } from '@capacitor/filesystem';\nimport { Dialog } from '@capacitor/dialog';\n// FFmpeg functionality will use @ffmpeg/ffmpeg instead\nimport { Capacitor } from '@capacitor/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/Services/quran.service\";\nimport * as i2 from \"src/app/Services/helper.service\";\nimport * as i3 from \"src/app/Services/youtube.service\";\nimport * as i4 from \"src/app/Services/gemini.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"primeng/inputtext\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/progressbar\";\nimport * as i9 from \"primeng/toast\";\nimport * as i10 from \"primeng/dropdown\";\nimport * as i11 from \"@angular/forms\";\nimport * as i12 from \"primeng/dialog\";\nimport * as i13 from \"primeng/tooltip\";\nimport * as i14 from \"primeng/slider\";\nimport * as i15 from \"../videos-dialog/videos-dialog.component\";\nimport * as i16 from \"../auth-dialog/auth-dialog.component\";\nimport * as i17 from \"../upload-dialog/upload-dialog.component\";\nimport * as i18 from \"../sidebar/sidebar.component\";\nimport * as i19 from \"../video-settings/video-settings.component\";\nimport * as i20 from \"../terms-dialog/terms-dialog.component\";\nimport * as i21 from \"../privacy-dialog/privacy-dialog.component\";\nimport * as i22 from \"../security-warning/security-warning.component\";\nimport * as i23 from \"../link-shortener/link-shortener.component\";\nfunction GeneratorComponent_div_10_div_43_div_1_p_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 52);\n    i0.ɵɵlistener(\"onClick\", function GeneratorComponent_div_10_div_43_div_1_p_button_3_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r11.showAuthDialog());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GeneratorComponent_div_10_div_43_div_1_p_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 53);\n    i0.ɵɵlistener(\"onClick\", function GeneratorComponent_div_10_div_43_div_1_p_button_4_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r13.showUploadDialog());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GeneratorComponent_div_10_div_43_div_1_p_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 54);\n    i0.ɵɵlistener(\"onClick\", function GeneratorComponent_div_10_div_43_div_1_p_button_5_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r15.showAuthDialog());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(4);\n    let tmp_0_0;\n    i0.ɵɵproperty(\"label\", \"\\u0645\\u0631\\u062D\\u0628\\u0627\\u064B\\u060C \" + (((tmp_0_0 = ctx_r10.youtubeService.getCurrentUser()) == null ? null : tmp_0_0.name) || \"\\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\"));\n  }\n}\nfunction GeneratorComponent_div_10_div_43_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵelement(1, \"video\", 47);\n    i0.ɵɵelementStart(2, \"div\", 48);\n    i0.ɵɵtemplate(3, GeneratorComponent_div_10_div_43_div_1_p_button_3_Template, 1, 0, \"p-button\", 49);\n    i0.ɵɵtemplate(4, GeneratorComponent_div_10_div_43_div_1_p_button_4_Template, 1, 0, \"p-button\", 50);\n    i0.ɵɵtemplate(5, GeneratorComponent_div_10_div_43_div_1_p_button_5_Template, 1, 1, \"p-button\", 51);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r7.videoURL, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r7.youtubeService.isAuthenticated());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.youtubeService.isAuthenticated());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.youtubeService.isAuthenticated());\n  }\n}\nfunction GeneratorComponent_div_10_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtemplate(1, GeneratorComponent_div_10_div_43_div_1_Template, 6, 4, \"div\", 45);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.videoURL != \"\");\n  }\n}\nfunction GeneratorComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 18)(2, \"div\", 19)(3, \"div\", 20)(4, \"label\", 21);\n    i0.ɵɵelement(5, \"i\", 22);\n    i0.ɵɵtext(6, \" \\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0633\\u0648\\u0631\\u0629 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p-dropdown\", 23);\n    i0.ɵɵlistener(\"ngModelChange\", function GeneratorComponent_div_10_Template_p_dropdown_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.currentSurah = $event);\n    })(\"onChange\", function GeneratorComponent_div_10_Template_p_dropdown_onChange_7_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const _r4 = i0.ɵɵreference(22);\n      const _r5 = i0.ɵɵreference(29);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.helper.SurahNumberRestrict(ctx_r19.GetCurrentSurahNumber(), _r4, _r5));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 20)(9, \"label\", 21);\n    i0.ɵɵelement(10, \"i\", 24);\n    i0.ɵɵtext(11, \" \\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0642\\u0627\\u0631\\u0626 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p-dropdown\", 25);\n    i0.ɵɵlistener(\"ngModelChange\", function GeneratorComponent_div_10_Template_p_dropdown_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.currentReciterId = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 20)(14, \"label\", 21);\n    i0.ɵɵelement(15, \"i\", 26);\n    i0.ɵɵtext(16, \" \\u0646\\u0637\\u0627\\u0642 \\u0627\\u0644\\u0622\\u064A\\u0627\\u062A \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 27)(18, \"div\", 28)(19, \"label\", 29);\n    i0.ɵɵtext(20, \"\\u0645\\u0646 \\u0627\\u0644\\u0622\\u064A\\u0629\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"input\", 30, 31);\n    i0.ɵɵlistener(\"input\", function GeneratorComponent_div_10_Template_input_input_21_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const _r4 = i0.ɵɵreference(22);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.helper.InputNumberRestrict(_r4));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 32);\n    i0.ɵɵelement(24, \"i\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 28)(26, \"label\", 29);\n    i0.ɵɵtext(27, \"\\u0625\\u0644\\u0649 \\u0627\\u0644\\u0622\\u064A\\u0629\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"input\", 30, 34);\n    i0.ɵɵlistener(\"input\", function GeneratorComponent_div_10_Template_input_input_28_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const _r5 = i0.ɵɵreference(29);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.helper.InputNumberRestrict(_r5));\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(30, \"div\", 35)(31, \"div\", 36)(32, \"label\", 21);\n    i0.ɵɵelement(33, \"i\", 37);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"p-slider\", 38);\n    i0.ɵɵlistener(\"ngModelChange\", function GeneratorComponent_div_10_Template_p_slider_ngModelChange_35_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.fontSize = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"div\", 36)(37, \"label\", 21);\n    i0.ɵɵelement(38, \"i\", 39);\n    i0.ɵɵtext(39, \" \\u0641\\u064A\\u062F\\u064A\\u0648 \\u0627\\u0644\\u062E\\u0644\\u0641\\u064A\\u0629 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"p-button\", 40);\n    i0.ɵɵlistener(\"onClick\", function GeneratorComponent_div_10_Template_p_button_onClick_40_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.videoPickerVisible = true);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(41, \"div\", 41)(42, \"p-button\", 42);\n    i0.ɵɵlistener(\"onClick\", function GeneratorComponent_div_10_Template_p_button_onClick_42_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const _r4 = i0.ɵɵreference(22);\n      const _r5 = i0.ɵɵreference(29);\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.GetAyahsAndLoadThem(ctx_r25.GetCurrentSurahNumber(), ctx_r25.currentReciterId, _r4.value, _r5.value));\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(43, GeneratorComponent_div_10_div_43_Template, 2, 1, \"div\", 43);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const _r4 = i0.ɵɵreference(22);\n    const _r5 = i0.ɵɵreference(29);\n    const ctx_r0 = i0.ɵɵnextContext();\n    let tmp_18_0;\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"options\", ctx_r0.suras)(\"optionLabel\", \"surahName\")(\"ngModel\", ctx_r0.currentSurah)(\"dropdownIcon\", \"pi pi-chevron-down\")(\"optionValue\", \"surahName\")(\"filter\", true);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"options\", ctx_r0.reciters)(\"optionLabel\", \"name\")(\"ngModel\", ctx_r0.currentReciterId)(\"dropdownIcon\", \"pi pi-chevron-down\")(\"optionValue\", \"id\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"min\", 1);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"min\", 1);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \\u062D\\u062C\\u0645 \\u0627\\u0644\\u062E\\u0637: \", ctx_r0.fontSize, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.fontSize)(\"min\", 12)(\"max\", 48)(\"step\", 1);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"label\", (tmp_18_0 = ctx_r0.getPickedVideo()) !== null && tmp_18_0 !== undefined ? tmp_18_0 : \"\\u0641\\u064A\\u062F\\u064A\\u0648 \\u0639\\u0634\\u0648\\u0627\\u0626\\u064A\")(\"outlined\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.currentReciterId == \"\" || ctx_r0.currentSurah == \"\" || !_r4.value || !_r5.value)(\"loading\", ctx_r0.ffmpegExecuting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r0.loadedAudio || ctx_r0.firstLoad) && !ctx_r0.ffmpegExecuting);\n  }\n}\nfunction GeneratorComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"div\", 56)(2, \"span\", 4);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"p-toast\")(5, \"p-progressBar\", 57);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Loading \", ctx_r2.currentLoading.name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r2.currentLoading.value);\n  }\n}\nfunction GeneratorComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"div\", 58)(2, \"div\", 59)(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6, \"Elapsed Time \");\n    i0.ɵɵelementStart(7, \"span\", 60);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(9, \"p-toast\")(10, \"p-progressBar\", 61);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r3.executingProgressLabel());\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r3.executingTime + \" s\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ariaValueMin\", 0)(\"ariaValueMax\", 100)(\"value\", ctx_r3.executingProgress());\n  }\n}\nconst _c0 = function () {\n  return {\n    width: \"50vw\"\n  };\n};\nconst _c1 = function () {\n  return {\n    \"960px\": \"75vw\"\n  };\n};\nconst baseURL = \"https://unpkg.com/@ffmpeg/core@0.12.6/dist/esm\";\nexport let GeneratorComponent = /*#__PURE__*/(() => {\n  class GeneratorComponent {\n    constructor(quranService, helper, youtubeService, geminiService) {\n      this.quranService = quranService;\n      this.helper = helper;\n      this.youtubeService = youtubeService;\n      this.geminiService = geminiService;\n      this.loaded = false;\n      this.loadedAudio = false;\n      this.ffmpeg = new FFmpeg();\n      this.videoURL = \"\";\n      this.message = \"\";\n      this.currentLoading = {\n        name: '',\n        value: 0\n      };\n      this.firstLoad = true;\n      this.ayatTexts = [];\n      this.suras = [];\n      this.reciters = [];\n      this.currentSurah = '';\n      this.currentReciterId = '';\n      this.ffmpegExecuting = false;\n      this.videoPickerVisible = false;\n      this.executingProgress = signal(0);\n      this.executingTime = 0;\n      this.ayahtTextAndAudio = [];\n      this.executingProgressLabel = signal('');\n      this.fontSize = 18;\n      this.clock = 0;\n      // YouTube Upload related properties\n      this.authDialogVisible = false;\n      this.uploadDialogVisible = false;\n      this.currentVideoBlob = null;\n      this.currentSurahInfo = null;\n      // New UI properties\n      this.sidebarVisible = false;\n      this.videoSettingsVisible = false;\n      this.termsVisible = false;\n      this.privacyVisible = false;\n      this.videoSettings = null;\n      this.previousPercentage = -1;\n    }\n    getPickedVideo() {\n      if (this.pickedVideo) {\n        return `Video ${this.pickedVideo}`;\n      }\n      return undefined;\n    }\n    ngAfterViewInit() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        yield _this.load();\n        _this.quranService.GetAllSuras().subscribe(suras => {\n          _this.suras = suras;\n        });\n        _this.quranService.GetReciters()?.subscribe(reciters => {\n          _this.reciters = reciters;\n        });\n      })();\n    }\n    GetAyahsAndLoadThem(surahNumber, reciter, startAyah, endAyah) {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        _this2.ayahtTextAndAudio = [];\n        _this2.ayatTexts = [];\n        let reciterId = Number.parseInt(reciter);\n        let start = Number.parseInt(startAyah);\n        let end = Number.parseInt(endAyah);\n        _this2.ayatTexts = (yield _this2.quranService.GetAyatTexts(surahNumber, start, end, 'arabic').toPromise()) ?? [];\n        let blobs = (yield _this2.quranService.GetAyahsAudio(reciterId, surahNumber, start, end).toPromise()) ?? [];\n        yield _this2.transcode(blobs);\n      })();\n    }\n    GetCurrentSurahNumber() {\n      this.currentSurah;\n      this.suras;\n      return this.suras.findIndex(x => x.surahName == this.currentSurah) + 1;\n    }\n    GetProgressText(url, name, recieved) {\n      url = url.toString();\n      let percentage = this.helper.getDownloadProgress(url, recieved);\n      if (percentage != this.previousPercentage) {\n        this.currentLoading = {\n          name: name,\n          value: percentage\n        };\n        this.previousPercentage = percentage;\n      }\n    }\n    load() {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        _this3.loaded = false;\n        if (Capacitor.getPlatform() != 'android') {\n          _this3.ffmpeg.on(\"log\", ({\n            message\n          }) => {\n            _this3.message = message;\n          });\n          _this3.ffmpeg.on(\"progress\", ({\n            progress,\n            time\n          }) => {\n            _this3.executingProgress.set(Math.floor(progress * 100));\n            _this3.executingTime = Math.floor(time / 1000000);\n          });\n          yield _this3.ffmpeg.load({\n            coreURL: yield toBlobURL(`assets/ffmpeg/ffmpeg-core.js`, 'text/javascript', true, ev => _this3.GetProgressText(ev.url, 'Core Script', ev.received)),\n            wasmURL: yield toBlobURL(`assets/ffmpeg/ffmpeg-core.wasm`, 'application/wasm', true, ev => _this3.GetProgressText(ev.url, 'Web Worker', ev.received)),\n            classWorkerURL: `${window.location.href}assets/ffmpeg/worker.js`\n          });\n        }\n        _this3.loaded = true;\n      })();\n    }\n    transcode(audios) {\n      var _this4 = this;\n      return _asyncToGenerator(function* () {\n        if (Capacitor.getPlatform() == 'android') {\n          yield _this4.transcodeAndroid(audios);\n          return;\n        }\n        _this4.firstLoad = true;\n        _this4.loadedAudio = true;\n        let audioNames = [];\n        for (let index = 0; index < audios.length; index++) {\n          let audioData = yield fetchFile(audios[index]);\n          let duration = yield _this4.helper.getDuration(audioData);\n          yield _this4.ffmpeg.writeFile(index + '.mp3', audioData);\n          _this4.ayahtTextAndAudio.push({\n            text: _this4.ayatTexts[index],\n            duration: duration ?? 0\n          });\n          // let duration = await this.ffmpeg.\n          audioNames.push(`file ${index}.mp3`);\n          if (index < audios.length - 1) {// Don't add silence after the last audio file\n            // audioNames.push(`file 'silence.mp3'`);\n          }\n        }\n        // let silenceCommand = ['-f', 'lavfi', '-i', 'anullsrc', '-t', '0.5', 'silence.mp3'];\n        // await this.ffmpeg.exec(silenceCommand);\n        // Create a text file with the names of the audio files to be concatenated\n        _this4.executingProgressLabel.set('Generating Subtitles');\n        let filelist = audioNames.join('\\n');\n        yield _this4.ffmpeg.writeFile('filelist.txt', filelist);\n        _this4.executingProgressLabel.set('Generating Audio');\n        // Use the concat demuxer in ffmpeg\n        let commands = ['-f', 'concat', '-safe', '0', '-i', 'filelist.txt', '-c', 'copy', 'output.mp3'];\n        let randomVideo = Math.max(Math.round(Math.random() * 15), 1);\n        let finalVideoName = _this4.pickedVideo ? _this4.pickedVideo : randomVideo;\n        yield _this4.ffmpeg.writeFile('video.mp4', yield fetchFile(`/assets/videos/${finalVideoName}.mp4`));\n        yield _this4.ffmpeg.exec(commands);\n        _this4.executingProgressLabel.set('Merging Audio with Video');\n        // let subtitleFile = new TextEncoder().encode(this.getSubTitles());\n        let subtitleFile = _this4.getSubtitlesAsAss('center', 'Al-QuranAlKareem', _this4.fontSize.toString());\n        yield _this4.ffmpeg.writeFile('subtitles.ass', subtitleFile);\n        yield _this4.ffmpeg.writeFile('/tmp/Al-QuranAlKareem', yield fetchFile('/assets/fonts/Al-QuranAlKareem.ttf'));\n        // await this.ffmpeg.writeFile('subtitles.ass',await fetchFile('/assets/subs/test.ass'));\n        _this4.ffmpegExecuting = true;\n        _this4.executingProgress.set(0);\n        yield _this4.ffmpeg.exec(['-stream_loop', '-1', '-i', 'video.mp4', '-i', 'output.mp3', '-c:v', 'copy', '-c:a', 'aac', '-map', '0:v:0', '-map', '1:a:0', '-shortest', 'output.mp4']);\n        //:fontsdir=/tmp:force_style='Fontname=Arimo,Fontsize=24,PrimaryColour=&H00FFFFFF,OutlineColour=&H000000FF,BackColour=&H00000000,Bold=1,Italic=0,Alignment=2,MarginV=40\n        let command = ['-i', 'output.mp4', \"-vf\", \"ass=subtitles.ass:fontsdir=tmp\", \"-c:v\", \"libx264\", \"-preset\", \"ultrafast\", \"-crf\", \"32\", \"-c:a\", \"copy\", 'outputsub.mp4'];\n        yield _this4.ffmpeg.exec(command);\n        const fileData = yield _this4.ffmpeg.readFile('outputsub.mp4');\n        const data = new Uint8Array(fileData);\n        _this4.videoURL = URL.createObjectURL(new Blob([data.buffer], {\n          type: 'video/mp4'\n        }));\n        // let result:number = await this.ffmpeg.exec(commands);\n        // if(result != 0)return;\n        // const fileData = await this.ffmpeg.readFile('output.mp3');\n        // const data = new Uint8Array(fileData as ArrayBuffer);\n        // this.videoURL = URL.createObjectURL(\n        //   new Blob([data.buffer], { type: 'audio/mpeg' })\n        // );\n        _this4.loadedAudio = true;\n        _this4.ffmpegExecuting = false;\n      })();\n    }\n    getSubtitlesAsAss(alignment, fontName, fontsize = '16') {\n      let assContent = `[Script Info]\n; Script generated by Ebby.co\nScriptType: v4.00+\nScaledBorderAndShadow: yes\n\n[V4+ Styles]\nFormat: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding\nStyle: Default,${fontName},${fontsize},&Hffffff,&Hffffff,&H000000,&H0,0,0,0,0,100,100,0,0,0,1,1,5,0,0,0,0,UTF-8\n\n[Events]\nFormat: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text\\n`;\n      // Set alignment based on the provided parameter\n      let alignmentCode = \"10\"; // Default: left alignment\n      if (alignment === \"center\") {\n        alignmentCode = \"2\";\n      } else if (alignment === \"right\") {\n        alignmentCode = \"8\";\n      }\n      let startTime = 0;\n      this.ayahtTextAndAudio.forEach((subtitle, index) => {\n        let endTime = startTime + subtitle.duration;\n        let start_time_str = this.formatTime(startTime);\n        let end_time_str = this.formatTime(endTime);\n        // let end = new Date(endTime * 1000).toISOString().substr(11, 12);\n        assContent += `Dialogue: 0,${start_time_str},${end_time_str},Default,,0,0,0,,${subtitle['text']}\\n`;\n        startTime = endTime;\n      });\n      return assContent;\n    }\n    formatTime(seconds) {\n      const hours = Math.floor(seconds / 3600);\n      const minutes = Math.floor(seconds % 3600 / 60);\n      const remainingSeconds = Math.fround(seconds % 60);\n      const hundredths = Math.floor(remainingSeconds % 1 * 100).toFixed(2);\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toFixed(2).toString().padStart(2, '0')}:${hundredths.toString().padStart(2, '0')}`;\n    }\n    ArrayToBase64(Array) {\n      var binary = '';\n      var len = Array.byteLength;\n      for (var i = 0; i < len; i++) {\n        binary += String.fromCharCode(Array[i]);\n      }\n      return window.btoa(binary);\n    }\n    transcodeAndroid(audios) {\n      var _this5 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          // if(!((await Filesystem.checkPermissions()).publicStorage == 'granted')){\n          //   await Filesystem.requestPermissions()\n          // }\n          yield Filesystem.requestPermissions();\n        } catch (error) {}\n        try {\n          let dirRes = yield Filesystem.readdir({\n            directory: Directory.Documents,\n            path: 'quranvideos'\n          });\n          for (let file of dirRes.files) {\n            yield Filesystem.deleteFile({\n              path: file.uri\n            });\n          }\n          yield Filesystem.rmdir({\n            directory: Directory.Documents,\n            path: 'quranvideos',\n            recursive: true\n          });\n        } catch (error) {}\n        try {\n          yield Filesystem.mkdir({\n            path: 'quranvideos',\n            directory: Directory.Documents\n          });\n        } catch (error) {}\n        let cachePath = '/storage/emulated/0/Documents/quranvideos';\n        let OutputcachePath = 'storage/emulated/0/Documents/quranvideos';\n        _this5.firstLoad = true;\n        _this5.loadedAudio = true;\n        let audioInfos = [];\n        let audioNames = [];\n        for (let index = 0; index < audios.length; index++) {\n          let audioData = yield fetchFile(audios[index]);\n          let duration = yield _this5.helper.getDuration(audioData);\n          // await this.ffmpeg.writeFile(index + '.mp3', audioData);\n          yield Filesystem.writeFile({\n            directory: Directory.Documents,\n            path: `quranvideos/${index + 1}.mp3`,\n            data: _this5.ArrayToBase64(audioData)\n          });\n          audioInfos.push({\n            name: `${index + 1}.mp3`,\n            duration: duration,\n            data: audioData\n          });\n          _this5.ayahtTextAndAudio.push({\n            text: _this5.ayatTexts[index],\n            duration: duration ?? 0\n          });\n          // let duration = await this.ffmpeg.\n          audioNames.push(`file ${cachePath}/${index + 1}.mp3`);\n          if (index < audios.length - 1) {// Don't add silence after the last audio file\n            // audioNames.push(`file 'silence.mp3'`);\n          }\n        }\n        // let silenceCommand = ['-f', 'lavfi', '-i', 'anullsrc', '-t', '0.5', 'silence.mp3'];\n        // await this.ffmpeg.exec(silenceCommand);\n        // Create a text file with the names of the audio files to be concatenated\n        _this5.executingProgressLabel.set('Generating Subtitles');\n        let filelist = audioNames.join('\\n');\n        // await this.ffmpeg.writeFile('filelist.txt', filelist);\n        _this5.executingProgressLabel.set('Generating Audio');\n        // Use the concat demuxer in ffmpeg\n        yield Filesystem.writeFile({\n          data: btoa(filelist),\n          directory: Directory.Documents,\n          path: 'quranvideos/filelist.txt'\n        });\n        // fmp.exec()\n        // Write filelist to FFmpeg virtual filesystem\n        yield _this5.ffmpeg.writeFile('filelist.txt', new TextEncoder().encode(filelist));\n        let commands = ['-f', 'concat', '-safe', '0', '-i', 'filelist.txt', '-c', 'copy', 'output.mp3'];\n        console.log('Executing FFmpeg command:', commands.join(' '));\n        yield _this5.ffmpeg.exec(commands);\n        console.log('Audio concatenation completed');\n        let randomVideo = Math.max(Math.round(Math.random() * 15), 1);\n        let finalVideoName = _this5.pickedVideo ? _this5.pickedVideo : randomVideo;\n        // Write video file to FFmpeg virtual filesystem\n        yield _this5.ffmpeg.writeFile('video.mp4', yield fetchFile(`/assets/videos/${finalVideoName}.mp4`));\n        console.log('Video file loaded to FFmpeg filesystem');\n        _this5.executingProgressLabel.set('Merging Audio with Video');\n        // Write subtitle and font files to FFmpeg virtual filesystem\n        let subtitleFile = _this5.getSubtitlesAsAss('center', 'Al-QuranAlKareem', _this5.fontSize.toString());\n        yield _this5.ffmpeg.writeFile('subtitles.ass', new TextEncoder().encode(subtitleFile));\n        yield _this5.ffmpeg.writeFile('Al-QuranAlKareem.ttf', yield fetchFile('/assets/fonts/Al-QuranAlKareem.ttf'));\n        yield _this5.ffmpeg.writeFile('arial.ttf', yield fetchFile('/assets/fonts/arial.ttf'));\n        yield _this5.ffmpeg.writeFile('Almarai-Regular.ttf', yield fetchFile('/assets/fonts/Almarai-Regular.ttf'));\n        console.log('Subtitle and font files loaded to FFmpeg filesystem');\n        _this5.ffmpegExecuting = true;\n        _this5.executingProgress.set(0);\n        // Merge video with audio using FFmpeg\n        console.log('----- start merging video with audio -----');\n        let mergeCommand = ['-stream_loop', '-1', '-i', 'video.mp4', '-i', 'output.mp3', '-c:v', 'copy', '-c:a', 'aac', '-map', '0:v:0', '-map', '1:a:0', '-shortest', 'merged_output.mp4'];\n        console.log('Executing merge command:', mergeCommand.join(' '));\n        yield _this5.ffmpeg.exec(mergeCommand);\n        console.log('Video and audio merge completed');\n        // Add subtitles to the merged video using FFmpeg\n        console.log('----- start adding subtitles -----');\n        let subtitleCommand = ['-i', 'merged_output.mp4', '-vf', 'ass=subtitles.ass', '-c:a', 'copy', 'final_output.mp4'];\n        console.log('Executing subtitle command:', subtitleCommand.join(' '));\n        yield _this5.ffmpeg.exec(subtitleCommand);\n        console.log('Subtitles added successfully');\n        // Read the final video from FFmpeg virtual filesystem\n        const finalVideoData = yield _this5.ffmpeg.readFile('final_output.mp4');\n        _this5.videoURL = _this5.ArrayToBase64(finalVideoData);\n        try {\n          try {\n            yield Filesystem.mkdir({\n              directory: Directory.Documents,\n              path: 'videos',\n              recursive: false\n            });\n          } catch (error) {}\n          yield Filesystem.writeFile({\n            data: _this5.videoURL,\n            path: `videos/generated-video-${Date.now()}.mp4`,\n            directory: Directory.Documents\n          });\n          yield Dialog.alert({\n            title: 'Video Saved!',\n            message: `Video has been saved to your device`,\n            buttonTitle: 'Ok'\n          });\n        } catch (error) {}\n        // let result:number = await this.ffmpeg.exec(commands);\n        // if(result != 0)return;\n        // const fileData = await this.ffmpeg.readFile('output.mp3');\n        // const data = new Uint8Array(fileData as ArrayBuffer);\n        // this.videoURL = URL.createObjectURL(\n        //   new Blob([data.buffer], { type: 'audio/mpeg' })\n        // );\n        _this5.loadedAudio = true;\n        _this5.ffmpegExecuting = false;\n        // Prepare video blob and surah info for YouTube upload\n        _this5.prepareVideoForUpload();\n      })();\n    }\n    // YouTube Upload Functions\n    prepareVideoForUpload() {\n      if (this.videoURL) {\n        // Convert video URL to Blob\n        fetch(this.videoURL).then(response => response.blob()).then(blob => {\n          this.currentVideoBlob = blob;\n        }).catch(error => {\n          console.error('Error preparing video blob:', error);\n        });\n        // Prepare surah info\n        this.currentSurahInfo = {\n          surahName: this.currentSurah,\n          surahNumber: this.GetCurrentSurahNumber(),\n          startAyah: parseInt(this.getStartAyah()),\n          endAyah: parseInt(this.getEndAyah()),\n          ayahTexts: this.ayatTexts\n        };\n      }\n    }\n    showAuthDialog() {\n      this.authDialogVisible = true;\n    }\n    showUploadDialog() {\n      if (!this.currentVideoBlob) {\n        this.prepareVideoForUpload();\n      }\n      this.uploadDialogVisible = true;\n    }\n    onAuthSuccess() {\n      // Authentication successful, user can now upload\n      console.log('YouTube authentication successful');\n    }\n    onUploadComplete(videoId) {\n      console.log('Video uploaded successfully with ID:', videoId);\n      // You can add additional logic here, like showing a success message\n    }\n\n    getStartAyah() {\n      // This should get the start ayah from the form\n      const startInput = document.querySelector('input[placeholder=\"Start Ayah\"]');\n      return startInput?.value || '1';\n    }\n    getEndAyah() {\n      // This should get the end ayah from the form\n      const endInput = document.querySelector('input[placeholder=\"End Ayah\"]');\n      return endInput?.value || '1';\n    }\n    // New methods for sidebar and dialogs\n    onSidebarMenuClick(itemId) {\n      switch (itemId) {\n        case 'video-settings':\n          this.videoSettingsVisible = true;\n          break;\n        case 'privacy-policy':\n          this.privacyVisible = true;\n          break;\n        case 'support':\n          this.openSupportEmail();\n          break;\n        case 'terms':\n          this.termsVisible = true;\n          break;\n      }\n    }\n    onVideoSettingsSaved(settings) {\n      this.videoSettings = settings;\n      // Apply settings to the application\n      this.fontSize = settings.fontSize;\n      console.log('Video settings saved:', settings);\n    }\n    openSupportEmail() {\n      const email = '<EMAIL>';\n      const subject = 'طلب دعم - مولد مقاطع القرآن الكريم';\n      const body = 'السلام عليكم ورحمة الله وبركاته،\\n\\nأحتاج إلى مساعدة في:\\n\\n';\n      const mailtoLink = `mailto:${email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;\n      window.open(mailtoLink, '_blank');\n    }\n    static {\n      this.ɵfac = function GeneratorComponent_Factory(t) {\n        return new (t || GeneratorComponent)(i0.ɵɵdirectiveInject(i1.QuranService), i0.ɵɵdirectiveInject(i2.HelperService), i0.ɵɵdirectiveInject(i3.YoutubeService), i0.ɵɵdirectiveInject(i4.GeminiService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: GeneratorComponent,\n        selectors: [[\"app-generator\"]],\n        decls: 24,\n        vars: 20,\n        consts: [[1, \"main-container\"], [1, \"app-header\"], [1, \"header-content\"], [1, \"header-left\"], [1, \"app-title\"], [1, \"app-subtitle\"], [1, \"header-right\"], [\"icon\", \"pi pi-bars\", \"severity\", \"secondary\", \"size\", \"large\", \"pTooltip\", \"\\u0627\\u0644\\u0642\\u0627\\u0626\\u0645\\u0629 \\u0627\\u0644\\u0631\\u0626\\u064A\\u0633\\u064A\\u0629\", \"tooltipPosition\", \"bottom\", 3, \"text\", \"rounded\", \"onClick\"], [4, \"ngIf\", \"ngIfElse\"], [\"progress\", \"\"], [\"class\", \"row my-5 justify-content-center mx-auto\", \"style\", \"width: 90%\", 4, \"ngIf\"], [\"header\", \"Background Video Gallery\", 3, \"visible\", \"resizable\", \"breakpoints\", \"visibleChange\"], [3, \"pickedVideo\"], [3, \"visible\", \"visibleChange\", \"authSuccess\"], [3, \"visible\", \"videoBlob\", \"surahInfo\", \"visibleChange\", \"uploadComplete\"], [3, \"visible\", \"visibleChange\", \"menuItemClick\"], [3, \"visible\", \"visibleChange\", \"settingsSaved\"], [3, \"visible\", \"visibleChange\"], [1, \"content-wrapper\"], [1, \"form-container\"], [1, \"form-group\"], [1, \"form-label\"], [1, \"pi\", \"pi-book\"], [\"placeholder\", \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0633\\u0648\\u0631\\u0629\", \"styleClass\", \"custom-dropdown\", 3, \"options\", \"optionLabel\", \"ngModel\", \"dropdownIcon\", \"optionValue\", \"filter\", \"ngModelChange\", \"onChange\"], [1, \"pi\", \"pi-user\"], [\"placeholder\", \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0642\\u0627\\u0631\\u0626\", \"styleClass\", \"custom-dropdown\", 3, \"options\", \"optionLabel\", \"ngModel\", \"dropdownIcon\", \"optionValue\", \"ngModelChange\"], [1, \"pi\", \"pi-list\"], [1, \"ayah-range-container\"], [1, \"ayah-input\"], [1, \"ayah-label\"], [\"type\", \"number\", \"pInputText\", \"\", \"placeholder\", \"1\", 1, \"ayah-number-input\", 3, \"min\", \"input\"], [\"start\", \"\"], [1, \"range-separator\"], [1, \"pi\", \"pi-arrow-left\"], [\"end\", \"\"], [1, \"form-row\"], [1, \"form-group\", \"half-width\"], [1, \"pi\", \"pi-font\"], [\"styleClass\", \"custom-slider\", 3, \"ngModel\", \"min\", \"max\", \"step\", \"ngModelChange\"], [1, \"pi\", \"pi-video\"], [\"severity\", \"primary\", \"icon\", \"pi pi-images\", \"styleClass\", \"video-picker-btn\", 3, \"label\", \"outlined\", \"onClick\"], [1, \"generate-section\"], [\"label\", \"\\u0625\\u0646\\u0634\\u0627\\u0621 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\", \"icon\", \"pi pi-play\", \"severity\", \"primary\", \"size\", \"large\", \"loadingIcon\", \"pi pi-spin pi-spinner\", \"styleClass\", \"generate-btn\", 3, \"disabled\", \"loading\", \"onClick\"], [\"class\", \"row my-5 justify-content-center mx-auto\", \"style\", \"width: 90%;\", 4, \"ngIf\"], [1, \"row\", \"my-5\", \"justify-content-center\", \"mx-auto\", 2, \"width\", \"90%\"], [\"class\", \"col-12 d-flex flex-column align-items-center\", 4, \"ngIf\"], [1, \"col-12\", \"d-flex\", \"flex-column\", \"align-items-center\"], [\"autoplay\", \"\", \"controls\", \"\", 1, \"col-10\", \"col-md-6\", \"col-lg-4\", \"mb-3\", 3, \"src\"], [1, \"d-flex\", \"gap-2\", \"flex-wrap\", \"justify-content-center\"], [\"label\", \"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644 \\u0625\\u0644\\u0649 YouTube\", \"icon\", \"pi pi-youtube\", \"styleClass\", \"p-button-danger\", 3, \"onClick\", 4, \"ngIf\"], [\"label\", \"\\u0646\\u0634\\u0631 \\u0639\\u0644\\u0649 YouTube\", \"icon\", \"pi pi-upload\", \"styleClass\", \"p-button-danger\", 3, \"onClick\", 4, \"ngIf\"], [\"icon\", \"pi pi-user\", \"styleClass\", \"p-button-outlined p-button-secondary\", 3, \"label\", \"onClick\", 4, \"ngIf\"], [\"label\", \"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644 \\u0625\\u0644\\u0649 YouTube\", \"icon\", \"pi pi-youtube\", \"styleClass\", \"p-button-danger\", 3, \"onClick\"], [\"label\", \"\\u0646\\u0634\\u0631 \\u0639\\u0644\\u0649 YouTube\", \"icon\", \"pi pi-upload\", \"styleClass\", \"p-button-danger\", 3, \"onClick\"], [\"icon\", \"pi pi-user\", \"styleClass\", \"p-button-outlined p-button-secondary\", 3, \"label\", \"onClick\"], [1, \"d-flex\", \"justify-content-center\", \"align-items-center\", 2, \"height\", \"85dvh\", \"width\", \"100dvw\"], [1, \"mx-auto\", \"w-50\"], [3, \"value\"], [1, \"col-10\", \"col-md-6\", \"col-lg-4\"], [1, \"d-flex\", \"justify-content-between\"], [1, \"fw-bold\", \"app-title\"], [3, \"ariaValueMin\", \"ariaValueMax\", \"value\"]],\n        template: function GeneratorComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h3\", 4);\n            i0.ɵɵtext(5, \"\\u0645\\u0648\\u0644\\u062F \\u0645\\u0642\\u0627\\u0637\\u0639 \\u0627\\u0644\\u0642\\u0631\\u0622\\u0646 \\u0627\\u0644\\u0643\\u0631\\u064A\\u0645\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"p\", 5);\n            i0.ɵɵtext(7, \"\\u0625\\u0646\\u0634\\u0627\\u0621 \\u0648\\u0646\\u0634\\u0631 \\u0645\\u0642\\u0627\\u0637\\u0639 \\u0642\\u0631\\u0622\\u0646\\u064A\\u0629 \\u0627\\u062D\\u062A\\u0631\\u0627\\u0641\\u064A\\u0629\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(8, \"div\", 6)(9, \"p-button\", 7);\n            i0.ɵɵlistener(\"onClick\", function GeneratorComponent_Template_p_button_onClick_9_listener() {\n              return ctx.sidebarVisible = true;\n            });\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵtemplate(10, GeneratorComponent_div_10_Template, 44, 23, \"div\", 8);\n            i0.ɵɵtemplate(11, GeneratorComponent_ng_template_11_Template, 6, 2, \"ng-template\", null, 9, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵtemplate(13, GeneratorComponent_div_13_Template, 11, 5, \"div\", 10);\n            i0.ɵɵelementStart(14, \"p-dialog\", 11);\n            i0.ɵɵlistener(\"visibleChange\", function GeneratorComponent_Template_p_dialog_visibleChange_14_listener($event) {\n              return ctx.videoPickerVisible = $event;\n            });\n            i0.ɵɵelementStart(15, \"app-videos-dialog\", 12);\n            i0.ɵɵlistener(\"pickedVideo\", function GeneratorComponent_Template_app_videos_dialog_pickedVideo_15_listener($event) {\n              ctx.pickedVideo = $event;\n              return ctx.videoPickerVisible = false;\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(16, \"app-auth-dialog\", 13);\n            i0.ɵɵlistener(\"visibleChange\", function GeneratorComponent_Template_app_auth_dialog_visibleChange_16_listener($event) {\n              return ctx.authDialogVisible = $event;\n            })(\"authSuccess\", function GeneratorComponent_Template_app_auth_dialog_authSuccess_16_listener() {\n              return ctx.onAuthSuccess();\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"app-upload-dialog\", 14);\n            i0.ɵɵlistener(\"visibleChange\", function GeneratorComponent_Template_app_upload_dialog_visibleChange_17_listener($event) {\n              return ctx.uploadDialogVisible = $event;\n            })(\"uploadComplete\", function GeneratorComponent_Template_app_upload_dialog_uploadComplete_17_listener($event) {\n              return ctx.onUploadComplete($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"app-sidebar\", 15);\n            i0.ɵɵlistener(\"visibleChange\", function GeneratorComponent_Template_app_sidebar_visibleChange_18_listener($event) {\n              return ctx.sidebarVisible = $event;\n            })(\"menuItemClick\", function GeneratorComponent_Template_app_sidebar_menuItemClick_18_listener($event) {\n              return ctx.onSidebarMenuClick($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"app-video-settings\", 16);\n            i0.ɵɵlistener(\"visibleChange\", function GeneratorComponent_Template_app_video_settings_visibleChange_19_listener($event) {\n              return ctx.videoSettingsVisible = $event;\n            })(\"settingsSaved\", function GeneratorComponent_Template_app_video_settings_settingsSaved_19_listener($event) {\n              return ctx.onVideoSettingsSaved($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"app-terms-dialog\", 17);\n            i0.ɵɵlistener(\"visibleChange\", function GeneratorComponent_Template_app_terms_dialog_visibleChange_20_listener($event) {\n              return ctx.termsVisible = $event;\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"app-privacy-dialog\", 17);\n            i0.ɵɵlistener(\"visibleChange\", function GeneratorComponent_Template_app_privacy_dialog_visibleChange_21_listener($event) {\n              return ctx.privacyVisible = $event;\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(22, \"app-security-warning\")(23, \"app-link-shortener\");\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            const _r1 = i0.ɵɵreference(12);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"text\", true)(\"rounded\", true);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.loaded)(\"ngIfElse\", _r1);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.ffmpegExecuting);\n            i0.ɵɵadvance(1);\n            i0.ɵɵstyleMap(i0.ɵɵpureFunction0(18, _c0));\n            i0.ɵɵproperty(\"visible\", ctx.videoPickerVisible)(\"resizable\", true)(\"breakpoints\", i0.ɵɵpureFunction0(19, _c1));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"visible\", ctx.authDialogVisible);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"visible\", ctx.uploadDialogVisible)(\"videoBlob\", ctx.currentVideoBlob)(\"surahInfo\", ctx.currentSurahInfo);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"visible\", ctx.sidebarVisible);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"visible\", ctx.videoSettingsVisible);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"visible\", ctx.termsVisible);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"visible\", ctx.privacyVisible);\n          }\n        },\n        dependencies: [i5.NgIf, i6.InputText, i7.Button, i8.ProgressBar, i9.Toast, i10.Dropdown, i11.NgControlStatus, i11.NgModel, i12.Dialog, i13.Tooltip, i14.Slider, i15.VideosDialogComponent, i16.AuthDialogComponent, i17.UploadDialogComponent, i18.SidebarComponent, i19.VideoSettingsComponent, i20.TermsDialogComponent, i21.PrivacyDialogComponent, i22.SecurityWarningComponent, i23.LinkShortenerComponent],\n        styles: [\"[_nghost-%COMP%]{--primary-color: #667eea;--primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);--secondary-color: #f8f9fa;--accent-color: #ffd700;--text-primary: #2c3e50;--text-secondary: #6c757d;--border-color: #e9ecef;--shadow: 0 4px 6px rgba(0, 0, 0, .1);--shadow-hover: 0 8px 15px rgba(0, 0, 0, .2)}  body{background:linear-gradient(135deg,#f5f7fa 0%,#c3cfe2 100%);min-height:100vh;font-family:Segoe UI,Tahoma,Geneva,Verdana,sans-serif}.main-container[_ngcontent-%COMP%]{min-height:100vh;padding:0}.app-header[_ngcontent-%COMP%]{background:var(--primary-gradient);color:#fff;padding:1.5rem 2rem;box-shadow:var(--shadow);position:sticky;top:0;z-index:100}.app-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;max-width:1200px;margin:0 auto}.app-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .app-title[_ngcontent-%COMP%]{margin:0;font-size:1.8rem;font-weight:700;color:#fff}.app-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .app-subtitle[_ngcontent-%COMP%]{margin:.5rem 0 0;font-size:1rem;opacity:.9;color:#ffffffe6}.app-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]     .p-button{background:rgba(255,255,255,.2);border:1px solid rgba(255,255,255,.3);color:#fff}.app-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]     .p-button:hover{background:rgba(255,255,255,.3);border-color:#fff6}.content-wrapper[_ngcontent-%COMP%]{max-width:800px;margin:2rem auto;padding:0 1rem}.form-container[_ngcontent-%COMP%]{background:white;border-radius:16px;padding:2rem;box-shadow:var(--shadow);border:1px solid var(--border-color)}.form-group[_ngcontent-%COMP%]{margin-bottom:1.5rem}.form-group[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;font-weight:600;color:var(--text-primary);margin-bottom:.75rem;font-size:1rem}.form-group[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:var(--primary-color);font-size:1.1rem}.form-row[_ngcontent-%COMP%]{display:flex;gap:1rem}.form-row[_ngcontent-%COMP%]   .half-width[_ngcontent-%COMP%]{flex:1}  .custom-dropdown .p-dropdown{width:100%;border:2px solid var(--border-color);border-radius:8px;transition:all .3s ease}  .custom-dropdown .p-dropdown:hover{border-color:var(--primary-color)}  .custom-dropdown .p-dropdown.p-focus{border-color:var(--primary-color);box-shadow:0 0 0 3px #667eea1a}  .custom-dropdown .p-dropdown-label{padding:.75rem 1rem;font-size:1rem}.ayah-range-container[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1rem}.ayah-range-container[_ngcontent-%COMP%]   .ayah-input[_ngcontent-%COMP%]{flex:1}.ayah-range-container[_ngcontent-%COMP%]   .ayah-input[_ngcontent-%COMP%]   .ayah-label[_ngcontent-%COMP%]{display:block;font-size:.9rem;color:var(--text-secondary);margin-bottom:.5rem;text-align:center}.ayah-range-container[_ngcontent-%COMP%]   .ayah-input[_ngcontent-%COMP%]   .ayah-number-input[_ngcontent-%COMP%]{width:100%;padding:.75rem;border:2px solid var(--border-color);border-radius:8px;text-align:center;font-size:1.1rem;font-weight:600;transition:all .3s ease}.ayah-range-container[_ngcontent-%COMP%]   .ayah-input[_ngcontent-%COMP%]   .ayah-number-input[_ngcontent-%COMP%]:hover{border-color:var(--primary-color)}.ayah-range-container[_ngcontent-%COMP%]   .ayah-input[_ngcontent-%COMP%]   .ayah-number-input[_ngcontent-%COMP%]:focus{border-color:var(--primary-color);box-shadow:0 0 0 3px #667eea1a;outline:none}.ayah-range-container[_ngcontent-%COMP%]   .range-separator[_ngcontent-%COMP%]{color:var(--primary-color);font-size:1.2rem;margin-top:1.5rem}  .custom-slider .p-slider{background:var(--border-color);border-radius:4px}  .custom-slider .p-slider .p-slider-range{background:var(--primary-gradient)}  .custom-slider .p-slider .p-slider-handle{background:white;border:3px solid var(--primary-color);box-shadow:var(--shadow)}  .custom-slider .p-slider .p-slider-handle:hover{transform:scale(1.1)}  .video-picker-btn{width:100%}  .video-picker-btn .p-button{width:100%;padding:.75rem 1rem;border-radius:8px;font-weight:500;transition:all .3s ease}  .video-picker-btn .p-button:hover{transform:translateY(-2px);box-shadow:var(--shadow-hover)}.generate-section[_ngcontent-%COMP%]{text-align:center;margin-top:2rem;padding-top:2rem;border-top:1px solid var(--border-color)}  .generate-btn .p-button{padding:1rem 2rem;font-size:1.1rem;font-weight:600;border-radius:12px;background:var(--primary-gradient);border:none;transition:all .3s ease}  .generate-btn .p-button:hover:not(:disabled){transform:translateY(-3px);box-shadow:var(--shadow-hover)}  .generate-btn .p-button:disabled{opacity:.6;cursor:not-allowed}@media (max-width: 768px){.app-header[_ngcontent-%COMP%]{padding:1rem}.app-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{flex-direction:column;gap:1rem;text-align:center}.app-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .app-title[_ngcontent-%COMP%]{font-size:1.5rem}.content-wrapper[_ngcontent-%COMP%]{margin:1rem auto;padding:0 .5rem}.form-container[_ngcontent-%COMP%]{padding:1.5rem;border-radius:12px}.form-row[_ngcontent-%COMP%], .ayah-range-container[_ngcontent-%COMP%]{flex-direction:column}.ayah-range-container[_ngcontent-%COMP%]   .range-separator[_ngcontent-%COMP%]{transform:rotate(90deg);margin:0}}\"]\n      });\n    }\n  }\n  return GeneratorComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}