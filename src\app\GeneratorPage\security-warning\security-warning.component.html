<div class="security-overlay" *ngIf="showWarning">
  <div class="security-warning-container">
    <div class="warning-content">
      
      <!-- Warning Icon -->
      <div class="warning-icon">
        <i class="pi pi-exclamation-triangle"></i>
      </div>
      
      <!-- Warning Title -->
      <h2 class="warning-title">{{ getWarningTitle() }}</h2>
      
      <!-- Warning Message -->
      <p class="warning-message">{{ getWarningMessage() }}</p>
      
      <!-- Security Details -->
      <div class="security-details" *ngIf="securityCheck">
        <div class="detail-item" *ngIf="securityCheck.isVpnDetected">
          <i class="pi pi-shield text-danger"></i>
          <span>تم اكتشاف VPN أو بروكسي</span>
        </div>
        
        <div class="detail-item" *ngIf="securityCheck.isAdBlockerDetected">
          <i class="pi pi-ban text-danger"></i>
          <span>تم اكتشاف أداة حظر الإعلانات</span>
        </div>
        
        <div class="detail-item" *ngIf="securityCheck.isDnsBlocked">
          <i class="pi pi-globe text-danger"></i>
          <span>تم اكتشاف DNS محجوب للإعلانات</span>
        </div>
      </div>
      
      <!-- Instructions -->
      <div class="instructions-section">
        <h3>خطوات الحل:</h3>
        <ol class="instructions-list">
          <li *ngFor="let instruction of getInstructions()">
            {{ instruction }}
          </li>
        </ol>
      </div>
      
      <!-- Important Notice -->
      <div class="important-notice">
        <i class="pi pi-info-circle"></i>
        <div>
          <h4>لماذا هذه القيود؟</h4>
          <p>
            هذه القيود ضرورية للحفاظ على استدامة الخدمة وتغطية تكاليف التشغيل. 
            الإعلانات تساعدنا في تقديم هذه الخدمة مجاناً لجميع المستخدمين.
          </p>
        </div>
      </div>
      
      <!-- Action Buttons -->
      <div class="action-buttons">
        <p-button 
          label="إعادة فحص" 
          icon="pi pi-refresh" 
          severity="primary"
          [loading]="isCheckingAgain"
          loadingIcon="pi pi-spin pi-spinner"
          (onClick)="recheckSecurity()">
        </p-button>
        
        <p-button 
          label="تواصل مع الدعم" 
          icon="pi pi-envelope" 
          severity="secondary"
          [outlined]="true"
          (onClick)="openSupportEmail()">
        </p-button>
      </div>
      
      <!-- Help Text -->
      <div class="help-text">
        <p>
          إذا كنت تواجه صعوبة في حل هذه المشكلة، يرجى التواصل مع فريق الدعم 
          وسنساعدك في أقرب وقت ممكن.
        </p>
      </div>
      
    </div>
  </div>
</div>
