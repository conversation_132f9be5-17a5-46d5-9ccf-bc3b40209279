{"ast": null, "code": "/**\n * Generate an unique message ID.\n */\nexport const getMessageID = (() => {\n  let messageID = 0;\n  return () => messageID++;\n})();", "map": {"version": 3, "names": ["getMessageID", "messageID"], "sources": ["C:/Users/<USER>/Desktop/تطبيق ناشر ايات قئانية/QuranVidGen/node_modules/@ffmpeg/ffmpeg/dist/esm/utils.js"], "sourcesContent": ["/**\n * Generate an unique message ID.\n */\nexport const getMessageID = (() => {\n    let messageID = 0;\n    return () => messageID++;\n})();\n"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,MAAMA,YAAY,GAAG,CAAC,MAAM;EAC/B,IAAIC,SAAS,GAAG,CAAC;EACjB,OAAO,MAAMA,SAAS,EAAE;AAC5B,CAAC,EAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}