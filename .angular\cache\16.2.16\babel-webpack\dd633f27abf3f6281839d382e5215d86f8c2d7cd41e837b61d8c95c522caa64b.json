{"ast": null, "code": "import { animation, style, animate, trigger, transition, useAnimation } from '@angular/animations';\nimport * as i2 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, PLATFORM_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ContentChildren, ViewChild, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i1 from 'primeng/api';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler, ConnectedOverlayScrollHandler } from 'primeng/dom';\nimport { ObjectUtils, ZIndexUtils } from 'primeng/utils';\nconst _c0 = [\"overlay\"];\nconst _c1 = [\"content\"];\nfunction Overlay_div_0_div_2_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c2 = function (a0, a1, a2) {\n  return {\n    showTransitionParams: a0,\n    hideTransitionParams: a1,\n    transform: a2\n  };\n};\nconst _c3 = function (a1) {\n  return {\n    value: \"visible\",\n    params: a1\n  };\n};\nconst _c4 = function (a0) {\n  return {\n    mode: a0\n  };\n};\nconst _c5 = function (a0) {\n  return {\n    $implicit: a0\n  };\n};\nfunction Overlay_div_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1, 3);\n    i0.ɵɵlistener(\"click\", function Overlay_div_0_div_2_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.onOverlayContentClick($event));\n    })(\"@overlayContentAnimation.start\", function Overlay_div_0_div_2_Template_div_animation_overlayContentAnimation_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r7.onOverlayContentAnimationStart($event));\n    })(\"@overlayContentAnimation.done\", function Overlay_div_0_div_2_Template_div_animation_overlayContentAnimation_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.onOverlayContentAnimationDone($event));\n    });\n    i0.ɵɵprojection(2);\n    i0.ɵɵtemplate(3, Overlay_div_0_div_2_ng_container_3_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r2.contentStyleClass);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r2.contentStyle)(\"ngClass\", \"p-overlay-content\")(\"@overlayContentAnimation\", i0.ɵɵpureFunction1(11, _c3, i0.ɵɵpureFunction3(7, _c2, ctx_r2.showTransitionOptions, ctx_r2.hideTransitionOptions, ctx_r2.transformOptions[ctx_r2.modal ? ctx_r2.overlayResponsiveDirection : \"default\"])));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.contentTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(15, _c5, i0.ɵɵpureFunction1(13, _c4, ctx_r2.overlayMode)));\n  }\n}\nconst _c6 = function (a1, a2, a3, a4, a5, a6, a7, a8, a9, a10, a11, a12, a13, a14) {\n  return {\n    \"p-overlay p-component\": true,\n    \"p-overlay-modal p-component-overlay p-component-overlay-enter\": a1,\n    \"p-overlay-center\": a2,\n    \"p-overlay-top\": a3,\n    \"p-overlay-top-start\": a4,\n    \"p-overlay-top-end\": a5,\n    \"p-overlay-bottom\": a6,\n    \"p-overlay-bottom-start\": a7,\n    \"p-overlay-bottom-end\": a8,\n    \"p-overlay-left\": a9,\n    \"p-overlay-left-start\": a10,\n    \"p-overlay-left-end\": a11,\n    \"p-overlay-right\": a12,\n    \"p-overlay-right-start\": a13,\n    \"p-overlay-right-end\": a14\n  };\n};\nfunction Overlay_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1, 2);\n    i0.ɵɵlistener(\"click\", function Overlay_div_0_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.onOverlayClick());\n    });\n    i0.ɵɵtemplate(2, Overlay_div_0_div_2_Template, 4, 17, \"div\", 0);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r0.styleClass);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r0.style)(\"ngClass\", i0.ɵɵpureFunctionV(5, _c6, [ctx_r0.modal, ctx_r0.modal && ctx_r0.overlayResponsiveDirection === \"center\", ctx_r0.modal && ctx_r0.overlayResponsiveDirection === \"top\", ctx_r0.modal && ctx_r0.overlayResponsiveDirection === \"top-start\", ctx_r0.modal && ctx_r0.overlayResponsiveDirection === \"top-end\", ctx_r0.modal && ctx_r0.overlayResponsiveDirection === \"bottom\", ctx_r0.modal && ctx_r0.overlayResponsiveDirection === \"bottom-start\", ctx_r0.modal && ctx_r0.overlayResponsiveDirection === \"bottom-end\", ctx_r0.modal && ctx_r0.overlayResponsiveDirection === \"left\", ctx_r0.modal && ctx_r0.overlayResponsiveDirection === \"left-start\", ctx_r0.modal && ctx_r0.overlayResponsiveDirection === \"left-end\", ctx_r0.modal && ctx_r0.overlayResponsiveDirection === \"right\", ctx_r0.modal && ctx_r0.overlayResponsiveDirection === \"right-start\", ctx_r0.modal && ctx_r0.overlayResponsiveDirection === \"right-end\"]));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.visible);\n  }\n}\nconst _c7 = [\"*\"];\nconst OVERLAY_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => Overlay),\n  multi: true\n};\nconst showOverlayContentAnimation = animation([style({\n  transform: '{{transform}}',\n  opacity: 0\n}), animate('{{showTransitionParams}}')]);\nconst hideOverlayContentAnimation = animation([animate('{{hideTransitionParams}}', style({\n  transform: '{{transform}}',\n  opacity: 0\n}))]);\n/**\n * This API allows overlay components to be controlled from the PrimeNGConfig. In this way, all overlay components in the application can have the same behavior.\n * @group Components\n */\nlet Overlay = /*#__PURE__*/(() => {\n  class Overlay {\n    document;\n    platformId;\n    el;\n    renderer;\n    config;\n    overlayService;\n    cd;\n    zone;\n    /**\n     * The visible property is an input that determines the visibility of the component.\n     * @defaultValue false\n     * @group Props\n     */\n    get visible() {\n      return this._visible;\n    }\n    set visible(value) {\n      this._visible = value;\n      if (this._visible && !this.modalVisible) {\n        this.modalVisible = true;\n      }\n    }\n    /**\n     * The mode property is an input that determines the overlay mode type or string.\n     * @defaultValue null\n     * @group Props\n     */\n    get mode() {\n      return this._mode || this.overlayOptions?.mode;\n    }\n    set mode(value) {\n      this._mode = value;\n    }\n    /**\n     * The style property is an input that determines the style object for the component.\n     * @defaultValue null\n     * @group Props\n     */\n    get style() {\n      return ObjectUtils.merge(this._style, this.modal ? this.overlayResponsiveOptions?.style : this.overlayOptions?.style);\n    }\n    set style(value) {\n      this._style = value;\n    }\n    /**\n     * The styleClass property is an input that determines the CSS class(es) for the component.\n     * @defaultValue null\n     * @group Props\n     */\n    get styleClass() {\n      return ObjectUtils.merge(this._styleClass, this.modal ? this.overlayResponsiveOptions?.styleClass : this.overlayOptions?.styleClass);\n    }\n    set styleClass(value) {\n      this._styleClass = value;\n    }\n    /**\n     * The contentStyle property is an input that determines the style object for the content of the component.\n     * @defaultValue null\n     * @group Props\n     */\n    get contentStyle() {\n      return ObjectUtils.merge(this._contentStyle, this.modal ? this.overlayResponsiveOptions?.contentStyle : this.overlayOptions?.contentStyle);\n    }\n    set contentStyle(value) {\n      this._contentStyle = value;\n    }\n    /**\n     * The contentStyleClass property is an input that determines the CSS class(es) for the content of the component.\n     * @defaultValue null\n     * @group Props\n     */\n    get contentStyleClass() {\n      return ObjectUtils.merge(this._contentStyleClass, this.modal ? this.overlayResponsiveOptions?.contentStyleClass : this.overlayOptions?.contentStyleClass);\n    }\n    set contentStyleClass(value) {\n      this._contentStyleClass = value;\n    }\n    /**\n     * The target property is an input that specifies the target element or selector for the component.\n     * @defaultValue null\n     * @group Props\n     */\n    get target() {\n      const value = this._target || this.overlayOptions?.target;\n      return value === undefined ? '@prev' : value;\n    }\n    set target(value) {\n      this._target = value;\n    }\n    /**\n     * Overlay can be mounted into its location, body or DOM element instance using this option.\n     * @defaultValue null\n     * @group Props\n     */\n    get appendTo() {\n      return this._appendTo || this.overlayOptions?.appendTo;\n    }\n    set appendTo(value) {\n      this._appendTo = value;\n    }\n    /**\n     * The autoZIndex determines whether to automatically manage layering. Its default value is 'false'.\n     * @defaultValue false\n     * @group Props\n     */\n    get autoZIndex() {\n      const value = this._autoZIndex || this.overlayOptions?.autoZIndex;\n      return value === undefined ? true : value;\n    }\n    set autoZIndex(value) {\n      this._autoZIndex = value;\n    }\n    /**\n     * The baseZIndex is base zIndex value to use in layering.\n     * @defaultValue null\n     * @group Props\n     */\n    get baseZIndex() {\n      const value = this._baseZIndex || this.overlayOptions?.baseZIndex;\n      return value === undefined ? 0 : value;\n    }\n    set baseZIndex(value) {\n      this._baseZIndex = value;\n    }\n    /**\n     * Transition options of the show or hide animation.\n     * @defaultValue .12s cubic-bezier(0, 0, 0.2, 1)\n     * @group Props\n     */\n    get showTransitionOptions() {\n      const value = this._showTransitionOptions || this.overlayOptions?.showTransitionOptions;\n      return value === undefined ? '.12s cubic-bezier(0, 0, 0.2, 1)' : value;\n    }\n    set showTransitionOptions(value) {\n      this._showTransitionOptions = value;\n    }\n    /**\n     * The hideTransitionOptions property is an input that determines the CSS transition options for hiding the component.\n     * @defaultValue .1s linear\n     * @group Props\n     */\n    get hideTransitionOptions() {\n      const value = this._hideTransitionOptions || this.overlayOptions?.hideTransitionOptions;\n      return value === undefined ? '.1s linear' : value;\n    }\n    set hideTransitionOptions(value) {\n      this._hideTransitionOptions = value;\n    }\n    /**\n     * The listener property is an input that specifies the listener object for the component.\n     * @defaultValue null\n     * @group Props\n     */\n    get listener() {\n      return this._listener || this.overlayOptions?.listener;\n    }\n    set listener(value) {\n      this._listener = value;\n    }\n    /**\n     * It is the option used to determine in which mode it should appear according to the given media or breakpoint.\n     * @defaultValue null\n     * @group Props\n     */\n    get responsive() {\n      return this._responsive || this.overlayOptions?.responsive;\n    }\n    set responsive(val) {\n      this._responsive = val;\n    }\n    /**\n     * The options property is an input that specifies the overlay options for the component.\n     * @defaultValue null\n     * @group Props\n     */\n    get options() {\n      return this._options;\n    }\n    set options(val) {\n      this._options = val;\n    }\n    /**\n     * This EventEmitter is used to notify changes in the visibility state of a component.\n     * @param {Boolean} boolean - Value of visibility as boolean.\n     * @group Emits\n     */\n    visibleChange = new EventEmitter();\n    /**\n     * Callback to invoke before the overlay is shown.\n     * @param {OverlayOnBeforeShowEvent} event - Custom overlay before show event.\n     * @group Emits\n     */\n    onBeforeShow = new EventEmitter();\n    /**\n     * Callback to invoke when the overlay is shown.\n     * @param {OverlayOnShowEvent} event - Custom overlay show event.\n     * @group Emits\n     */\n    onShow = new EventEmitter();\n    /**\n     * Callback to invoke before the overlay is hidden.\n     * @param {OverlayOnBeforeHideEvent} event - Custom overlay before hide event.\n     * @group Emits\n     */\n    onBeforeHide = new EventEmitter();\n    /**\n     * Callback to invoke when the overlay is hidden\n     * @param {OverlayOnHideEvent} event - Custom hide event.\n     * @group Emits\n     */\n    onHide = new EventEmitter();\n    /**\n     * Callback to invoke when the animation is started.\n     * @param {AnimationEvent} event - Animation event.\n     * @group Emits\n     */\n    onAnimationStart = new EventEmitter();\n    /**\n     * Callback to invoke when the animation is done.\n     * @param {AnimationEvent} event - Animation event.\n     * @group Emits\n     */\n    onAnimationDone = new EventEmitter();\n    templates;\n    overlayViewChild;\n    contentViewChild;\n    contentTemplate;\n    _visible = false;\n    _mode;\n    _style;\n    _styleClass;\n    _contentStyle;\n    _contentStyleClass;\n    _target;\n    _appendTo;\n    _autoZIndex;\n    _baseZIndex;\n    _showTransitionOptions;\n    _hideTransitionOptions;\n    _listener;\n    _responsive;\n    _options;\n    modalVisible = false;\n    isOverlayClicked = false;\n    isOverlayContentClicked = false;\n    scrollHandler;\n    documentClickListener;\n    documentResizeListener;\n    documentKeyboardListener;\n    window;\n    transformOptions = {\n      default: 'scaleY(0.8)',\n      center: 'scale(0.7)',\n      top: 'translate3d(0px, -100%, 0px)',\n      'top-start': 'translate3d(0px, -100%, 0px)',\n      'top-end': 'translate3d(0px, -100%, 0px)',\n      bottom: 'translate3d(0px, 100%, 0px)',\n      'bottom-start': 'translate3d(0px, 100%, 0px)',\n      'bottom-end': 'translate3d(0px, 100%, 0px)',\n      left: 'translate3d(-100%, 0px, 0px)',\n      'left-start': 'translate3d(-100%, 0px, 0px)',\n      'left-end': 'translate3d(-100%, 0px, 0px)',\n      right: 'translate3d(100%, 0px, 0px)',\n      'right-start': 'translate3d(100%, 0px, 0px)',\n      'right-end': 'translate3d(100%, 0px, 0px)'\n    };\n    get modal() {\n      if (isPlatformBrowser(this.platformId)) {\n        return this.mode === 'modal' || this.overlayResponsiveOptions && this.window?.matchMedia(this.overlayResponsiveOptions.media?.replace('@media', '') || `(max-width: ${this.overlayResponsiveOptions.breakpoint})`).matches;\n      }\n    }\n    get overlayMode() {\n      return this.mode || (this.modal ? 'modal' : 'overlay');\n    }\n    get overlayOptions() {\n      return {\n        ...this.config?.overlayOptions,\n        ...this.options\n      }; // TODO: Improve performance\n    }\n\n    get overlayResponsiveOptions() {\n      return {\n        ...this.overlayOptions?.responsive,\n        ...this.responsive\n      }; // TODO: Improve performance\n    }\n\n    get overlayResponsiveDirection() {\n      return this.overlayResponsiveOptions?.direction || 'center';\n    }\n    get overlayEl() {\n      return this.overlayViewChild?.nativeElement;\n    }\n    get contentEl() {\n      return this.contentViewChild?.nativeElement;\n    }\n    get targetEl() {\n      return DomHandler.getTargetElement(this.target, this.el?.nativeElement);\n    }\n    constructor(document, platformId, el, renderer, config, overlayService, cd, zone) {\n      this.document = document;\n      this.platformId = platformId;\n      this.el = el;\n      this.renderer = renderer;\n      this.config = config;\n      this.overlayService = overlayService;\n      this.cd = cd;\n      this.zone = zone;\n      this.window = this.document.defaultView;\n    }\n    ngAfterContentInit() {\n      this.templates?.forEach(item => {\n        switch (item.getType()) {\n          case 'content':\n            this.contentTemplate = item.template;\n            break;\n          // TODO: new template types may be added.\n          default:\n            this.contentTemplate = item.template;\n            break;\n        }\n      });\n    }\n    show(overlay, isFocus = false) {\n      this.onVisibleChange(true);\n      this.handleEvents('onShow', {\n        overlay: overlay || this.overlayEl,\n        target: this.targetEl,\n        mode: this.overlayMode\n      });\n      isFocus && DomHandler.focus(this.targetEl);\n      this.modal && DomHandler.addClass(this.document?.body, 'p-overflow-hidden');\n    }\n    hide(overlay, isFocus = false) {\n      if (!this.visible) {\n        return;\n      } else {\n        this.onVisibleChange(false);\n        this.handleEvents('onHide', {\n          overlay: overlay || this.overlayEl,\n          target: this.targetEl,\n          mode: this.overlayMode\n        });\n        isFocus && DomHandler.focus(this.targetEl);\n        this.modal && DomHandler.removeClass(this.document?.body, 'p-overflow-hidden');\n      }\n    }\n    alignOverlay() {\n      !this.modal && DomHandler.alignOverlay(this.overlayEl, this.targetEl, this.appendTo);\n    }\n    onVisibleChange(visible) {\n      this._visible = visible;\n      this.visibleChange.emit(visible);\n    }\n    onOverlayClick() {\n      this.isOverlayClicked = true;\n    }\n    onOverlayContentClick(event) {\n      this.overlayService.add({\n        originalEvent: event,\n        target: this.targetEl\n      });\n      this.isOverlayContentClicked = true;\n    }\n    onOverlayContentAnimationStart(event) {\n      switch (event.toState) {\n        case 'visible':\n          this.handleEvents('onBeforeShow', {\n            overlay: this.overlayEl,\n            target: this.targetEl,\n            mode: this.overlayMode\n          });\n          if (this.autoZIndex) {\n            ZIndexUtils.set(this.overlayMode, this.overlayEl, this.baseZIndex + this.config?.zIndex[this.overlayMode]);\n          }\n          DomHandler.appendOverlay(this.overlayEl, this.appendTo === 'body' ? this.document.body : this.appendTo, this.appendTo);\n          this.alignOverlay();\n          break;\n        case 'void':\n          this.handleEvents('onBeforeHide', {\n            overlay: this.overlayEl,\n            target: this.targetEl,\n            mode: this.overlayMode\n          });\n          this.modal && DomHandler.addClass(this.overlayEl, 'p-component-overlay-leave');\n          break;\n      }\n      this.handleEvents('onAnimationStart', event);\n    }\n    onOverlayContentAnimationDone(event) {\n      const container = this.overlayEl || event.element.parentElement;\n      switch (event.toState) {\n        case 'visible':\n          this.show(container, true);\n          this.bindListeners();\n          break;\n        case 'void':\n          this.hide(container, true);\n          this.unbindListeners();\n          DomHandler.appendOverlay(this.overlayEl, this.targetEl, this.appendTo);\n          ZIndexUtils.clear(container);\n          this.modalVisible = false;\n          this.cd.markForCheck();\n          break;\n      }\n      this.handleEvents('onAnimationDone', event);\n    }\n    handleEvents(name, params) {\n      this[name].emit(params);\n      this.options && this.options[name] && this.options[name](params);\n      this.config?.overlayOptions && (this.config?.overlayOptions)[name] && (this.config?.overlayOptions)[name](params);\n    }\n    bindListeners() {\n      this.bindScrollListener();\n      this.bindDocumentClickListener();\n      this.bindDocumentResizeListener();\n      this.bindDocumentKeyboardListener();\n    }\n    unbindListeners() {\n      this.unbindScrollListener();\n      this.unbindDocumentClickListener();\n      this.unbindDocumentResizeListener();\n      this.unbindDocumentKeyboardListener();\n    }\n    bindScrollListener() {\n      if (!this.scrollHandler) {\n        this.scrollHandler = new ConnectedOverlayScrollHandler(this.targetEl, event => {\n          const valid = this.listener ? this.listener(event, {\n            type: 'scroll',\n            mode: this.overlayMode,\n            valid: true\n          }) : true;\n          valid && this.hide(event, true);\n        });\n      }\n      this.scrollHandler.bindScrollListener();\n    }\n    unbindScrollListener() {\n      if (this.scrollHandler) {\n        this.scrollHandler.unbindScrollListener();\n      }\n    }\n    bindDocumentClickListener() {\n      if (!this.documentClickListener) {\n        this.documentClickListener = this.renderer.listen(this.document, 'click', event => {\n          const isTargetClicked = this.targetEl && (this.targetEl.isSameNode(event.target) || !this.isOverlayClicked && this.targetEl.contains(event.target));\n          const isOutsideClicked = !isTargetClicked && !this.isOverlayContentClicked;\n          const valid = this.listener ? this.listener(event, {\n            type: 'outside',\n            mode: this.overlayMode,\n            valid: event.which !== 3 && isOutsideClicked\n          }) : isOutsideClicked;\n          valid && this.hide(event);\n          this.isOverlayClicked = this.isOverlayContentClicked = false;\n        });\n      }\n    }\n    unbindDocumentClickListener() {\n      if (this.documentClickListener) {\n        this.documentClickListener();\n        this.documentClickListener = null;\n      }\n    }\n    bindDocumentResizeListener() {\n      if (!this.documentResizeListener) {\n        this.documentResizeListener = this.renderer.listen(this.window, 'resize', event => {\n          const valid = this.listener ? this.listener(event, {\n            type: 'resize',\n            mode: this.overlayMode,\n            valid: !DomHandler.isTouchDevice()\n          }) : !DomHandler.isTouchDevice();\n          valid && this.hide(event, true);\n        });\n      }\n    }\n    unbindDocumentResizeListener() {\n      if (this.documentResizeListener) {\n        this.documentResizeListener();\n        this.documentResizeListener = null;\n      }\n    }\n    bindDocumentKeyboardListener() {\n      if (this.documentKeyboardListener) {\n        return;\n      }\n      this.zone.runOutsideAngular(() => {\n        this.documentKeyboardListener = this.renderer.listen(this.window, 'keydown', event => {\n          if (this.overlayOptions.hideOnEscape === false || event.code !== 'Escape') {\n            return;\n          }\n          const valid = this.listener ? this.listener(event, {\n            type: 'keydown',\n            mode: this.overlayMode,\n            valid: !DomHandler.isTouchDevice()\n          }) : !DomHandler.isTouchDevice();\n          if (valid) {\n            this.zone.run(() => {\n              this.hide(event, true);\n            });\n          }\n        });\n      });\n    }\n    unbindDocumentKeyboardListener() {\n      if (this.documentKeyboardListener) {\n        this.documentKeyboardListener();\n        this.documentKeyboardListener = null;\n      }\n    }\n    ngOnDestroy() {\n      this.hide(this.overlayEl, true);\n      if (this.overlayEl) {\n        DomHandler.appendOverlay(this.overlayEl, this.targetEl, this.appendTo);\n        ZIndexUtils.clear(this.overlayEl);\n      }\n      if (this.scrollHandler) {\n        this.scrollHandler.destroy();\n        this.scrollHandler = null;\n      }\n      this.unbindListeners();\n    }\n    static ɵfac = function Overlay_Factory(t) {\n      return new (t || Overlay)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i1.PrimeNGConfig), i0.ɵɵdirectiveInject(i1.OverlayService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: Overlay,\n      selectors: [[\"p-overlay\"]],\n      contentQueries: function Overlay_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      viewQuery: function Overlay_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.overlayViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.contentViewChild = _t.first);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        visible: \"visible\",\n        mode: \"mode\",\n        style: \"style\",\n        styleClass: \"styleClass\",\n        contentStyle: \"contentStyle\",\n        contentStyleClass: \"contentStyleClass\",\n        target: \"target\",\n        appendTo: \"appendTo\",\n        autoZIndex: \"autoZIndex\",\n        baseZIndex: \"baseZIndex\",\n        showTransitionOptions: \"showTransitionOptions\",\n        hideTransitionOptions: \"hideTransitionOptions\",\n        listener: \"listener\",\n        responsive: \"responsive\",\n        options: \"options\"\n      },\n      outputs: {\n        visibleChange: \"visibleChange\",\n        onBeforeShow: \"onBeforeShow\",\n        onShow: \"onShow\",\n        onBeforeHide: \"onBeforeHide\",\n        onHide: \"onHide\",\n        onAnimationStart: \"onAnimationStart\",\n        onAnimationDone: \"onAnimationDone\"\n      },\n      features: [i0.ɵɵProvidersFeature([OVERLAY_VALUE_ACCESSOR])],\n      ngContentSelectors: _c7,\n      decls: 1,\n      vars: 1,\n      consts: [[3, \"ngStyle\", \"class\", \"ngClass\", \"click\", 4, \"ngIf\"], [3, \"ngStyle\", \"ngClass\", \"click\"], [\"overlay\", \"\"], [\"content\", \"\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"]],\n      template: function Overlay_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, Overlay_div_0_Template, 3, 20, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.modalVisible);\n        }\n      },\n      dependencies: [i2.NgClass, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle],\n      styles: [\"@layer primeng{.p-overlay{position:absolute;top:0;left:0}.p-overlay-modal{display:flex;align-items:center;justify-content:center;position:fixed;top:0;left:0;width:100%;height:100%}.p-overlay-content{transform-origin:inherit}.p-overlay-modal>.p-overlay-content{z-index:1;width:90%}.p-overlay-top{align-items:flex-start}.p-overlay-top-start{align-items:flex-start;justify-content:flex-start}.p-overlay-top-end{align-items:flex-start;justify-content:flex-end}.p-overlay-bottom{align-items:flex-end}.p-overlay-bottom-start{align-items:flex-end;justify-content:flex-start}.p-overlay-bottom-end{align-items:flex-end;justify-content:flex-end}.p-overlay-left{justify-content:flex-start}.p-overlay-left-start{justify-content:flex-start;align-items:flex-start}.p-overlay-left-end{justify-content:flex-start;align-items:flex-end}.p-overlay-right{justify-content:flex-end}.p-overlay-right-start{justify-content:flex-end;align-items:flex-start}.p-overlay-right-end{justify-content:flex-end;align-items:flex-end}}\\n\"],\n      encapsulation: 2,\n      data: {\n        animation: [trigger('overlayContentAnimation', [transition(':enter', [useAnimation(showOverlayContentAnimation)]), transition(':leave', [useAnimation(hideOverlayContentAnimation)])])]\n      },\n      changeDetection: 0\n    });\n  }\n  return Overlay;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet OverlayModule = /*#__PURE__*/(() => {\n  class OverlayModule {\n    static ɵfac = function OverlayModule_Factory(t) {\n      return new (t || OverlayModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: OverlayModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, SharedModule, SharedModule]\n    });\n  }\n  return OverlayModule;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { OVERLAY_VALUE_ACCESSOR, Overlay, OverlayModule };\n//# sourceMappingURL=primeng-overlay.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}