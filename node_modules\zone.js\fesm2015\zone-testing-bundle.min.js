"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2022 Google LLC. https://angular.io/
 * License: MIT
 */!function(e){const t=e.performance;function n(e){t&&t.mark&&t.mark(e)}function r(e,n){t&&t.measure&&t.measure(e,n)}n("Zone");const o=e.__Zone_symbol_prefix||"__zone_symbol__";function s(e){return o+e}const i=!0===e[s("forceDuplicateZoneCheck")];if(e.Zone){if(i||"function"!=typeof e.Zone.__symbol__)throw new Error("Zone already loaded.");return e.Zone}class a{static{this.__symbol__=s}static assertZonePatched(){if(e.Promise!==D.ZoneAwarePromise)throw new Error("Zone.js has detected that ZoneAwarePromise `(window|global).Promise` has been overwritten.\nMost likely cause is that a Promise polyfill has been loaded after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. If you must load one, do so before loading zone.js.)")}static get root(){let e=a.current;for(;e.parent;)e=e.parent;return e}static get current(){return R.zone}static get currentTask(){return C}static __load_patch(t,o,s=!1){if(D.hasOwnProperty(t)){if(!s&&i)throw Error("Already loaded patch: "+t)}else if(!e["__Zone_disable_"+t]){const s="Zone:"+t;n(s),D[t]=o(e,a,I),r(s,s)}}get parent(){return this._parent}get name(){return this._name}constructor(e,t){this._parent=e,this._name=t?t.name||"unnamed":"<root>",this._properties=t&&t.properties||{},this._zoneDelegate=new l(this,this._parent&&this._parent._zoneDelegate,t)}get(e){const t=this.getZoneWith(e);if(t)return t._properties[e]}getZoneWith(e){let t=this;for(;t;){if(t._properties.hasOwnProperty(e))return t;t=t._parent}return null}fork(e){if(!e)throw new Error("ZoneSpec required!");return this._zoneDelegate.fork(this,e)}wrap(e,t){if("function"!=typeof e)throw new Error("Expecting function got: "+e);const n=this._zoneDelegate.intercept(this,e,t),r=this;return function(){return r.runGuarded(n,this,arguments,t)}}run(e,t,n,r){R={parent:R,zone:this};try{return this._zoneDelegate.invoke(this,e,t,n,r)}finally{R=R.parent}}runGuarded(e,t=null,n,r){R={parent:R,zone:this};try{try{return this._zoneDelegate.invoke(this,e,t,n,r)}catch(e){if(this._zoneDelegate.handleError(this,e))throw e}}finally{R=R.parent}}runTask(e,t,n){if(e.zone!=this)throw new Error("A task can only be run in the zone of creation! (Creation: "+(e.zone||g).name+"; Execution: "+this.name+")");if(e.state===E&&(e.type===A||e.type===O))return;const r=e.state!=v;r&&e._transitionTo(v,b),e.runCount++;const o=C;C=e,R={parent:R,zone:this};try{e.type==O&&e.data&&!e.data.isPeriodic&&(e.cancelFn=void 0);try{return this._zoneDelegate.invokeTask(this,e,t,n)}catch(e){if(this._zoneDelegate.handleError(this,e))throw e}}finally{e.state!==E&&e.state!==w&&(e.type==A||e.data&&e.data.isPeriodic?r&&e._transitionTo(b,v):(e.runCount=0,this._updateTaskCount(e,-1),r&&e._transitionTo(E,v,E))),R=R.parent,C=o}}scheduleTask(e){if(e.zone&&e.zone!==this){let t=this;for(;t;){if(t===e.zone)throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${e.zone.name}`);t=t.parent}}e._transitionTo(S,E);const t=[];e._zoneDelegates=t,e._zone=this;try{e=this._zoneDelegate.scheduleTask(this,e)}catch(t){throw e._transitionTo(w,S,E),this._zoneDelegate.handleError(this,t),t}return e._zoneDelegates===t&&this._updateTaskCount(e,1),e.state==S&&e._transitionTo(b,S),e}scheduleMicroTask(e,t,n,r){return this.scheduleTask(new u(Z,e,t,n,r,void 0))}scheduleMacroTask(e,t,n,r,o){return this.scheduleTask(new u(O,e,t,n,r,o))}scheduleEventTask(e,t,n,r,o){return this.scheduleTask(new u(A,e,t,n,r,o))}cancelTask(e){if(e.zone!=this)throw new Error("A task can only be cancelled in the zone of creation! (Creation: "+(e.zone||g).name+"; Execution: "+this.name+")");if(e.state===b||e.state===v){e._transitionTo(P,b,v);try{this._zoneDelegate.cancelTask(this,e)}catch(t){throw e._transitionTo(w,P),this._zoneDelegate.handleError(this,t),t}return this._updateTaskCount(e,-1),e._transitionTo(E,P),e.runCount=0,e}}_updateTaskCount(e,t){const n=e._zoneDelegates;-1==t&&(e._zoneDelegates=null);for(let r=0;r<n.length;r++)n[r]._updateTaskCount(e.type,t)}}const c={name:"",onHasTask:(e,t,n,r)=>e.hasTask(n,r),onScheduleTask:(e,t,n,r)=>e.scheduleTask(n,r),onInvokeTask:(e,t,n,r,o,s)=>e.invokeTask(n,r,o,s),onCancelTask:(e,t,n,r)=>e.cancelTask(n,r)};class l{constructor(e,t,n){this._taskCounts={microTask:0,macroTask:0,eventTask:0},this.zone=e,this._parentDelegate=t,this._forkZS=n&&(n&&n.onFork?n:t._forkZS),this._forkDlgt=n&&(n.onFork?t:t._forkDlgt),this._forkCurrZone=n&&(n.onFork?this.zone:t._forkCurrZone),this._interceptZS=n&&(n.onIntercept?n:t._interceptZS),this._interceptDlgt=n&&(n.onIntercept?t:t._interceptDlgt),this._interceptCurrZone=n&&(n.onIntercept?this.zone:t._interceptCurrZone),this._invokeZS=n&&(n.onInvoke?n:t._invokeZS),this._invokeDlgt=n&&(n.onInvoke?t:t._invokeDlgt),this._invokeCurrZone=n&&(n.onInvoke?this.zone:t._invokeCurrZone),this._handleErrorZS=n&&(n.onHandleError?n:t._handleErrorZS),this._handleErrorDlgt=n&&(n.onHandleError?t:t._handleErrorDlgt),this._handleErrorCurrZone=n&&(n.onHandleError?this.zone:t._handleErrorCurrZone),this._scheduleTaskZS=n&&(n.onScheduleTask?n:t._scheduleTaskZS),this._scheduleTaskDlgt=n&&(n.onScheduleTask?t:t._scheduleTaskDlgt),this._scheduleTaskCurrZone=n&&(n.onScheduleTask?this.zone:t._scheduleTaskCurrZone),this._invokeTaskZS=n&&(n.onInvokeTask?n:t._invokeTaskZS),this._invokeTaskDlgt=n&&(n.onInvokeTask?t:t._invokeTaskDlgt),this._invokeTaskCurrZone=n&&(n.onInvokeTask?this.zone:t._invokeTaskCurrZone),this._cancelTaskZS=n&&(n.onCancelTask?n:t._cancelTaskZS),this._cancelTaskDlgt=n&&(n.onCancelTask?t:t._cancelTaskDlgt),this._cancelTaskCurrZone=n&&(n.onCancelTask?this.zone:t._cancelTaskCurrZone),this._hasTaskZS=null,this._hasTaskDlgt=null,this._hasTaskDlgtOwner=null,this._hasTaskCurrZone=null;const r=n&&n.onHasTask;(r||t&&t._hasTaskZS)&&(this._hasTaskZS=r?n:c,this._hasTaskDlgt=t,this._hasTaskDlgtOwner=this,this._hasTaskCurrZone=e,n.onScheduleTask||(this._scheduleTaskZS=c,this._scheduleTaskDlgt=t,this._scheduleTaskCurrZone=this.zone),n.onInvokeTask||(this._invokeTaskZS=c,this._invokeTaskDlgt=t,this._invokeTaskCurrZone=this.zone),n.onCancelTask||(this._cancelTaskZS=c,this._cancelTaskDlgt=t,this._cancelTaskCurrZone=this.zone))}fork(e,t){return this._forkZS?this._forkZS.onFork(this._forkDlgt,this.zone,e,t):new a(e,t)}intercept(e,t,n){return this._interceptZS?this._interceptZS.onIntercept(this._interceptDlgt,this._interceptCurrZone,e,t,n):t}invoke(e,t,n,r,o){return this._invokeZS?this._invokeZS.onInvoke(this._invokeDlgt,this._invokeCurrZone,e,t,n,r,o):t.apply(n,r)}handleError(e,t){return!this._handleErrorZS||this._handleErrorZS.onHandleError(this._handleErrorDlgt,this._handleErrorCurrZone,e,t)}scheduleTask(e,t){let n=t;if(this._scheduleTaskZS)this._hasTaskZS&&n._zoneDelegates.push(this._hasTaskDlgtOwner),n=this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt,this._scheduleTaskCurrZone,e,t),n||(n=t);else if(t.scheduleFn)t.scheduleFn(t);else{if(t.type!=Z)throw new Error("Task is missing scheduleFn.");k(t)}return n}invokeTask(e,t,n,r){return this._invokeTaskZS?this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt,this._invokeTaskCurrZone,e,t,n,r):t.callback.apply(n,r)}cancelTask(e,t){let n;if(this._cancelTaskZS)n=this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt,this._cancelTaskCurrZone,e,t);else{if(!t.cancelFn)throw Error("Task is not cancelable");n=t.cancelFn(t)}return n}hasTask(e,t){try{this._hasTaskZS&&this._hasTaskZS.onHasTask(this._hasTaskDlgt,this._hasTaskCurrZone,e,t)}catch(t){this.handleError(e,t)}}_updateTaskCount(e,t){const n=this._taskCounts,r=n[e],o=n[e]=r+t;if(o<0)throw new Error("More tasks executed then were scheduled.");0!=r&&0!=o||this.hasTask(this.zone,{microTask:n.microTask>0,macroTask:n.macroTask>0,eventTask:n.eventTask>0,change:e})}}class u{constructor(t,n,r,o,s,i){if(this._zone=null,this.runCount=0,this._zoneDelegates=null,this._state="notScheduled",this.type=t,this.source=n,this.data=o,this.scheduleFn=s,this.cancelFn=i,!r)throw new Error("callback is not defined");this.callback=r;const a=this;this.invoke=t===A&&o&&o.useG?u.invokeTask:function(){return u.invokeTask.call(e,a,this,arguments)}}static invokeTask(e,t,n){e||(e=this),M++;try{return e.runCount++,e.zone.runTask(e,t,n)}finally{1==M&&y(),M--}}get zone(){return this._zone}get state(){return this._state}cancelScheduleRequest(){this._transitionTo(E,S)}_transitionTo(e,t,n){if(this._state!==t&&this._state!==n)throw new Error(`${this.type} '${this.source}': can not transition to '${e}', expecting state '${t}'${n?" or '"+n+"'":""}, was '${this._state}'.`);this._state=e,e==E&&(this._zoneDelegates=null)}toString(){return this.data&&void 0!==this.data.handleId?this.data.handleId.toString():Object.prototype.toString.call(this)}toJSON(){return{type:this.type,state:this.state,source:this.source,zone:this.zone.name,runCount:this.runCount}}}const h=s("setTimeout"),p=s("Promise"),d=s("then");let f,_=[],T=!1;function m(t){if(f||e[p]&&(f=e[p].resolve(0)),f){let e=f[d];e||(e=f.then),e.call(f,t)}else e[h](t,0)}function k(e){0===M&&0===_.length&&m(y),e&&_.push(e)}function y(){if(!T){for(T=!0;_.length;){const e=_;_=[];for(let t=0;t<e.length;t++){const n=e[t];try{n.zone.runTask(n,null,null)}catch(e){I.onUnhandledError(e)}}}I.microtaskDrainDone(),T=!1}}const g={name:"NO ZONE"},E="notScheduled",S="scheduling",b="scheduled",v="running",P="canceling",w="unknown",Z="microTask",O="macroTask",A="eventTask",D={},I={symbol:s,currentZoneFrame:()=>R,onUnhandledError:F,microtaskDrainDone:F,scheduleMicroTask:k,showUncaughtError:()=>!a[s("ignoreConsoleErrorUncaughtError")],patchEventTarget:()=>[],patchOnProperties:F,patchMethod:()=>F,bindArguments:()=>[],patchThen:()=>F,patchMacroTask:()=>F,patchEventPrototype:()=>F,isIEOrEdge:()=>!1,getGlobalObjects:()=>{},ObjectDefineProperty:()=>F,ObjectGetOwnPropertyDescriptor:()=>{},ObjectCreate:()=>{},ArraySlice:()=>[],patchClass:()=>F,wrapWithCurrentZone:()=>F,filterProperties:()=>[],attachOriginToPatched:()=>F,_redefineProperty:()=>F,patchCallbacks:()=>F,nativeScheduleMicroTask:m};let R={parent:null,zone:new a(null,null)},C=null,M=0;function F(){}r("Zone","Zone"),e.Zone=a}("undefined"!=typeof window&&window||"undefined"!=typeof self&&self||global);const ObjectGetOwnPropertyDescriptor=Object.getOwnPropertyDescriptor,ObjectDefineProperty=Object.defineProperty,ObjectGetPrototypeOf=Object.getPrototypeOf,ObjectCreate=Object.create,ArraySlice=Array.prototype.slice,ADD_EVENT_LISTENER_STR="addEventListener",REMOVE_EVENT_LISTENER_STR="removeEventListener",ZONE_SYMBOL_ADD_EVENT_LISTENER=Zone.__symbol__("addEventListener"),ZONE_SYMBOL_REMOVE_EVENT_LISTENER=Zone.__symbol__("removeEventListener"),TRUE_STR="true",FALSE_STR="false",ZONE_SYMBOL_PREFIX=Zone.__symbol__("");function wrapWithCurrentZone(e,t){return Zone.current.wrap(e,t)}function scheduleMacroTaskWithCurrentZone(e,t,n,r,o){return Zone.current.scheduleMacroTask(e,t,n,r,o)}const zoneSymbol=Zone.__symbol__,isWindowExists="undefined"!=typeof window,internalWindow=isWindowExists?window:void 0,_global=isWindowExists&&internalWindow||"object"==typeof self&&self||global,REMOVE_ATTRIBUTE="removeAttribute";function bindArguments(e,t){for(let n=e.length-1;n>=0;n--)"function"==typeof e[n]&&(e[n]=wrapWithCurrentZone(e[n],t+"_"+n));return e}function patchPrototype(e,t){const n=e.constructor.name;for(let r=0;r<t.length;r++){const o=t[r],s=e[o];if(s){if(!isPropertyWritable(ObjectGetOwnPropertyDescriptor(e,o)))continue;e[o]=(e=>{const t=function(){return e.apply(this,bindArguments(arguments,n+"."+o))};return attachOriginToPatched(t,e),t})(s)}}}function isPropertyWritable(e){return!e||!1!==e.writable&&!("function"==typeof e.get&&void 0===e.set)}const isWebWorker="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope,isNode=!("nw"in _global)&&void 0!==_global.process&&"[object process]"==={}.toString.call(_global.process),isBrowser=!isNode&&!isWebWorker&&!(!isWindowExists||!internalWindow.HTMLElement),isMix=void 0!==_global.process&&"[object process]"==={}.toString.call(_global.process)&&!isWebWorker&&!(!isWindowExists||!internalWindow.HTMLElement),zoneSymbolEventNames$1={},wrapFn=function(e){if(!(e=e||_global.event))return;let t=zoneSymbolEventNames$1[e.type];t||(t=zoneSymbolEventNames$1[e.type]=zoneSymbol("ON_PROPERTY"+e.type));const n=this||e.target||_global,r=n[t];let o;return isBrowser&&n===internalWindow&&"error"===e.type?(o=r&&r.call(this,e.message,e.filename,e.lineno,e.colno,e.error),!0===o&&e.preventDefault()):(o=r&&r.apply(this,arguments),null==o||o||e.preventDefault()),o};function patchProperty(e,t,n){let r=ObjectGetOwnPropertyDescriptor(e,t);if(!r&&n&&ObjectGetOwnPropertyDescriptor(n,t)&&(r={enumerable:!0,configurable:!0}),!r||!r.configurable)return;const o=zoneSymbol("on"+t+"patched");if(e.hasOwnProperty(o)&&e[o])return;delete r.writable,delete r.value;const s=r.get,i=r.set,a=t.slice(2);let c=zoneSymbolEventNames$1[a];c||(c=zoneSymbolEventNames$1[a]=zoneSymbol("ON_PROPERTY"+a)),r.set=function(t){let n=this;n||e!==_global||(n=_global),n&&("function"==typeof n[c]&&n.removeEventListener(a,wrapFn),i&&i.call(n,null),n[c]=t,"function"==typeof t&&n.addEventListener(a,wrapFn,!1))},r.get=function(){let n=this;if(n||e!==_global||(n=_global),!n)return null;const o=n[c];if(o)return o;if(s){let e=s.call(this);if(e)return r.set.call(this,e),"function"==typeof n[REMOVE_ATTRIBUTE]&&n.removeAttribute(t),e}return null},ObjectDefineProperty(e,t,r),e[o]=!0}function patchOnProperties(e,t,n){if(t)for(let r=0;r<t.length;r++)patchProperty(e,"on"+t[r],n);else{const t=[];for(const n in e)"on"==n.slice(0,2)&&t.push(n);for(let r=0;r<t.length;r++)patchProperty(e,t[r],n)}}const originalInstanceKey=zoneSymbol("originalInstance");function patchClass(e){const t=_global[e];if(!t)return;_global[zoneSymbol(e)]=t,_global[e]=function(){const n=bindArguments(arguments,e);switch(n.length){case 0:this[originalInstanceKey]=new t;break;case 1:this[originalInstanceKey]=new t(n[0]);break;case 2:this[originalInstanceKey]=new t(n[0],n[1]);break;case 3:this[originalInstanceKey]=new t(n[0],n[1],n[2]);break;case 4:this[originalInstanceKey]=new t(n[0],n[1],n[2],n[3]);break;default:throw new Error("Arg list too long.")}},attachOriginToPatched(_global[e],t);const n=new t((function(){}));let r;for(r in n)"XMLHttpRequest"===e&&"responseBlob"===r||function(t){"function"==typeof n[t]?_global[e].prototype[t]=function(){return this[originalInstanceKey][t].apply(this[originalInstanceKey],arguments)}:ObjectDefineProperty(_global[e].prototype,t,{set:function(n){"function"==typeof n?(this[originalInstanceKey][t]=wrapWithCurrentZone(n,e+"."+t),attachOriginToPatched(this[originalInstanceKey][t],n)):this[originalInstanceKey][t]=n},get:function(){return this[originalInstanceKey][t]}})}(r);for(r in t)"prototype"!==r&&t.hasOwnProperty(r)&&(_global[e][r]=t[r])}function patchMethod(e,t,n){let r=e;for(;r&&!r.hasOwnProperty(t);)r=ObjectGetPrototypeOf(r);!r&&e[t]&&(r=e);const o=zoneSymbol(t);let s=null;if(r&&(!(s=r[o])||!r.hasOwnProperty(o))&&(s=r[o]=r[t],isPropertyWritable(r&&ObjectGetOwnPropertyDescriptor(r,t)))){const e=n(s,o,t);r[t]=function(){return e(this,arguments)},attachOriginToPatched(r[t],s)}return s}function patchMacroTask(e,t,n){let r=null;function o(e){const t=e.data;return t.args[t.cbIdx]=function(){e.invoke.apply(this,arguments)},r.apply(t.target,t.args),e}r=patchMethod(e,t,(e=>function(t,r){const s=n(t,r);return s.cbIdx>=0&&"function"==typeof r[s.cbIdx]?scheduleMacroTaskWithCurrentZone(s.name,r[s.cbIdx],s,o):e.apply(t,r)}))}function attachOriginToPatched(e,t){e[zoneSymbol("OriginalDelegate")]=t}let isDetectedIEOrEdge=!1,ieOrEdge=!1;function isIE(){try{const e=internalWindow.navigator.userAgent;if(-1!==e.indexOf("MSIE ")||-1!==e.indexOf("Trident/"))return!0}catch(e){}return!1}function isIEOrEdge(){if(isDetectedIEOrEdge)return ieOrEdge;isDetectedIEOrEdge=!0;try{const e=internalWindow.navigator.userAgent;-1===e.indexOf("MSIE ")&&-1===e.indexOf("Trident/")&&-1===e.indexOf("Edge/")||(ieOrEdge=!0)}catch(e){}return ieOrEdge}Zone.__load_patch("ZoneAwarePromise",((e,t,n)=>{const r=Object.getOwnPropertyDescriptor,o=Object.defineProperty,s=n.symbol,i=[],a=!0===e[s("DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION")],c=s("Promise"),l=s("then"),u="__creationTrace__";n.onUnhandledError=e=>{if(n.showUncaughtError()){const t=e&&e.rejection;t?console.error("Unhandled Promise rejection:",t instanceof Error?t.message:t,"; Zone:",e.zone.name,"; Task:",e.task&&e.task.source,"; Value:",t,t instanceof Error?t.stack:void 0):console.error(e)}},n.microtaskDrainDone=()=>{for(;i.length;){const e=i.shift();try{e.zone.runGuarded((()=>{if(e.throwOriginal)throw e.rejection;throw e}))}catch(e){p(e)}}};const h=s("unhandledPromiseRejectionHandler");function p(e){n.onUnhandledError(e);try{const n=t[h];"function"==typeof n&&n.call(this,e)}catch(e){}}function d(e){return e&&e.then}function f(e){return e}function _(e){return N.reject(e)}const T=s("state"),m=s("value"),k=s("finally"),y=s("parentPromiseValue"),g=s("parentPromiseState"),E="Promise.then",S=null,b=!0,v=!1,P=0;function w(e,t){return n=>{try{D(e,t,n)}catch(t){D(e,!1,t)}}}const Z=function(){let e=!1;return function t(n){return function(){e||(e=!0,n.apply(null,arguments))}}},O="Promise resolved with itself",A=s("currentTaskTrace");function D(e,r,s){const c=Z();if(e===s)throw new TypeError(O);if(e[T]===S){let l=null;try{"object"!=typeof s&&"function"!=typeof s||(l=s&&s.then)}catch(t){return c((()=>{D(e,!1,t)}))(),e}if(r!==v&&s instanceof N&&s.hasOwnProperty(T)&&s.hasOwnProperty(m)&&s[T]!==S)R(s),D(e,s[T],s[m]);else if(r!==v&&"function"==typeof l)try{l.call(s,c(w(e,r)),c(w(e,!1)))}catch(t){c((()=>{D(e,!1,t)}))()}else{e[T]=r;const c=e[m];if(e[m]=s,e[k]===k&&r===b&&(e[T]=e[g],e[m]=e[y]),r===v&&s instanceof Error){const e=t.currentTask&&t.currentTask.data&&t.currentTask.data[u];e&&o(s,A,{configurable:!0,enumerable:!1,writable:!0,value:e})}for(let t=0;t<c.length;)C(e,c[t++],c[t++],c[t++],c[t++]);if(0==c.length&&r==v){e[T]=P;let r=s;try{throw new Error("Uncaught (in promise): "+function e(t){return t&&t.toString===Object.prototype.toString?(t.constructor&&t.constructor.name||"")+": "+JSON.stringify(t):t?t.toString():Object.prototype.toString.call(t)}(s)+(s&&s.stack?"\n"+s.stack:""))}catch(e){r=e}a&&(r.throwOriginal=!0),r.rejection=s,r.promise=e,r.zone=t.current,r.task=t.currentTask,i.push(r),n.scheduleMicroTask()}}}return e}const I=s("rejectionHandledHandler");function R(e){if(e[T]===P){try{const n=t[I];n&&"function"==typeof n&&n.call(this,{rejection:e[m],promise:e})}catch(e){}e[T]=v;for(let t=0;t<i.length;t++)e===i[t].promise&&i.splice(t,1)}}function C(e,t,n,r,o){R(e);const s=e[T],i=s?"function"==typeof r?r:f:"function"==typeof o?o:_;t.scheduleMicroTask(E,(()=>{try{const r=e[m],o=!!n&&k===n[k];o&&(n[y]=r,n[g]=s);const a=t.run(i,void 0,o&&i!==_&&i!==f?[]:[r]);D(n,!0,a)}catch(e){D(n,!1,e)}}),n)}const M=function(){},F=e.AggregateError;class N{static toString(){return"function ZoneAwarePromise() { [native code] }"}static resolve(e){return D(new this(null),b,e)}static reject(e){return D(new this(null),v,e)}static any(e){if(!e||"function"!=typeof e[Symbol.iterator])return Promise.reject(new F([],"All promises were rejected"));const t=[];let n=0;try{for(let r of e)n++,t.push(N.resolve(r))}catch(e){return Promise.reject(new F([],"All promises were rejected"))}if(0===n)return Promise.reject(new F([],"All promises were rejected"));let r=!1;const o=[];return new N(((e,s)=>{for(let i=0;i<t.length;i++)t[i].then((t=>{r||(r=!0,e(t))}),(e=>{o.push(e),n--,0===n&&(r=!0,s(new F(o,"All promises were rejected")))}))}))}static race(e){let t,n,r=new this(((e,r)=>{t=e,n=r}));function o(e){t(e)}function s(e){n(e)}for(let t of e)d(t)||(t=this.resolve(t)),t.then(o,s);return r}static all(e){return N.allWithCallback(e)}static allSettled(e){return(this&&this.prototype instanceof N?this:N).allWithCallback(e,{thenCallback:e=>({status:"fulfilled",value:e}),errorCallback:e=>({status:"rejected",reason:e})})}static allWithCallback(e,t){let n,r,o=new this(((e,t)=>{n=e,r=t})),s=2,i=0;const a=[];for(let o of e){d(o)||(o=this.resolve(o));const e=i;try{o.then((r=>{a[e]=t?t.thenCallback(r):r,s--,0===s&&n(a)}),(o=>{t?(a[e]=t.errorCallback(o),s--,0===s&&n(a)):r(o)}))}catch(e){r(e)}s++,i++}return s-=2,0===s&&n(a),o}constructor(e){const t=this;if(!(t instanceof N))throw new Error("Must be an instanceof Promise.");t[T]=S,t[m]=[];try{const n=Z();e&&e(n(w(t,b)),n(w(t,v)))}catch(e){D(t,!1,e)}}get[Symbol.toStringTag](){return"Promise"}get[Symbol.species](){return N}then(e,n){let r=this.constructor?.[Symbol.species];r&&"function"==typeof r||(r=this.constructor||N);const o=new r(M),s=t.current;return this[T]==S?this[m].push(s,o,e,n):C(this,s,o,e,n),o}catch(e){return this.then(null,e)}finally(e){let n=this.constructor?.[Symbol.species];n&&"function"==typeof n||(n=N);const r=new n(M);r[k]=k;const o=t.current;return this[T]==S?this[m].push(o,r,e,e):C(this,o,r,e,e),r}}N.resolve=N.resolve,N.reject=N.reject,N.race=N.race,N.all=N.all;const j=e[c]=e.Promise;e.Promise=N;const z=s("thenPatched");function x(e){const t=e.prototype,n=r(t,"then");if(n&&(!1===n.writable||!n.configurable))return;const o=t.then;t[l]=o,e.prototype.then=function(e,t){return new N(((e,t)=>{o.call(this,e,t)})).then(e,t)},e[z]=!0}return n.patchThen=x,j&&(x(j),patchMethod(e,"fetch",(e=>function t(e){return function(t,n){let r=e.apply(t,n);if(r instanceof N)return r;let o=r.constructor;return o[z]||x(o),r}}(e)))),Promise[t.__symbol__("uncaughtPromiseErrors")]=i,N})),Zone.__load_patch("toString",(e=>{const t=Function.prototype.toString,n=zoneSymbol("OriginalDelegate"),r=zoneSymbol("Promise"),o=zoneSymbol("Error"),s=function s(){if("function"==typeof this){const s=this[n];if(s)return"function"==typeof s?t.call(s):Object.prototype.toString.call(s);if(this===Promise){const n=e[r];if(n)return t.call(n)}if(this===Error){const n=e[o];if(n)return t.call(n)}}return t.call(this)};s[n]=t,Function.prototype.toString=s;const i=Object.prototype.toString;Object.prototype.toString=function(){return"function"==typeof Promise&&this instanceof Promise?"[object Promise]":i.call(this)}}));let passiveSupported=!1;if("undefined"!=typeof window)try{const e=Object.defineProperty({},"passive",{get:function(){passiveSupported=!0}});window.addEventListener("test",e,e),window.removeEventListener("test",e,e)}catch(e){passiveSupported=!1}const OPTIMIZED_ZONE_EVENT_TASK_DATA={useG:!0},zoneSymbolEventNames={},globalSources={},EVENT_NAME_SYMBOL_REGX=new RegExp("^"+ZONE_SYMBOL_PREFIX+"(\\w+)(true|false)$"),IMMEDIATE_PROPAGATION_SYMBOL=zoneSymbol("propagationStopped");function prepareEventNames(e,t){const n=(t?t(e):e)+FALSE_STR,r=(t?t(e):e)+TRUE_STR,o=ZONE_SYMBOL_PREFIX+n,s=ZONE_SYMBOL_PREFIX+r;zoneSymbolEventNames[e]={},zoneSymbolEventNames[e][FALSE_STR]=o,zoneSymbolEventNames[e][TRUE_STR]=s}function patchEventTarget(e,t,n,r){const o=r&&r.add||"addEventListener",s=r&&r.rm||"removeEventListener",i=r&&r.listeners||"eventListeners",a=r&&r.rmAll||"removeAllListeners",c=zoneSymbol(o),l="."+o+":",u="prependListener",h="."+u+":",p=function(e,t,n){if(e.isRemoved)return;const r=e.callback;let o;"object"==typeof r&&r.handleEvent&&(e.callback=e=>r.handleEvent(e),e.originalDelegate=r);try{e.invoke(e,t,[n])}catch(e){o=e}const i=e.options;return i&&"object"==typeof i&&i.once&&t[s].call(t,n.type,e.originalDelegate?e.originalDelegate:e.callback,i),o};function d(n,r,o){if(!(r=r||e.event))return;const s=n||r.target||e,i=s[zoneSymbolEventNames[r.type][o?TRUE_STR:FALSE_STR]];if(i){const e=[];if(1===i.length){const t=p(i[0],s,r);t&&e.push(t)}else{const t=i.slice();for(let n=0;n<t.length&&(!r||!0!==r[IMMEDIATE_PROPAGATION_SYMBOL]);n++){const o=p(t[n],s,r);o&&e.push(o)}}if(1===e.length)throw e[0];for(let n=0;n<e.length;n++){const r=e[n];t.nativeScheduleMicroTask((()=>{throw r}))}}}const f=function(e){return d(this,e,!1)},_=function(e){return d(this,e,!0)};function T(t,n){if(!t)return!1;let r=!0;n&&void 0!==n.useG&&(r=n.useG);const p=n&&n.vh;let d=!0;n&&void 0!==n.chkDup&&(d=n.chkDup);let T=!1;n&&void 0!==n.rt&&(T=n.rt);let m=t;for(;m&&!m.hasOwnProperty(o);)m=ObjectGetPrototypeOf(m);if(!m&&t[o]&&(m=t),!m)return!1;if(m[c])return!1;const k=n&&n.eventNameToString,y={},g=m[c]=m[o],E=m[zoneSymbol(s)]=m[s],S=m[zoneSymbol(i)]=m[i],b=m[zoneSymbol(a)]=m[a];let v;n&&n.prepend&&(v=m[zoneSymbol(n.prepend)]=m[n.prepend]);const P=r?function(e){if(!y.isExisting)return g.call(y.target,y.eventName,y.capture?_:f,y.options)}:function(e){return g.call(y.target,y.eventName,e.invoke,y.options)},w=r?function(e){if(!e.isRemoved){const t=zoneSymbolEventNames[e.eventName];let n;t&&(n=t[e.capture?TRUE_STR:FALSE_STR]);const r=n&&e.target[n];if(r)for(let t=0;t<r.length;t++)if(r[t]===e){r.splice(t,1),e.isRemoved=!0,0===r.length&&(e.allRemoved=!0,e.target[n]=null);break}}if(e.allRemoved)return E.call(e.target,e.eventName,e.capture?_:f,e.options)}:function(e){return E.call(e.target,e.eventName,e.invoke,e.options)},Z=n&&n.diff?n.diff:function(e,t){const n=typeof t;return"function"===n&&e.callback===t||"object"===n&&e.originalDelegate===t},O=Zone[zoneSymbol("UNPATCHED_EVENTS")],A=e[zoneSymbol("PASSIVE_EVENTS")],D=function(t,o,s,i,a=!1,c=!1){return function(){const l=this||e;let u=arguments[0];n&&n.transferEventName&&(u=n.transferEventName(u));let h=arguments[1];if(!h)return t.apply(this,arguments);if(isNode&&"uncaughtException"===u)return t.apply(this,arguments);let f=!1;if("function"!=typeof h){if(!h.handleEvent)return t.apply(this,arguments);f=!0}if(p&&!p(t,h,l,arguments))return;const _=passiveSupported&&!!A&&-1!==A.indexOf(u),T=function m(e,t){return!passiveSupported&&"object"==typeof e&&e?!!e.capture:passiveSupported&&t?"boolean"==typeof e?{capture:e,passive:!0}:e?"object"==typeof e&&!1!==e.passive?{...e,passive:!0}:e:{passive:!0}:e}(arguments[2],_);if(O)for(let e=0;e<O.length;e++)if(u===O[e])return _?t.call(l,u,h,T):t.apply(this,arguments);const g=!!T&&("boolean"==typeof T||T.capture),E=!(!T||"object"!=typeof T)&&T.once,S=Zone.current;let b=zoneSymbolEventNames[u];b||(prepareEventNames(u,k),b=zoneSymbolEventNames[u]);const v=b[g?TRUE_STR:FALSE_STR];let P,w=l[v],D=!1;if(w){if(D=!0,d)for(let e=0;e<w.length;e++)if(Z(w[e],h))return}else w=l[v]=[];const I=l.constructor.name,R=globalSources[I];R&&(P=R[u]),P||(P=I+o+(k?k(u):u)),y.options=T,E&&(y.options.once=!1),y.target=l,y.capture=g,y.eventName=u,y.isExisting=D;const C=r?OPTIMIZED_ZONE_EVENT_TASK_DATA:void 0;C&&(C.taskData=y);const M=S.scheduleEventTask(P,h,C,s,i);return y.target=null,C&&(C.taskData=null),E&&(T.once=!0),(passiveSupported||"boolean"!=typeof M.options)&&(M.options=T),M.target=l,M.capture=g,M.eventName=u,f&&(M.originalDelegate=h),c?w.unshift(M):w.push(M),a?l:void 0}};return m[o]=D(g,l,P,w,T),v&&(m[u]=D(v,h,(function(e){return v.call(y.target,y.eventName,e.invoke,y.options)}),w,T,!0)),m[s]=function(){const t=this||e;let r=arguments[0];n&&n.transferEventName&&(r=n.transferEventName(r));const o=arguments[2],s=!!o&&("boolean"==typeof o||o.capture),i=arguments[1];if(!i)return E.apply(this,arguments);if(p&&!p(E,i,t,arguments))return;const a=zoneSymbolEventNames[r];let c;a&&(c=a[s?TRUE_STR:FALSE_STR]);const l=c&&t[c];if(l)for(let e=0;e<l.length;e++){const n=l[e];if(Z(n,i))return l.splice(e,1),n.isRemoved=!0,0===l.length&&(n.allRemoved=!0,t[c]=null,"string"==typeof r)&&(t[ZONE_SYMBOL_PREFIX+"ON_PROPERTY"+r]=null),n.zone.cancelTask(n),T?t:void 0}return E.apply(this,arguments)},m[i]=function(){const t=this||e;let r=arguments[0];n&&n.transferEventName&&(r=n.transferEventName(r));const o=[],s=findEventTasks(t,k?k(r):r);for(let e=0;e<s.length;e++){const t=s[e];o.push(t.originalDelegate?t.originalDelegate:t.callback)}return o},m[a]=function(){const t=this||e;let r=arguments[0];if(r){n&&n.transferEventName&&(r=n.transferEventName(r));const e=zoneSymbolEventNames[r];if(e){const n=t[e[FALSE_STR]],o=t[e[TRUE_STR]];if(n){const e=n.slice();for(let t=0;t<e.length;t++){const n=e[t];this[s].call(this,r,n.originalDelegate?n.originalDelegate:n.callback,n.options)}}if(o){const e=o.slice();for(let t=0;t<e.length;t++){const n=e[t];this[s].call(this,r,n.originalDelegate?n.originalDelegate:n.callback,n.options)}}}}else{const e=Object.keys(t);for(let t=0;t<e.length;t++){const n=EVENT_NAME_SYMBOL_REGX.exec(e[t]);let r=n&&n[1];r&&"removeListener"!==r&&this[a].call(this,r)}this[a].call(this,"removeListener")}if(T)return this},attachOriginToPatched(m[o],g),attachOriginToPatched(m[s],E),b&&attachOriginToPatched(m[a],b),S&&attachOriginToPatched(m[i],S),!0}let m=[];for(let e=0;e<n.length;e++)m[e]=T(n[e],r);return m}function findEventTasks(e,t){if(!t){const n=[];for(let r in e){const o=EVENT_NAME_SYMBOL_REGX.exec(r);let s=o&&o[1];if(s&&(!t||s===t)){const t=e[r];if(t)for(let e=0;e<t.length;e++)n.push(t[e])}}return n}let n=zoneSymbolEventNames[t];n||(prepareEventNames(t),n=zoneSymbolEventNames[t]);const r=e[n[FALSE_STR]],o=e[n[TRUE_STR]];return r?o?r.concat(o):r.slice():o?o.slice():[]}function patchEventPrototype(e,t){const n=e.Event;n&&n.prototype&&t.patchMethod(n.prototype,"stopImmediatePropagation",(e=>function(t,n){t[IMMEDIATE_PROPAGATION_SYMBOL]=!0,e&&e.apply(t,n)}))}function patchCallbacks(e,t,n,r,o){const s=Zone.__symbol__(r);if(t[s])return;const i=t[s]=t[r];t[r]=function(s,a,c){return a&&a.prototype&&o.forEach((function(t){const o=`${n}.${r}::`+t,s=a.prototype;try{if(s.hasOwnProperty(t)){const n=e.ObjectGetOwnPropertyDescriptor(s,t);n&&n.value?(n.value=e.wrapWithCurrentZone(n.value,o),e._redefineProperty(a.prototype,t,n)):s[t]&&(s[t]=e.wrapWithCurrentZone(s[t],o))}else s[t]&&(s[t]=e.wrapWithCurrentZone(s[t],o))}catch{}})),i.call(t,s,a,c)},e.attachOriginToPatched(t[r],i)}function filterProperties(e,t,n){if(!n||0===n.length)return t;const r=n.filter((t=>t.target===e));if(!r||0===r.length)return t;const o=r[0].ignoreProperties;return t.filter((e=>-1===o.indexOf(e)))}function patchFilteredProperties(e,t,n,r){e&&patchOnProperties(e,filterProperties(e,t,n),r)}function getOnEventNames(e){return Object.getOwnPropertyNames(e).filter((e=>e.startsWith("on")&&e.length>2)).map((e=>e.substring(2)))}function propertyDescriptorPatch(e,t){if(isNode&&!isMix)return;if(Zone[e.symbol("patchEvents")])return;const n=t.__Zone_ignore_on_properties;let r=[];if(isBrowser){const e=window;r=r.concat(["Document","SVGElement","Element","HTMLElement","HTMLBodyElement","HTMLMediaElement","HTMLFrameSetElement","HTMLFrameElement","HTMLIFrameElement","HTMLMarqueeElement","Worker"]);const t=isIE()?[{target:e,ignoreProperties:["error"]}]:[];patchFilteredProperties(e,getOnEventNames(e),n?n.concat(t):n,ObjectGetPrototypeOf(e))}r=r.concat(["XMLHttpRequest","XMLHttpRequestEventTarget","IDBIndex","IDBRequest","IDBOpenDBRequest","IDBDatabase","IDBTransaction","IDBCursor","WebSocket"]);for(let e=0;e<r.length;e++){const o=t[r[e]];o&&o.prototype&&patchFilteredProperties(o.prototype,getOnEventNames(o.prototype),n)}}function patchQueueMicrotask(e,t){t.patchMethod(e,"queueMicrotask",(e=>function(e,t){Zone.current.scheduleMicroTask("queueMicrotask",t[0])}))}Zone.__load_patch("util",((e,t,n)=>{const r=getOnEventNames(e);n.patchOnProperties=patchOnProperties,n.patchMethod=patchMethod,n.bindArguments=bindArguments,n.patchMacroTask=patchMacroTask;const o=t.__symbol__("BLACK_LISTED_EVENTS"),s=t.__symbol__("UNPATCHED_EVENTS");e[s]&&(e[o]=e[s]),e[o]&&(t[o]=t[s]=e[o]),n.patchEventPrototype=patchEventPrototype,n.patchEventTarget=patchEventTarget,n.isIEOrEdge=isIEOrEdge,n.ObjectDefineProperty=ObjectDefineProperty,n.ObjectGetOwnPropertyDescriptor=ObjectGetOwnPropertyDescriptor,n.ObjectCreate=ObjectCreate,n.ArraySlice=ArraySlice,n.patchClass=patchClass,n.wrapWithCurrentZone=wrapWithCurrentZone,n.filterProperties=filterProperties,n.attachOriginToPatched=attachOriginToPatched,n._redefineProperty=Object.defineProperty,n.patchCallbacks=patchCallbacks,n.getGlobalObjects=()=>({globalSources:globalSources,zoneSymbolEventNames:zoneSymbolEventNames,eventNames:r,isBrowser:isBrowser,isMix:isMix,isNode:isNode,TRUE_STR:TRUE_STR,FALSE_STR:FALSE_STR,ZONE_SYMBOL_PREFIX:ZONE_SYMBOL_PREFIX,ADD_EVENT_LISTENER_STR:"addEventListener",REMOVE_EVENT_LISTENER_STR:"removeEventListener"})}));const taskSymbol=zoneSymbol("zoneTask");function patchTimer(e,t,n,r){let o=null,s=null;n+=r;const i={};function a(t){const n=t.data;return n.args[0]=function(){return t.invoke.apply(this,arguments)},n.handleId=o.apply(e,n.args),t}function c(t){return s.call(e,t.data.handleId)}o=patchMethod(e,t+=r,(n=>function(o,s){if("function"==typeof s[0]){const e={isPeriodic:"Interval"===r,delay:"Timeout"===r||"Interval"===r?s[1]||0:void 0,args:s},n=s[0];s[0]=function t(){try{return n.apply(this,arguments)}finally{e.isPeriodic||("number"==typeof e.handleId?delete i[e.handleId]:e.handleId&&(e.handleId[taskSymbol]=null))}};const o=scheduleMacroTaskWithCurrentZone(t,s[0],e,a,c);if(!o)return o;const l=o.data.handleId;return"number"==typeof l?i[l]=o:l&&(l[taskSymbol]=o),l&&l.ref&&l.unref&&"function"==typeof l.ref&&"function"==typeof l.unref&&(o.ref=l.ref.bind(l),o.unref=l.unref.bind(l)),"number"==typeof l||l?l:o}return n.apply(e,s)})),s=patchMethod(e,n,(t=>function(n,r){const o=r[0];let s;"number"==typeof o?s=i[o]:(s=o&&o[taskSymbol],s||(s=o)),s&&"string"==typeof s.type?"notScheduled"!==s.state&&(s.cancelFn&&s.data.isPeriodic||0===s.runCount)&&("number"==typeof o?delete i[o]:o&&(o[taskSymbol]=null),s.zone.cancelTask(s)):t.apply(e,r)}))}function patchCustomElements(e,t){const{isBrowser:n,isMix:r}=t.getGlobalObjects();(n||r)&&e.customElements&&"customElements"in e&&t.patchCallbacks(t,e.customElements,"customElements","define",["connectedCallback","disconnectedCallback","adoptedCallback","attributeChangedCallback"])}function eventTargetPatch(e,t){if(Zone[t.symbol("patchEventTarget")])return;const{eventNames:n,zoneSymbolEventNames:r,TRUE_STR:o,FALSE_STR:s,ZONE_SYMBOL_PREFIX:i}=t.getGlobalObjects();for(let e=0;e<n.length;e++){const t=n[e],a=i+(t+s),c=i+(t+o);r[t]={},r[t][s]=a,r[t][o]=c}const a=e.EventTarget;return a&&a.prototype?(t.patchEventTarget(e,t,[a&&a.prototype]),!0):void 0}function patchEvent(e,t){t.patchEventPrototype(e,t)}Zone.__load_patch("legacy",(e=>{const t=e[Zone.__symbol__("legacyPatch")];t&&t()})),Zone.__load_patch("timers",(e=>{const t="set",n="clear";patchTimer(e,t,n,"Timeout"),patchTimer(e,t,n,"Interval"),patchTimer(e,t,n,"Immediate")})),Zone.__load_patch("requestAnimationFrame",(e=>{patchTimer(e,"request","cancel","AnimationFrame"),patchTimer(e,"mozRequest","mozCancel","AnimationFrame"),patchTimer(e,"webkitRequest","webkitCancel","AnimationFrame")})),Zone.__load_patch("blocking",((e,t)=>{const n=["alert","prompt","confirm"];for(let r=0;r<n.length;r++)patchMethod(e,n[r],((n,r,o)=>function(r,s){return t.current.run(n,e,s,o)}))})),Zone.__load_patch("EventTarget",((e,t,n)=>{patchEvent(e,n),eventTargetPatch(e,n);const r=e.XMLHttpRequestEventTarget;r&&r.prototype&&n.patchEventTarget(e,n,[r.prototype])})),Zone.__load_patch("MutationObserver",((e,t,n)=>{patchClass("MutationObserver"),patchClass("WebKitMutationObserver")})),Zone.__load_patch("IntersectionObserver",((e,t,n)=>{patchClass("IntersectionObserver")})),Zone.__load_patch("FileReader",((e,t,n)=>{patchClass("FileReader")})),Zone.__load_patch("on_property",((e,t,n)=>{propertyDescriptorPatch(n,e)})),Zone.__load_patch("customElements",((e,t,n)=>{patchCustomElements(e,n)})),Zone.__load_patch("XHR",((e,t)=>{!function n(e){const n=e.XMLHttpRequest;if(!n)return;const l=n.prototype;let u=l[ZONE_SYMBOL_ADD_EVENT_LISTENER],h=l[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];if(!u){const t=e.XMLHttpRequestEventTarget;if(t){const e=t.prototype;u=e[ZONE_SYMBOL_ADD_EVENT_LISTENER],h=e[ZONE_SYMBOL_REMOVE_EVENT_LISTENER]}}const p="readystatechange",d="scheduled";function f(e){const n=e.data,o=n.target;o[i]=!1,o[c]=!1;const a=o[s];u||(u=o[ZONE_SYMBOL_ADD_EVENT_LISTENER],h=o[ZONE_SYMBOL_REMOVE_EVENT_LISTENER]),a&&h.call(o,p,a);const l=o[s]=()=>{if(o.readyState===o.DONE)if(!n.aborted&&o[i]&&e.state===d){const r=o[t.__symbol__("loadfalse")];if(0!==o.status&&r&&r.length>0){const s=e.invoke;e.invoke=function(){const r=o[t.__symbol__("loadfalse")];for(let t=0;t<r.length;t++)r[t]===e&&r.splice(t,1);n.aborted||e.state!==d||s.call(e)},r.push(e)}else e.invoke()}else n.aborted||!1!==o[i]||(o[c]=!0)};return u.call(o,p,l),o[r]||(o[r]=e),g.apply(o,n.args),o[i]=!0,e}function _(){}function T(e){const t=e.data;return t.aborted=!0,E.apply(t.target,t.args)}const m=patchMethod(l,"open",(()=>function(e,t){return e[o]=0==t[2],e[a]=t[1],m.apply(e,t)})),k=zoneSymbol("fetchTaskAborting"),y=zoneSymbol("fetchTaskScheduling"),g=patchMethod(l,"send",(()=>function(e,n){if(!0===t.current[y])return g.apply(e,n);if(e[o])return g.apply(e,n);{const t={target:e,url:e[a],isPeriodic:!1,args:n,aborted:!1},r=scheduleMacroTaskWithCurrentZone("XMLHttpRequest.send",_,t,f,T);e&&!0===e[c]&&!t.aborted&&r.state===d&&r.invoke()}})),E=patchMethod(l,"abort",(()=>function(e,n){const o=function s(e){return e[r]}(e);if(o&&"string"==typeof o.type){if(null==o.cancelFn||o.data&&o.data.aborted)return;o.zone.cancelTask(o)}else if(!0===t.current[k])return E.apply(e,n)}))}(e);const r=zoneSymbol("xhrTask"),o=zoneSymbol("xhrSync"),s=zoneSymbol("xhrListener"),i=zoneSymbol("xhrScheduled"),a=zoneSymbol("xhrURL"),c=zoneSymbol("xhrErrorBeforeScheduled")})),Zone.__load_patch("geolocation",(e=>{e.navigator&&e.navigator.geolocation&&patchPrototype(e.navigator.geolocation,["getCurrentPosition","watchPosition"])})),Zone.__load_patch("PromiseRejectionEvent",((e,t)=>{function n(t){return function(n){findEventTasks(e,t).forEach((r=>{const o=e.PromiseRejectionEvent;if(o){const e=new o(t,{promise:n.promise,reason:n.rejection});r.invoke(e)}}))}}e.PromiseRejectionEvent&&(t[zoneSymbol("unhandledPromiseRejectionHandler")]=n("unhandledrejection"),t[zoneSymbol("rejectionHandledHandler")]=n("rejectionhandled"))})),Zone.__load_patch("queueMicrotask",((e,t,n)=>{patchQueueMicrotask(e,n)}));const NEWLINE="\n",IGNORE_FRAMES={},creationTrace="__creationTrace__",ERROR_TAG="STACKTRACE TRACKING",SEP_TAG="__SEP_TAG__";let sepTemplate=SEP_TAG+"@[native]";class LongStackTrace{constructor(){this.error=getStacktrace(),this.timestamp=new Date}}function getStacktraceWithUncaughtError(){return new Error(ERROR_TAG)}function getStacktraceWithCaughtError(){try{throw getStacktraceWithUncaughtError()}catch(e){return e}}const error=getStacktraceWithUncaughtError(),caughtError=getStacktraceWithCaughtError(),getStacktrace=error.stack?getStacktraceWithUncaughtError:caughtError.stack?getStacktraceWithCaughtError:getStacktraceWithUncaughtError;function getFrames(e){return e.stack?e.stack.split(NEWLINE):[]}function addErrorStack(e,t){let n=getFrames(t);for(let t=0;t<n.length;t++)IGNORE_FRAMES.hasOwnProperty(n[t])||e.push(n[t])}function renderLongStackTrace(e,t){const n=[t?t.trim():""];if(e){let t=(new Date).getTime();for(let r=0;r<e.length;r++){const o=e[r],s=o.timestamp;let i=`____________________Elapsed ${t-s.getTime()} ms; At: ${s}`;i=i.replace(/[^\w\d]/g,"_"),n.push(sepTemplate.replace(SEP_TAG,i)),addErrorStack(n,o.error),t=s.getTime()}}return n.join(NEWLINE)}function stackTracesEnabled(){return Error.stackTraceLimit>0}function captureStackTraces(e,t){t>0&&(e.push(getFrames((new LongStackTrace).error)),captureStackTraces(e,t-1))}function computeIgnoreFrames(){if(!stackTracesEnabled())return;const e=[];captureStackTraces(e,2);const t=e[0],n=e[1];for(let e=0;e<t.length;e++){const n=t[e];if(-1==n.indexOf(ERROR_TAG)){let e=n.match(/^\s*at\s+/);if(e){sepTemplate=e[0]+SEP_TAG+" (http://localhost)";break}}}for(let e=0;e<t.length;e++){const r=t[e];if(r!==n[e])break;IGNORE_FRAMES[r]=!0}}Zone.longStackTraceZoneSpec={name:"long-stack-trace",longStackTraceLimit:10,getLongStackTrace:function(e){if(!e)return;const t=e[Zone.__symbol__("currentTaskTrace")];return t?renderLongStackTrace(t,e.stack):e.stack},onScheduleTask:function(e,t,n,r){if(stackTracesEnabled()){const e=Zone.currentTask;let t=e&&e.data&&e.data[creationTrace]||[];t=[new LongStackTrace].concat(t),t.length>this.longStackTraceLimit&&(t.length=this.longStackTraceLimit),r.data||(r.data={}),"eventTask"===r.type&&(r.data={...r.data}),r.data[creationTrace]=t}return e.scheduleTask(n,r)},onHandleError:function(e,t,n,r){if(stackTracesEnabled()){const e=Zone.currentTask||r.task;if(r instanceof Error&&e){const t=renderLongStackTrace(e.data&&e.data[creationTrace],r.stack);try{r.stack=r.longStack=t}catch(e){}}}return e.handleError(n,r)}},computeIgnoreFrames();class ProxyZoneSpec{static get(){return Zone.current.get("ProxyZoneSpec")}static isLoaded(){return ProxyZoneSpec.get()instanceof ProxyZoneSpec}static assertPresent(){if(!ProxyZoneSpec.isLoaded())throw new Error("Expected to be running in 'ProxyZone', but it was not found.");return ProxyZoneSpec.get()}constructor(e=null){this.defaultSpecDelegate=e,this.name="ProxyZone",this._delegateSpec=null,this.properties={ProxyZoneSpec:this},this.propertyKeys=null,this.lastTaskState=null,this.isNeedToTriggerHasTask=!1,this.tasks=[],this.setDelegate(e)}setDelegate(e){const t=this._delegateSpec!==e;this._delegateSpec=e,this.propertyKeys&&this.propertyKeys.forEach((e=>delete this.properties[e])),this.propertyKeys=null,e&&e.properties&&(this.propertyKeys=Object.keys(e.properties),this.propertyKeys.forEach((t=>this.properties[t]=e.properties[t]))),t&&this.lastTaskState&&(this.lastTaskState.macroTask||this.lastTaskState.microTask)&&(this.isNeedToTriggerHasTask=!0)}getDelegate(){return this._delegateSpec}resetDelegate(){this.getDelegate(),this.setDelegate(this.defaultSpecDelegate)}tryTriggerHasTask(e,t,n){this.isNeedToTriggerHasTask&&this.lastTaskState&&(this.isNeedToTriggerHasTask=!1,this.onHasTask(e,t,n,this.lastTaskState))}removeFromTasks(e){if(this.tasks)for(let t=0;t<this.tasks.length;t++)if(this.tasks[t]===e)return void this.tasks.splice(t,1)}getAndClearPendingTasksInfo(){if(0===this.tasks.length)return"";const e="--Pending async tasks are: ["+this.tasks.map((e=>{const t=e.data&&Object.keys(e.data).map((t=>t+":"+e.data[t])).join(",");return`type: ${e.type}, source: ${e.source}, args: {${t}}`}))+"]";return this.tasks=[],e}onFork(e,t,n,r){return this._delegateSpec&&this._delegateSpec.onFork?this._delegateSpec.onFork(e,t,n,r):e.fork(n,r)}onIntercept(e,t,n,r,o){return this._delegateSpec&&this._delegateSpec.onIntercept?this._delegateSpec.onIntercept(e,t,n,r,o):e.intercept(n,r,o)}onInvoke(e,t,n,r,o,s,i){return this.tryTriggerHasTask(e,t,n),this._delegateSpec&&this._delegateSpec.onInvoke?this._delegateSpec.onInvoke(e,t,n,r,o,s,i):e.invoke(n,r,o,s,i)}onHandleError(e,t,n,r){return this._delegateSpec&&this._delegateSpec.onHandleError?this._delegateSpec.onHandleError(e,t,n,r):e.handleError(n,r)}onScheduleTask(e,t,n,r){return"eventTask"!==r.type&&this.tasks.push(r),this._delegateSpec&&this._delegateSpec.onScheduleTask?this._delegateSpec.onScheduleTask(e,t,n,r):e.scheduleTask(n,r)}onInvokeTask(e,t,n,r,o,s){return"eventTask"!==r.type&&this.removeFromTasks(r),this.tryTriggerHasTask(e,t,n),this._delegateSpec&&this._delegateSpec.onInvokeTask?this._delegateSpec.onInvokeTask(e,t,n,r,o,s):e.invokeTask(n,r,o,s)}onCancelTask(e,t,n,r){return"eventTask"!==r.type&&this.removeFromTasks(r),this.tryTriggerHasTask(e,t,n),this._delegateSpec&&this._delegateSpec.onCancelTask?this._delegateSpec.onCancelTask(e,t,n,r):e.cancelTask(n,r)}onHasTask(e,t,n,r){this.lastTaskState=r,this._delegateSpec&&this._delegateSpec.onHasTask?this._delegateSpec.onHasTask(e,t,n,r):e.hasTask(n,r)}}Zone.ProxyZoneSpec=ProxyZoneSpec;class SyncTestZoneSpec{constructor(e){this.runZone=Zone.current,this.name="syncTestZone for "+e}onScheduleTask(e,t,n,r){switch(r.type){case"microTask":case"macroTask":throw new Error(`Cannot call ${r.source} from within a sync test (${this.name}).`);case"eventTask":r=e.scheduleTask(n,r)}return r}}Zone.SyncTestZoneSpec=SyncTestZoneSpec,Zone.__load_patch("jasmine",((e,t,n)=>{if(!t)throw new Error("Missing: zone.js");if("undefined"!=typeof jest)return;if("undefined"==typeof jasmine||jasmine.__zone_patch__)return;jasmine.__zone_patch__=!0;const r=t.SyncTestZoneSpec,o=t.ProxyZoneSpec;if(!r)throw new Error("Missing: SyncTestZoneSpec");if(!o)throw new Error("Missing: ProxyZoneSpec");const s=t.current,i=t.__symbol__,a=!0===e[i("fakeAsyncDisablePatchingClock")],c=!a&&(!0===e[i("fakeAsyncPatchLock")]||!0===e[i("fakeAsyncAutoFakeAsyncWhenClockPatched")]);if(!0!==e[i("ignoreUnhandledRejection")]){const t=jasmine.GlobalErrors;t&&!jasmine[i("GlobalErrors")]&&(jasmine[i("GlobalErrors")]=t,jasmine.GlobalErrors=function(){const n=new t,r=n.install;return r&&!n[i("install")]&&(n[i("install")]=r,n.install=function(){const t="undefined"!=typeof process&&!!process.on,n=t?process.listeners("unhandledRejection"):e.eventListeners("unhandledrejection"),o=r.apply(this,arguments);return t?process.removeAllListeners("unhandledRejection"):e.removeAllListeners("unhandledrejection"),n&&n.forEach((n=>{t?process.on("unhandledRejection",n):e.addEventListener("unhandledrejection",n)})),o}),n})}const l=jasmine.getEnv();if(["describe","xdescribe","fdescribe"].forEach((e=>{let t=l[e];l[e]=function(e,n){return t.call(this,e,function o(e,t){return function(){return s.fork(new r(`jasmine.describe#${e}`)).run(t,this,arguments)}}(e,n))}})),["it","xit","fit"].forEach((e=>{let t=l[e];l[i(e)]=t,l[e]=function(e,n,r){return arguments[1]=h(n),t.apply(this,arguments)}})),["beforeEach","afterEach","beforeAll","afterAll"].forEach((e=>{let t=l[e];l[i(e)]=t,l[e]=function(e,n){return arguments[0]=h(e),t.apply(this,arguments)}})),!a){const e=jasmine[i("clock")]=jasmine.clock;jasmine.clock=function(){const n=e.apply(this,arguments);if(!n[i("patched")]){n[i("patched")]=i("patched");const e=n[i("tick")]=n.tick;n.tick=function(){const n=t.current.get("FakeAsyncTestZoneSpec");return n?n.tick.apply(n,arguments):e.apply(this,arguments)};const r=n[i("mockDate")]=n.mockDate;n.mockDate=function(){const e=t.current.get("FakeAsyncTestZoneSpec");if(e){const t=arguments.length>0?arguments[0]:new Date;return e.setFakeBaseSystemTime.apply(e,t&&"function"==typeof t.getTime?[t.getTime()]:arguments)}return r.apply(this,arguments)},c&&["install","uninstall"].forEach((e=>{const r=n[i(e)]=n[e];n[e]=function(){if(!t.FakeAsyncTestZoneSpec)return r.apply(this,arguments);jasmine[i("clockInstalled")]="install"===e}}))}return n}}if(!jasmine[t.__symbol__("createSpyObj")]){const e=jasmine.createSpyObj;jasmine[t.__symbol__("createSpyObj")]=e,jasmine.createSpyObj=function(){const t=Array.prototype.slice.call(arguments);let n;if(t.length>=3&&t[2]){const r=Object.defineProperty;Object.defineProperty=function(e,t,n){return r.call(this,e,t,{...n,configurable:!0,enumerable:!0})};try{n=e.apply(this,t)}finally{Object.defineProperty=r}}else n=e.apply(this,t);return n}}function u(e,n,r,o){const s=!!jasmine[i("clockInstalled")],a=r.testProxyZone;if(s&&c){const n=t[t.__symbol__("fakeAsyncTest")];n&&"function"==typeof n.fakeAsync&&(e=n.fakeAsync(e))}return o?a.run(e,n,[o]):a.run(e,n)}function h(e){return e&&(e.length?function(t){return u(e,this,this.queueRunner,t)}:function(){return u(e,this,this.queueRunner)})}const p=jasmine.QueueRunner;jasmine.QueueRunner=function(n){function r(r){r.onComplete&&(r.onComplete=(e=>()=>{this.testProxyZone=null,this.testProxyZoneSpec=null,s.scheduleMicroTask("jasmine.onComplete",e)})(r.onComplete));const o=e[t.__symbol__("setTimeout")],i=e[t.__symbol__("clearTimeout")];o&&(r.timeout={setTimeout:o||e.setTimeout,clearTimeout:i||e.clearTimeout}),jasmine.UserContext?(r.userContext||(r.userContext=new jasmine.UserContext),r.userContext.queueRunner=this):(r.userContext||(r.userContext={}),r.userContext.queueRunner=this);const a=r.onException;r.onException=function(e){if(e&&"Timeout - Async callback was not invoked within timeout specified by jasmine.DEFAULT_TIMEOUT_INTERVAL."===e.message){const t=this&&this.testProxyZoneSpec;if(t){const n=t.getAndClearPendingTasksInfo();try{e.message+=n}catch(e){}}}a&&a.call(this,e)},n.call(this,r)}return function(e,t){for(const n in t)t.hasOwnProperty(n)&&(e[n]=t[n]);function n(){this.constructor=e}e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}(r,n),r.prototype.execute=function(){let e=t.current,r=!1;for(;e;){if(e===s){r=!0;break}e=e.parent}if(!r)throw new Error("Unexpected Zone: "+t.current.name);this.testProxyZoneSpec=new o,this.testProxyZone=s.fork(this.testProxyZoneSpec),t.currentTask?n.prototype.execute.call(this):t.current.scheduleMicroTask("jasmine.execute().forceTask",(()=>p.prototype.execute.call(this)))},r}(p)})),Zone.__load_patch("jest",((e,t,n)=>{if("undefined"==typeof jest||jest.__zone_patch__)return;t[n.symbol("ignoreConsoleErrorUncaughtError")]=!0,jest.__zone_patch__=!0;const r=t.ProxyZoneSpec,o=t.SyncTestZoneSpec;if(!r)throw new Error("Missing ProxyZoneSpec");const s=t.current,i=s.fork(new o("jest.describe")),a=new r,c=s.fork(a);function l(e){return function(...t){return i.run(e,this,t)}}function u(e,r=!1){if("function"!=typeof e)return e;const o=function(){if(!0===t[n.symbol("useFakeTimersCalled")]&&e&&!e.isFakeAsync){const n=t[t.__symbol__("fakeAsyncTest")];n&&"function"==typeof n.fakeAsync&&(e=n.fakeAsync(e))}return a.isTestFunc=r,c.run(e,null,arguments)};return Object.defineProperty(o,"length",{configurable:!0,writable:!0,enumerable:!1}),o.length=e.length,o}["describe","xdescribe","fdescribe"].forEach((n=>{let r=e[n];e[t.__symbol__(n)]||(e[t.__symbol__(n)]=r,e[n]=function(...e){return e[1]=l(e[1]),r.apply(this,e)},e[n].each=function o(e){return function(...t){const n=e.apply(this,t);return function(...e){return e[1]=l(e[1]),n.apply(this,e)}}}(r.each))})),e.describe.only=e.fdescribe,e.describe.skip=e.xdescribe,["it","xit","fit","test","xtest"].forEach((n=>{let r=e[n];e[t.__symbol__(n)]||(e[t.__symbol__(n)]=r,e[n]=function(...e){return e[1]=u(e[1],!0),r.apply(this,e)},e[n].each=function o(e){return function(...t){return function(...n){return n[1]=u(n[1]),e.apply(this,t).apply(this,n)}}}(r.each),e[n].todo=r.todo)})),e.it.only=e.fit,e.it.skip=e.xit,e.test.only=e.fit,e.test.skip=e.xit,["beforeEach","afterEach","beforeAll","afterAll"].forEach((n=>{let r=e[n];e[t.__symbol__(n)]||(e[t.__symbol__(n)]=r,e[n]=function(...e){return e[0]=u(e[0]),r.apply(this,e)})})),t.patchJestObject=function e(r,o=!1){function s(){return!!t.current.get("FakeAsyncTestZoneSpec")}function i(){const e=t.current.get("ProxyZoneSpec");return e&&e.isTestFunc}r[n.symbol("fakeTimers")]||(r[n.symbol("fakeTimers")]=!0,n.patchMethod(r,"_checkFakeTimers",(e=>function(t,n){return!!s()||e.apply(t,n)})),n.patchMethod(r,"useFakeTimers",(e=>function(r,s){return t[n.symbol("useFakeTimersCalled")]=!0,o||i()?e.apply(r,s):r})),n.patchMethod(r,"useRealTimers",(e=>function(r,s){return t[n.symbol("useFakeTimersCalled")]=!1,o||i()?e.apply(r,s):r})),n.patchMethod(r,"setSystemTime",(e=>function(n,r){const o=t.current.get("FakeAsyncTestZoneSpec");if(!o||!s())return e.apply(n,r);o.setFakeBaseSystemTime(r[0])})),n.patchMethod(r,"getRealSystemTime",(e=>function(n,r){const o=t.current.get("FakeAsyncTestZoneSpec");return o&&s()?o.getRealSystemTime():e.apply(n,r)})),n.patchMethod(r,"runAllTicks",(e=>function(n,r){const o=t.current.get("FakeAsyncTestZoneSpec");if(!o)return e.apply(n,r);o.flushMicrotasks()})),n.patchMethod(r,"runAllTimers",(e=>function(n,r){const o=t.current.get("FakeAsyncTestZoneSpec");if(!o)return e.apply(n,r);o.flush(100,!0)})),n.patchMethod(r,"advanceTimersByTime",(e=>function(n,r){const o=t.current.get("FakeAsyncTestZoneSpec");if(!o)return e.apply(n,r);o.tick(r[0])})),n.patchMethod(r,"runOnlyPendingTimers",(e=>function(n,r){const o=t.current.get("FakeAsyncTestZoneSpec");if(!o)return e.apply(n,r);o.flushOnlyPendingTimers()})),n.patchMethod(r,"advanceTimersToNextTimer",(e=>function(n,r){const o=t.current.get("FakeAsyncTestZoneSpec");if(!o)return e.apply(n,r);o.tickToNext(r[0])})),n.patchMethod(r,"clearAllTimers",(e=>function(n,r){const o=t.current.get("FakeAsyncTestZoneSpec");if(!o)return e.apply(n,r);o.removeAllTimers()})),n.patchMethod(r,"getTimerCount",(e=>function(n,r){const o=t.current.get("FakeAsyncTestZoneSpec");return o?o.getTimerCount():e.apply(n,r)})))}})),Zone.__load_patch("mocha",((e,t)=>{const n=e.Mocha;if(void 0===n)return;if(void 0===t)throw new Error("Missing Zone.js");const r=t.ProxyZoneSpec,o=t.SyncTestZoneSpec;if(!r)throw new Error("Missing ProxyZoneSpec");if(n.__zone_patch__)throw new Error('"Mocha" has already been patched with "Zone".');n.__zone_patch__=!0;const s=t.current,i=s.fork(new o("Mocha.describe"));let a=null;const c=s.fork(new r),l={after:e.after,afterEach:e.afterEach,before:e.before,beforeEach:e.beforeEach,describe:e.describe,it:e.it};function u(e,t,n){for(let r=0;r<e.length;r++){let o=e[r];"function"==typeof o&&(e[r]=0===o.length?t(o):n(o),e[r].toString=function(){return o.toString()})}return e}function h(e){return u(e,(function(e){return function(){return i.run(e,this,arguments)}}))}function p(e){return u(e,(function(e){return function(){return a.run(e,this)}}),(function(e){return function(t){return a.run(e,this,[t])}}))}function d(e){return u(e,(function(e){return function(){return c.run(e,this)}}),(function(e){return function(t){return c.run(e,this,[t])}}))}var f,_;e.describe=e.suite=function(){return l.describe.apply(this,h(arguments))},e.xdescribe=e.suite.skip=e.describe.skip=function(){return l.describe.skip.apply(this,h(arguments))},e.describe.only=e.suite.only=function(){return l.describe.only.apply(this,h(arguments))},e.it=e.specify=e.test=function(){return l.it.apply(this,p(arguments))},e.xit=e.xspecify=e.it.skip=function(){return l.it.skip.apply(this,p(arguments))},e.it.only=e.test.only=function(){return l.it.only.apply(this,p(arguments))},e.after=e.suiteTeardown=function(){return l.after.apply(this,d(arguments))},e.afterEach=e.teardown=function(){return l.afterEach.apply(this,p(arguments))},e.before=e.suiteSetup=function(){return l.before.apply(this,d(arguments))},e.beforeEach=e.setup=function(){return l.beforeEach.apply(this,p(arguments))},f=n.Runner.prototype.runTest,_=n.Runner.prototype.run,n.Runner.prototype.runTest=function(e){t.current.scheduleMicroTask("mocha.forceTask",(()=>{f.call(this,e)}))},n.Runner.prototype.run=function(e){return this.on("test",(e=>{a=s.fork(new r)})),this.on("fail",((e,t)=>{const n=a&&a.get("ProxyZoneSpec");if(n&&t)try{t.message+=n.getAndClearPendingTasksInfo()}catch(e){}})),_.call(this,e)}})),function(e){class t{static{this.symbolParentUnresolved=Zone.__symbol__("parentUnresolved")}constructor(t,n,r){this.finishCallback=t,this.failCallback=n,this._pendingMicroTasks=!1,this._pendingMacroTasks=!1,this._alreadyErrored=!1,this._isSync=!1,this._existingFinishTimer=null,this.entryFunction=null,this.runZone=Zone.current,this.unresolvedChainedPromiseCount=0,this.supportWaitUnresolvedChainedPromise=!1,this.name="asyncTestZone for "+r,this.properties={AsyncTestZoneSpec:this},this.supportWaitUnresolvedChainedPromise=!0===e[Zone.__symbol__("supportWaitUnResolvedChainedPromise")]}isUnresolvedChainedPromisePending(){return this.unresolvedChainedPromiseCount>0}_finishCallbackIfDone(){null!==this._existingFinishTimer&&(clearTimeout(this._existingFinishTimer),this._existingFinishTimer=null),this._pendingMicroTasks||this._pendingMacroTasks||this.supportWaitUnresolvedChainedPromise&&this.isUnresolvedChainedPromisePending()||this.runZone.run((()=>{this._existingFinishTimer=setTimeout((()=>{this._alreadyErrored||this._pendingMicroTasks||this._pendingMacroTasks||this.finishCallback()}),0)}))}patchPromiseForTest(){if(!this.supportWaitUnresolvedChainedPromise)return;const e=Promise[Zone.__symbol__("patchPromiseForTest")];e&&e()}unPatchPromiseForTest(){if(!this.supportWaitUnresolvedChainedPromise)return;const e=Promise[Zone.__symbol__("unPatchPromiseForTest")];e&&e()}onScheduleTask(e,n,r,o){return"eventTask"!==o.type&&(this._isSync=!1),"microTask"===o.type&&o.data&&o.data instanceof Promise&&!0===o.data[t.symbolParentUnresolved]&&this.unresolvedChainedPromiseCount--,e.scheduleTask(r,o)}onInvokeTask(e,t,n,r,o,s){return"eventTask"!==r.type&&(this._isSync=!1),e.invokeTask(n,r,o,s)}onCancelTask(e,t,n,r){return"eventTask"!==r.type&&(this._isSync=!1),e.cancelTask(n,r)}onInvoke(e,t,n,r,o,s,i){this.entryFunction||(this.entryFunction=r);try{return this._isSync=!0,e.invoke(n,r,o,s,i)}finally{this._isSync&&this.entryFunction===r&&this._finishCallbackIfDone()}}onHandleError(e,t,n,r){return e.handleError(n,r)&&(this.failCallback(r),this._alreadyErrored=!0),!1}onHasTask(e,t,n,r){e.hasTask(n,r),t===n&&("microTask"==r.change?(this._pendingMicroTasks=r.microTask,this._finishCallbackIfDone()):"macroTask"==r.change&&(this._pendingMacroTasks=r.macroTask,this._finishCallbackIfDone()))}}Zone.AsyncTestZoneSpec=t}("undefined"!=typeof window&&window||"undefined"!=typeof self&&self||global),Zone.__load_patch("asynctest",((e,t,n)=>{function r(e,n,r,o){const s=t.current,i=t.AsyncTestZoneSpec;if(void 0===i)throw new Error("AsyncTestZoneSpec is needed for the async() test helper but could not be found. Please make sure that your environment includes zone.js/plugins/async-test");const a=t.ProxyZoneSpec;if(!a)throw new Error("ProxyZoneSpec is needed for the async() test helper but could not be found. Please make sure that your environment includes zone.js/plugins/proxy");const c=a.get();a.assertPresent();const l=t.current.getZoneWith("ProxyZoneSpec"),u=c.getDelegate();return l.parent.run((()=>{const e=new i((()=>{c.getDelegate()==e&&c.setDelegate(u),e.unPatchPromiseForTest(),s.run((()=>{r()}))}),(t=>{c.getDelegate()==e&&c.setDelegate(u),e.unPatchPromiseForTest(),s.run((()=>{o(t)}))}),"test");c.setDelegate(e),e.patchPromiseForTest()})),t.current.runGuarded(e,n)}t[n.symbol("asyncTest")]=function t(n){return e.jasmine?function(e){e||((e=function(){}).fail=function(e){throw e}),r(n,this,e,(t=>{if("string"==typeof t)return e.fail(new Error(t));e.fail(t)}))}:function(){return new Promise(((e,t)=>{r(n,this,e,t)}))}}})),function(e){const t=e.Date;function n(){if(0===arguments.length){const e=new t;return e.setTime(n.now()),e}{const e=Array.prototype.slice.call(arguments);return new t(...e)}}n.now=function(){const e=Zone.current.get("FakeAsyncTestZoneSpec");return e?e.getFakeSystemTime():t.now.apply(this,arguments)},n.UTC=t.UTC,n.parse=t.parse;const r={setTimeout:e.setTimeout,setInterval:e.setInterval,clearTimeout:e.clearTimeout,clearInterval:e.clearInterval};class o{static{this.nextId=1}constructor(){this._schedulerQueue=[],this._currentTickTime=0,this._currentFakeBaseSystemTime=t.now(),this._currentTickRequeuePeriodicEntries=[]}getCurrentTickTime(){return this._currentTickTime}getFakeSystemTime(){return this._currentFakeBaseSystemTime+this._currentTickTime}setFakeBaseSystemTime(e){this._currentFakeBaseSystemTime=e}getRealSystemTime(){return t.now()}scheduleFunction(e,t,n){let r=(n={args:[],isPeriodic:!1,isRequestAnimationFrame:!1,id:-1,isRequeuePeriodic:!1,...n}).id<0?o.nextId++:n.id,s={endTime:this._currentTickTime+t,id:r,func:e,args:n.args,delay:t,isPeriodic:n.isPeriodic,isRequestAnimationFrame:n.isRequestAnimationFrame};n.isRequeuePeriodic&&this._currentTickRequeuePeriodicEntries.push(s);let i=0;for(;i<this._schedulerQueue.length&&!(s.endTime<this._schedulerQueue[i].endTime);i++);return this._schedulerQueue.splice(i,0,s),r}removeScheduledFunctionWithId(e){for(let t=0;t<this._schedulerQueue.length;t++)if(this._schedulerQueue[t].id==e){this._schedulerQueue.splice(t,1);break}}removeAll(){this._schedulerQueue=[]}getTimerCount(){return this._schedulerQueue.length}tickToNext(e=1,t,n){this._schedulerQueue.length<e||this.tick(this._schedulerQueue[e-1].endTime-this._currentTickTime,t,n)}tick(t=0,n,r){let o=this._currentTickTime+t,s=0;const i=(r=Object.assign({processNewMacroTasksSynchronously:!0},r)).processNewMacroTasksSynchronously?this._schedulerQueue:this._schedulerQueue.slice();if(0===i.length&&n)n(t);else{for(;i.length>0&&(this._currentTickRequeuePeriodicEntries=[],!(o<i[0].endTime));){let t=i.shift();if(!r.processNewMacroTasksSynchronously){const e=this._schedulerQueue.indexOf(t);e>=0&&this._schedulerQueue.splice(e,1)}if(s=this._currentTickTime,this._currentTickTime=t.endTime,n&&n(this._currentTickTime-s),!t.func.apply(e,t.isRequestAnimationFrame?[this._currentTickTime]:t.args))break;r.processNewMacroTasksSynchronously||this._currentTickRequeuePeriodicEntries.forEach((e=>{let t=0;for(;t<i.length&&!(e.endTime<i[t].endTime);t++);i.splice(t,0,e)}))}s=this._currentTickTime,this._currentTickTime=o,n&&n(this._currentTickTime-s)}}flushOnlyPendingTimers(e){if(0===this._schedulerQueue.length)return 0;const t=this._currentTickTime;return this.tick(this._schedulerQueue[this._schedulerQueue.length-1].endTime-t,e,{processNewMacroTasksSynchronously:!1}),this._currentTickTime-t}flush(e=20,t=!1,n){return t?this.flushPeriodic(n):this.flushNonPeriodic(e,n)}flushPeriodic(e){if(0===this._schedulerQueue.length)return 0;const t=this._currentTickTime;return this.tick(this._schedulerQueue[this._schedulerQueue.length-1].endTime-t,e),this._currentTickTime-t}flushNonPeriodic(t,n){const r=this._currentTickTime;let o=0,s=0;for(;this._schedulerQueue.length>0;){if(s++,s>t)throw new Error("flush failed after reaching the limit of "+t+" tasks. Does your code use a polling timeout?");if(0===this._schedulerQueue.filter((e=>!e.isPeriodic&&!e.isRequestAnimationFrame)).length)break;const r=this._schedulerQueue.shift();if(o=this._currentTickTime,this._currentTickTime=r.endTime,n&&n(this._currentTickTime-o),!r.func.apply(e,r.args))break}return this._currentTickTime-r}}class s{static assertInZone(){if(null==Zone.current.get("FakeAsyncTestZoneSpec"))throw new Error("The code should be running in the fakeAsync zone to call this function")}constructor(t,n=!1,r){this.trackPendingRequestAnimationFrame=n,this.macroTaskOptions=r,this._scheduler=new o,this._microtasks=[],this._lastError=null,this._uncaughtPromiseErrors=Promise[Zone.__symbol__("uncaughtPromiseErrors")],this.pendingPeriodicTimers=[],this.pendingTimers=[],this.patchDateLocked=!1,this.properties={FakeAsyncTestZoneSpec:this},this.name="fakeAsyncTestZone for "+t,this.macroTaskOptions||(this.macroTaskOptions=e[Zone.__symbol__("FakeAsyncTestMacroTask")])}_fnAndFlush(t,n){return(...r)=>(t.apply(e,r),null===this._lastError?(null!=n.onSuccess&&n.onSuccess.apply(e),this.flushMicrotasks()):null!=n.onError&&n.onError.apply(e),null===this._lastError)}static _removeTimer(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}_dequeueTimer(e){return()=>{s._removeTimer(this.pendingTimers,e)}}_requeuePeriodicTimer(e,t,n,r){return()=>{-1!==this.pendingPeriodicTimers.indexOf(r)&&this._scheduler.scheduleFunction(e,t,{args:n,isPeriodic:!0,id:r,isRequeuePeriodic:!0})}}_dequeuePeriodicTimer(e){return()=>{s._removeTimer(this.pendingPeriodicTimers,e)}}_setTimeout(e,t,n,r=!0){let s=this._dequeueTimer(o.nextId),i=this._fnAndFlush(e,{onSuccess:s,onError:s}),a=this._scheduler.scheduleFunction(i,t,{args:n,isRequestAnimationFrame:!r});return r&&this.pendingTimers.push(a),a}_clearTimeout(e){s._removeTimer(this.pendingTimers,e),this._scheduler.removeScheduledFunctionWithId(e)}_setInterval(e,t,n){let r=o.nextId,s={onSuccess:null,onError:this._dequeuePeriodicTimer(r)},i=this._fnAndFlush(e,s);return s.onSuccess=this._requeuePeriodicTimer(i,t,n,r),this._scheduler.scheduleFunction(i,t,{args:n,isPeriodic:!0}),this.pendingPeriodicTimers.push(r),r}_clearInterval(e){s._removeTimer(this.pendingPeriodicTimers,e),this._scheduler.removeScheduledFunctionWithId(e)}_resetLastErrorAndThrow(){let e=this._lastError||this._uncaughtPromiseErrors[0];throw this._uncaughtPromiseErrors.length=0,this._lastError=null,e}getCurrentTickTime(){return this._scheduler.getCurrentTickTime()}getFakeSystemTime(){return this._scheduler.getFakeSystemTime()}setFakeBaseSystemTime(e){this._scheduler.setFakeBaseSystemTime(e)}getRealSystemTime(){return this._scheduler.getRealSystemTime()}static patchDate(){e[Zone.__symbol__("disableDatePatching")]||e.Date!==n&&(e.Date=n,n.prototype=t.prototype,s.checkTimerPatch())}static resetDate(){e.Date===n&&(e.Date=t)}static checkTimerPatch(){e.setTimeout!==r.setTimeout&&(e.setTimeout=r.setTimeout,e.clearTimeout=r.clearTimeout),e.setInterval!==r.setInterval&&(e.setInterval=r.setInterval,e.clearInterval=r.clearInterval)}lockDatePatch(){this.patchDateLocked=!0,s.patchDate()}unlockDatePatch(){this.patchDateLocked=!1,s.resetDate()}tickToNext(e=1,t,n={processNewMacroTasksSynchronously:!0}){e<=0||(s.assertInZone(),this.flushMicrotasks(),this._scheduler.tickToNext(e,t,n),null!==this._lastError&&this._resetLastErrorAndThrow())}tick(e=0,t,n={processNewMacroTasksSynchronously:!0}){s.assertInZone(),this.flushMicrotasks(),this._scheduler.tick(e,t,n),null!==this._lastError&&this._resetLastErrorAndThrow()}flushMicrotasks(){for(s.assertInZone();this._microtasks.length>0;){let e=this._microtasks.shift();e.func.apply(e.target,e.args)}(()=>{(null!==this._lastError||this._uncaughtPromiseErrors.length)&&this._resetLastErrorAndThrow()})()}flush(e,t,n){s.assertInZone(),this.flushMicrotasks();const r=this._scheduler.flush(e,t,n);return null!==this._lastError&&this._resetLastErrorAndThrow(),r}flushOnlyPendingTimers(e){s.assertInZone(),this.flushMicrotasks();const t=this._scheduler.flushOnlyPendingTimers(e);return null!==this._lastError&&this._resetLastErrorAndThrow(),t}removeAllTimers(){s.assertInZone(),this._scheduler.removeAll(),this.pendingPeriodicTimers=[],this.pendingTimers=[]}getTimerCount(){return this._scheduler.getTimerCount()+this._microtasks.length}onScheduleTask(e,t,n,r){switch(r.type){case"microTask":let t,o=r.data&&r.data.args;if(o){let e=r.data.cbIdx;"number"==typeof o.length&&o.length>e+1&&(t=Array.prototype.slice.call(o,e+1))}this._microtasks.push({func:r.invoke,args:t,target:r.data&&r.data.target});break;case"macroTask":switch(r.source){case"setTimeout":r.data.handleId=this._setTimeout(r.invoke,r.data.delay,Array.prototype.slice.call(r.data.args,2));break;case"setImmediate":r.data.handleId=this._setTimeout(r.invoke,0,Array.prototype.slice.call(r.data.args,1));break;case"setInterval":r.data.handleId=this._setInterval(r.invoke,r.data.delay,Array.prototype.slice.call(r.data.args,2));break;case"XMLHttpRequest.send":throw new Error("Cannot make XHRs from within a fake async test. Request URL: "+r.data.url);case"requestAnimationFrame":case"webkitRequestAnimationFrame":case"mozRequestAnimationFrame":r.data.handleId=this._setTimeout(r.invoke,16,r.data.args,this.trackPendingRequestAnimationFrame);break;default:const e=this.findMacroTaskOption(r);if(e){const t=r.data&&r.data.args,n=t&&t.length>1?t[1]:0;let o=e.callbackArgs?e.callbackArgs:t;e.isPeriodic?(r.data.handleId=this._setInterval(r.invoke,n,o),r.data.isPeriodic=!0):r.data.handleId=this._setTimeout(r.invoke,n,o);break}throw new Error("Unknown macroTask scheduled in fake async test: "+r.source)}break;case"eventTask":r=e.scheduleTask(n,r)}return r}onCancelTask(e,t,n,r){switch(r.source){case"setTimeout":case"requestAnimationFrame":case"webkitRequestAnimationFrame":case"mozRequestAnimationFrame":return this._clearTimeout(r.data.handleId);case"setInterval":return this._clearInterval(r.data.handleId);default:const t=this.findMacroTaskOption(r);if(t){const e=r.data.handleId;return t.isPeriodic?this._clearInterval(e):this._clearTimeout(e)}return e.cancelTask(n,r)}}onInvoke(e,t,n,r,o,i,a){try{return s.patchDate(),e.invoke(n,r,o,i,a)}finally{this.patchDateLocked||s.resetDate()}}findMacroTaskOption(e){if(!this.macroTaskOptions)return null;for(let t=0;t<this.macroTaskOptions.length;t++){const n=this.macroTaskOptions[t];if(n.source===e.source)return n}return null}onHandleError(e,t,n,r){return this._lastError=r,!1}}Zone.FakeAsyncTestZoneSpec=s}("object"==typeof window&&window||"object"==typeof self&&self||global),Zone.__load_patch("fakeasync",((e,t,n)=>{const r=t&&t.FakeAsyncTestZoneSpec;function o(){return t&&t.ProxyZoneSpec}let s=null;function i(){s&&s.unlockDatePatch(),s=null,o()&&o().assertPresent().resetDelegate()}function a(){if(null==s&&(s=t.current.get("FakeAsyncTestZoneSpec"),null==s))throw new Error("The code should be running in the fakeAsync zone to call this function");return s}function c(){a().flushMicrotasks()}t[n.symbol("fakeAsyncTest")]={resetFakeAsyncZone:i,flushMicrotasks:c,discardPeriodicTasks:function l(){a().pendingPeriodicTimers.length=0},tick:function u(e=0,t=!1){a().tick(e,null,t)},flush:function h(e){return a().flush(e)},fakeAsync:function p(e){const n=function(...n){const a=o();if(!a)throw new Error("ProxyZoneSpec is needed for the async() test helper but could not be found. Please make sure that your environment includes zone.js/plugins/proxy");const l=a.assertPresent();if(t.current.get("FakeAsyncTestZoneSpec"))throw new Error("fakeAsync() calls can not be nested");try{if(!s){if(l.getDelegate()instanceof r)throw new Error("fakeAsync() calls can not be nested");s=new r}let t;const o=l.getDelegate();l.setDelegate(s),s.lockDatePatch();try{t=e.apply(this,n),c()}finally{l.setDelegate(o)}if(s.pendingPeriodicTimers.length>0)throw new Error(`${s.pendingPeriodicTimers.length} periodic timer(s) still in the queue.`);if(s.pendingTimers.length>0)throw new Error(`${s.pendingTimers.length} timer(s) still in the queue.`);return t}finally{i()}};return n.isFakeAsync=!0,n}}}),!0),Zone.__load_patch("promisefortest",((e,t,n)=>{const r=n.symbol("state"),o=n.symbol("parentUnresolved");Promise[n.symbol("patchPromiseForTest")]=function e(){let n=Promise[t.__symbol__("ZonePromiseThen")];n||(n=Promise[t.__symbol__("ZonePromiseThen")]=Promise.prototype.then,Promise.prototype.then=function(){const e=n.apply(this,arguments);if(null===this[r]){const n=t.current.get("AsyncTestZoneSpec");n&&(n.unresolvedChainedPromiseCount++,e[o]=!0)}return e})},Promise[n.symbol("unPatchPromiseForTest")]=function e(){const n=Promise[t.__symbol__("ZonePromiseThen")];n&&(Promise.prototype.then=n,Promise[t.__symbol__("ZonePromiseThen")]=void 0)}}));