{"ast": null, "code": "import { CORE_SIZE } from './constants';\nimport { fromEvent, map, take } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./quran.service\";\nexport let HelperService = /*#__PURE__*/(() => {\n  class HelperService {\n    constructor(quranService) {\n      this.quranService = quranService;\n    }\n    SurahNumberRestrict(surahNumber, startAyahInput, endAyahInput) {\n      startAyahInput.disabled = true;\n      endAyahInput.disabled = true;\n      startAyahInput.value = '';\n      endAyahInput.value = '';\n      this.quranService.GetSurah(surahNumber)?.subscribe(surah => {\n        startAyahInput.value = '1';\n        endAyahInput.value = surah.totalAyah.toString();\n        startAyahInput.max = surah.totalAyah.toString();\n        endAyahInput.max = surah.totalAyah.toString();\n        startAyahInput.disabled = false;\n        endAyahInput.disabled = false;\n      });\n    }\n    InputNumberRestrict(input) {\n      let numVal = Number.parseInt(input.value);\n      let max = Number.parseInt(input.max);\n      let min = Number.parseInt(input.min);\n      if (numVal > max) {\n        input.value = max.toString();\n      }\n      if (numVal < min) {\n        input.value = min.toString();\n      }\n    }\n    getDownloadProgress(url, recieved) {\n      const total = CORE_SIZE[url];\n      return Math.floor(recieved / total * 100);\n    }\n    getDuration(data) {\n      // Create a Blob from the Uint8Array\n      const blob = new Blob([data], {\n        type: 'audio/mpeg'\n      });\n      // Create an object URL from the Blob\n      const url = URL.createObjectURL(blob);\n      const audio = new Audio(url);\n      return fromEvent(audio, 'loadedmetadata').pipe(map(() => audio.duration), take(1)).toPromise();\n    }\n    static {\n      this.ɵfac = function HelperService_Factory(t) {\n        return new (t || HelperService)(i0.ɵɵinject(i1.QuranService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: HelperService,\n        factory: HelperService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return HelperService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}