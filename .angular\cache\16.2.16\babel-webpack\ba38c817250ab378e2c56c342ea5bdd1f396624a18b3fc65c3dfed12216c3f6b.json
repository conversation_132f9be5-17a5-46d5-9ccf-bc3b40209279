{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/\\u062A\\u0637\\u0628\\u064A\\u0642 \\u0646\\u0627\\u0634\\u0631 \\u0627\\u064A\\u0627\\u062A \\u0642\\u0626\\u0627\\u0646\\u064A\\u0629/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { WebPlugin } from '@capacitor/core';\nexport class DialogWeb extends WebPlugin {\n  alert(options) {\n    return _asyncToGenerator(function* () {\n      window.alert(options.message);\n    })();\n  }\n  prompt(options) {\n    return _asyncToGenerator(function* () {\n      const val = window.prompt(options.message, options.inputText || '');\n      return {\n        value: val !== null ? val : '',\n        cancelled: val === null\n      };\n    })();\n  }\n  confirm(options) {\n    return _asyncToGenerator(function* () {\n      const val = window.confirm(options.message);\n      return {\n        value: val\n      };\n    })();\n  }\n}\n//# sourceMappingURL=web.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}