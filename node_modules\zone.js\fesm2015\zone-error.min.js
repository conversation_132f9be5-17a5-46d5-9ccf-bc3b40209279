"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2022 Google LLC. https://angular.io/
 * License: MIT
 */Zone.__load_patch("Error",((e,r,t)=>{const n=t.symbol("zoneJsInternalStackFrames"),a=e[t.symbol("Error")]=e.Error,o={};let c,i,s,l,u;e.Error=h;const k="stackRewrite",f=e.__Zone_Error_BlacklistedStackFrames_policy||e.__Zone_Error_ZoneJsInternalStackFrames_policy||"default";function p(e,r,t=!0){let n=e.split("\n"),a=0;for(;n[a]!==c&&n[a]!==i&&n[a]!==s&&n[a]!==l&&n[a]!==u&&a<n.length;)a++;for(;a<n.length&&r;a++){let e=n[a];if(e.trim())switch(o[e]){case 0:n.splice(a,1),a--;break;case 1:r=r.parent?r.parent:null,n.splice(a,1),a--;break;default:n[a]+=t?` [${r.zone.name}]`:` [${r.zoneName}]`}}return n.join("\n")}function h(){let e=a.apply(this,arguments);const r=e.originalStack=e.stack;if(h[k]&&r){let n=t.currentZoneFrame();if("lazy"===f)e[t.symbol("zoneFrameNames")]=function a(e){let r={zoneName:e.zone.name},t=r;for(;e.parent;){const t={zoneName:(e=e.parent).zone.name};r.parent=t,r=t}return t}(n);else if("default"===f)try{e.stack=e.zoneAwareStack=p(r,n)}catch(e){}}return this instanceof a&&this.constructor!=a?(Object.keys(e).concat("stack","message").forEach((r=>{const t=e[r];if(void 0!==t)try{this[r]=t}catch(e){}})),this):e}h.prototype=a.prototype,h[n]=o,h[k]=!1;const m=t.symbol("zoneAwareStack");"lazy"===f&&Object.defineProperty(h.prototype,"zoneAwareStack",{configurable:!0,enumerable:!0,get:function(){return this[m]||(this[m]=p(this.originalStack,this[t.symbol("zoneFrameNames")],!1)),this[m]},set:function(e){this.originalStack=e,this[m]=p(this.originalStack,this[t.symbol("zoneFrameNames")],!1)}});const d=["stackTraceLimit","captureStackTrace","prepareStackTrace"],T=Object.keys(a);if(T&&T.forEach((e=>{0===d.filter((r=>r===e)).length&&Object.defineProperty(h,e,{get:function(){return a[e]},set:function(r){a[e]=r}})})),a.hasOwnProperty("stackTraceLimit")&&(a.stackTraceLimit=Math.max(a.stackTraceLimit,15),Object.defineProperty(h,"stackTraceLimit",{get:function(){return a.stackTraceLimit},set:function(e){return a.stackTraceLimit=e}})),a.hasOwnProperty("captureStackTrace")&&Object.defineProperty(h,"captureStackTrace",{value:function e(r,t){a.captureStackTrace(r,t)}}),Object.defineProperty(h,"prepareStackTrace",{get:function(){return a.prepareStackTrace},set:function(e){return a.prepareStackTrace=e&&"function"==typeof e?function(r,t){if(t)for(let e=0;e<t.length;e++)if("zoneCaptureStackTrace"===t[e].getFunctionName()){t.splice(e,1);break}return e.call(this,r,t)}:e}}),"disable"===f)return;const E=r.current.fork({name:"detect",onHandleError:function(e,r,t,n){if(n.originalStack&&Error===h){let e=n.originalStack.split(/\n/),r=!1,t=!1,a=!1;for(;e.length;){let n=e.shift();if(/:\d+:\d+/.test(n)||"ZoneAwareError"===n){let e=n.split("(")[0].split("@")[0],f=1;if(-1!==e.indexOf("ZoneAwareError")&&(-1!==e.indexOf("new ZoneAwareError")?(c=n,i=n.replace("new ZoneAwareError","new Error.ZoneAwareError")):(s=n,l=n.replace("Error.",""),-1===n.indexOf("Error.ZoneAwareError")&&(u=n.replace("ZoneAwareError","Error.ZoneAwareError"))),o[i]=0),-1!==e.indexOf("runGuarded")?t=!0:-1!==e.indexOf("runTask")?a=!0:-1!==e.indexOf("run")?r=!0:f=0,o[n]=f,r&&t&&a){h[k]=!0;break}}}}return!1}}).fork({name:"child",onScheduleTask:function(e,r,t,n){return e.scheduleTask(t,n)},onInvokeTask:function(e,r,t,n,a,o){return e.invokeTask(t,n,a,o)},onCancelTask:function(e,r,t,n){return e.cancelTask(t,n)},onInvoke:function(e,r,t,n,a,o,c){return e.invoke(t,n,a,o,c)}}),y=Error.stackTraceLimit;Error.stackTraceLimit=100,E.run((()=>{E.runGuarded((()=>{const e=()=>{};E.scheduleEventTask(n,(()=>{E.scheduleMacroTask(n,(()=>{E.scheduleMicroTask(n,(()=>{throw new Error}),void 0,(r=>{r._transitionTo=e,r.invoke()})),E.scheduleMicroTask(n,(()=>{throw Error()}),void 0,(r=>{r._transitionTo=e,r.invoke()}))}),void 0,(r=>{r._transitionTo=e,r.invoke()}),(()=>{}))}),void 0,(r=>{r._transitionTo=e,r.invoke()}),(()=>{}))}))})),Error.stackTraceLimit=y}));