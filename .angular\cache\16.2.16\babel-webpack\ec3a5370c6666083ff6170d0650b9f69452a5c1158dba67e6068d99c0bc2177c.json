{"ast": null, "code": "export var Directory = /*#__PURE__*/function (Directory) {\n  /**\n   * The Documents directory.\n   * On iOS it's the app's documents directory.\n   * Use this directory to store user-generated content.\n   * On Android it's the Public Documents folder, so it's accessible from other apps.\n   * It's not accesible on Android 10 unless the app enables legacy External Storage\n   * by adding `android:requestLegacyExternalStorage=\"true\"` in the `application` tag\n   * in the `AndroidManifest.xml`.\n   * On Android 11 or newer the app can only access the files/folders the app created.\n   *\n   * @since 1.0.0\n   */\n  Directory[\"Documents\"] = \"DOCUMENTS\";\n  /**\n   * The Data directory.\n   * On iOS it will use the Documents directory.\n   * On Android it's the directory holding application files.\n   * Files will be deleted when the application is uninstalled.\n   *\n   * @since 1.0.0\n   */\n  Directory[\"Data\"] = \"DATA\";\n  /**\n   * The Library directory.\n   * On iOS it will use the Library directory.\n   * On Android it's the directory holding application files.\n   * Files will be deleted when the application is uninstalled.\n   *\n   * @since 1.1.0\n   */\n  Directory[\"Library\"] = \"LIBRARY\";\n  /**\n   * The Cache directory.\n   * Can be deleted in cases of low memory, so use this directory to write app-specific files.\n   * that your app can re-create easily.\n   *\n   * @since 1.0.0\n   */\n  Directory[\"Cache\"] = \"CACHE\";\n  /**\n   * The external directory.\n   * On iOS it will use the Documents directory.\n   * On Android it's the directory on the primary shared/external\n   * storage device where the application can place persistent files it owns.\n   * These files are internal to the applications, and not typically visible\n   * to the user as media.\n   * Files will be deleted when the application is uninstalled.\n   *\n   * @since 1.0.0\n   */\n  Directory[\"External\"] = \"EXTERNAL\";\n  /**\n   * The external storage directory.\n   * On iOS it will use the Documents directory.\n   * On Android it's the primary shared/external storage directory.\n   * It's not accesible on Android 10 unless the app enables legacy External Storage\n   * by adding `android:requestLegacyExternalStorage=\"true\"` in the `application` tag\n   * in the `AndroidManifest.xml`.\n   * It's not accesible on Android 11 or newer.\n   *\n   * @since 1.0.0\n   */\n  Directory[\"ExternalStorage\"] = \"EXTERNAL_STORAGE\";\n  return Directory;\n}(Directory || {});\nexport var Encoding = /*#__PURE__*/function (Encoding) {\n  /**\n   * Eight-bit UCS Transformation Format\n   *\n   * @since 1.0.0\n   */\n  Encoding[\"UTF8\"] = \"utf8\";\n  /**\n   * Seven-bit ASCII, a.k.a. ISO646-US, a.k.a. the Basic Latin block of the\n   * Unicode character set\n   * This encoding is only supported on Android.\n   *\n   * @since 1.0.0\n   */\n  Encoding[\"ASCII\"] = \"ascii\";\n  /**\n   * Sixteen-bit UCS Transformation Format, byte order identified by an\n   * optional byte-order mark\n   * This encoding is only supported on Android.\n   *\n   * @since 1.0.0\n   */\n  Encoding[\"UTF16\"] = \"utf16\";\n  return Encoding;\n}(Encoding || {});\n/**\n * @deprecated Use `Directory`.\n * @since 1.0.0\n */\nexport const FilesystemDirectory = Directory;\n/**\n * @deprecated Use `Encoding`.\n * @since 1.0.0\n */\nexport const FilesystemEncoding = Encoding;\n//# sourceMappingURL=definitions.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}