{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction VideosDialogComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 5);\n    i0.ɵɵlistener(\"click\", function VideosDialogComponent_ng_container_1_Template_div_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r4);\n      const number_r1 = restoredCtx.$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.pickVideo(number_r1));\n    })(\"mouseover\", function VideosDialogComponent_ng_container_1_Template_div_mouseover_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const _r2 = i0.ɵɵreference(3);\n      return i0.ɵɵresetView(_r2.play());\n    })(\"mouseleave\", function VideosDialogComponent_ng_container_1_Template_div_mouseleave_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const _r2 = i0.ɵɵreference(3);\n      return i0.ɵɵresetView(_r2.pause());\n    });\n    i0.ɵɵelement(2, \"video\", 6, 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const number_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", \"assets/videos/\" + number_r1 + \".mp4\", i0.ɵɵsanitizeUrl);\n  }\n}\nexport let VideosDialogComponent = /*#__PURE__*/(() => {\n  class VideosDialogComponent {\n    constructor() {\n      this.videoNumbers = Array.from({\n        length: 15\n      }, (_, i) => i + 1);\n      this.pickedVideo = new EventEmitter();\n    }\n    pickVideo(number) {\n      this.pickedVideo.emit(number);\n    }\n    static {\n      this.ɵfac = function VideosDialogComponent_Factory(t) {\n        return new (t || VideosDialogComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: VideosDialogComponent,\n        selectors: [[\"app-videos-dialog\"]],\n        outputs: {\n          pickedVideo: \"pickedVideo\"\n        },\n        decls: 5,\n        vars: 1,\n        consts: [[1, \"row\", \"w-100\", \"object-fit-contain\", \"gap-1\", \"row-gap-3\", \"px-4\", \"my-4\", \"justify-content-around\"], [4, \"ngFor\", \"ngForOf\"], [1, \"col-5\", \"col-md-3\", \"col-lg-2\", \"p-0\", \"m-0\", \"d-flex\", \"justify-content-center\", \"align-items-center\", 2, \"cursor\", \"pointer\", 3, \"click\"], [1, \"video-item\", \"bg-danger\", \"hover-effect\", \"d-flex\", \"justify-content-center\", \"align-items-center\"], [\"width\", \"80px\", \"height\", \"80px\", \"src\", \"assets/images/random.svg\"], [1, \"col-5\", \"aftero\", \"col-md-3\", \"col-lg-2\", \"p-0\", \"m-0\", \"d-flex\", \"justify-content-center\", \"align-items-center\", 2, \"cursor\", \"pointer\", 3, \"click\", \"mouseover\", \"mouseleave\"], [1, \"video-item\", 3, \"src\"], [\"vid\", \"\"]],\n        template: function VideosDialogComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵtemplate(1, VideosDialogComponent_ng_container_1_Template, 4, 1, \"ng-container\", 1);\n            i0.ɵɵelementStart(2, \"div\", 2);\n            i0.ɵɵlistener(\"click\", function VideosDialogComponent_Template_div_click_2_listener() {\n              return ctx.pickVideo(undefined);\n            });\n            i0.ɵɵelementStart(3, \"div\", 3);\n            i0.ɵɵelement(4, \"img\", 4);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngForOf\", ctx.videoNumbers);\n          }\n        },\n        dependencies: [i1.NgForOf],\n        styles: [\".video-item[_ngcontent-%COMP%]{width:100%;height:100%;padding:0;margin:0;object-fit:cover;border-radius:12px;position:relative;z-index:1}.aftero[_ngcontent-%COMP%]{position:relative}.aftero[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;left:50%;top:50%;width:100%;height:100%;border-radius:12px;transform:translate(-50%,-50%);z-index:5;display:block;background-color:#0006;transition:all .2s ease-in-out}.aftero[_ngcontent-%COMP%]:hover:after{background-color:transparent}.hover-effect[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_rotateOnHover 2s infinite ease-in-out}@keyframes _ngcontent-%COMP%_rotateOnHover{50%{transform:rotate(360deg)}}\"]\n      });\n    }\n  }\n  return VideosDialogComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}