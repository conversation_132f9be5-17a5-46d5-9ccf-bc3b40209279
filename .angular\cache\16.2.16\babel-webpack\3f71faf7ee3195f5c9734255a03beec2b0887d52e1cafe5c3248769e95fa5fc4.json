{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { isPlatformBrowser, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, Component, Inject, Input, ContentChildren, EventEmitter, PLATFORM_ID, ChangeDetectionStrategy, ViewEncapsulation, Output, ViewChild, NgModule } from '@angular/core';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { <PERSON><PERSON>and<PERSON> } from 'primeng/dom';\nimport { ChevronLeftIcon } from 'primeng/icons/chevronleft';\nimport { ChevronRightIcon } from 'primeng/icons/chevronright';\nimport { TimesIcon } from 'primeng/icons/times';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i2 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { UniqueComponentId } from 'primeng/utils';\n\n/**\n * TabPanel is a helper component for TabView component.\n * @group Components\n */\nfunction TabPanel_div_0_ng_container_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TabPanel_div_0_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TabPanel_div_0_ng_container_2_ng_container_1_Template, 1, 0, \"ng-container\", 3);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.contentTemplate);\n  }\n}\nfunction TabPanel_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵprojection(1);\n    i0.ɵɵtemplate(2, TabPanel_div_0_ng_container_2_Template, 2, 1, \"ng-container\", 2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"hidden\", !ctx_r0.selected);\n    i0.ɵɵattribute(\"id\", ctx_r0.tabView.getTabContentId(ctx_r0.id))(\"aria-hidden\", !ctx_r0.selected)(\"aria-labelledby\", ctx_r0.tabView.getTabHeaderActionId(ctx_r0.id))(\"data-pc-name\", \"tabpanel\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.contentTemplate && (ctx_r0.cache ? ctx_r0.loaded : ctx_r0.selected));\n  }\n}\nconst _c0 = [\"*\"];\nconst _c1 = [\"content\"];\nconst _c2 = [\"navbar\"];\nconst _c3 = [\"prevBtn\"];\nconst _c4 = [\"nextBtn\"];\nconst _c5 = [\"inkbar\"];\nconst _c6 = [\"elementToObserve\"];\nfunction TabView_button_3_ChevronLeftIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronLeftIcon\");\n  }\n  if (rf & 2) {\n    i0.ɵɵattribute(\"aria-hidden\", true);\n  }\n}\nfunction TabView_button_3_3_ng_template_0_Template(rf, ctx) {}\nfunction TabView_button_3_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TabView_button_3_3_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction TabView_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 13, 14);\n    i0.ɵɵlistener(\"click\", function TabView_button_3_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.navBackward());\n    });\n    i0.ɵɵtemplate(2, TabView_button_3_ChevronLeftIcon_2_Template, 1, 1, \"ChevronLeftIcon\", 15);\n    i0.ɵɵtemplate(3, TabView_button_3_3_Template, 1, 0, null, 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"tabindex\", ctx_r1.tabindex)(\"aria-label\", ctx_r1.prevButtonAriaLabel);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.previousIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.previousIconTemplate);\n  }\n}\nfunction TabView_ng_template_8_li_0_ng_container_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 25);\n  }\n  if (rf & 2) {\n    const tab_r13 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", tab_r13.leftIcon);\n  }\n}\nfunction TabView_ng_template_8_li_0_ng_container_2_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction TabView_ng_template_8_li_0_ng_container_2_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TabView_ng_template_8_li_0_ng_container_2_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction TabView_ng_template_8_li_0_ng_container_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 26);\n    i0.ɵɵtemplate(1, TabView_ng_template_8_li_0_ng_container_2_span_2_1_Template, 1, 0, null, 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r13 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", tab_r13.leftIconTemplate);\n  }\n}\nfunction TabView_ng_template_8_li_0_ng_container_2_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 27);\n  }\n  if (rf & 2) {\n    const tab_r13 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", tab_r13.rightIcon);\n  }\n}\nfunction TabView_ng_template_8_li_0_ng_container_2_span_6_1_ng_template_0_Template(rf, ctx) {}\nfunction TabView_ng_template_8_li_0_ng_container_2_span_6_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TabView_ng_template_8_li_0_ng_container_2_span_6_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction TabView_ng_template_8_li_0_ng_container_2_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 28);\n    i0.ɵɵtemplate(1, TabView_ng_template_8_li_0_ng_container_2_span_6_1_Template, 1, 0, null, 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r13 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", tab_r13.rightIconTemplate);\n  }\n}\nfunction TabView_ng_template_8_li_0_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TabView_ng_template_8_li_0_ng_container_2_span_1_Template, 1, 1, \"span\", 20);\n    i0.ɵɵtemplate(2, TabView_ng_template_8_li_0_ng_container_2_span_2_Template, 2, 1, \"span\", 21);\n    i0.ɵɵelementStart(3, \"span\", 22);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, TabView_ng_template_8_li_0_ng_container_2_span_5_Template, 1, 1, \"span\", 23);\n    i0.ɵɵtemplate(6, TabView_ng_template_8_li_0_ng_container_2_span_6_Template, 2, 1, \"span\", 24);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const tab_r13 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", tab_r13.leftIcon && !tab_r13.leftIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", tab_r13.leftIconTemplate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tab_r13.header);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", tab_r13.rightIcon && !tab_r13.rightIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", tab_r13.rightIconTemplate);\n  }\n}\nfunction TabView_ng_template_8_li_0_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TabView_ng_template_8_li_0_ng_container_4_TimesIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"TimesIcon\", 31);\n    i0.ɵɵlistener(\"click\", function TabView_ng_template_8_li_0_ng_container_4_TimesIcon_1_Template_TimesIcon_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r37);\n      const tab_r13 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r35 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r35.close($event, tab_r13));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-tabview-close\");\n  }\n}\nfunction TabView_ng_template_8_li_0_ng_container_4_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 32);\n  }\n}\nfunction TabView_ng_template_8_li_0_ng_container_4_3_ng_template_0_Template(rf, ctx) {}\nfunction TabView_ng_template_8_li_0_ng_container_4_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TabView_ng_template_8_li_0_ng_container_4_3_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction TabView_ng_template_8_li_0_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TabView_ng_template_8_li_0_ng_container_4_TimesIcon_1_Template, 1, 1, \"TimesIcon\", 29);\n    i0.ɵɵtemplate(2, TabView_ng_template_8_li_0_ng_container_4_span_2_Template, 1, 0, \"span\", 30);\n    i0.ɵɵtemplate(3, TabView_ng_template_8_li_0_ng_container_4_3_Template, 1, 0, null, 16);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const tab_r13 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !tab_r13.closeIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", tab_r13.closeIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", tab_r13.closeIconTemplate);\n  }\n}\nconst _c7 = function (a0, a1) {\n  return {\n    \"p-highlight\": a0,\n    \"p-disabled\": a1\n  };\n};\nfunction TabView_ng_template_8_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r42 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 18)(1, \"a\", 19);\n    i0.ɵɵlistener(\"click\", function TabView_ng_template_8_li_0_Template_a_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r42);\n      const tab_r13 = i0.ɵɵnextContext().$implicit;\n      const ctx_r40 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r40.open($event, tab_r13));\n    })(\"keydown\", function TabView_ng_template_8_li_0_Template_a_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r42);\n      const tab_r13 = i0.ɵɵnextContext().$implicit;\n      const ctx_r43 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r43.onTabKeyDown($event, tab_r13));\n    });\n    i0.ɵɵtemplate(2, TabView_ng_template_8_li_0_ng_container_2_Template, 7, 5, \"ng-container\", 15);\n    i0.ɵɵtemplate(3, TabView_ng_template_8_li_0_ng_container_3_Template, 1, 0, \"ng-container\", 16);\n    i0.ɵɵtemplate(4, TabView_ng_template_8_li_0_ng_container_4_Template, 4, 3, \"ng-container\", 15);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r45 = i0.ɵɵnextContext();\n    const tab_r13 = ctx_r45.$implicit;\n    const i_r14 = ctx_r45.index;\n    const ctx_r15 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(tab_r13.headerStyleClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(19, _c7, tab_r13.selected, tab_r13.disabled))(\"ngStyle\", tab_r13.headerStyle);\n    i0.ɵɵattribute(\"data-p-disabled\", tab_r13.disabled);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"pTooltip\", tab_r13.tooltip)(\"tooltipPosition\", tab_r13.tooltipPosition)(\"positionStyle\", tab_r13.tooltipPositionStyle)(\"tooltipStyleClass\", tab_r13.tooltipStyleClass);\n    i0.ɵɵattribute(\"id\", ctx_r15.getTabHeaderActionId(tab_r13.id))(\"aria-controls\", ctx_r15.getTabContentId(tab_r13.id))(\"aria-selected\", tab_r13.selected)(\"tabindex\", tab_r13.disabled || !tab_r13.selected ? \"-1\" : ctx_r15.tabindex)(\"aria-disabled\", tab_r13.disabled)(\"data-pc-index\", i_r14)(\"data-pc-section\", \"headeraction\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !tab_r13.headerTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", tab_r13.headerTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", tab_r13.closable);\n  }\n}\nfunction TabView_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TabView_ng_template_8_li_0_Template, 5, 22, \"li\", 17);\n  }\n  if (rf & 2) {\n    const tab_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngIf\", !tab_r13.closed);\n  }\n}\nfunction TabView_button_11_ChevronRightIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronRightIcon\");\n  }\n  if (rf & 2) {\n    i0.ɵɵattribute(\"aria-hidden\", true);\n  }\n}\nfunction TabView_button_11_3_ng_template_0_Template(rf, ctx) {}\nfunction TabView_button_11_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TabView_button_11_3_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction TabView_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r51 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 33, 34);\n    i0.ɵɵlistener(\"click\", function TabView_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r51);\n      const ctx_r50 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r50.navForward());\n    });\n    i0.ɵɵtemplate(2, TabView_button_11_ChevronRightIcon_2_Template, 1, 1, \"ChevronRightIcon\", 15);\n    i0.ɵɵtemplate(3, TabView_button_11_3_Template, 1, 0, null, 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"tabindex\", ctx_r6.tabindex)(\"aria-label\", ctx_r6.nextButtonAriaLabel);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.nextIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r6.nextIconTemplate);\n  }\n}\nconst _c8 = function (a1) {\n  return {\n    \"p-tabview p-component\": true,\n    \"p-tabview-scrollable\": a1\n  };\n};\nlet TabPanel = /*#__PURE__*/(() => {\n  class TabPanel {\n    el;\n    viewContainer;\n    cd;\n    /**\n     * Defines if tab can be removed.\n     * @group Props\n     */\n    closable = false;\n    /**\n     * Inline style of the tab header.\n     * @group Props\n     */\n    get headerStyle() {\n      return this._headerStyle;\n    }\n    set headerStyle(headerStyle) {\n      this._headerStyle = headerStyle;\n      this.tabView.cd.markForCheck();\n    }\n    /**\n     * Style class of the tab header.\n     * @group Props\n     */\n    get headerStyleClass() {\n      return this._headerStyleClass;\n    }\n    set headerStyleClass(headerStyleClass) {\n      this._headerStyleClass = headerStyleClass;\n      this.tabView.cd.markForCheck();\n    }\n    /**\n     * Whether a lazy loaded panel should avoid getting loaded again on reselection.\n     * @group Props\n     */\n    cache = true;\n    /**\n     * Advisory information to display in a tooltip on hover.\n     * @group Props\n     */\n    tooltip;\n    /**\n     * Position of the tooltip.\n     * @group Props\n     */\n    tooltipPosition = 'top';\n    /**\n     * Type of CSS position.\n     * @group Props\n     */\n    tooltipPositionStyle = 'absolute';\n    /**\n     * Style class of the tooltip.\n     * @group Props\n     */\n    tooltipStyleClass;\n    /**\n     * Defines if tab is active.\n     * @defaultValue false\n     * @group Props\n     */\n    get selected() {\n      return !!this._selected;\n    }\n    set selected(val) {\n      this._selected = val;\n      if (!this.loaded) {\n        this.cd.detectChanges();\n      }\n      if (val) this.loaded = true;\n    }\n    /**\n     * When true, tab cannot be activated.\n     * @defaultValue false\n     * @group Props\n     */\n    get disabled() {\n      return !!this._disabled;\n    }\n    set disabled(disabled) {\n      this._disabled = disabled;\n      this.tabView.cd.markForCheck();\n    }\n    /**\n     * Title of the tabPanel.\n     * @group Props\n     */\n    get header() {\n      return this._header;\n    }\n    set header(header) {\n      this._header = header;\n      // We have to wait for the rendering and then retrieve the actual size element from the DOM.\n      // in future `Promise.resolve` can be changed to `queueMicrotask` (if ie11 support will be dropped)\n      Promise.resolve().then(() => {\n        this.tabView.updateInkBar();\n        this.tabView.cd.markForCheck();\n      });\n    }\n    /**\n     * Left icon of the tabPanel.\n     * @group Props\n     * @deprecated since v15.4.2, use `lefticon` template instead.\n     */\n    get leftIcon() {\n      return this._leftIcon;\n    }\n    set leftIcon(leftIcon) {\n      this._leftIcon = leftIcon;\n      this.tabView.cd.markForCheck();\n    }\n    /**\n     * Left icon of the tabPanel.\n     * @group Props\n     * @deprecated since v15.4.2, use `righticon` template instead.\n     */\n    get rightIcon() {\n      return this._rightIcon;\n    }\n    set rightIcon(rightIcon) {\n      this._rightIcon = rightIcon;\n      this.tabView.cd.markForCheck();\n    }\n    templates;\n    closed = false;\n    view = null;\n    _headerStyle;\n    _headerStyleClass;\n    _selected;\n    _disabled;\n    _header;\n    _leftIcon;\n    _rightIcon = undefined;\n    loaded = false;\n    id;\n    contentTemplate;\n    headerTemplate;\n    leftIconTemplate;\n    rightIconTemplate;\n    closeIconTemplate;\n    tabView;\n    constructor(tabView, el, viewContainer, cd) {\n      this.el = el;\n      this.viewContainer = viewContainer;\n      this.cd = cd;\n      this.tabView = tabView;\n      this.id = UniqueComponentId();\n    }\n    ngAfterContentInit() {\n      this.templates.forEach(item => {\n        switch (item.getType()) {\n          case 'header':\n            this.headerTemplate = item.template;\n            break;\n          case 'content':\n            this.contentTemplate = item.template;\n            break;\n          case 'righticon':\n            this.rightIconTemplate = item.template;\n            break;\n          case 'lefticon':\n            this.leftIconTemplate = item.template;\n            break;\n          case 'closeicon':\n            this.closeIconTemplate = item.template;\n            break;\n          default:\n            this.contentTemplate = item.template;\n            break;\n        }\n      });\n    }\n    ngOnDestroy() {\n      this.view = null;\n    }\n    static ɵfac = function TabPanel_Factory(t) {\n      return new (t || TabPanel)(i0.ɵɵdirectiveInject(forwardRef(() => TabView)), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TabPanel,\n      selectors: [[\"p-tabPanel\"]],\n      contentQueries: function TabPanel_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        closable: \"closable\",\n        headerStyle: \"headerStyle\",\n        headerStyleClass: \"headerStyleClass\",\n        cache: \"cache\",\n        tooltip: \"tooltip\",\n        tooltipPosition: \"tooltipPosition\",\n        tooltipPositionStyle: \"tooltipPositionStyle\",\n        tooltipStyleClass: \"tooltipStyleClass\",\n        selected: \"selected\",\n        disabled: \"disabled\",\n        header: \"header\",\n        leftIcon: \"leftIcon\",\n        rightIcon: \"rightIcon\"\n      },\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"p-tabview-panel\", \"role\", \"tabpanel\", 3, \"hidden\", 4, \"ngIf\"], [\"role\", \"tabpanel\", 1, \"p-tabview-panel\", 3, \"hidden\"], [4, \"ngIf\"], [4, \"ngTemplateOutlet\"]],\n      template: function TabPanel_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, TabPanel_div_0_Template, 3, 6, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", !ctx.closed);\n        }\n      },\n      dependencies: [i1.NgIf, i1.NgTemplateOutlet],\n      encapsulation: 2\n    });\n  }\n  return TabPanel;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * TabView is a container component to group content with tabs.\n * @group Components\n */\nlet TabView = /*#__PURE__*/(() => {\n  class TabView {\n    platformId;\n    el;\n    cd;\n    renderer;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    style;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Whether tab close is controlled at onClose event or not.\n     * @defaultValue false\n     * @group Props\n     */\n    controlClose;\n    /**\n     * When enabled displays buttons at each side of the tab headers to scroll the tab list.\n     * @defaultValue false\n     * @group Props\n     */\n    scrollable;\n    /**\n     * Index of the active tab to change selected tab programmatically.\n     * @group Props\n     */\n    get activeIndex() {\n      return this._activeIndex;\n    }\n    set activeIndex(val) {\n      this._activeIndex = val;\n      if (this.preventActiveIndexPropagation) {\n        this.preventActiveIndexPropagation = false;\n        return;\n      }\n      if (this.tabs && this.tabs.length && this._activeIndex != null && this.tabs.length > this._activeIndex) {\n        this.findSelectedTab().selected = false;\n        this.tabs[this._activeIndex].selected = true;\n        this.tabChanged = true;\n        this.updateScrollBar(val);\n      }\n    }\n    /**\n     * When enabled, the focused tab is activated.\n     * @group Props\n     */\n    selectOnFocus = false;\n    /**\n     * Used to define a string aria label attribute the forward navigation button.\n     * @group Props\n     */\n    nextButtonAriaLabel;\n    /**\n     * Used to define a string aria label attribute the backward navigation button.\n     * @group Props\n     */\n    prevButtonAriaLabel;\n    /**\n     * When activated, navigation buttons will automatically hide or show based on the available space within the container.\n     * @group Props\n     */\n    autoHideButtons = true;\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    tabindex = 0;\n    /**\n     * Callback to invoke on tab change.\n     * @param {TabViewChangeEvent} event - Custom tab change event\n     * @group Emits\n     */\n    onChange = new EventEmitter();\n    /**\n     * Callback to invoke on tab close.\n     * @param {TabViewCloseEvent} event - Custom tab close event\n     * @group Emits\n     */\n    onClose = new EventEmitter();\n    /**\n     * Callback to invoke on the active tab change.\n     * @param {number} index - New active index\n     * @group Emits\n     */\n    activeIndexChange = new EventEmitter();\n    content;\n    navbar;\n    prevBtn;\n    nextBtn;\n    inkbar;\n    tabPanels;\n    templates;\n    initialized;\n    tabs;\n    _activeIndex;\n    preventActiveIndexPropagation;\n    tabChanged;\n    backwardIsDisabled = true;\n    forwardIsDisabled = false;\n    tabChangesSubscription;\n    nextIconTemplate;\n    previousIconTemplate;\n    resizeObserver;\n    container;\n    list;\n    buttonVisible;\n    elementToObserve;\n    constructor(platformId, el, cd, renderer) {\n      this.platformId = platformId;\n      this.el = el;\n      this.cd = cd;\n      this.renderer = renderer;\n    }\n    ngAfterContentInit() {\n      this.initTabs();\n      this.tabChangesSubscription = this.tabPanels.changes.subscribe(_ => {\n        this.initTabs();\n      });\n      this.templates.forEach(item => {\n        switch (item.getType()) {\n          case 'previousicon':\n            this.previousIconTemplate = item.template;\n            break;\n          case 'nexticon':\n            this.nextIconTemplate = item.template;\n            break;\n        }\n      });\n    }\n    ngAfterViewInit() {\n      if (isPlatformBrowser(this.platformId)) {\n        if (this.autoHideButtons) {\n          this.bindResizeObserver();\n        }\n      }\n    }\n    bindResizeObserver() {\n      this.container = DomHandler.findSingle(this.el.nativeElement, '[data-pc-section=\"navcontent\"]');\n      this.list = DomHandler.findSingle(this.el.nativeElement, '[data-pc-section=\"nav\"]');\n      this.resizeObserver = new ResizeObserver(() => {\n        if (this.list.offsetWidth > this.container.offsetWidth) {\n          this.buttonVisible = true;\n        } else {\n          this.buttonVisible = false;\n        }\n        this.updateButtonState();\n        this.cd.detectChanges();\n      });\n      this.resizeObserver.observe(this.container);\n    }\n    unbindResizeObserver() {\n      this.resizeObserver.unobserve(this.elementToObserve.nativeElement);\n      this.resizeObserver = null;\n    }\n    ngAfterViewChecked() {\n      if (isPlatformBrowser(this.platformId)) {\n        if (this.tabChanged) {\n          this.updateInkBar();\n          this.tabChanged = false;\n        }\n      }\n    }\n    ngOnDestroy() {\n      if (this.tabChangesSubscription) {\n        this.tabChangesSubscription.unsubscribe();\n      }\n      if (this.resizeObserver) {\n        this.unbindResizeObserver();\n      }\n    }\n    getTabHeaderActionId(tabId) {\n      return `${tabId}_header_action`;\n    }\n    getTabContentId(tabId) {\n      return `${tabId}_content`;\n    }\n    initTabs() {\n      this.tabs = this.tabPanels.toArray();\n      let selectedTab = this.findSelectedTab();\n      if (!selectedTab && this.tabs.length) {\n        if (this.activeIndex != null && this.tabs.length > this.activeIndex) this.tabs[this.activeIndex].selected = true;else this.tabs[0].selected = true;\n        this.tabChanged = true;\n      }\n      this.cd.markForCheck();\n    }\n    onTabKeyDown(event, tab) {\n      switch (event.code) {\n        case 'ArrowLeft':\n          this.onTabArrowLeftKey(event);\n          break;\n        case 'ArrowRight':\n          this.onTabArrowRightKey(event);\n          break;\n        case 'Home':\n          this.onTabHomeKey(event);\n          break;\n        case 'End':\n          this.onTabEndKey(event);\n          break;\n        case 'PageDown':\n          this.onTabEndKey(event);\n          break;\n        case 'PageUp':\n          this.onTabHomeKey(event);\n          break;\n        case 'Enter':\n        case 'Space':\n          this.open(event, tab);\n          break;\n        default:\n          break;\n      }\n    }\n    onTabArrowLeftKey(event) {\n      const prevHeaderAction = this.findPrevHeaderAction(event.target.parentElement);\n      const index = DomHandler.getAttribute(prevHeaderAction, 'data-pc-index');\n      prevHeaderAction ? this.changeFocusedTab(event, prevHeaderAction, index) : this.onTabEndKey(event);\n      event.preventDefault();\n    }\n    onTabArrowRightKey(event) {\n      const nextHeaderAction = this.findNextHeaderAction(event.target.parentElement);\n      const index = DomHandler.getAttribute(nextHeaderAction, 'data-pc-index');\n      nextHeaderAction ? this.changeFocusedTab(event, nextHeaderAction, index) : this.onTabHomeKey(event);\n      event.preventDefault();\n    }\n    onTabHomeKey(event) {\n      const firstHeaderAction = this.findFirstHeaderAction();\n      const index = DomHandler.getAttribute(firstHeaderAction, 'data-pc-index');\n      this.changeFocusedTab(event, firstHeaderAction, index);\n      event.preventDefault();\n    }\n    onTabEndKey(event) {\n      const lastHeaderAction = this.findLastHeaderAction();\n      const index = DomHandler.getAttribute(lastHeaderAction, 'data-pc-index');\n      this.changeFocusedTab(event, lastHeaderAction, index);\n      event.preventDefault();\n    }\n    changeFocusedTab(event, element, index) {\n      if (element) {\n        DomHandler.focus(element);\n        element.scrollIntoView({\n          block: 'nearest'\n        });\n        if (this.selectOnFocus) {\n          const tab = this.tabs[index];\n          this.open(event, tab);\n        }\n      }\n    }\n    findNextHeaderAction(tabElement, selfCheck = false) {\n      const headerElement = selfCheck ? tabElement : tabElement.nextElementSibling;\n      return headerElement ? DomHandler.getAttribute(headerElement, 'data-p-disabled') || DomHandler.getAttribute(headerElement, 'data-pc-section') === 'inkbar' ? this.findNextHeaderAction(headerElement) : DomHandler.findSingle(headerElement, '[data-pc-section=\"headeraction\"]') : null;\n    }\n    findPrevHeaderAction(tabElement, selfCheck = false) {\n      const headerElement = selfCheck ? tabElement : tabElement.previousElementSibling;\n      return headerElement ? DomHandler.getAttribute(headerElement, 'data-p-disabled') || DomHandler.getAttribute(headerElement, 'data-pc-section') === 'inkbar' ? this.findPrevHeaderAction(headerElement) : DomHandler.findSingle(headerElement, '[data-pc-section=\"headeraction\"]') : null;\n    }\n    findFirstHeaderAction() {\n      const firstEl = this.navbar.nativeElement.firstElementChild;\n      return this.findNextHeaderAction(firstEl, true);\n    }\n    findLastHeaderAction() {\n      const lastEl = this.navbar.nativeElement.lastElementChild;\n      const lastHeaderAction = DomHandler.getAttribute(lastEl, 'data-pc-section') === 'inkbar' ? lastEl.previousElementSibling : lastEl;\n      return this.findPrevHeaderAction(lastHeaderAction, true);\n    }\n    open(event, tab) {\n      if (tab.disabled) {\n        if (event) {\n          event.preventDefault();\n        }\n        return;\n      }\n      if (!tab.selected) {\n        let selectedTab = this.findSelectedTab();\n        if (selectedTab) {\n          selectedTab.selected = false;\n        }\n        this.tabChanged = true;\n        tab.selected = true;\n        let selectedTabIndex = this.findTabIndex(tab);\n        this.preventActiveIndexPropagation = true;\n        this.activeIndexChange.emit(selectedTabIndex);\n        this.onChange.emit({\n          originalEvent: event,\n          index: selectedTabIndex\n        });\n        this.updateScrollBar(selectedTabIndex);\n      }\n      if (event) {\n        event.preventDefault();\n      }\n    }\n    close(event, tab) {\n      if (this.controlClose) {\n        this.onClose.emit({\n          originalEvent: event,\n          index: this.findTabIndex(tab),\n          close: () => {\n            this.closeTab(tab);\n          }\n        });\n      } else {\n        this.closeTab(tab);\n        this.onClose.emit({\n          originalEvent: event,\n          index: this.findTabIndex(tab)\n        });\n      }\n    }\n    closeTab(tab) {\n      if (tab.disabled) {\n        return;\n      }\n      if (tab.selected) {\n        this.tabChanged = true;\n        tab.selected = false;\n        for (let i = 0; i < this.tabs.length; i++) {\n          let tabPanel = this.tabs[i];\n          if (!tabPanel.closed && !tab.disabled) {\n            tabPanel.selected = true;\n            break;\n          }\n        }\n      }\n      tab.closed = true;\n    }\n    findSelectedTab() {\n      for (let i = 0; i < this.tabs.length; i++) {\n        if (this.tabs[i].selected) {\n          return this.tabs[i];\n        }\n      }\n      return null;\n    }\n    findTabIndex(tab) {\n      let index = -1;\n      for (let i = 0; i < this.tabs.length; i++) {\n        if (this.tabs[i] == tab) {\n          index = i;\n          break;\n        }\n      }\n      return index;\n    }\n    getBlockableElement() {\n      return this.el.nativeElement.children[0];\n    }\n    updateInkBar() {\n      if (this.navbar) {\n        const tabHeader = DomHandler.findSingle(this.navbar.nativeElement, 'li.p-highlight');\n        if (!tabHeader) {\n          return;\n        }\n        this.inkbar.nativeElement.style.width = DomHandler.getWidth(tabHeader) + 'px';\n        this.inkbar.nativeElement.style.left = DomHandler.getOffset(tabHeader).left - DomHandler.getOffset(this.navbar.nativeElement).left + 'px';\n      }\n    }\n    updateScrollBar(index) {\n      let tabHeader = this.navbar.nativeElement.children[index];\n      tabHeader.scrollIntoView({\n        block: 'nearest'\n      });\n    }\n    updateButtonState() {\n      const content = this.content.nativeElement;\n      const {\n        scrollLeft,\n        scrollWidth\n      } = content;\n      const width = DomHandler.getWidth(content);\n      this.backwardIsDisabled = scrollLeft === 0;\n      this.forwardIsDisabled = scrollLeft === scrollWidth - width;\n    }\n    onScroll(event) {\n      this.scrollable && this.updateButtonState();\n      event.preventDefault();\n    }\n    getVisibleButtonWidths() {\n      return [this.prevBtn?.nativeElement, this.nextBtn?.nativeElement].reduce((acc, el) => el ? acc + DomHandler.getWidth(el) : acc, 0);\n    }\n    navBackward() {\n      const content = this.content.nativeElement;\n      const width = DomHandler.getWidth(content) - this.getVisibleButtonWidths();\n      const pos = content.scrollLeft - width;\n      content.scrollLeft = pos <= 0 ? 0 : pos;\n    }\n    navForward() {\n      const content = this.content.nativeElement;\n      const width = DomHandler.getWidth(content) - this.getVisibleButtonWidths();\n      const pos = content.scrollLeft + width;\n      const lastPos = content.scrollWidth - width;\n      content.scrollLeft = pos >= lastPos ? lastPos : pos;\n    }\n    static ɵfac = function TabView_Factory(t) {\n      return new (t || TabView)(i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.Renderer2));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TabView,\n      selectors: [[\"p-tabView\"]],\n      contentQueries: function TabView_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, TabPanel, 4);\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tabPanels = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      viewQuery: function TabView_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n          i0.ɵɵviewQuery(_c3, 5);\n          i0.ɵɵviewQuery(_c4, 5);\n          i0.ɵɵviewQuery(_c5, 5);\n          i0.ɵɵviewQuery(_c6, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.content = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.navbar = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.prevBtn = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nextBtn = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.inkbar = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.elementToObserve = _t.first);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        style: \"style\",\n        styleClass: \"styleClass\",\n        controlClose: \"controlClose\",\n        scrollable: \"scrollable\",\n        activeIndex: \"activeIndex\",\n        selectOnFocus: \"selectOnFocus\",\n        nextButtonAriaLabel: \"nextButtonAriaLabel\",\n        prevButtonAriaLabel: \"prevButtonAriaLabel\",\n        autoHideButtons: \"autoHideButtons\",\n        tabindex: \"tabindex\"\n      },\n      outputs: {\n        onChange: \"onChange\",\n        onClose: \"onClose\",\n        activeIndexChange: \"activeIndexChange\"\n      },\n      ngContentSelectors: _c0,\n      decls: 14,\n      vars: 13,\n      consts: [[3, \"ngClass\", \"ngStyle\"], [1, \"p-tabview-nav-container\"], [\"elementToObserve\", \"\"], [\"class\", \"p-tabview-nav-prev p-tabview-nav-btn p-link\", \"type\", \"button\", \"pRipple\", \"\", 3, \"click\", 4, \"ngIf\"], [1, \"p-tabview-nav-content\", 3, \"scroll\"], [\"content\", \"\"], [\"role\", \"tablist\", 1, \"p-tabview-nav\"], [\"navbar\", \"\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [\"role\", \"presentation\", \"aria-hidden\", \"true\", 1, \"p-tabview-ink-bar\"], [\"inkbar\", \"\"], [\"class\", \"p-tabview-nav-next p-tabview-nav-btn p-link\", \"type\", \"button\", \"pRipple\", \"\", 3, \"click\", 4, \"ngIf\"], [1, \"p-tabview-panels\"], [\"type\", \"button\", \"pRipple\", \"\", 1, \"p-tabview-nav-prev\", \"p-tabview-nav-btn\", \"p-link\", 3, \"click\"], [\"prevBtn\", \"\"], [4, \"ngIf\"], [4, \"ngTemplateOutlet\"], [\"role\", \"presentation\", 3, \"ngClass\", \"ngStyle\", \"class\", 4, \"ngIf\"], [\"role\", \"presentation\", 3, \"ngClass\", \"ngStyle\"], [\"role\", \"tab\", \"pRipple\", \"\", 1, \"p-tabview-nav-link\", 3, \"pTooltip\", \"tooltipPosition\", \"positionStyle\", \"tooltipStyleClass\", \"click\", \"keydown\"], [\"class\", \"p-tabview-left-icon\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"p-tabview-left-icon\", 4, \"ngIf\"], [1, \"p-tabview-title\"], [\"class\", \"p-tabview-right-icon\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"p-tabview-right-icon\", 4, \"ngIf\"], [1, \"p-tabview-left-icon\", 3, \"ngClass\"], [1, \"p-tabview-left-icon\"], [1, \"p-tabview-right-icon\", 3, \"ngClass\"], [1, \"p-tabview-right-icon\"], [3, \"styleClass\", \"click\", 4, \"ngIf\"], [\"class\", \"tab.closeIconTemplate\", 4, \"ngIf\"], [3, \"styleClass\", \"click\"], [1, \"tab.closeIconTemplate\"], [\"type\", \"button\", \"pRipple\", \"\", 1, \"p-tabview-nav-next\", \"p-tabview-nav-btn\", \"p-link\", 3, \"click\"], [\"nextBtn\", \"\"]],\n      template: function TabView_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1, 2);\n          i0.ɵɵtemplate(3, TabView_button_3_Template, 4, 4, \"button\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4, 5);\n          i0.ɵɵlistener(\"scroll\", function TabView_Template_div_scroll_4_listener($event) {\n            return ctx.onScroll($event);\n          });\n          i0.ɵɵelementStart(6, \"ul\", 6, 7);\n          i0.ɵɵtemplate(8, TabView_ng_template_8_Template, 1, 1, \"ng-template\", 8);\n          i0.ɵɵelement(9, \"li\", 9, 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(11, TabView_button_11_Template, 4, 4, \"button\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"div\", 12);\n          i0.ɵɵprojection(13);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.styleClass);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(11, _c8, ctx.scrollable))(\"ngStyle\", ctx.style);\n          i0.ɵɵattribute(\"data-pc-name\", \"tabview\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.scrollable && !ctx.backwardIsDisabled && ctx.autoHideButtons);\n          i0.ɵɵadvance(1);\n          i0.ɵɵattribute(\"data-pc-section\", \"navcontent\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵattribute(\"data-pc-section\", \"nav\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.tabs);\n          i0.ɵɵadvance(1);\n          i0.ɵɵattribute(\"data-pc-section\", \"inkbar\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.scrollable && !ctx.forwardIsDisabled && ctx.buttonVisible);\n        }\n      },\n      dependencies: function () {\n        return [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, i2.Tooltip, i3.Ripple, TimesIcon, ChevronLeftIcon, ChevronRightIcon];\n      },\n      styles: [\"@layer primeng{.p-tabview-nav-container{position:relative}.p-tabview-scrollable .p-tabview-nav-container{overflow:hidden}.p-tabview-nav-content{overflow-x:auto;overflow-y:hidden;scroll-behavior:smooth;scrollbar-width:none;overscroll-behavior:contain auto}.p-tabview-nav{display:inline-flex;min-width:100%;margin:0;padding:0;list-style-type:none;flex:1 1 auto}.p-tabview-nav-link{cursor:pointer;-webkit-user-select:none;user-select:none;display:flex;align-items:center;position:relative;text-decoration:none;overflow:hidden}.p-tabview-ink-bar{display:none;z-index:1}.p-tabview-nav-link:focus{z-index:1}.p-tabview-title{line-height:1;white-space:nowrap}.p-tabview-nav-btn{position:absolute;top:0;z-index:2;height:100%;display:flex;align-items:center;justify-content:center}.p-tabview-nav-prev{left:0}.p-tabview-nav-next{right:0}.p-tabview-nav-content::-webkit-scrollbar{display:none}.p-tabview-close{z-index:1}}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return TabView;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet TabViewModule = /*#__PURE__*/(() => {\n  class TabViewModule {\n    static ɵfac = function TabViewModule_Factory(t) {\n      return new (t || TabViewModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: TabViewModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, SharedModule, TooltipModule, RippleModule, TimesIcon, ChevronLeftIcon, ChevronRightIcon, SharedModule]\n    });\n  }\n  return TabViewModule;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TabPanel, TabView, TabViewModule };\n//# sourceMappingURL=primeng-tabview.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}