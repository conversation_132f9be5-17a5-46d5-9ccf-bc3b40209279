Write-Host "========================================" -ForegroundColor Green
Write-Host "    بناء APK باستخدام Gradle المثبت" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# تعيين متغيرات البيئة
$env:GRADLE_HOME = "C:\gradle\gradle-7.6.4"
$env:PATH = "$env:GRADLE_HOME\bin;$env:PATH"
$env:JAVA_HOME = "C:\Program Files\Microsoft\jdk-*********-hotspot"

Write-Host "متغيرات البيئة:" -ForegroundColor Yellow
Write-Host "GRADLE_HOME: $env:GRADLE_HOME" -ForegroundColor Cyan
Write-Host "JAVA_HOME: $env:JAVA_HOME" -ForegroundColor Cyan

# التحقق من وجود Gradle
if (-not (Test-Path "$env:GRADLE_HOME\bin\gradle.bat")) {
    Write-Host "❌ Gradle غير موجود في: $env:GRADLE_HOME" -ForegroundColor Red
    Write-Host "يرجى تشغيل download_gradle.ps1 أولاً" -ForegroundColor Yellow
    Read-Host "اضغط Enter للخروج..."
    exit 1
}

# التحقق من وجود Java
try {
    $javaVersion = java -version 2>&1
    Write-Host "✅ Java متوفر" -ForegroundColor Green
}
catch {
    Write-Host "❌ Java غير متوفر أو غير مُعرف بشكل صحيح" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج..."
    exit 1
}

# التحقق من وجود مجلد المشروع
$projectPath = "C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية"
$androidPath = "$projectPath\android"

if (-not (Test-Path $androidPath)) {
    Write-Host "❌ مجلد Android غير موجود في: $androidPath" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج..."
    exit 1
}

Write-Host "✅ جميع المتطلبات متوفرة" -ForegroundColor Green

# الانتقال إلى مجلد Android
Write-Host ""
Write-Host "الانتقال إلى مجلد Android..." -ForegroundColor Yellow
Set-Location $androidPath

# بناء APK
Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "بدء بناء APK..." -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

try {
    # تشغيل Gradle
    Write-Host "تشغيل: gradle assembleDebug" -ForegroundColor Cyan
    & "$env:GRADLE_HOME\bin\gradle.bat" assembleDebug
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ تم بناء APK بنجاح!" -ForegroundColor Green
    } else {
        throw "فشل في بناء APK"
    }
}
catch {
    Write-Host "❌ خطأ في بناء APK: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج..."
    exit 1
}

# البحث عن ملف APK
Write-Host ""
Write-Host "البحث عن ملف APK..." -ForegroundColor Yellow

$apkPaths = @(
    "app\build\outputs\apk\debug\app-debug.apk",
    "app\build\outputs\apk\debug\app-debug-unsigned.apk",
    "build\outputs\apk\debug\app-debug.apk"
)

$apkFound = $false
$apkPath = ""

foreach ($path in $apkPaths) {
    if (Test-Path $path) {
        $apkPath = $path
        $apkFound = $true
        break
    }
}

if ($apkFound) {
    Write-Host "✅ تم العثور على APK في: $apkPath" -ForegroundColor Green
    
    # معلومات الملف
    $fileInfo = Get-Item $apkPath
    $fileSize = [math]::Round($fileInfo.Length / 1MB, 2)
    Write-Host "حجم الملف: $fileSize MB" -ForegroundColor Cyan
    Write-Host "تاريخ الإنشاء: $($fileInfo.CreationTime)" -ForegroundColor Cyan
    
    # نسخ إلى سطح المكتب
    $desktopPath = "$env:USERPROFILE\Desktop\QuranVidGen-Debug.apk"
    
    try {
        Copy-Item $apkPath $desktopPath -Force
        Write-Host "✅ تم نسخ APK إلى سطح المكتب: QuranVidGen-Debug.apk" -ForegroundColor Green
        
        Write-Host ""
        Write-Host "========================================" -ForegroundColor Green
        Write-Host "تم بناء APK بنجاح!" -ForegroundColor Green
        Write-Host "========================================" -ForegroundColor Green
        Write-Host "مسار الملف: $desktopPath" -ForegroundColor White
        Write-Host "حجم الملف: $fileSize MB" -ForegroundColor White
        Write-Host ""
        Write-Host "يمكنك الآن تثبيت التطبيق على جهاز Android" -ForegroundColor Yellow
        
    }
    catch {
        Write-Host "❌ خطأ في نسخ الملف: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "مسار APK الأصلي: $apkPath" -ForegroundColor Yellow
    }
    
} else {
    Write-Host "❌ لم يتم العثور على ملف APK" -ForegroundColor Red
    Write-Host "تحقق من الأخطاء أعلاه" -ForegroundColor Yellow
}

# العودة إلى المجلد الأصلي
Set-Location $projectPath

Write-Host ""
Read-Host "اضغط Enter للخروج..."
