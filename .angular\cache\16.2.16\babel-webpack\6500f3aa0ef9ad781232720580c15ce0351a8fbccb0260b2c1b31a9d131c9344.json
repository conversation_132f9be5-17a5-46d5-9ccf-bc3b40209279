{"ast": null, "code": "import { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i2 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, PLATFORM_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ContentChildren, HostListener, NgModule } from '@angular/core';\nimport * as i1 from 'primeng/api';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler, ConnectedOverlayScrollHandler } from 'primeng/dom';\nimport { TimesIcon } from 'primeng/icons/times';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport { ZIndexUtils } from 'primeng/utils';\n\n/**\n * OverlayPanel is a container component positioned as connected to its target.\n * @group Components\n */\nfunction OverlayPanel_div_0_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction OverlayPanel_div_0_button_4_TimesIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"TimesIcon\", 8);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-overlaypanel-close-icon\");\n  }\n}\nfunction OverlayPanel_div_0_button_4_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction OverlayPanel_div_0_button_4_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, OverlayPanel_div_0_button_4_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction OverlayPanel_div_0_button_4_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 9);\n    i0.ɵɵtemplate(1, OverlayPanel_div_0_button_4_span_2_1_Template, 1, 0, null, 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r4.closeIconTemplate);\n  }\n}\nfunction OverlayPanel_div_0_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function OverlayPanel_div_0_button_4_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r7.onCloseClick($event));\n    })(\"keydown.enter\", function OverlayPanel_div_0_button_4_Template_button_keydown_enter_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.hide());\n    });\n    i0.ɵɵtemplate(1, OverlayPanel_div_0_button_4_TimesIcon_1_Template, 1, 1, \"TimesIcon\", 6);\n    i0.ɵɵtemplate(2, OverlayPanel_div_0_button_4_span_2_Template, 2, 1, \"span\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"aria-label\", ctx_r2.ariaCloseLabel);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.closeIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.closeIconTemplate);\n  }\n}\nconst _c0 = function (a0, a1) {\n  return {\n    showTransitionParams: a0,\n    hideTransitionParams: a1\n  };\n};\nconst _c1 = function (a0, a1) {\n  return {\n    value: a0,\n    params: a1\n  };\n};\nfunction OverlayPanel_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵlistener(\"click\", function OverlayPanel_div_0_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onOverlayClick($event));\n    })(\"@animation.start\", function OverlayPanel_div_0_Template_div_animation_animation_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.onAnimationStart($event));\n    })(\"@animation.done\", function OverlayPanel_div_0_Template_div_animation_animation_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.onAnimationEnd($event));\n    });\n    i0.ɵɵelementStart(1, \"div\", 2);\n    i0.ɵɵlistener(\"click\", function OverlayPanel_div_0_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.onContentClick());\n    })(\"mousedown\", function OverlayPanel_div_0_Template_div_mousedown_1_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.onContentClick());\n    });\n    i0.ɵɵprojection(2);\n    i0.ɵɵtemplate(3, OverlayPanel_div_0_ng_container_3_Template, 1, 0, \"ng-container\", 3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, OverlayPanel_div_0_button_4_Template, 3, 3, \"button\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r0.styleClass);\n    i0.ɵɵproperty(\"ngClass\", \"p-overlaypanel p-component\")(\"ngStyle\", ctx_r0.style)(\"@animation\", i0.ɵɵpureFunction2(11, _c1, ctx_r0.overlayVisible ? \"open\" : \"close\", i0.ɵɵpureFunction2(8, _c0, ctx_r0.showTransitionOptions, ctx_r0.hideTransitionOptions)));\n    i0.ɵɵattribute(\"aria-modal\", ctx_r0.overlayVisible);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.contentTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showCloseIcon);\n  }\n}\nconst _c2 = [\"*\"];\nlet OverlayPanel = /*#__PURE__*/(() => {\n  class OverlayPanel {\n    document;\n    platformId;\n    el;\n    renderer;\n    cd;\n    zone;\n    config;\n    overlayService;\n    /**\n     * Enables to hide the overlay when outside is clicked.\n     * @group Props\n     */\n    dismissable = true;\n    /**\n     * When enabled, displays a close icon at top right corner.\n     * @group Props\n     */\n    showCloseIcon;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    style;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    styleClass;\n    /**\n     *  Target element to attach the panel, valid values are \"body\" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n     * @group Props\n     */\n    appendTo = 'body';\n    /**\n     * Whether to automatically manage layering.\n     * @group Props\n     */\n    autoZIndex = true;\n    /**\n     * Aria label of the close icon.\n     * @group Props\n     */\n    ariaCloseLabel;\n    /**\n     * Base zIndex value to use in layering.\n     * @group Props\n     */\n    baseZIndex = 0;\n    /**\n     * When enabled, first button receives focus on show.\n     * @group Props\n     */\n    focusOnShow = true;\n    /**\n     * Transition options of the show animation.\n     * @group Props\n     */\n    showTransitionOptions = '.12s cubic-bezier(0, 0, 0.2, 1)';\n    /**\n     * Transition options of the hide animation.\n     * @group Props\n     */\n    hideTransitionOptions = '.1s linear';\n    /**\n     * Callback to invoke when an overlay becomes visible.\n     * @group Emits\n     */\n    onShow = new EventEmitter();\n    /**\n     * Callback to invoke when an overlay gets hidden.\n     * @group Emits\n     */\n    onHide = new EventEmitter();\n    templates;\n    container;\n    overlayVisible = false;\n    render = false;\n    isOverlayAnimationInProgress = false;\n    selfClick = false;\n    documentClickListener;\n    target;\n    willHide;\n    scrollHandler;\n    documentResizeListener;\n    contentTemplate;\n    closeIconTemplate;\n    destroyCallback;\n    overlayEventListener;\n    overlaySubscription;\n    constructor(document, platformId, el, renderer, cd, zone, config, overlayService) {\n      this.document = document;\n      this.platformId = platformId;\n      this.el = el;\n      this.renderer = renderer;\n      this.cd = cd;\n      this.zone = zone;\n      this.config = config;\n      this.overlayService = overlayService;\n    }\n    ngAfterContentInit() {\n      this.templates?.forEach(item => {\n        switch (item.getType()) {\n          case 'content':\n            this.contentTemplate = item.template;\n            break;\n          case 'closeicon':\n            this.closeIconTemplate = item.template;\n            break;\n          default:\n            this.contentTemplate = item.template;\n            break;\n        }\n        this.cd.markForCheck();\n      });\n    }\n    bindDocumentClickListener() {\n      if (isPlatformBrowser(this.platformId)) {\n        if (!this.documentClickListener && this.dismissable) {\n          let documentEvent = DomHandler.isIOS() ? 'touchstart' : 'click';\n          const documentTarget = this.el ? this.el.nativeElement.ownerDocument : this.document;\n          this.documentClickListener = this.renderer.listen(documentTarget, documentEvent, event => {\n            if (!this.container?.contains(event.target) && this.target !== event.target && !this.target.contains(event.target)) {\n              this.hide();\n            }\n            this.cd.markForCheck();\n          });\n        }\n      }\n    }\n    unbindDocumentClickListener() {\n      if (this.documentClickListener) {\n        this.documentClickListener();\n        this.documentClickListener = null;\n        this.selfClick = false;\n      }\n    }\n    /**\n     * Toggles the visibility of the panel.\n     * @param {Event} event - Browser event\n     * @param {Target} target - Target element.\n     * @group Method\n     */\n    toggle(event, target) {\n      if (this.isOverlayAnimationInProgress) {\n        return;\n      }\n      if (this.overlayVisible) {\n        if (this.hasTargetChanged(event, target)) {\n          this.destroyCallback = () => {\n            this.show(null, target || event.currentTarget || event.target);\n          };\n        }\n        this.hide();\n      } else {\n        this.show(event, target);\n      }\n    }\n    /**\n     * Displays the panel.\n     * @param {Event} event - Browser event\n     * @param {Target} target - Target element.\n     * @group Method\n     */\n    show(event, target) {\n      target && event && event.stopPropagation();\n      if (this.isOverlayAnimationInProgress) {\n        return;\n      }\n      this.target = target || event.currentTarget || event.target;\n      this.overlayVisible = true;\n      this.render = true;\n      this.cd.markForCheck();\n    }\n    onOverlayClick(event) {\n      this.overlayService.add({\n        originalEvent: event,\n        target: this.el.nativeElement\n      });\n      this.selfClick = true;\n    }\n    onContentClick() {\n      this.selfClick = true;\n    }\n    hasTargetChanged(event, target) {\n      return this.target != null && this.target !== (target || event.currentTarget || event.target);\n    }\n    appendContainer() {\n      if (this.appendTo) {\n        if (this.appendTo === 'body') this.renderer.appendChild(this.document.body, this.container);else DomHandler.appendChild(this.container, this.appendTo);\n      }\n    }\n    restoreAppend() {\n      if (this.container && this.appendTo) {\n        this.renderer.appendChild(this.el.nativeElement, this.container);\n      }\n    }\n    align() {\n      if (this.autoZIndex) {\n        ZIndexUtils.set('overlay', this.container, this.baseZIndex + this.config.zIndex.overlay);\n      }\n      DomHandler.absolutePosition(this.container, this.target);\n      const containerOffset = DomHandler.getOffset(this.container);\n      const targetOffset = DomHandler.getOffset(this.target);\n      const borderRadius = this.document.defaultView?.getComputedStyle(this.container).getPropertyValue('border-radius');\n      let arrowLeft = 0;\n      if (containerOffset.left < targetOffset.left) {\n        arrowLeft = targetOffset.left - containerOffset.left - parseFloat(borderRadius) * 2;\n      }\n      this.container?.style.setProperty('--overlayArrowLeft', `${arrowLeft}px`);\n      if (containerOffset.top < targetOffset.top) {\n        DomHandler.addClass(this.container, 'p-overlaypanel-flipped');\n        if (this.showCloseIcon) {\n          this.renderer.setStyle(this.container, 'margin-top', '-30px');\n        }\n      }\n    }\n    onAnimationStart(event) {\n      if (event.toState === 'open') {\n        this.container = event.element;\n        this.appendContainer();\n        this.align();\n        this.bindDocumentClickListener();\n        this.bindDocumentResizeListener();\n        this.bindScrollListener();\n        if (this.focusOnShow) {\n          this.focus();\n        }\n        this.overlayEventListener = e => {\n          if (this.container && this.container.contains(e.target)) {\n            this.selfClick = true;\n          }\n        };\n        this.overlaySubscription = this.overlayService.clickObservable.subscribe(this.overlayEventListener);\n        this.onShow.emit(null);\n      }\n      this.isOverlayAnimationInProgress = true;\n    }\n    onAnimationEnd(event) {\n      switch (event.toState) {\n        case 'void':\n          if (this.destroyCallback) {\n            this.destroyCallback();\n            this.destroyCallback = null;\n          }\n          if (this.overlaySubscription) {\n            this.overlaySubscription.unsubscribe();\n          }\n          break;\n        case 'close':\n          if (this.autoZIndex) {\n            ZIndexUtils.clear(this.container);\n          }\n          if (this.overlaySubscription) {\n            this.overlaySubscription.unsubscribe();\n          }\n          this.onContainerDestroy();\n          this.onHide.emit({});\n          this.render = false;\n          break;\n      }\n      this.isOverlayAnimationInProgress = false;\n    }\n    focus() {\n      let focusable = DomHandler.findSingle(this.container, '[autofocus]');\n      if (focusable) {\n        this.zone.runOutsideAngular(() => {\n          setTimeout(() => focusable.focus(), 5);\n        });\n      }\n    }\n    /**\n     * Hides the panel.\n     * @group Method\n     */\n    hide() {\n      this.overlayVisible = false;\n      this.cd.markForCheck();\n    }\n    onCloseClick(event) {\n      this.hide();\n      event.preventDefault();\n    }\n    onEscapeKeydown(event) {\n      this.hide();\n    }\n    onWindowResize() {\n      if (this.overlayVisible && !DomHandler.isTouchDevice()) {\n        this.hide();\n      }\n    }\n    bindDocumentResizeListener() {\n      if (isPlatformBrowser(this.platformId)) {\n        if (!this.documentResizeListener) {\n          const window = this.document.defaultView;\n          this.documentResizeListener = this.renderer.listen(window, 'resize', this.onWindowResize.bind(this));\n        }\n      }\n    }\n    unbindDocumentResizeListener() {\n      if (this.documentResizeListener) {\n        this.documentResizeListener();\n        this.documentResizeListener = null;\n      }\n    }\n    bindScrollListener() {\n      if (isPlatformBrowser(this.platformId)) {\n        if (!this.scrollHandler) {\n          this.scrollHandler = new ConnectedOverlayScrollHandler(this.target, () => {\n            if (this.overlayVisible) {\n              this.hide();\n            }\n          });\n        }\n        this.scrollHandler.bindScrollListener();\n      }\n    }\n    unbindScrollListener() {\n      if (this.scrollHandler) {\n        this.scrollHandler.unbindScrollListener();\n      }\n    }\n    onContainerDestroy() {\n      if (!this.cd.destroyed) {\n        this.target = null;\n      }\n      this.unbindDocumentClickListener();\n      this.unbindDocumentResizeListener();\n      this.unbindScrollListener();\n    }\n    ngOnDestroy() {\n      if (this.scrollHandler) {\n        this.scrollHandler.destroy();\n        this.scrollHandler = null;\n      }\n      if (this.container && this.autoZIndex) {\n        ZIndexUtils.clear(this.container);\n      }\n      if (!this.cd.destroyed) {\n        this.target = null;\n      }\n      this.destroyCallback = null;\n      if (this.container) {\n        this.restoreAppend();\n        this.onContainerDestroy();\n      }\n      if (this.overlaySubscription) {\n        this.overlaySubscription.unsubscribe();\n      }\n    }\n    static ɵfac = function OverlayPanel_Factory(t) {\n      return new (t || OverlayPanel)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1.PrimeNGConfig), i0.ɵɵdirectiveInject(i1.OverlayService));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: OverlayPanel,\n      selectors: [[\"p-overlayPanel\"]],\n      contentQueries: function OverlayPanel_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      hostBindings: function OverlayPanel_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown.escape\", function OverlayPanel_keydown_escape_HostBindingHandler($event) {\n            return ctx.onEscapeKeydown($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      inputs: {\n        dismissable: \"dismissable\",\n        showCloseIcon: \"showCloseIcon\",\n        style: \"style\",\n        styleClass: \"styleClass\",\n        appendTo: \"appendTo\",\n        autoZIndex: \"autoZIndex\",\n        ariaCloseLabel: \"ariaCloseLabel\",\n        baseZIndex: \"baseZIndex\",\n        focusOnShow: \"focusOnShow\",\n        showTransitionOptions: \"showTransitionOptions\",\n        hideTransitionOptions: \"hideTransitionOptions\"\n      },\n      outputs: {\n        onShow: \"onShow\",\n        onHide: \"onHide\"\n      },\n      ngContentSelectors: _c2,\n      decls: 1,\n      vars: 1,\n      consts: [[\"role\", \"dialog\", 3, \"ngClass\", \"ngStyle\", \"class\", \"click\", 4, \"ngIf\"], [\"role\", \"dialog\", 3, \"ngClass\", \"ngStyle\", \"click\"], [1, \"p-overlaypanel-content\", 3, \"click\", \"mousedown\"], [4, \"ngTemplateOutlet\"], [\"type\", \"button\", \"class\", \"p-overlaypanel-close p-link\", \"pRipple\", \"\", 3, \"click\", \"keydown.enter\", 4, \"ngIf\"], [\"type\", \"button\", \"pRipple\", \"\", 1, \"p-overlaypanel-close\", \"p-link\", 3, \"click\", \"keydown.enter\"], [3, \"styleClass\", 4, \"ngIf\"], [\"class\", \"p-overlaypanel-close-icon\", 4, \"ngIf\"], [3, \"styleClass\"], [1, \"p-overlaypanel-close-icon\"]],\n      template: function OverlayPanel_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, OverlayPanel_div_0_Template, 5, 14, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.render);\n        }\n      },\n      dependencies: function () {\n        return [i2.NgClass, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i3.Ripple, TimesIcon];\n      },\n      styles: [\"@layer primeng{.p-overlaypanel{position:absolute;margin-top:10px;top:0;left:0}.p-overlaypanel-flipped{margin-top:0;margin-bottom:10px}.p-overlaypanel-close{display:flex;justify-content:center;align-items:center;overflow:hidden;position:relative}.p-overlaypanel:after,.p-overlaypanel:before{bottom:100%;left:calc(var(--overlayArrowLeft, 0) + 1.25rem);content:\\\" \\\";height:0;width:0;position:absolute;pointer-events:none}.p-overlaypanel:after{border-width:8px;margin-left:-8px}.p-overlaypanel:before{border-width:10px;margin-left:-10px}.p-overlaypanel-shifted:after,.p-overlaypanel-shifted:before{left:auto;right:1.25em;margin-left:auto}.p-overlaypanel-flipped:after,.p-overlaypanel-flipped:before{bottom:auto;top:100%}.p-overlaypanel.p-overlaypanel-flipped:after{border-bottom-color:transparent}.p-overlaypanel.p-overlaypanel-flipped:before{border-bottom-color:transparent}}\\n\"],\n      encapsulation: 2,\n      data: {\n        animation: [trigger('animation', [state('void', style({\n          transform: 'scaleY(0.8)',\n          opacity: 0\n        })), state('close', style({\n          opacity: 0\n        })), state('open', style({\n          transform: 'translateY(0)',\n          opacity: 1\n        })), transition('void => open', animate('{{showTransitionParams}}')), transition('open => close', animate('{{hideTransitionParams}}'))])]\n      },\n      changeDetection: 0\n    });\n  }\n  return OverlayPanel;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet OverlayPanelModule = /*#__PURE__*/(() => {\n  class OverlayPanelModule {\n    static ɵfac = function OverlayPanelModule_Factory(t) {\n      return new (t || OverlayPanelModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: OverlayPanelModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, RippleModule, SharedModule, TimesIcon, SharedModule]\n    });\n  }\n  return OverlayPanelModule;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { OverlayPanel, OverlayPanelModule };\n//# sourceMappingURL=primeng-overlaypanel.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}