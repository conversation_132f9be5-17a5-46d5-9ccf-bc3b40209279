<div class="shortener-overlay" *ngIf="showInterface">
  <div class="shortener-container">
    <div class="shortener-content">
      
      <!-- Header -->
      <div class="header-section">
        <div class="icon-container">
          <i class="pi pi-link"></i>
        </div>
        <h2 class="title">اختصار رابط للمتابعة</h2>
        <p class="subtitle">يرجى اختصار رابط واحد لاستخدام التطبيق لمدة 24 ساعة</p>
      </div>

      <!-- Status Information -->
      <div class="status-section" *ngIf="status">
        <div class="status-card">
          <div class="status-icon">
            <i class="pi pi-clock"></i>
          </div>
          <div class="status-text">
            <h3>حالة الوصول</h3>
            <p>{{ getTimeRemainingText() }}</p>
          </div>
        </div>
      </div>

      <!-- Current Link Section -->
      <div class="link-section" *ngIf="status?.currentLink">
        <h3>الرابط المطلوب اختصاره:</h3>
        <div class="link-container">
          <div class="link-display">
            <i class="pi pi-external-link"></i>
            <span class="link-text">{{ status?.currentLink || '' }}</span>
          </div>
          <p-button 
            label="فتح الرابط" 
            icon="pi pi-external-link" 
            severity="primary"
            [outlined]="true"
            (onClick)="openCurrentLink()">
          </p-button>
        </div>
      </div>

      <!-- Instructions -->
      <div class="instructions-section">
        <h3>خطوات الاختصار:</h3>
        <ol class="instructions-list">
          <li>انقر على "فتح الرابط" أعلاه</li>
          <li>انتظر حتى يتم تحميل الصفحة (5 ثوانٍ)</li>
          <li>انقر على "متابعة" أو "Skip Ad" في الصفحة</li>
          <li>ارجع هنا وانقر على "متابعة" أدناه</li>
        </ol>
      </div>

      <!-- Action Buttons -->
      <div class="action-buttons">
        <p-button 
          label="متابعة" 
          icon="pi pi-check" 
          severity="success"
          size="large"
          [loading]="isProcessing"
          loadingIcon="pi pi-spin pi-spinner"
          (onClick)="onContinueClicked()"
          styleClass="continue-btn">
        </p-button>
        
        <div class="secondary-buttons">
          <p-button 
            label="الرابط لا يعمل" 
            icon="pi pi-refresh" 
            severity="warning"
            [outlined]="true"
            (onClick)="onLinkNotWorking()">
          </p-button>
          
          <p-button 
            label="حدثت مشكلة" 
            icon="pi pi-exclamation-triangle" 
            severity="danger"
            [outlined]="true"
            (onClick)="onProblemOccurred()">
          </p-button>
        </div>
      </div>

      <!-- Alternative Option -->
      <div class="alternative-section">
        <div class="divider-container">
          <div class="divider-line"></div>
          <span class="divider-text">أو</span>
          <div class="divider-line"></div>
        </div>
        
        <div class="ad-option">
          <div class="ad-info">
            <i class="pi pi-video"></i>
            <div>
              <h4>مشاهدة إعلان قصير</h4>
              <p>احصل على 3 ساعات من الاستخدام بدلاً من 24 ساعة</p>
            </div>
          </div>
          
          <p-button
            label="مشاهدة إعلان"
            icon="pi pi-play"
            severity="info"
            [loading]="isLoadingAd"
            loadingIcon="pi pi-spin pi-spinner"
            (onClick)="onWatchAd()"
            styleClass="ad-btn">
          </p-button>

          <!-- Ad Error Message -->
          <div class="ad-error" *ngIf="adError">
            <i class="pi pi-exclamation-triangle"></i>
            <span>{{ adError }}</span>
          </div>
        </div>
      </div>

      <!-- Help Section -->
      <div class="help-section">
        <div class="help-content">
          <i class="pi pi-question-circle"></i>
          <div>
            <h4>تحتاج مساعدة؟</h4>
            <p>إذا واجهت مشكلة، يُرجى المحاولة برابط مختلف أو التواصل مع المطور</p>
          </div>
        </div>
        
        <div class="help-buttons">
          <p-button 
            label="تحديث الحالة" 
            icon="pi pi-refresh" 
            severity="secondary"
            [text]="true"
            size="small"
            (onClick)="refreshStatus()">
          </p-button>
          
          <p-button 
            label="تواصل مع المطور" 
            icon="pi pi-envelope" 
            severity="secondary"
            [text]="true"
            size="small"
            (onClick)="openSupportEmail()">
          </p-button>
        </div>
      </div>

      <!-- Footer Info -->
      <div class="footer-info">
        <div class="info-item">
          <i class="pi pi-shield"></i>
          <span>آمن ومضمون</span>
        </div>
        <div class="info-item">
          <i class="pi pi-clock"></i>
          <span>صالح لمدة 24 ساعة</span>
        </div>
        <div class="info-item">
          <i class="pi pi-check"></i>
          <span>مجاني تماماً</span>
        </div>
      </div>

    </div>
  </div>
</div>
