{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/\\u062A\\u0637\\u0628\\u064A\\u0642 \\u0646\\u0627\\u0634\\u0631 \\u0627\\u064A\\u0627\\u062A \\u0642\\u0626\\u0627\\u0646\\u064A\\u0629/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { supportsLocalStorage } from './helpers';\n/**\n * @experimental\n */\nexport const internals = {\n  /**\n   * @experimental\n   */\n  debug: !!(globalThis && supportsLocalStorage() && globalThis.localStorage && globalThis.localStorage.getItem('supabase.gotrue-js.locks.debug') === 'true')\n};\n/**\n * An error thrown when a lock cannot be acquired after some amount of time.\n *\n * Use the {@link #isAcquireTimeout} property instead of checking with `instanceof`.\n */\nexport class LockAcquireTimeoutError extends Error {\n  constructor(message) {\n    super(message);\n    this.isAcquireTimeout = true;\n  }\n}\nexport class NavigatorLockAcquireTimeoutError extends LockAcquireTimeoutError {}\nexport class ProcessLockAcquireTimeoutError extends LockAcquireTimeoutError {}\n/**\n * Implements a global exclusive lock using the Navigator LockManager API. It\n * is available on all browsers released after 2022-03-15 with Safari being the\n * last one to release support. If the API is not available, this function will\n * throw. Make sure you check availablility before configuring {@link\n * GoTrueClient}.\n *\n * You can turn on debugging by setting the `supabase.gotrue-js.locks.debug`\n * local storage item to `true`.\n *\n * Internals:\n *\n * Since the LockManager API does not preserve stack traces for the async\n * function passed in the `request` method, a trick is used where acquiring the\n * lock releases a previously started promise to run the operation in the `fn`\n * function. The lock waits for that promise to finish (with or without error),\n * while the function will finally wait for the result anyway.\n *\n * @param name Name of the lock to be acquired.\n * @param acquireTimeout If negative, no timeout. If 0 an error is thrown if\n *                       the lock can't be acquired without waiting. If positive, the lock acquire\n *                       will time out after so many milliseconds. An error is\n *                       a timeout if it has `isAcquireTimeout` set to true.\n * @param fn The operation to run once the lock is acquired.\n */\nexport function navigatorLock(_x, _x2, _x3) {\n  return _navigatorLock.apply(this, arguments);\n}\nfunction _navigatorLock() {\n  _navigatorLock = _asyncToGenerator(function* (name, acquireTimeout, fn) {\n    if (internals.debug) {\n      console.log('@supabase/gotrue-js: navigatorLock: acquire lock', name, acquireTimeout);\n    }\n    const abortController = new globalThis.AbortController();\n    if (acquireTimeout > 0) {\n      setTimeout(() => {\n        abortController.abort();\n        if (internals.debug) {\n          console.log('@supabase/gotrue-js: navigatorLock acquire timed out', name);\n        }\n      }, acquireTimeout);\n    }\n    // MDN article: https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request\n    // Wrapping navigator.locks.request() with a plain Promise is done as some\n    // libraries like zone.js patch the Promise object to track the execution\n    // context. However, it appears that most browsers use an internal promise\n    // implementation when using the navigator.locks.request() API causing them\n    // to lose context and emit confusing log messages or break certain features.\n    // This wrapping is believed to help zone.js track the execution context\n    // better.\n    return yield Promise.resolve().then(() => globalThis.navigator.locks.request(name, acquireTimeout === 0 ? {\n      mode: 'exclusive',\n      ifAvailable: true\n    } : {\n      mode: 'exclusive',\n      signal: abortController.signal\n    }, /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (lock) {\n        if (lock) {\n          if (internals.debug) {\n            console.log('@supabase/gotrue-js: navigatorLock: acquired', name, lock.name);\n          }\n          try {\n            return yield fn();\n          } finally {\n            if (internals.debug) {\n              console.log('@supabase/gotrue-js: navigatorLock: released', name, lock.name);\n            }\n          }\n        } else {\n          if (acquireTimeout === 0) {\n            if (internals.debug) {\n              console.log('@supabase/gotrue-js: navigatorLock: not immediately available', name);\n            }\n            throw new NavigatorLockAcquireTimeoutError(`Acquiring an exclusive Navigator LockManager lock \"${name}\" immediately failed`);\n          } else {\n            if (internals.debug) {\n              try {\n                const result = yield globalThis.navigator.locks.query();\n                console.log('@supabase/gotrue-js: Navigator LockManager state', JSON.stringify(result, null, '  '));\n              } catch (e) {\n                console.warn('@supabase/gotrue-js: Error when querying Navigator LockManager state', e);\n              }\n            }\n            // Browser is not following the Navigator LockManager spec, it\n            // returned a null lock when we didn't use ifAvailable. So we can\n            // pretend the lock is acquired in the name of backward compatibility\n            // and user experience and just run the function.\n            console.warn('@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request');\n            return yield fn();\n          }\n        }\n      });\n      return function (_x7) {\n        return _ref.apply(this, arguments);\n      };\n    }()));\n  });\n  return _navigatorLock.apply(this, arguments);\n}\nconst PROCESS_LOCKS = {};\n/**\n * Implements a global exclusive lock that works only in the current process.\n * Useful for environments like React Native or other non-browser\n * single-process (i.e. no concept of \"tabs\") environments.\n *\n * Use {@link #navigatorLock} in browser environments.\n *\n * @param name Name of the lock to be acquired.\n * @param acquireTimeout If negative, no timeout. If 0 an error is thrown if\n *                       the lock can't be acquired without waiting. If positive, the lock acquire\n *                       will time out after so many milliseconds. An error is\n *                       a timeout if it has `isAcquireTimeout` set to true.\n * @param fn The operation to run once the lock is acquired.\n */\nexport function processLock(_x4, _x5, _x6) {\n  return _processLock.apply(this, arguments);\n}\n//# sourceMappingURL=locks.js.map\nfunction _processLock() {\n  _processLock = _asyncToGenerator(function* (name, acquireTimeout, fn) {\n    var _a;\n    const previousOperation = (_a = PROCESS_LOCKS[name]) !== null && _a !== void 0 ? _a : Promise.resolve();\n    const currentOperation = Promise.race([previousOperation.catch(() => {\n      // ignore error of previous operation that we're waiting to finish\n      return null;\n    }), acquireTimeout >= 0 ? new Promise((_, reject) => {\n      setTimeout(() => {\n        reject(new ProcessLockAcquireTimeoutError(`Acquring process lock with name \"${name}\" timed out`));\n      }, acquireTimeout);\n    }) : null].filter(x => x)).catch(e => {\n      if (e && e.isAcquireTimeout) {\n        throw e;\n      }\n      return null;\n    }).then( /*#__PURE__*/_asyncToGenerator(function* () {\n      // previous operations finished and we didn't get a race on the acquire\n      // timeout, so the current operation can finally start\n      return yield fn();\n    }));\n    PROCESS_LOCKS[name] = currentOperation.catch( /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(function* (e) {\n        if (e && e.isAcquireTimeout) {\n          // if the current operation timed out, it doesn't mean that the previous\n          // operation finished, so we need contnue waiting for it to finish\n          yield previousOperation;\n          return null;\n        }\n        throw e;\n      });\n      return function (_x8) {\n        return _ref3.apply(this, arguments);\n      };\n    }());\n    // finally wait for the current operation to finish successfully, with an\n    // error or with an acquire timeout error\n    return yield currentOperation;\n  });\n  return _processLock.apply(this, arguments);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}