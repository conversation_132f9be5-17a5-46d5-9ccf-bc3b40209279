<p-dialog 
  header="إعدادات الفيديو" 
  [(visible)]="visible" 
  [modal]="true"
  [closable]="true"
  [resizable]="false"
  [maximizable]="true"
  styleClass="video-settings-dialog"
  [style]="{width: '90vw', height: '90vh'}"
  (onHide)="onHide()">

  <div class="settings-container">
    <form [formGroup]="settingsForm" class="settings-form">
      
      <!-- Gemini AI Settings -->
      <p-panel header="إعدادات الذكاء الاصطناعي" [toggleable]="true">
        <div class="form-section">
          <div class="form-row">
            <label for="geminiApiKey">مفتاح Gemini API *</label>
            <div class="api-key-container">
              <input 
                id="geminiApiKey"
                type="password" 
                pInputText 
                formControlName="geminiApiKey"
                placeholder="أدخل مفتاح API الخاص بك"
                class="w-100">
              <p-button
                label="اختبار الاتصال"
                icon="pi pi-check"
                severity="secondary"
                size="small"
                [loading]="isTestingConnection"
                loadingIcon="pi pi-spin pi-spinner"
                (onClick)="testGeminiConnection()">
              </p-button>
            </div>

            <!-- Connection test result -->
            <div class="connection-result" *ngIf="connectionTestResult">
              <p [ngClass]="{
                'success-message': connectionTestResult.includes('✅'),
                'warning-message': connectionTestResult.includes('⚠️') || connectionTestResult.includes('💰'),
                'error-message': connectionTestResult.includes('❌')
              }">
                {{ connectionTestResult }}
              </p>
            </div>
          </div>

          <div class="form-row">
            <label for="geminiModel">نموذج Gemini *</label>
            <p-dropdown
              id="geminiModel"
              [options]="geminiModels"
              formControlName="geminiModel"
              optionLabel="label"
              optionValue="value"
              placeholder="اختر النموذج"
              (onChange)="onModelChange()"
              class="w-100">
              <ng-template let-option pTemplate="item">
                <div class="model-option">
                  <div class="model-name">{{ option.label }}</div>
                  <div class="model-description">{{ option.description }}</div>
                  <span class="model-badge" *ngIf="option.isPaid">مدفوع</span>
                </div>
              </ng-template>
            </p-dropdown>
          </div>
        </div>
      </p-panel>

      <!-- Font Settings -->
      <p-panel header="إعدادات الخط" [toggleable]="true">
        <div class="form-section">
          <div class="form-row">
            <label for="fontFamily">نوع الخط *</label>
            <p-dropdown 
              id="fontFamily"
              [options]="fontFamilies" 
              formControlName="fontFamily"
              optionLabel="label" 
              optionValue="value"
              placeholder="اختر نوع الخط"
              class="w-100">
            </p-dropdown>
          </div>
          
          <div class="form-row" *ngIf="settingsForm.get('fontFamily')?.value === 'custom'">
            <label>رفع خط مخصص</label>
            <p-fileUpload 
              mode="basic" 
              accept=".ttf,.otf,.woff,.woff2"
              [maxFileSize]="5000000"
              chooseLabel="اختر ملف الخط"
              (onSelect)="onFontUpload($event)">
            </p-fileUpload>
          </div>
          
          <div class="form-row">
            <label for="fontSize">حجم الخط: {{settingsForm.get('fontSize')?.value}}</label>
            <p-slider 
              id="fontSize"
              formControlName="fontSize"
              [min]="12" 
              [max]="48" 
              [step]="1"
              class="w-100">
            </p-slider>
          </div>
        </div>
      </p-panel>

      <!-- Video Quality Settings -->
      <p-panel header="إعدادات جودة الفيديو" [toggleable]="true">
        <div class="form-section">
          <div class="form-row">
            <label for="videoQuality">جودة الفيديو *</label>
            <p-dropdown 
              id="videoQuality"
              [options]="videoQualities" 
              formControlName="videoQuality"
              optionLabel="label" 
              optionValue="value"
              placeholder="اختر جودة الفيديو"
              class="w-100">
            </p-dropdown>
          </div>
          
          <div class="form-row">
            <label for="audioQuality">جودة الصوت *</label>
            <p-dropdown 
              id="audioQuality"
              [options]="audioQualities" 
              formControlName="audioQuality"
              optionLabel="label" 
              optionValue="value"
              placeholder="اختر جودة الصوت"
              class="w-100">
            </p-dropdown>
          </div>
        </div>
      </p-panel>

      <!-- Subtitle Settings -->
      <p-panel header="إعدادات الترجمة" [toggleable]="true">
        <div class="form-section">
          <div class="form-row">
            <label for="subtitlePosition">موضع الترجمة *</label>
            <p-dropdown 
              id="subtitlePosition"
              [options]="subtitlePositions" 
              formControlName="subtitlePosition"
              optionLabel="label" 
              optionValue="value"
              placeholder="اختر موضع الترجمة"
              class="w-100">
            </p-dropdown>
          </div>
          
          <div class="color-settings">
            <div class="color-row">
              <label for="textColor">لون النص</label>
              <input 
                id="textColor"
                type="color" 
                formControlName="textColor"
                class="color-picker">
            </div>
            
            <div class="color-row">
              <label for="outlineColor">لون الحدود</label>
              <input 
                id="outlineColor"
                type="color" 
                formControlName="outlineColor"
                class="color-picker">
            </div>
            
            <div class="color-row">
              <label for="backgroundColor">لون الخلفية</label>
              <input 
                id="backgroundColor"
                type="color" 
                formControlName="backgroundColor"
                class="color-picker">
            </div>
          </div>
        </div>
      </p-panel>

    </form>
  </div>

  <ng-template pTemplate="footer">
    <div class="dialog-footer">
      <p-button 
        label="إعادة تعيين" 
        icon="pi pi-refresh" 
        severity="secondary"
        (onClick)="resetToDefaults()">
      </p-button>
      <p-button 
        label="إلغاء" 
        icon="pi pi-times" 
        severity="secondary"
        (onClick)="onHide()">
      </p-button>
      <p-button 
        label="حفظ الإعدادات" 
        icon="pi pi-check" 
        severity="primary"
        [disabled]="!settingsForm.valid"
        (onClick)="onSave()">
      </p-button>
    </div>
  </ng-template>
</p-dialog>
