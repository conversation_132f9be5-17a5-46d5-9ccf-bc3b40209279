{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/\\u062A\\u0637\\u0628\\u064A\\u0642 \\u0646\\u0627\\u0634\\u0631 \\u0627\\u064A\\u0627\\u062A \\u0642\\u0626\\u0627\\u0646\\u064A\\u0629/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { WebPlugin, buildRequestInit } from '@capacitor/core';\nimport { Encoding } from './definitions';\nfunction resolve(path) {\n  const posix = path.split('/').filter(item => item !== '.');\n  const newPosix = [];\n  posix.forEach(item => {\n    if (item === '..' && newPosix.length > 0 && newPosix[newPosix.length - 1] !== '..') {\n      newPosix.pop();\n    } else {\n      newPosix.push(item);\n    }\n  });\n  return newPosix.join('/');\n}\nfunction isPathParent(parent, children) {\n  parent = resolve(parent);\n  children = resolve(children);\n  const pathsA = parent.split('/');\n  const pathsB = children.split('/');\n  return parent !== children && pathsA.every((value, index) => value === pathsB[index]);\n}\nexport let FilesystemWeb = /*#__PURE__*/(() => {\n  class FilesystemWeb extends WebPlugin {\n    constructor() {\n      var _this;\n      super(...arguments);\n      _this = this;\n      this.DB_VERSION = 1;\n      this.DB_NAME = 'Disc';\n      this._writeCmds = ['add', 'put', 'delete'];\n      /**\n       * Function that performs a http request to a server and downloads the file to the specified destination\n       *\n       * @param options the options for the download operation\n       * @returns a promise that resolves with the download file result\n       */\n      this.downloadFile = /*#__PURE__*/function () {\n        var _ref = _asyncToGenerator(function* (options) {\n          var _a, _b;\n          const requestInit = buildRequestInit(options, options.webFetchExtra);\n          const response = yield fetch(options.url, requestInit);\n          let blob;\n          if (!options.progress) blob = yield response.blob();else if (!(response === null || response === void 0 ? void 0 : response.body)) blob = new Blob();else {\n            const reader = response.body.getReader();\n            let bytes = 0;\n            const chunks = [];\n            const contentType = response.headers.get('content-type');\n            const contentLength = parseInt(response.headers.get('content-length') || '0', 10);\n            while (true) {\n              const {\n                done,\n                value\n              } = yield reader.read();\n              if (done) break;\n              chunks.push(value);\n              bytes += (value === null || value === void 0 ? void 0 : value.length) || 0;\n              const status = {\n                url: options.url,\n                bytes,\n                contentLength\n              };\n              _this.notifyListeners('progress', status);\n            }\n            const allChunks = new Uint8Array(bytes);\n            let position = 0;\n            for (const chunk of chunks) {\n              if (typeof chunk === 'undefined') continue;\n              allChunks.set(chunk, position);\n              position += chunk.length;\n            }\n            blob = new Blob([allChunks.buffer], {\n              type: contentType || undefined\n            });\n          }\n          const result = yield _this.writeFile({\n            path: options.path,\n            directory: (_a = options.directory) !== null && _a !== void 0 ? _a : undefined,\n            recursive: (_b = options.recursive) !== null && _b !== void 0 ? _b : false,\n            data: blob\n          });\n          return {\n            path: result.uri,\n            blob\n          };\n        });\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }();\n    }\n    initDb() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        if (_this2._db !== undefined) {\n          return _this2._db;\n        }\n        if (!('indexedDB' in window)) {\n          throw _this2.unavailable(\"This browser doesn't support IndexedDB\");\n        }\n        return new Promise((resolve, reject) => {\n          const request = indexedDB.open(_this2.DB_NAME, _this2.DB_VERSION);\n          request.onupgradeneeded = FilesystemWeb.doUpgrade;\n          request.onsuccess = () => {\n            _this2._db = request.result;\n            resolve(request.result);\n          };\n          request.onerror = () => reject(request.error);\n          request.onblocked = () => {\n            console.warn('db blocked');\n          };\n        });\n      })();\n    }\n    static doUpgrade(event) {\n      const eventTarget = event.target;\n      const db = eventTarget.result;\n      switch (event.oldVersion) {\n        case 0:\n        case 1:\n        default:\n          {\n            if (db.objectStoreNames.contains('FileStorage')) {\n              db.deleteObjectStore('FileStorage');\n            }\n            const store = db.createObjectStore('FileStorage', {\n              keyPath: 'path'\n            });\n            store.createIndex('by_folder', 'folder');\n          }\n      }\n    }\n    dbRequest(cmd, args) {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        const readFlag = _this3._writeCmds.indexOf(cmd) !== -1 ? 'readwrite' : 'readonly';\n        return _this3.initDb().then(conn => {\n          return new Promise((resolve, reject) => {\n            const tx = conn.transaction(['FileStorage'], readFlag);\n            const store = tx.objectStore('FileStorage');\n            const req = store[cmd](...args);\n            req.onsuccess = () => resolve(req.result);\n            req.onerror = () => reject(req.error);\n          });\n        });\n      })();\n    }\n    dbIndexRequest(indexName, cmd, args) {\n      var _this4 = this;\n      return _asyncToGenerator(function* () {\n        const readFlag = _this4._writeCmds.indexOf(cmd) !== -1 ? 'readwrite' : 'readonly';\n        return _this4.initDb().then(conn => {\n          return new Promise((resolve, reject) => {\n            const tx = conn.transaction(['FileStorage'], readFlag);\n            const store = tx.objectStore('FileStorage');\n            const index = store.index(indexName);\n            const req = index[cmd](...args);\n            req.onsuccess = () => resolve(req.result);\n            req.onerror = () => reject(req.error);\n          });\n        });\n      })();\n    }\n    getPath(directory, uriPath) {\n      const cleanedUriPath = uriPath !== undefined ? uriPath.replace(/^[/]+|[/]+$/g, '') : '';\n      let fsPath = '';\n      if (directory !== undefined) fsPath += '/' + directory;\n      if (uriPath !== '') fsPath += '/' + cleanedUriPath;\n      return fsPath;\n    }\n    clear() {\n      var _this5 = this;\n      return _asyncToGenerator(function* () {\n        const conn = yield _this5.initDb();\n        const tx = conn.transaction(['FileStorage'], 'readwrite');\n        const store = tx.objectStore('FileStorage');\n        store.clear();\n      })();\n    }\n    /**\n     * Read a file from disk\n     * @param options options for the file read\n     * @return a promise that resolves with the read file data result\n     */\n    readFile(options) {\n      var _this6 = this;\n      return _asyncToGenerator(function* () {\n        const path = _this6.getPath(options.directory, options.path);\n        // const encoding = options.encoding;\n        const entry = yield _this6.dbRequest('get', [path]);\n        if (entry === undefined) throw Error('File does not exist.');\n        return {\n          data: entry.content ? entry.content : ''\n        };\n      })();\n    }\n    /**\n     * Write a file to disk in the specified location on device\n     * @param options options for the file write\n     * @return a promise that resolves with the file write result\n     */\n    writeFile(options) {\n      var _this7 = this;\n      return _asyncToGenerator(function* () {\n        const path = _this7.getPath(options.directory, options.path);\n        let data = options.data;\n        const encoding = options.encoding;\n        const doRecursive = options.recursive;\n        const occupiedEntry = yield _this7.dbRequest('get', [path]);\n        if (occupiedEntry && occupiedEntry.type === 'directory') throw Error('The supplied path is a directory.');\n        const parentPath = path.substr(0, path.lastIndexOf('/'));\n        const parentEntry = yield _this7.dbRequest('get', [parentPath]);\n        if (parentEntry === undefined) {\n          const subDirIndex = parentPath.indexOf('/', 1);\n          if (subDirIndex !== -1) {\n            const parentArgPath = parentPath.substr(subDirIndex);\n            yield _this7.mkdir({\n              path: parentArgPath,\n              directory: options.directory,\n              recursive: doRecursive\n            });\n          }\n        }\n        if (!encoding && !(data instanceof Blob)) {\n          data = data.indexOf(',') >= 0 ? data.split(',')[1] : data;\n          if (!_this7.isBase64String(data)) throw Error('The supplied data is not valid base64 content.');\n        }\n        const now = Date.now();\n        const pathObj = {\n          path: path,\n          folder: parentPath,\n          type: 'file',\n          size: data instanceof Blob ? data.size : data.length,\n          ctime: now,\n          mtime: now,\n          content: data\n        };\n        yield _this7.dbRequest('put', [pathObj]);\n        return {\n          uri: pathObj.path\n        };\n      })();\n    }\n    /**\n     * Append to a file on disk in the specified location on device\n     * @param options options for the file append\n     * @return a promise that resolves with the file write result\n     */\n    appendFile(options) {\n      var _this8 = this;\n      return _asyncToGenerator(function* () {\n        const path = _this8.getPath(options.directory, options.path);\n        let data = options.data;\n        const encoding = options.encoding;\n        const parentPath = path.substr(0, path.lastIndexOf('/'));\n        const now = Date.now();\n        let ctime = now;\n        const occupiedEntry = yield _this8.dbRequest('get', [path]);\n        if (occupiedEntry && occupiedEntry.type === 'directory') throw Error('The supplied path is a directory.');\n        const parentEntry = yield _this8.dbRequest('get', [parentPath]);\n        if (parentEntry === undefined) {\n          const subDirIndex = parentPath.indexOf('/', 1);\n          if (subDirIndex !== -1) {\n            const parentArgPath = parentPath.substr(subDirIndex);\n            yield _this8.mkdir({\n              path: parentArgPath,\n              directory: options.directory,\n              recursive: true\n            });\n          }\n        }\n        if (!encoding && !_this8.isBase64String(data)) throw Error('The supplied data is not valid base64 content.');\n        if (occupiedEntry !== undefined) {\n          if (occupiedEntry.content instanceof Blob) {\n            throw Error('The occupied entry contains a Blob object which cannot be appended to.');\n          }\n          if (occupiedEntry.content !== undefined && !encoding) {\n            data = btoa(atob(occupiedEntry.content) + atob(data));\n          } else {\n            data = occupiedEntry.content + data;\n          }\n          ctime = occupiedEntry.ctime;\n        }\n        const pathObj = {\n          path: path,\n          folder: parentPath,\n          type: 'file',\n          size: data.length,\n          ctime: ctime,\n          mtime: now,\n          content: data\n        };\n        yield _this8.dbRequest('put', [pathObj]);\n      })();\n    }\n    /**\n     * Delete a file from disk\n     * @param options options for the file delete\n     * @return a promise that resolves with the deleted file data result\n     */\n    deleteFile(options) {\n      var _this9 = this;\n      return _asyncToGenerator(function* () {\n        const path = _this9.getPath(options.directory, options.path);\n        const entry = yield _this9.dbRequest('get', [path]);\n        if (entry === undefined) throw Error('File does not exist.');\n        const entries = yield _this9.dbIndexRequest('by_folder', 'getAllKeys', [IDBKeyRange.only(path)]);\n        if (entries.length !== 0) throw Error('Folder is not empty.');\n        yield _this9.dbRequest('delete', [path]);\n      })();\n    }\n    /**\n     * Create a directory.\n     * @param options options for the mkdir\n     * @return a promise that resolves with the mkdir result\n     */\n    mkdir(options) {\n      var _this0 = this;\n      return _asyncToGenerator(function* () {\n        const path = _this0.getPath(options.directory, options.path);\n        const doRecursive = options.recursive;\n        const parentPath = path.substr(0, path.lastIndexOf('/'));\n        const depth = (path.match(/\\//g) || []).length;\n        const parentEntry = yield _this0.dbRequest('get', [parentPath]);\n        const occupiedEntry = yield _this0.dbRequest('get', [path]);\n        if (depth === 1) throw Error('Cannot create Root directory');\n        if (occupiedEntry !== undefined) throw Error('Current directory does already exist.');\n        if (!doRecursive && depth !== 2 && parentEntry === undefined) throw Error('Parent directory must exist');\n        if (doRecursive && depth !== 2 && parentEntry === undefined) {\n          const parentArgPath = parentPath.substr(parentPath.indexOf('/', 1));\n          yield _this0.mkdir({\n            path: parentArgPath,\n            directory: options.directory,\n            recursive: doRecursive\n          });\n        }\n        const now = Date.now();\n        const pathObj = {\n          path: path,\n          folder: parentPath,\n          type: 'directory',\n          size: 0,\n          ctime: now,\n          mtime: now\n        };\n        yield _this0.dbRequest('put', [pathObj]);\n      })();\n    }\n    /**\n     * Remove a directory\n     * @param options the options for the directory remove\n     */\n    rmdir(options) {\n      var _this1 = this;\n      return _asyncToGenerator(function* () {\n        const {\n          path,\n          directory,\n          recursive\n        } = options;\n        const fullPath = _this1.getPath(directory, path);\n        const entry = yield _this1.dbRequest('get', [fullPath]);\n        if (entry === undefined) throw Error('Folder does not exist.');\n        if (entry.type !== 'directory') throw Error('Requested path is not a directory');\n        const readDirResult = yield _this1.readdir({\n          path,\n          directory\n        });\n        if (readDirResult.files.length !== 0 && !recursive) throw Error('Folder is not empty');\n        for (const entry of readDirResult.files) {\n          const entryPath = `${path}/${entry.name}`;\n          const entryObj = yield _this1.stat({\n            path: entryPath,\n            directory\n          });\n          if (entryObj.type === 'file') {\n            yield _this1.deleteFile({\n              path: entryPath,\n              directory\n            });\n          } else {\n            yield _this1.rmdir({\n              path: entryPath,\n              directory,\n              recursive\n            });\n          }\n        }\n        yield _this1.dbRequest('delete', [fullPath]);\n      })();\n    }\n    /**\n     * Return a list of files from the directory (not recursive)\n     * @param options the options for the readdir operation\n     * @return a promise that resolves with the readdir directory listing result\n     */\n    readdir(options) {\n      var _this10 = this;\n      return _asyncToGenerator(function* () {\n        const path = _this10.getPath(options.directory, options.path);\n        const entry = yield _this10.dbRequest('get', [path]);\n        if (options.path !== '' && entry === undefined) throw Error('Folder does not exist.');\n        const entries = yield _this10.dbIndexRequest('by_folder', 'getAllKeys', [IDBKeyRange.only(path)]);\n        const files = yield Promise.all(entries.map( /*#__PURE__*/function () {\n          var _ref2 = _asyncToGenerator(function* (e) {\n            let subEntry = yield _this10.dbRequest('get', [e]);\n            if (subEntry === undefined) {\n              subEntry = yield _this10.dbRequest('get', [e + '/']);\n            }\n            return {\n              name: e.substring(path.length + 1),\n              type: subEntry.type,\n              size: subEntry.size,\n              ctime: subEntry.ctime,\n              mtime: subEntry.mtime,\n              uri: subEntry.path\n            };\n          });\n          return function (_x2) {\n            return _ref2.apply(this, arguments);\n          };\n        }()));\n        return {\n          files: files\n        };\n      })();\n    }\n    /**\n     * Return full File URI for a path and directory\n     * @param options the options for the stat operation\n     * @return a promise that resolves with the file stat result\n     */\n    getUri(options) {\n      var _this11 = this;\n      return _asyncToGenerator(function* () {\n        const path = _this11.getPath(options.directory, options.path);\n        let entry = yield _this11.dbRequest('get', [path]);\n        if (entry === undefined) {\n          entry = yield _this11.dbRequest('get', [path + '/']);\n        }\n        return {\n          uri: (entry === null || entry === void 0 ? void 0 : entry.path) || path\n        };\n      })();\n    }\n    /**\n     * Return data about a file\n     * @param options the options for the stat operation\n     * @return a promise that resolves with the file stat result\n     */\n    stat(options) {\n      var _this12 = this;\n      return _asyncToGenerator(function* () {\n        const path = _this12.getPath(options.directory, options.path);\n        let entry = yield _this12.dbRequest('get', [path]);\n        if (entry === undefined) {\n          entry = yield _this12.dbRequest('get', [path + '/']);\n        }\n        if (entry === undefined) throw Error('Entry does not exist.');\n        return {\n          type: entry.type,\n          size: entry.size,\n          ctime: entry.ctime,\n          mtime: entry.mtime,\n          uri: entry.path\n        };\n      })();\n    }\n    /**\n     * Rename a file or directory\n     * @param options the options for the rename operation\n     * @return a promise that resolves with the rename result\n     */\n    rename(options) {\n      var _this13 = this;\n      return _asyncToGenerator(function* () {\n        yield _this13._copy(options, true);\n        return;\n      })();\n    }\n    /**\n     * Copy a file or directory\n     * @param options the options for the copy operation\n     * @return a promise that resolves with the copy result\n     */\n    copy(options) {\n      var _this14 = this;\n      return _asyncToGenerator(function* () {\n        return _this14._copy(options, false);\n      })();\n    }\n    requestPermissions() {\n      return _asyncToGenerator(function* () {\n        return {\n          publicStorage: 'granted'\n        };\n      })();\n    }\n    checkPermissions() {\n      return _asyncToGenerator(function* () {\n        return {\n          publicStorage: 'granted'\n        };\n      })();\n    }\n    /**\n     * Function that can perform a copy or a rename\n     * @param options the options for the rename operation\n     * @param doRename whether to perform a rename or copy operation\n     * @return a promise that resolves with the result\n     */\n    _copy(_x3) {\n      var _this15 = this;\n      return _asyncToGenerator(function* (options, doRename = false) {\n        let {\n          toDirectory\n        } = options;\n        const {\n          to,\n          from,\n          directory: fromDirectory\n        } = options;\n        if (!to || !from) {\n          throw Error('Both to and from must be provided');\n        }\n        // If no \"to\" directory is provided, use the \"from\" directory\n        if (!toDirectory) {\n          toDirectory = fromDirectory;\n        }\n        const fromPath = _this15.getPath(fromDirectory, from);\n        const toPath = _this15.getPath(toDirectory, to);\n        // Test that the \"to\" and \"from\" locations are different\n        if (fromPath === toPath) {\n          return {\n            uri: toPath\n          };\n        }\n        if (isPathParent(fromPath, toPath)) {\n          throw Error('To path cannot contain the from path');\n        }\n        // Check the state of the \"to\" location\n        let toObj;\n        try {\n          toObj = yield _this15.stat({\n            path: to,\n            directory: toDirectory\n          });\n        } catch (e) {\n          // To location does not exist, ensure the directory containing \"to\" location exists and is a directory\n          const toPathComponents = to.split('/');\n          toPathComponents.pop();\n          const toPath = toPathComponents.join('/');\n          // Check the containing directory of the \"to\" location exists\n          if (toPathComponents.length > 0) {\n            const toParentDirectory = yield _this15.stat({\n              path: toPath,\n              directory: toDirectory\n            });\n            if (toParentDirectory.type !== 'directory') {\n              throw new Error('Parent directory of the to path is a file');\n            }\n          }\n        }\n        // Cannot overwrite a directory\n        if (toObj && toObj.type === 'directory') {\n          throw new Error('Cannot overwrite a directory with a file');\n        }\n        // Ensure the \"from\" object exists\n        const fromObj = yield _this15.stat({\n          path: from,\n          directory: fromDirectory\n        });\n        // Set the mtime/ctime of the supplied path\n        const updateTime = /*#__PURE__*/function () {\n          var _ref3 = _asyncToGenerator(function* (path, ctime, mtime) {\n            const fullPath = _this15.getPath(toDirectory, path);\n            const entry = yield _this15.dbRequest('get', [fullPath]);\n            entry.ctime = ctime;\n            entry.mtime = mtime;\n            yield _this15.dbRequest('put', [entry]);\n          });\n          return function updateTime(_x4, _x5, _x6) {\n            return _ref3.apply(this, arguments);\n          };\n        }();\n        const ctime = fromObj.ctime ? fromObj.ctime : Date.now();\n        switch (fromObj.type) {\n          // The \"from\" object is a file\n          case 'file':\n            {\n              // Read the file\n              const file = yield _this15.readFile({\n                path: from,\n                directory: fromDirectory\n              });\n              // Optionally remove the file\n              if (doRename) {\n                yield _this15.deleteFile({\n                  path: from,\n                  directory: fromDirectory\n                });\n              }\n              let encoding;\n              if (!(file.data instanceof Blob) && !_this15.isBase64String(file.data)) {\n                encoding = Encoding.UTF8;\n              }\n              // Write the file to the new location\n              const writeResult = yield _this15.writeFile({\n                path: to,\n                directory: toDirectory,\n                data: file.data,\n                encoding: encoding\n              });\n              // Copy the mtime/ctime of a renamed file\n              if (doRename) {\n                yield updateTime(to, ctime, fromObj.mtime);\n              }\n              // Resolve promise\n              return writeResult;\n            }\n          case 'directory':\n            {\n              if (toObj) {\n                throw Error('Cannot move a directory over an existing object');\n              }\n              try {\n                // Create the to directory\n                yield _this15.mkdir({\n                  path: to,\n                  directory: toDirectory,\n                  recursive: false\n                });\n                // Copy the mtime/ctime of a renamed directory\n                if (doRename) {\n                  yield updateTime(to, ctime, fromObj.mtime);\n                }\n              } catch (e) {\n                // ignore\n              }\n              // Iterate over the contents of the from location\n              const contents = (yield _this15.readdir({\n                path: from,\n                directory: fromDirectory\n              })).files;\n              for (const filename of contents) {\n                // Move item from the from directory to the to directory\n                yield _this15._copy({\n                  from: `${from}/${filename.name}`,\n                  to: `${to}/${filename.name}`,\n                  directory: fromDirectory,\n                  toDirectory\n                }, doRename);\n              }\n              // Optionally remove the original from directory\n              if (doRename) {\n                yield _this15.rmdir({\n                  path: from,\n                  directory: fromDirectory\n                });\n              }\n            }\n        }\n        return {\n          uri: toPath\n        };\n      }).apply(this, arguments);\n    }\n    isBase64String(str) {\n      try {\n        return btoa(atob(str)) == str;\n      } catch (err) {\n        return false;\n      }\n    }\n  }\n  FilesystemWeb._debug = true;\n  //# sourceMappingURL=web.js.map\n  return FilesystemWeb;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}