{"ast": null, "code": "import { ArgumentOutOfRangeError } from '../util/ArgumentOutOfRangeError';\nimport { filter } from './filter';\nimport { throwIfEmpty } from './throwIfEmpty';\nimport { defaultIfEmpty } from './defaultIfEmpty';\nimport { take } from './take';\nexport function elementAt(index, defaultValue) {\n  if (index < 0) {\n    throw new ArgumentOutOfRangeError();\n  }\n  const hasDefaultValue = arguments.length >= 2;\n  return source => source.pipe(filter((v, i) => i === index), take(1), hasDefaultValue ? defaultIfEmpty(defaultValue) : throwIfEmpty(() => new ArgumentOutOfRangeError()));\n}\n//# sourceMappingURL=elementAt.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}