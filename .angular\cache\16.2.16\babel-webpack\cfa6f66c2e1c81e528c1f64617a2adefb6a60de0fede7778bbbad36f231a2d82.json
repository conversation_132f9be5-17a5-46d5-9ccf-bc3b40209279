{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/\\u062A\\u0637\\u0628\\u064A\\u0642 \\u0646\\u0627\\u0634\\u0631 \\u0627\\u064A\\u0627\\u062A \\u0642\\u0626\\u0627\\u0646\\u064A\\u0629/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport { API_VERSIONS, API_VERSION_HEADER_NAME } from './constants';\nimport { expiresAt, looksLikeFetchResponse, parseResponseAPIVersion } from './helpers';\nimport { AuthApiError, AuthRetryableFetchError, AuthWeakPasswordError, AuthUnknownError, AuthSessionMissingError } from './errors';\nconst _getErrorMessage = err => err.msg || err.message || err.error_description || err.error || JSON.stringify(err);\nconst NETWORK_ERROR_CODES = [502, 503, 504];\nexport function handleError(_x) {\n  return _handleError.apply(this, arguments);\n}\nfunction _handleError() {\n  _handleError = _asyncToGenerator(function* (error) {\n    var _a;\n    if (!looksLikeFetchResponse(error)) {\n      throw new AuthRetryableFetchError(_getErrorMessage(error), 0);\n    }\n    if (NETWORK_ERROR_CODES.includes(error.status)) {\n      // status in 500...599 range - server had an error, request might be retryed.\n      throw new AuthRetryableFetchError(_getErrorMessage(error), error.status);\n    }\n    let data;\n    try {\n      data = yield error.json();\n    } catch (e) {\n      throw new AuthUnknownError(_getErrorMessage(e), e);\n    }\n    let errorCode = undefined;\n    const responseAPIVersion = parseResponseAPIVersion(error);\n    if (responseAPIVersion && responseAPIVersion.getTime() >= API_VERSIONS['2024-01-01'].timestamp && typeof data === 'object' && data && typeof data.code === 'string') {\n      errorCode = data.code;\n    } else if (typeof data === 'object' && data && typeof data.error_code === 'string') {\n      errorCode = data.error_code;\n    }\n    if (!errorCode) {\n      // Legacy support for weak password errors, when there were no error codes\n      if (typeof data === 'object' && data && typeof data.weak_password === 'object' && data.weak_password && Array.isArray(data.weak_password.reasons) && data.weak_password.reasons.length && data.weak_password.reasons.reduce((a, i) => a && typeof i === 'string', true)) {\n        throw new AuthWeakPasswordError(_getErrorMessage(data), error.status, data.weak_password.reasons);\n      }\n    } else if (errorCode === 'weak_password') {\n      throw new AuthWeakPasswordError(_getErrorMessage(data), error.status, ((_a = data.weak_password) === null || _a === void 0 ? void 0 : _a.reasons) || []);\n    } else if (errorCode === 'session_not_found') {\n      // The `session_id` inside the JWT does not correspond to a row in the\n      // `sessions` table. This usually means the user has signed out, has been\n      // deleted, or their session has somehow been terminated.\n      throw new AuthSessionMissingError();\n    }\n    throw new AuthApiError(_getErrorMessage(data), error.status || 500, errorCode);\n  });\n  return _handleError.apply(this, arguments);\n}\nconst _getRequestParams = (method, options, parameters, body) => {\n  const params = {\n    method,\n    headers: (options === null || options === void 0 ? void 0 : options.headers) || {}\n  };\n  if (method === 'GET') {\n    return params;\n  }\n  params.headers = Object.assign({\n    'Content-Type': 'application/json;charset=UTF-8'\n  }, options === null || options === void 0 ? void 0 : options.headers);\n  params.body = JSON.stringify(body);\n  return Object.assign(Object.assign({}, params), parameters);\n};\nexport function _request(_x2, _x3, _x4, _x5) {\n  return _request2.apply(this, arguments);\n}\nfunction _request2() {\n  _request2 = _asyncToGenerator(function* (fetcher, method, url, options) {\n    var _a;\n    const headers = Object.assign({}, options === null || options === void 0 ? void 0 : options.headers);\n    if (!headers[API_VERSION_HEADER_NAME]) {\n      headers[API_VERSION_HEADER_NAME] = API_VERSIONS['2024-01-01'].name;\n    }\n    if (options === null || options === void 0 ? void 0 : options.jwt) {\n      headers['Authorization'] = `Bearer ${options.jwt}`;\n    }\n    const qs = (_a = options === null || options === void 0 ? void 0 : options.query) !== null && _a !== void 0 ? _a : {};\n    if (options === null || options === void 0 ? void 0 : options.redirectTo) {\n      qs['redirect_to'] = options.redirectTo;\n    }\n    const queryString = Object.keys(qs).length ? '?' + new URLSearchParams(qs).toString() : '';\n    const data = yield _handleRequest(fetcher, method, url + queryString, {\n      headers,\n      noResolveJson: options === null || options === void 0 ? void 0 : options.noResolveJson\n    }, {}, options === null || options === void 0 ? void 0 : options.body);\n    return (options === null || options === void 0 ? void 0 : options.xform) ? options === null || options === void 0 ? void 0 : options.xform(data) : {\n      data: Object.assign({}, data),\n      error: null\n    };\n  });\n  return _request2.apply(this, arguments);\n}\nfunction _handleRequest(_x6, _x7, _x8, _x9, _x0, _x1) {\n  return _handleRequest2.apply(this, arguments);\n}\nfunction _handleRequest2() {\n  _handleRequest2 = _asyncToGenerator(function* (fetcher, method, url, options, parameters, body) {\n    const requestParams = _getRequestParams(method, options, parameters, body);\n    let result;\n    try {\n      result = yield fetcher(url, Object.assign({}, requestParams));\n    } catch (e) {\n      console.error(e);\n      // fetch failed, likely due to a network or CORS error\n      throw new AuthRetryableFetchError(_getErrorMessage(e), 0);\n    }\n    if (!result.ok) {\n      yield handleError(result);\n    }\n    if (options === null || options === void 0 ? void 0 : options.noResolveJson) {\n      return result;\n    }\n    try {\n      return yield result.json();\n    } catch (e) {\n      yield handleError(e);\n    }\n  });\n  return _handleRequest2.apply(this, arguments);\n}\nexport function _sessionResponse(data) {\n  var _a;\n  let session = null;\n  if (hasSession(data)) {\n    session = Object.assign({}, data);\n    if (!data.expires_at) {\n      session.expires_at = expiresAt(data.expires_in);\n    }\n  }\n  const user = (_a = data.user) !== null && _a !== void 0 ? _a : data;\n  return {\n    data: {\n      session,\n      user\n    },\n    error: null\n  };\n}\nexport function _sessionResponsePassword(data) {\n  const response = _sessionResponse(data);\n  if (!response.error && data.weak_password && typeof data.weak_password === 'object' && Array.isArray(data.weak_password.reasons) && data.weak_password.reasons.length && data.weak_password.message && typeof data.weak_password.message === 'string' && data.weak_password.reasons.reduce((a, i) => a && typeof i === 'string', true)) {\n    response.data.weak_password = data.weak_password;\n  }\n  return response;\n}\nexport function _userResponse(data) {\n  var _a;\n  const user = (_a = data.user) !== null && _a !== void 0 ? _a : data;\n  return {\n    data: {\n      user\n    },\n    error: null\n  };\n}\nexport function _ssoResponse(data) {\n  return {\n    data,\n    error: null\n  };\n}\nexport function _generateLinkResponse(data) {\n  const {\n      action_link,\n      email_otp,\n      hashed_token,\n      redirect_to,\n      verification_type\n    } = data,\n    rest = __rest(data, [\"action_link\", \"email_otp\", \"hashed_token\", \"redirect_to\", \"verification_type\"]);\n  const properties = {\n    action_link,\n    email_otp,\n    hashed_token,\n    redirect_to,\n    verification_type\n  };\n  const user = Object.assign({}, rest);\n  return {\n    data: {\n      properties,\n      user\n    },\n    error: null\n  };\n}\nexport function _noResolveJsonResponse(data) {\n  return data;\n}\n/**\n * hasSession checks if the response object contains a valid session\n * @param data A response object\n * @returns true if a session is in the response\n */\nfunction hasSession(data) {\n  return data.access_token && data.refresh_token && data.expires_in;\n}\n//# sourceMappingURL=fetch.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}