import { Injectable } from '@angular/core';
import { Observable, from, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { API_KEYS } from '../config/api-keys';
import { ErrorHandlerService } from './error-handler.service';

export interface GeneratedContent {
  title: string;
  description: string;
  tags: string[];
}

export interface SurahInfo {
  surahName: string;
  surahNumber: number;
  startAyah: number;
  endAyah: number;
  ayahTexts: string[];
}

export interface GeminiModel {
  id: string;
  name: string;
  description: string;
  isPaid: boolean;
  isAvailable: boolean;
}

export interface ModelAvailability {
  model: string;
  available: boolean;
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class GeminiService {
  private readonly API_KEY = API_KEYS.GEMINI_API_KEY;
  private genAI!: GoogleGenerativeAI;
  private model: any;
  private currentModel: string = 'gemini-2.5-flash';

  // Available Gemini models
  private readonly availableModels: GeminiModel[] = [
    {
      id: 'gemini-2.5-pro',
      name: 'Gemini 2.5 Pro',
      description: 'النموذج الأكثر تقدماً مع قدرات محسنة',
      isPaid: true,
      isAvailable: true
    },
    {
      id: 'gemini-2.5-flash',
      name: 'Gemini 2.5 Flash',
      description: 'نموذج سريع ومجاني مع أداء جيد',
      isPaid: false,
      isAvailable: true
    },
    {
      id: 'gemini-2.0-flash-exp',
      name: 'Gemini 2.0 Flash (تجريبي)',
      description: 'نموذج تجريبي مجاني',
      isPaid: false,
      isAvailable: true
    }
  ];

  constructor(private errorHandler: ErrorHandlerService) {
    this.initializeService();
  }

  private initializeService(): void {
    if (this.isApiKeyConfigured()) {
      this.genAI = new GoogleGenerativeAI(this.API_KEY);
      this.updateModel(this.currentModel);
    }
  }

  public updateApiKey(apiKey: string): void {
    if (apiKey && apiKey.trim().length > 0) {
      this.genAI = new GoogleGenerativeAI(apiKey);
      this.updateModel(this.currentModel);
    }
  }

  public updateModel(modelId: string): void {
    try {
      this.currentModel = modelId;
      this.model = this.genAI.getGenerativeModel({ model: modelId });
    } catch (error) {
      console.error('Error updating model:', error);
      throw new Error(`فشل في تحديث النموذج: ${modelId}`);
    }
  }

  public generateVideoContent(surahInfo: SurahInfo): Observable<GeneratedContent> {
    return from(this.performContentGeneration(surahInfo)).pipe(
      catchError(error => {
        this.errorHandler.handleError(error, 'gemini-generation');
        return throwError(() => error);
      })
    );
  }

  private async performContentGeneration(surahInfo: SurahInfo): Promise<GeneratedContent> {
    const prompt = this.buildPrompt(surahInfo);
    
    try {
      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();
      
      return this.parseGeneratedContent(text);
    } catch (error) {
      throw new Error(`Failed to generate content: ${error}`);
    }
  }

  private buildPrompt(surahInfo: SurahInfo): string {
    const ayahRange = surahInfo.startAyah === surahInfo.endAyah 
      ? `الآية ${surahInfo.startAyah}` 
      : `الآيات ${surahInfo.startAyah} إلى ${surahInfo.endAyah}`;

    const ayahTexts = surahInfo.ayahTexts.join(' ');

    return `
أنت خبير في القرآن الكريم ومتخصص في إنشاء محتوى إسلامي جذاب لمنصات التواصل الاجتماعي.

معلومات المقطع:
- السورة: ${surahInfo.surahName}
- رقم السورة: ${surahInfo.surahNumber}
- ${ayahRange}
- نص الآيات: ${ayahTexts}

المطلوب منك إنشاء:
1. عنوان جذاب ومناسب للفيديو (لا يزيد عن 100 حرف)
2. وصف تفصيلي للفيديو (200-500 كلمة)
3. علامات هاشتاج مناسبة (10-15 علامة)

يجب أن يكون المحتوى:
- محترم ومناسب للمحتوى الديني
- جذاب للمشاهدين
- يحتوي على معلومات مفيدة عن السورة والآيات
- مناسب لمنصة يوتيوب

يرجى تنسيق الإجابة كما يلي:
TITLE: [العنوان هنا]
DESCRIPTION: [الوصف هنا]
TAGS: [العلامات مفصولة بفواصل]
`;
  }

  private parseGeneratedContent(text: string): GeneratedContent {
    const lines = text.split('\n');
    let title = '';
    let description = '';
    let tags: string[] = [];

    for (const line of lines) {
      if (line.startsWith('TITLE:')) {
        title = line.replace('TITLE:', '').trim();
      } else if (line.startsWith('DESCRIPTION:')) {
        description = line.replace('DESCRIPTION:', '').trim();
      } else if (line.startsWith('TAGS:')) {
        const tagString = line.replace('TAGS:', '').trim();
        tags = tagString.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
      }
    }

    // Fallback parsing if the format is not followed exactly
    if (!title || !description) {
      const fallback = this.fallbackParsing(text);
      title = title || fallback.title;
      description = description || fallback.description;
      tags = tags.length > 0 ? tags : fallback.tags;
    }

    return {
      title: title || 'مقطع قرآني مميز',
      description: description || 'استمع إلى تلاوة عذبة من القرآن الكريم',
      tags: tags.length > 0 ? tags : ['القرآن', 'تلاوة', 'إسلام', 'قرآن_كريم']
    };
  }

  private fallbackParsing(text: string): GeneratedContent {
    // Simple fallback parsing
    const lines = text.split('\n').filter(line => line.trim().length > 0);
    
    let title = lines[0] || 'مقطع قرآني مميز';
    if (title.length > 100) {
      title = title.substring(0, 97) + '...';
    }

    let description = lines.slice(1, -1).join(' ') || 'استمع إلى تلاوة عذبة من القرآن الكريم';
    
    const defaultTags = ['القرآن', 'تلاوة', 'إسلام', 'قرآن_كريم', 'آيات', 'دين', 'مسلم'];
    
    return {
      title,
      description,
      tags: defaultTags
    };
  }

  public generateTitleOnly(surahInfo: SurahInfo): Observable<string> {
    const prompt = `
أنشئ عنوان جذاب لفيديو قرآني يحتوي على:
- السورة: ${surahInfo.surahName}
- الآيات: ${surahInfo.startAyah} إلى ${surahInfo.endAyah}

العنوان يجب أن يكون:
- لا يزيد عن 100 حرف
- جذاب ومناسب
- يحتوي على اسم السورة

أعطني العنوان فقط بدون أي إضافات.
`;

    return from(this.model.generateContent(prompt)).pipe(
      map((result: any) => result.response.text().trim()),
      catchError(error => {
        console.error('Title generation failed:', error);
        return throwError(() => error);
      })
    );
  }

  public generateDescriptionOnly(surahInfo: SurahInfo): Observable<string> {
    const prompt = `
أنشئ وصف تفصيلي لفيديو قرآني يحتوي على:
- السورة: ${surahInfo.surahName}
- الآيات: ${surahInfo.startAyah} إلى ${surahInfo.endAyah}
- نص الآيات: ${surahInfo.ayahTexts.join(' ')}

الوصف يجب أن يكون:
- بين 200-500 كلمة
- يحتوي على معلومات مفيدة عن السورة
- مناسب لمنصة يوتيوب
- محترم ومناسب للمحتوى الديني

أعطني الوصف فقط بدون أي إضافات.
`;

    return from(this.model.generateContent(prompt)).pipe(
      map((result: any) => result.response.text().trim()),
      catchError(error => {
        console.error('Description generation failed:', error);
        return throwError(() => error);
      })
    );
  }

  public isApiKeyConfigured(): boolean {
    return this.API_KEY !== 'YOUR_GEMINI_API_KEY_HERE' && this.API_KEY.length > 0;
  }

  public getAvailableModels(): GeminiModel[] {
    return this.availableModels;
  }

  public getCurrentModel(): string {
    return this.currentModel;
  }

  public async checkModelAvailability(modelId: string, apiKey?: string): Promise<ModelAvailability> {
    try {
      const testGenAI = apiKey ? new GoogleGenerativeAI(apiKey) : this.genAI;
      const testModel = testGenAI.getGenerativeModel({ model: modelId });

      // Test with a simple prompt
      const result = await testModel.generateContent('Test');
      const response = await result.response;
      response.text(); // Ensure we can get text

      return {
        model: modelId,
        available: true
      };
    } catch (error: any) {
      let errorMessage = 'خطأ غير معروف';

      if (error.message?.includes('API_KEY_INVALID')) {
        errorMessage = 'مفتاح API غير صحيح';
      } else if (error.message?.includes('PERMISSION_DENIED')) {
        errorMessage = 'ليس لديك صلاحية للوصول لهذا النموذج';
      } else if (error.message?.includes('MODEL_NOT_FOUND')) {
        errorMessage = 'النموذج غير متوفر';
      } else if (error.message?.includes('QUOTA_EXCEEDED')) {
        errorMessage = 'تم تجاوز الحد المسموح للاستخدام';
      }

      return {
        model: modelId,
        available: false,
        error: errorMessage
      };
    }
  }

  public async testConnection(apiKey?: string, modelId?: string): Promise<boolean> {
    try {
      const testApiKey = apiKey || this.API_KEY;
      const testModelId = modelId || this.currentModel;

      if (!testApiKey || testApiKey === 'YOUR_GEMINI_API_KEY_HERE') {
        throw new Error('مفتاح API غير صحيح');
      }

      const availability = await this.checkModelAvailability(testModelId, testApiKey);
      return availability.available;
    } catch (error) {
      console.error('Connection test failed:', error);
      return false;
    }
  }

  public getModelInfo(modelId: string): GeminiModel | undefined {
    return this.availableModels.find(model => model.id === modelId);
  }

  public isModelPaid(modelId: string): boolean {
    const model = this.getModelInfo(modelId);
    return model?.isPaid || false;
  }
}
