{"ast": null, "code": "import { trigger, transition, style, animate } from '@angular/animations';\nimport * as i2 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { PLATFORM_ID, Pipe, Inject, EventEmitter, forwardRef, Component, ViewEncapsulation, Input, Output, computed, signal, ChangeDetectionStrategy, ViewChild, ContentChildren, NgModule } from '@angular/core';\nimport * as i3 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport * as i5 from 'primeng/api';\nimport { PrimeTemplate } from 'primeng/api';\nimport { DomHandler, ConnectedOverlayScrollHandler } from 'primeng/dom';\nimport * as i4 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i6 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { UniqueComponentId, ZIndexUtils } from 'primeng/utils';\nimport * as i1 from '@angular/platform-browser';\nconst _c0 = [\"pMenuItemContent\", \"\"];\nfunction MenuItemContent_ng_container_1_a_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c1 = function (a0) {\n  return {\n    \"p-disabled\": a0\n  };\n};\nconst _c2 = function (a0) {\n  return {\n    $implicit: a0\n  };\n};\nfunction MenuItemContent_ng_container_1_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 5);\n    i0.ɵɵtemplate(1, MenuItemContent_ng_container_1_a_1_ng_container_1_Template, 1, 0, \"ng-container\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    const _r2 = i0.ɵɵreference(4);\n    i0.ɵɵproperty(\"target\", ctx_r4.item.target)(\"ngClass\", i0.ɵɵpureFunction1(10, _c1, ctx_r4.item.disabled));\n    i0.ɵɵattribute(\"title\", ctx_r4.item.title)(\"href\", ctx_r4.item.url || null, i0.ɵɵsanitizeUrl)(\"data-automationid\", ctx_r4.item.automationId)(\"tabindex\", -1)(\"data-pc-section\", \"action\")(\"aria-hidden\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r2)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(12, _c2, ctx_r4.item));\n  }\n}\nfunction MenuItemContent_ng_container_1_a_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c3 = function () {\n  return {\n    exact: false\n  };\n};\nfunction MenuItemContent_ng_container_1_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 7);\n    i0.ɵɵtemplate(1, MenuItemContent_ng_container_1_a_2_ng_container_1_Template, 1, 0, \"ng-container\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    const _r2 = i0.ɵɵreference(4);\n    i0.ɵɵproperty(\"routerLink\", ctx_r5.item.routerLink)(\"queryParams\", ctx_r5.item.queryParams)(\"routerLinkActiveOptions\", ctx_r5.item.routerLinkActiveOptions || i0.ɵɵpureFunction0(18, _c3))(\"target\", ctx_r5.item.target)(\"ngClass\", i0.ɵɵpureFunction1(19, _c1, ctx_r5.item.disabled))(\"fragment\", ctx_r5.item.fragment)(\"queryParamsHandling\", ctx_r5.item.queryParamsHandling)(\"preserveFragment\", ctx_r5.item.preserveFragment)(\"skipLocationChange\", ctx_r5.item.skipLocationChange)(\"replaceUrl\", ctx_r5.item.replaceUrl)(\"state\", ctx_r5.item.state);\n    i0.ɵɵattribute(\"data-automationid\", ctx_r5.item.automationId)(\"tabindex\", -1)(\"data-pc-section\", \"action\")(\"aria-hidden\", true)(\"title\", ctx_r5.item.title);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r2)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(21, _c2, ctx_r5.item));\n  }\n}\nfunction MenuItemContent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MenuItemContent_ng_container_1_a_1_Template, 2, 14, \"a\", 3);\n    i0.ɵɵtemplate(2, MenuItemContent_ng_container_1_a_2_Template, 2, 23, \"a\", 4);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r0.item == null ? null : ctx_r0.item.routerLink));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.item == null ? null : ctx_r0.item.routerLink);\n  }\n}\nfunction MenuItemContent_ng_container_2_1_ng_template_0_Template(rf, ctx) {}\nfunction MenuItemContent_ng_container_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MenuItemContent_ng_container_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction MenuItemContent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MenuItemContent_ng_container_2_1_Template, 1, 0, null, 6);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c2, ctx_r1.item));\n  }\n}\nfunction MenuItemContent_ng_template_3_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 12);\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r10.item.iconClass);\n    i0.ɵɵproperty(\"ngClass\", ctx_r10.item.icon)(\"ngStyle\", ctx_r10.item.iconStyle);\n  }\n}\nfunction MenuItemContent_ng_template_3_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r11.item.label);\n  }\n}\nfunction MenuItemContent_ng_template_3_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 14);\n    i0.ɵɵpipe(1, \"safeHtml\");\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(1, 1, ctx_r13.item.label), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction MenuItemContent_ng_template_3_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 15);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r14.item.badgeStyleClass);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r14.item.badge);\n  }\n}\nfunction MenuItemContent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MenuItemContent_ng_template_3_span_0_Template, 1, 4, \"span\", 8);\n    i0.ɵɵtemplate(1, MenuItemContent_ng_template_3_span_1_Template, 2, 1, \"span\", 9);\n    i0.ɵɵtemplate(2, MenuItemContent_ng_template_3_ng_template_2_Template, 2, 3, \"ng-template\", null, 10, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(4, MenuItemContent_ng_template_3_span_4_Template, 2, 2, \"span\", 11);\n  }\n  if (rf & 2) {\n    const _r12 = i0.ɵɵreference(3);\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.item.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.item.escape !== false)(\"ngIfElse\", _r12);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.item.badge);\n  }\n}\nconst _c4 = [\"list\"];\nconst _c5 = [\"container\"];\nfunction Menu_div_0_div_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Menu_div_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵtemplate(1, Menu_div_0_div_2_ng_container_1_Template, 1, 0, \"ng-container\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"data-pc-section\", \"start\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.startTemplate);\n  }\n}\nconst _c6 = function (a0) {\n  return {\n    \"p-hidden\": a0\n  };\n};\nfunction Menu_div_0_5_ng_template_0_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 13);\n  }\n  if (rf & 2) {\n    const submenu_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c6, submenu_r9.visible === false));\n  }\n}\nfunction Menu_div_0_5_ng_template_0_li_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const submenu_r9 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(submenu_r9.label);\n  }\n}\nfunction Menu_div_0_5_ng_template_0_li_1_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 17);\n    i0.ɵɵpipe(1, \"safeHtml\");\n  }\n  if (rf & 2) {\n    const submenu_r9 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(1, 1, submenu_r9.label), i0.ɵɵsanitizeHtml);\n  }\n}\nconst _c7 = function (a0, a1) {\n  return {\n    \"p-hidden\": a0,\n    flex: a1\n  };\n};\nfunction Menu_div_0_5_ng_template_0_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 14);\n    i0.ɵɵtemplate(1, Menu_div_0_5_ng_template_0_li_1_span_1_Template, 2, 1, \"span\", 15);\n    i0.ɵɵtemplate(2, Menu_div_0_5_ng_template_0_li_1_ng_template_2_Template, 2, 3, \"ng-template\", null, 16, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const _r16 = i0.ɵɵreference(3);\n    const ctx_r20 = i0.ɵɵnextContext();\n    const submenu_r9 = ctx_r20.$implicit;\n    const i_r10 = ctx_r20.index;\n    const ctx_r12 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(6, _c7, submenu_r9.visible === false, submenu_r9.visible))(\"tooltipOptions\", submenu_r9.tooltipOptions);\n    i0.ɵɵattribute(\"data-automationid\", submenu_r9.automationId)(\"id\", ctx_r12.menuitemId(submenu_r9, ctx_r12.id, i_r10));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", submenu_r9.escape !== false)(\"ngIfElse\", _r16);\n  }\n}\nfunction Menu_div_0_5_ng_template_0_ng_template_2_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 13);\n  }\n  if (rf & 2) {\n    const item_r21 = i0.ɵɵnextContext().$implicit;\n    const submenu_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c6, item_r21.visible === false || submenu_r9.visible === false));\n  }\n}\nconst _c8 = function (a0, a1, a2) {\n  return {\n    \"p-hidden\": a0,\n    \"p-focus\": a1,\n    \"p-disabled\": a2\n  };\n};\nfunction Menu_div_0_5_ng_template_0_ng_template_2_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 19);\n    i0.ɵɵlistener(\"onMenuItemClick\", function Menu_div_0_5_ng_template_0_ng_template_2_li_1_Template_li_onMenuItemClick_0_listener($event) {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r27.itemClick($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r29 = i0.ɵɵnextContext();\n    const item_r21 = ctx_r29.$implicit;\n    const j_r22 = ctx_r29.index;\n    const ctx_r30 = i0.ɵɵnextContext();\n    const submenu_r9 = ctx_r30.$implicit;\n    const i_r10 = ctx_r30.index;\n    const ctx_r24 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassMap(item_r21.styleClass);\n    i0.ɵɵproperty(\"pMenuItemContent\", item_r21)(\"itemTemplate\", ctx_r24.itemTemplate)(\"ngClass\", i0.ɵɵpureFunction3(13, _c8, item_r21.visible === false || submenu_r9.visible === false, ctx_r24.focusedOptionId() && ctx_r24.menuitemId(item_r21, ctx_r24.id, i_r10, j_r22) === ctx_r24.focusedOptionId(), ctx_r24.disabled(item_r21.disabled)))(\"ngStyle\", item_r21.style)(\"tooltipOptions\", item_r21.tooltipOptions);\n    i0.ɵɵattribute(\"data-pc-section\", \"menuitem\")(\"aria-label\", ctx_r24.label(item_r21.label))(\"data-p-focused\", ctx_r24.isItemFocused(ctx_r24.menuitemId(item_r21, ctx_r24.id, i_r10, j_r22)))(\"data-p-disabled\", ctx_r24.disabled(item_r21.disabled))(\"aria-disabled\", ctx_r24.disabled(item_r21.disabled))(\"id\", ctx_r24.menuitemId(item_r21, ctx_r24.id, i_r10, j_r22));\n  }\n}\nfunction Menu_div_0_5_ng_template_0_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Menu_div_0_5_ng_template_0_ng_template_2_li_0_Template, 1, 3, \"li\", 11);\n    i0.ɵɵtemplate(1, Menu_div_0_5_ng_template_0_ng_template_2_li_1_Template, 1, 17, \"li\", 18);\n  }\n  if (rf & 2) {\n    const item_r21 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngIf\", item_r21.separator);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r21.separator);\n  }\n}\nfunction Menu_div_0_5_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Menu_div_0_5_ng_template_0_li_0_Template, 1, 3, \"li\", 11);\n    i0.ɵɵtemplate(1, Menu_div_0_5_ng_template_0_li_1_Template, 4, 9, \"li\", 12);\n    i0.ɵɵtemplate(2, Menu_div_0_5_ng_template_0_ng_template_2_Template, 2, 2, \"ng-template\", 10);\n  }\n  if (rf & 2) {\n    const submenu_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngIf\", submenu_r9.separator);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !submenu_r9.separator);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", submenu_r9.items);\n  }\n}\nfunction Menu_div_0_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Menu_div_0_5_ng_template_0_Template, 3, 3, \"ng-template\", 10);\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.model);\n  }\n}\nfunction Menu_div_0_6_ng_template_0_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 13);\n  }\n  if (rf & 2) {\n    const item_r32 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c6, item_r32.visible === false));\n  }\n}\nfunction Menu_div_0_6_ng_template_0_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r38 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 19);\n    i0.ɵɵlistener(\"onMenuItemClick\", function Menu_div_0_6_ng_template_0_li_1_Template_li_onMenuItemClick_0_listener($event) {\n      i0.ɵɵrestoreView(_r38);\n      const ctx_r37 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r37.itemClick($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r39 = i0.ɵɵnextContext();\n    const item_r32 = ctx_r39.$implicit;\n    const i_r33 = ctx_r39.index;\n    const ctx_r35 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassMap(item_r32.styleClass);\n    i0.ɵɵproperty(\"pMenuItemContent\", item_r32)(\"itemTemplate\", ctx_r35.itemTemplate)(\"ngClass\", i0.ɵɵpureFunction3(13, _c8, item_r32.visible === false, ctx_r35.focusedOptionId() && ctx_r35.menuitemId(item_r32, ctx_r35.id, i_r33, ctx_r35.j) === ctx_r35.focusedOptionId(), ctx_r35.disabled(item_r32.disabled)))(\"ngStyle\", item_r32.style)(\"tooltipOptions\", item_r32.tooltipOptions);\n    i0.ɵɵattribute(\"data-pc-section\", \"menuitem\")(\"aria-label\", ctx_r35.label(item_r32.label))(\"data-p-focused\", ctx_r35.isItemFocused(ctx_r35.menuitemId(item_r32, ctx_r35.id, i_r33)))(\"data-p-disabled\", ctx_r35.disabled(item_r32.disabled))(\"aria-disabled\", ctx_r35.disabled(item_r32.disabled))(\"id\", ctx_r35.menuitemId(item_r32, ctx_r35.id, i_r33));\n  }\n}\nfunction Menu_div_0_6_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Menu_div_0_6_ng_template_0_li_0_Template, 1, 3, \"li\", 11);\n    i0.ɵɵtemplate(1, Menu_div_0_6_ng_template_0_li_1_Template, 1, 17, \"li\", 18);\n  }\n  if (rf & 2) {\n    const item_r32 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngIf\", item_r32.separator);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r32.separator);\n  }\n}\nfunction Menu_div_0_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Menu_div_0_6_ng_template_0_Template, 2, 2, \"ng-template\", 10);\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.model);\n  }\n}\nfunction Menu_div_0_div_7_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Menu_div_0_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtemplate(1, Menu_div_0_div_7_ng_container_1_Template, 1, 0, \"ng-container\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"data-pc-section\", \"end\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r6.endTemplate);\n  }\n}\nconst _c9 = function (a1) {\n  return {\n    \"p-menu p-component\": true,\n    \"p-menu-overlay\": a1\n  };\n};\nconst _c10 = function (a0, a1) {\n  return {\n    showTransitionParams: a0,\n    hideTransitionParams: a1\n  };\n};\nconst _c11 = function (a1) {\n  return {\n    value: \"visible\",\n    params: a1\n  };\n};\nfunction Menu_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r42 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1, 2);\n    i0.ɵɵlistener(\"click\", function Menu_div_0_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r42);\n      const ctx_r41 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r41.onOverlayClick($event));\n    })(\"@overlayAnimation.start\", function Menu_div_0_Template_div_animation_overlayAnimation_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r42);\n      const ctx_r43 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r43.onOverlayAnimationStart($event));\n    })(\"@overlayAnimation.done\", function Menu_div_0_Template_div_animation_overlayAnimation_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r42);\n      const ctx_r44 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r44.onOverlayAnimationEnd($event));\n    });\n    i0.ɵɵtemplate(2, Menu_div_0_div_2_Template, 2, 2, \"div\", 3);\n    i0.ɵɵelementStart(3, \"ul\", 4, 5);\n    i0.ɵɵlistener(\"focus\", function Menu_div_0_Template_ul_focus_3_listener($event) {\n      i0.ɵɵrestoreView(_r42);\n      const ctx_r45 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r45.onListFocus($event));\n    })(\"blur\", function Menu_div_0_Template_ul_blur_3_listener($event) {\n      i0.ɵɵrestoreView(_r42);\n      const ctx_r46 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r46.onListBlur($event));\n    })(\"keydown\", function Menu_div_0_Template_ul_keydown_3_listener($event) {\n      i0.ɵɵrestoreView(_r42);\n      const ctx_r47 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r47.onListKeyDown($event));\n    });\n    i0.ɵɵtemplate(5, Menu_div_0_5_Template, 1, 1, null, 6);\n    i0.ɵɵtemplate(6, Menu_div_0_6_Template, 1, 1, null, 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, Menu_div_0_div_7_Template, 2, 2, \"div\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r0.styleClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(18, _c9, ctx_r0.popup))(\"ngStyle\", ctx_r0.style)(\"@overlayAnimation\", i0.ɵɵpureFunction1(23, _c11, i0.ɵɵpureFunction2(20, _c10, ctx_r0.showTransitionOptions, ctx_r0.hideTransitionOptions)))(\"@.disabled\", ctx_r0.popup !== true);\n    i0.ɵɵattribute(\"data-pc-name\", \"menu\")(\"id\", ctx_r0.id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.startTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"tabindex\", ctx_r0.tabindex);\n    i0.ɵɵattribute(\"id\", ctx_r0.id + \"_list\")(\"data-pc-section\", \"menu\")(\"aria-activedescendant\", ctx_r0.activedescendant())(\"aria-label\", ctx_r0.ariaLabel)(\"aria-labelledBy\", ctx_r0.ariaLabelledBy);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.hasSubMenu());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.hasSubMenu());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.endTemplate);\n  }\n}\nlet SafeHtmlPipe = /*#__PURE__*/(() => {\n  class SafeHtmlPipe {\n    platformId;\n    sanitizer;\n    constructor(platformId, sanitizer) {\n      this.platformId = platformId;\n      this.sanitizer = sanitizer;\n    }\n    transform(value) {\n      if (!value || !isPlatformBrowser(this.platformId)) {\n        return value;\n      }\n      return this.sanitizer.bypassSecurityTrustHtml(value);\n    }\n    static ɵfac = function SafeHtmlPipe_Factory(t) {\n      return new (t || SafeHtmlPipe)(i0.ɵɵdirectiveInject(PLATFORM_ID, 16), i0.ɵɵdirectiveInject(i1.DomSanitizer, 16));\n    };\n    static ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"safeHtml\",\n      type: SafeHtmlPipe,\n      pure: true\n    });\n  }\n  return SafeHtmlPipe;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MenuItemContent = /*#__PURE__*/(() => {\n  class MenuItemContent {\n    item;\n    itemTemplate;\n    id;\n    onMenuItemClick = new EventEmitter();\n    menu;\n    constructor(menu) {\n      this.menu = menu;\n    }\n    onItemClick(event, item) {\n      this.onMenuItemClick.emit({\n        originalEvent: event,\n        item: {\n          ...item,\n          id: this.id\n        }\n      });\n    }\n    static ɵfac = function MenuItemContent_Factory(t) {\n      return new (t || MenuItemContent)(i0.ɵɵdirectiveInject(forwardRef(() => Menu)));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MenuItemContent,\n      selectors: [[\"\", \"pMenuItemContent\", \"\"]],\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        item: [\"pMenuItemContent\", \"item\"],\n        itemTemplate: \"itemTemplate\",\n        id: \"id\"\n      },\n      outputs: {\n        onMenuItemClick: \"onMenuItemClick\"\n      },\n      attrs: _c0,\n      decls: 5,\n      vars: 3,\n      consts: [[1, \"p-menuitem-content\", 3, \"click\"], [4, \"ngIf\"], [\"itemContent\", \"\"], [\"class\", \"p-menuitem-link\", \"pRipple\", \"\", 3, \"target\", \"ngClass\", 4, \"ngIf\"], [\"routerLinkActive\", \"p-menuitem-link-active\", \"class\", \"p-menuitem-link\", \"pRipple\", \"\", 3, \"routerLink\", \"queryParams\", \"routerLinkActiveOptions\", \"target\", \"ngClass\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", 4, \"ngIf\"], [\"pRipple\", \"\", 1, \"p-menuitem-link\", 3, \"target\", \"ngClass\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"routerLinkActive\", \"p-menuitem-link-active\", \"pRipple\", \"\", 1, \"p-menuitem-link\", 3, \"routerLink\", \"queryParams\", \"routerLinkActiveOptions\", \"target\", \"ngClass\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\"], [\"class\", \"p-menuitem-icon\", 3, \"ngClass\", \"class\", \"ngStyle\", 4, \"ngIf\"], [\"class\", \"p-menuitem-text\", 4, \"ngIf\", \"ngIfElse\"], [\"htmlLabel\", \"\"], [\"class\", \"p-menuitem-badge\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\"], [1, \"p-menuitem-text\"], [1, \"p-menuitem-text\", 3, \"innerHTML\"], [1, \"p-menuitem-badge\", 3, \"ngClass\"]],\n      template: function MenuItemContent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵlistener(\"click\", function MenuItemContent_Template_div_click_0_listener($event) {\n            return ctx.onItemClick($event, ctx.item);\n          });\n          i0.ɵɵtemplate(1, MenuItemContent_ng_container_1_Template, 3, 2, \"ng-container\", 1);\n          i0.ɵɵtemplate(2, MenuItemContent_ng_container_2_Template, 2, 4, \"ng-container\", 1);\n          i0.ɵɵtemplate(3, MenuItemContent_ng_template_3_Template, 5, 4, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-pc-section\", \"content\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.itemTemplate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.itemTemplate);\n        }\n      },\n      dependencies: [i2.NgClass, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i3.RouterLink, i3.RouterLinkActive, i4.Ripple, SafeHtmlPipe],\n      encapsulation: 2\n    });\n  }\n  return MenuItemContent;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Menu is a navigation / command component that supports dynamic and static positioning.\n * @group Components\n */\nlet Menu = /*#__PURE__*/(() => {\n  class Menu {\n    document;\n    platformId;\n    el;\n    renderer;\n    cd;\n    config;\n    overlayService;\n    /**\n     * An array of menuitems.\n     * @group Props\n     */\n    model;\n    /**\n     * Defines if menu would displayed as a popup.\n     * @group Props\n     */\n    popup;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    style;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Target element to attach the overlay, valid values are \"body\" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n     * @group Props\n     */\n    appendTo;\n    /**\n     * Whether to automatically manage layering.\n     * @group Props\n     */\n    autoZIndex = true;\n    /**\n     * Base zIndex value to use in layering.\n     * @group Props\n     */\n    baseZIndex = 0;\n    /**\n     * Transition options of the show animation.\n     * @group Props\n     */\n    showTransitionOptions = '.12s cubic-bezier(0, 0, 0.2, 1)';\n    /**\n     * Transition options of the hide animation.\n     * @group Props\n     */\n    hideTransitionOptions = '.1s linear';\n    /**\n     * Defines a string value that labels an interactive element.\n     * @group Props\n     */\n    ariaLabel;\n    /**\n     * Identifier of the underlying input element.\n     * @group Props\n     */\n    ariaLabelledBy;\n    /**\n     * Current id state as a string.\n     * @group Props\n     */\n    id;\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    tabindex = 0;\n    /**\n     * Callback to invoke when overlay menu is shown.\n     * @group Emits\n     */\n    onShow = new EventEmitter();\n    /**\n     * Callback to invoke when overlay menu is hidden.\n     * @group Emits\n     */\n    onHide = new EventEmitter();\n    /**\n     * Callback to invoke when the list loses focus.\n     * @param {Event} event - blur event.\n     * @group Emits\n     */\n    onBlur = new EventEmitter();\n    /**\n     * Callback to invoke when the list receives focus.\n     * @param {Event} event - focus event.\n     * @group Emits\n     */\n    onFocus = new EventEmitter();\n    listViewChild;\n    containerViewChild;\n    templates;\n    startTemplate;\n    endTemplate;\n    itemTemplate;\n    container;\n    scrollHandler;\n    documentClickListener;\n    documentResizeListener;\n    preventDocumentDefault;\n    target;\n    visible;\n    focusedOptionId = computed(() => {\n      return this.focusedOptionIndex() !== -1 ? this.focusedOptionIndex() : null;\n    });\n    focusedOptionIndex = signal(-1);\n    selectedOptionIndex = signal(-1);\n    focused = false;\n    overlayVisible = false;\n    relativeAlign;\n    constructor(document, platformId, el, renderer, cd, config, overlayService) {\n      this.document = document;\n      this.platformId = platformId;\n      this.el = el;\n      this.renderer = renderer;\n      this.cd = cd;\n      this.config = config;\n      this.overlayService = overlayService;\n      this.id = this.id || UniqueComponentId();\n    }\n    /**\n     * Toggles the visibility of the popup menu.\n     * @param {Event} event - Browser event.\n     * @group Method\n     */\n    toggle(event) {\n      if (this.visible) this.hide();else this.show(event);\n      this.preventDocumentDefault = true;\n    }\n    /**\n     * Displays the popup menu.\n     * @param {Event} event - Browser event.\n     * @group Method\n     */\n    show(event) {\n      this.target = event.currentTarget;\n      this.relativeAlign = event.relativeAlign;\n      this.visible = true;\n      this.preventDocumentDefault = true;\n      this.overlayVisible = true;\n      this.cd.markForCheck();\n    }\n    ngOnInit() {\n      if (!this.popup) {\n        this.bindDocumentClickListener();\n      }\n    }\n    ngAfterContentInit() {\n      this.templates?.forEach(item => {\n        switch (item.getType()) {\n          case 'start':\n            this.startTemplate = item.template;\n            break;\n          case 'end':\n            this.endTemplate = item.template;\n            break;\n          case 'itemTemplate':\n            this.itemTemplate = item.template;\n            break;\n          default:\n            this.itemTemplate = item.template;\n            break;\n        }\n      });\n    }\n    onOverlayAnimationStart(event) {\n      switch (event.toState) {\n        case 'visible':\n          if (this.popup) {\n            this.container = event.element;\n            this.moveOnTop();\n            this.onShow.emit({});\n            this.appendOverlay();\n            this.alignOverlay();\n            this.bindDocumentClickListener();\n            this.bindDocumentResizeListener();\n            this.bindScrollListener();\n            DomHandler.focus(this.listViewChild.nativeElement);\n            this.changeFocusedOptionIndex(0);\n          }\n          break;\n        case 'void':\n          this.onOverlayHide();\n          this.onHide.emit({});\n          break;\n      }\n    }\n    onOverlayAnimationEnd(event) {\n      switch (event.toState) {\n        case 'void':\n          if (this.autoZIndex) {\n            ZIndexUtils.clear(event.element);\n          }\n          break;\n      }\n    }\n    alignOverlay() {\n      if (this.relativeAlign) DomHandler.relativePosition(this.container, this.target);else DomHandler.absolutePosition(this.container, this.target);\n    }\n    appendOverlay() {\n      if (this.appendTo) {\n        if (this.appendTo === 'body') this.renderer.appendChild(this.document.body, this.container);else DomHandler.appendChild(this.container, this.appendTo);\n      }\n    }\n    restoreOverlayAppend() {\n      if (this.container && this.appendTo) {\n        this.renderer.appendChild(this.el.nativeElement, this.container);\n      }\n    }\n    moveOnTop() {\n      if (this.autoZIndex) {\n        ZIndexUtils.set('menu', this.container, this.baseZIndex + this.config.zIndex.menu);\n      }\n    }\n    /**\n     * Hides the popup menu.\n     * @group Method\n     */\n    hide() {\n      this.visible = false;\n      this.relativeAlign = false;\n      this.cd.markForCheck();\n    }\n    onWindowResize() {\n      if (this.visible && !DomHandler.isTouchDevice()) {\n        this.hide();\n      }\n    }\n    menuitemId(item, id, index, childIndex) {\n      return item?.id ?? `${id}_${index}${childIndex !== undefined ? '_' + childIndex : ''}`;\n    }\n    isItemFocused(id) {\n      return this.focusedOptionId() === id;\n    }\n    label(label) {\n      return typeof label === 'function' ? label() : label;\n    }\n    disabled(disabled) {\n      return typeof disabled === 'function' ? disabled() : typeof disabled === 'undefined' ? false : disabled;\n    }\n    activedescendant() {\n      return this.focused ? this.focusedOptionId() : undefined;\n    }\n    onListFocus(event) {\n      this.focused = true;\n      if (!this.popup) {\n        if (this.selectedOptionIndex() !== -1) {\n          this.changeFocusedOptionIndex(this.selectedOptionIndex());\n          this.selectedOptionIndex.set(-1);\n        } else {\n          this.changeFocusedOptionIndex(0);\n        }\n      }\n      this.onFocus.emit(event);\n    }\n    onListBlur(event) {\n      this.focused = false;\n      this.changeFocusedOptionIndex(-1);\n      this.selectedOptionIndex.set(-1);\n      this.focusedOptionIndex.set(-1);\n      this.onBlur.emit(event);\n    }\n    onListKeyDown(event) {\n      switch (event.code) {\n        case 'ArrowDown':\n          this.onArrowDownKey(event);\n          break;\n        case 'ArrowUp':\n          this.onArrowUpKey(event);\n          break;\n        case 'Home':\n          this.onHomeKey(event);\n          break;\n        case 'End':\n          this.onEndKey(event);\n          break;\n        case 'Enter':\n          this.onEnterKey(event);\n          break;\n        case 'Space':\n          this.onSpaceKey(event);\n          break;\n        case 'Escape':\n          if (this.popup) {\n            DomHandler.focus(this.target);\n            this.hide();\n          }\n          break;\n        case 'Tab':\n          this.overlayVisible && this.hide();\n          break;\n        default:\n          break;\n      }\n    }\n    onArrowDownKey(event) {\n      const optionIndex = this.findNextOptionIndex(this.focusedOptionIndex());\n      this.changeFocusedOptionIndex(optionIndex);\n      event.preventDefault();\n    }\n    onArrowUpKey(event) {\n      if (event.altKey && this.popup) {\n        DomHandler.focus(this.target);\n        this.hide();\n        event.preventDefault();\n      } else {\n        const optionIndex = this.findPrevOptionIndex(this.focusedOptionIndex());\n        this.changeFocusedOptionIndex(optionIndex);\n        event.preventDefault();\n      }\n    }\n    onHomeKey(event) {\n      this.changeFocusedOptionIndex(0);\n      event.preventDefault();\n    }\n    onEndKey(event) {\n      this.changeFocusedOptionIndex(DomHandler.find(this.containerViewChild.nativeElement, 'li[data-pc-section=\"menuitem\"][data-p-disabled=\"false\"]').length - 1);\n      event.preventDefault();\n    }\n    onEnterKey(event) {\n      const element = DomHandler.findSingle(this.containerViewChild.nativeElement, `li[id=\"${`${this.focusedOptionIndex()}`}\"]`);\n      const anchorElement = element && DomHandler.findSingle(element, 'a[data-pc-section=\"action\"]');\n      this.popup && DomHandler.focus(this.target);\n      anchorElement ? anchorElement.click() : element && element.click();\n      event.preventDefault();\n    }\n    onSpaceKey(event) {\n      this.onEnterKey(event);\n    }\n    findNextOptionIndex(index) {\n      const links = DomHandler.find(this.containerViewChild.nativeElement, 'li[data-pc-section=\"menuitem\"][data-p-disabled=\"false\"]');\n      const matchedOptionIndex = [...links].findIndex(link => link.id === index);\n      return matchedOptionIndex > -1 ? matchedOptionIndex + 1 : 0;\n    }\n    findPrevOptionIndex(index) {\n      const links = DomHandler.find(this.containerViewChild.nativeElement, 'li[data-pc-section=\"menuitem\"][data-p-disabled=\"false\"]');\n      const matchedOptionIndex = [...links].findIndex(link => link.id === index);\n      return matchedOptionIndex > -1 ? matchedOptionIndex - 1 : 0;\n    }\n    changeFocusedOptionIndex(index) {\n      const links = DomHandler.find(this.containerViewChild.nativeElement, 'li[data-pc-section=\"menuitem\"][data-p-disabled=\"false\"]');\n      if (links.length > 0) {\n        let order = index >= links.length ? links.length - 1 : index < 0 ? 0 : index;\n        order > -1 && this.focusedOptionIndex.set(links[order].getAttribute('id'));\n      }\n    }\n    itemClick(event) {\n      const {\n        originalEvent,\n        item\n      } = event;\n      if (item.disabled) {\n        originalEvent.preventDefault();\n        return;\n      }\n      if (!item.url && !item.routerLink) {\n        originalEvent.preventDefault();\n      }\n      if (item.command) {\n        item.command({\n          originalEvent: originalEvent,\n          item: item\n        });\n      }\n      if (this.popup) {\n        this.hide();\n      }\n      if (!this.popup && this.focusedOptionIndex() !== item.id) {\n        this.focusedOptionIndex.set(item.id);\n      }\n    }\n    onOverlayClick(event) {\n      if (this.popup) {\n        this.overlayService.add({\n          originalEvent: event,\n          target: this.el.nativeElement\n        });\n      }\n      this.preventDocumentDefault = true;\n    }\n    bindDocumentClickListener() {\n      if (!this.documentClickListener && isPlatformBrowser(this.platformId)) {\n        const documentTarget = this.el ? this.el.nativeElement.ownerDocument : 'document';\n        this.documentClickListener = this.renderer.listen(documentTarget, 'click', event => {\n          const isOutsideContainer = this.containerViewChild.nativeElement && !this.containerViewChild.nativeElement.contains(event.target);\n          const isOutsideTarget = !(this.target && (this.target === event.target || this.target.contains(event.target)));\n          if (!this.popup && isOutsideContainer && isOutsideTarget) {\n            this.onListBlur(event);\n          }\n          if (this.preventDocumentDefault && this.overlayVisible && isOutsideContainer && isOutsideTarget) {\n            this.hide();\n            this.preventDocumentDefault = false;\n          }\n        });\n      }\n    }\n    unbindDocumentClickListener() {\n      if (this.documentClickListener) {\n        this.documentClickListener();\n        this.documentClickListener = null;\n      }\n    }\n    bindDocumentResizeListener() {\n      if (!this.documentResizeListener && isPlatformBrowser(this.platformId)) {\n        const window = this.document.defaultView;\n        this.documentResizeListener = this.renderer.listen(window, 'resize', this.onWindowResize.bind(this));\n      }\n    }\n    unbindDocumentResizeListener() {\n      if (this.documentResizeListener) {\n        this.documentResizeListener();\n        this.documentResizeListener = null;\n      }\n    }\n    bindScrollListener() {\n      if (!this.scrollHandler && isPlatformBrowser(this.platformId)) {\n        this.scrollHandler = new ConnectedOverlayScrollHandler(this.target, () => {\n          if (this.visible) {\n            this.hide();\n          }\n        });\n      }\n      this.scrollHandler?.bindScrollListener();\n    }\n    unbindScrollListener() {\n      if (this.scrollHandler) {\n        this.scrollHandler.unbindScrollListener();\n      }\n    }\n    onOverlayHide() {\n      this.unbindDocumentClickListener();\n      this.unbindDocumentResizeListener();\n      this.unbindScrollListener();\n      this.preventDocumentDefault = false;\n      if (!this.cd.destroyed) {\n        this.target = null;\n      }\n    }\n    ngOnDestroy() {\n      if (this.popup) {\n        if (this.scrollHandler) {\n          this.scrollHandler.destroy();\n          this.scrollHandler = null;\n        }\n        if (this.container && this.autoZIndex) {\n          ZIndexUtils.clear(this.container);\n        }\n        this.restoreOverlayAppend();\n        this.onOverlayHide();\n      }\n      if (!this.popup) {\n        this.unbindDocumentClickListener();\n      }\n    }\n    hasSubMenu() {\n      if (this.model) {\n        for (var item of this.model) {\n          if (item.items) {\n            return true;\n          }\n        }\n      }\n      return false;\n    }\n    isItemHidden(item) {\n      if (item.separator) {\n        return item.visible === false || item.items && item.items.some(subitem => subitem.visible !== false);\n      }\n      return item.visible === false;\n    }\n    static ɵfac = function Menu_Factory(t) {\n      return new (t || Menu)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i5.PrimeNGConfig), i0.ɵɵdirectiveInject(i5.OverlayService));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: Menu,\n      selectors: [[\"p-menu\"]],\n      contentQueries: function Menu_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      viewQuery: function Menu_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c4, 5);\n          i0.ɵɵviewQuery(_c5, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.containerViewChild = _t.first);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        model: \"model\",\n        popup: \"popup\",\n        style: \"style\",\n        styleClass: \"styleClass\",\n        appendTo: \"appendTo\",\n        autoZIndex: \"autoZIndex\",\n        baseZIndex: \"baseZIndex\",\n        showTransitionOptions: \"showTransitionOptions\",\n        hideTransitionOptions: \"hideTransitionOptions\",\n        ariaLabel: \"ariaLabel\",\n        ariaLabelledBy: \"ariaLabelledBy\",\n        id: \"id\",\n        tabindex: \"tabindex\"\n      },\n      outputs: {\n        onShow: \"onShow\",\n        onHide: \"onHide\",\n        onBlur: \"onBlur\",\n        onFocus: \"onFocus\"\n      },\n      decls: 1,\n      vars: 1,\n      consts: [[3, \"ngClass\", \"class\", \"ngStyle\", \"click\", 4, \"ngIf\"], [3, \"ngClass\", \"ngStyle\", \"click\"], [\"container\", \"\"], [\"class\", \"p-menu-start\", 4, \"ngIf\"], [\"role\", \"menu\", 1, \"p-menu-list\", \"p-reset\", 3, \"tabindex\", \"focus\", \"blur\", \"keydown\"], [\"list\", \"\"], [4, \"ngIf\"], [\"class\", \"p-menu-end\", 4, \"ngIf\"], [1, \"p-menu-start\"], [4, \"ngTemplateOutlet\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [\"class\", \"p-menuitem-separator\", \"role\", \"separator\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"p-submenu-header\", \"pTooltip\", \"\", \"role\", \"none\", 3, \"ngClass\", \"tooltipOptions\", 4, \"ngIf\"], [\"role\", \"separator\", 1, \"p-menuitem-separator\", 3, \"ngClass\"], [\"pTooltip\", \"\", \"role\", \"none\", 1, \"p-submenu-header\", 3, \"ngClass\", \"tooltipOptions\"], [4, \"ngIf\", \"ngIfElse\"], [\"htmlSubmenuLabel\", \"\"], [3, \"innerHTML\"], [\"class\", \"p-menuitem\", \"pTooltip\", \"\", \"role\", \"menuitem\", 3, \"pMenuItemContent\", \"itemTemplate\", \"ngClass\", \"ngStyle\", \"class\", \"tooltipOptions\", \"onMenuItemClick\", 4, \"ngIf\"], [\"pTooltip\", \"\", \"role\", \"menuitem\", 1, \"p-menuitem\", 3, \"pMenuItemContent\", \"itemTemplate\", \"ngClass\", \"ngStyle\", \"tooltipOptions\", \"onMenuItemClick\"], [1, \"p-menu-end\"]],\n      template: function Menu_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, Menu_div_0_Template, 8, 25, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", !ctx.popup || ctx.visible);\n        }\n      },\n      dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i6.Tooltip, MenuItemContent, SafeHtmlPipe],\n      styles: [\"@layer primeng{.p-menu-overlay{position:absolute;top:0;left:0}.p-menu ul{margin:0;padding:0;list-style:none}.p-menu .p-submenu-header{align-items:center}.p-menu .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-menu .p-menuitem-text{line-height:1}}\\n\"],\n      encapsulation: 2,\n      data: {\n        animation: [trigger('overlayAnimation', [transition(':enter', [style({\n          opacity: 0,\n          transform: 'scaleY(0.8)'\n        }), animate('{{showTransitionParams}}')]), transition(':leave', [animate('{{hideTransitionParams}}', style({\n          opacity: 0\n        }))])])]\n      },\n      changeDetection: 0\n    });\n  }\n  return Menu;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MenuModule = /*#__PURE__*/(() => {\n  class MenuModule {\n    static ɵfac = function MenuModule_Factory(t) {\n      return new (t || MenuModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MenuModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, RouterModule, RippleModule, TooltipModule, RouterModule, TooltipModule]\n    });\n  }\n  return MenuModule;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Menu, MenuItemContent, MenuModule, SafeHtmlPipe };\n//# sourceMappingURL=primeng-menu.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}