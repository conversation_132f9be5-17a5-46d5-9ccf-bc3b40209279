import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { AppModule } from './app/app.module';

// إضافة معالجة الأخطاء العامة
window.addEventListener('error', (event) => {
  console.error('Global error:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);
});

// التأكد من تحميل DOM قبل بدء التطبيق
document.addEventListener('DOMContentLoaded', () => {
  platformBrowserDynamic().bootstrapModule(AppModule)
    .catch(err => {
      console.error('Bootstrap error:', err);
      // إظهار رسالة خطأ للمستخدم
      document.body.innerHTML = `
        <div style="padding: 20px; text-align: center; font-family: Arial;">
          <h2>خطأ في تحميل التطبيق</h2>
          <p>حدث خطأ أثناء تحميل التطبيق. يرجى إعادة تشغيل التطبيق.</p>
          <p style="color: red; font-size: 12px;">${err.message}</p>
        </div>
      `;
    });
});
