
// Modern color scheme
:host {
  --primary-color: #667eea;
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-color: #f8f9fa;
  --accent-color: #ffd700;
  --text-primary: #2c3e50;
  --text-secondary: #6c757d;
  --border-color: #e9ecef;
  --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-hover: 0 8px 15px rgba(0, 0, 0, 0.2);
}

::ng-deep body {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.main-container {
  min-height: 100vh;
  padding: 0;
}

.app-header {
  background: var(--primary-gradient);
  color: white;
  padding: 1.5rem 2rem;
  box-shadow: var(--shadow);
  position: sticky;
  top: 0;
  z-index: 100;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
  }

  .header-left {
    .app-title {
      margin: 0;
      font-size: 1.8rem;
      font-weight: 700;
      color: white;
    }

    .app-subtitle {
      margin: 0.5rem 0 0 0;
      font-size: 1rem;
      opacity: 0.9;
      color: rgba(255, 255, 255, 0.9);
    }
  }

  .header-right {
    ::ng-deep .p-button {
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;

      &:hover {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.4);
      }
    }
  }
}

.content-wrapper {
  max-width: 800px;
  margin: 2rem auto;
  padding: 0 1rem;
}

.form-container {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
}

.form-group {
  margin-bottom: 1.5rem;

  .form-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    font-size: 1rem;

    i {
      color: var(--primary-color);
      font-size: 1.1rem;
    }
  }
}

.form-row {
  display: flex;
  gap: 1rem;

  .half-width {
    flex: 1;
  }
}

::ng-deep .custom-dropdown {
  .p-dropdown {
    width: 100%;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      border-color: var(--primary-color);
    }

    &.p-focus {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
  }

  .p-dropdown-label {
    padding: 0.75rem 1rem;
    font-size: 1rem;
  }
}

.ayah-range-container {
  display: flex;
  align-items: center;
  gap: 1rem;

  .ayah-input {
    flex: 1;

    .ayah-label {
      display: block;
      font-size: 0.9rem;
      color: var(--text-secondary);
      margin-bottom: 0.5rem;
      text-align: center;
    }

    .ayah-number-input {
      width: 100%;
      padding: 0.75rem;
      border: 2px solid var(--border-color);
      border-radius: 8px;
      text-align: center;
      font-size: 1.1rem;
      font-weight: 600;
      transition: all 0.3s ease;

      &:hover {
        border-color: var(--primary-color);
      }

      &:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        outline: none;
      }
    }
  }

  .range-separator {
    color: var(--primary-color);
    font-size: 1.2rem;
    margin-top: 1.5rem;
  }
}

::ng-deep .custom-slider {
  .p-slider {
    background: var(--border-color);
    border-radius: 4px;

    .p-slider-range {
      background: var(--primary-gradient);
    }

    .p-slider-handle {
      background: white;
      border: 3px solid var(--primary-color);
      box-shadow: var(--shadow);

      &:hover {
        transform: scale(1.1);
      }
    }
  }
}

::ng-deep .video-picker-btn {
  width: 100%;

  .p-button {
    width: 100%;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-hover);
    }
  }
}

.generate-section {
  text-align: center;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid var(--border-color);
}

::ng-deep .generate-btn {
  .p-button {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 12px;
    background: var(--primary-gradient);
    border: none;
    transition: all 0.3s ease;

    &:hover:not(:disabled) {
      transform: translateY(-3px);
      box-shadow: var(--shadow-hover);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .app-header {
    padding: 1rem;

    .header-content {
      flex-direction: column;
      gap: 1rem;
      text-align: center;
    }

    .header-left .app-title {
      font-size: 1.5rem;
    }
  }

  .content-wrapper {
    margin: 1rem auto;
    padding: 0 0.5rem;
  }

  .form-container {
    padding: 1.5rem;
    border-radius: 12px;
  }

  .form-row {
    flex-direction: column;
  }

  .ayah-range-container {
    flex-direction: column;

    .range-separator {
      transform: rotate(90deg);
      margin: 0;
    }
  }
}
