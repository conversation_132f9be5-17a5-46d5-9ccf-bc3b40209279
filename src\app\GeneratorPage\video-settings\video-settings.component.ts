import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { GeminiService, GeminiModel } from '../../Services/gemini.service';

export interface VideoSettings {
  geminiApiKey: string;
  geminiModel: string;
  fontSize: number;
  fontFamily: string;
  customFont?: File;
  videoQuality: string;
  audioQuality: string;
  subtitlePosition: string;
  subtitleStyle: string;
  backgroundColor: string;
  textColor: string;
  outlineColor: string;
  enableShadow: boolean;
  shadowColor: string;
}

@Component({
  selector: 'app-video-settings',
  templateUrl: './video-settings.component.html',
  styleUrls: ['./video-settings.component.scss']
})
export class VideoSettingsComponent implements OnInit {
  @Input() visible: boolean = false;
  @Output() visibleChange = new EventEmitter<boolean>();
  @Output() settingsSaved = new EventEmitter<VideoSettings>();

  settingsForm!: FormGroup;
  geminiModels: { label: string; value: string; description?: string; isPaid?: boolean }[] = [];
  isTestingConnection = false;
  connectionTestResult: string | null = null;

  fontFamilies = [
    { label: 'Al-Quran Al-Kareem', value: 'Al-QuranAlKareem' },
    { label: 'Amiri', value: 'Amiri' },
    { label: 'Noto Sans Arabic', value: 'NotoSansArabic' },
    { label: 'Cairo', value: 'Cairo' },
    { label: 'خط مخصص', value: 'custom' }
  ];

  videoQualities = [
    { label: '1080p (Full HD)', value: '1080p' },
    { label: '720p (HD)', value: '720p' },
    { label: '480p (SD)', value: '480p' }
  ];

  audioQualities = [
    { label: 'عالية (320 kbps)', value: 'high' },
    { label: 'متوسطة (192 kbps)', value: 'medium' },
    { label: 'منخفضة (128 kbps)', value: 'low' }
  ];

  subtitlePositions = [
    { label: 'أسفل', value: 'bottom' },
    { label: 'وسط', value: 'center' },
    { label: 'أعلى', value: 'top' }
  ];

  constructor(
    private fb: FormBuilder,
    private geminiService: GeminiService
  ) {}

  ngOnInit(): void {
    this.loadGeminiModels();
    this.initializeForm();
  }

  private loadGeminiModels(): void {
    const models = this.geminiService.getAvailableModels();
    this.geminiModels = models.map(model => ({
      label: model.name,
      value: model.id,
      description: model.description,
      isPaid: model.isPaid
    }));
  }

  initializeForm(): void {
    this.settingsForm = this.fb.group({
      geminiApiKey: ['', [Validators.required]],
      geminiModel: ['gemini-2.5-flash', [Validators.required]],
      fontSize: [18, [Validators.required, Validators.min(12), Validators.max(48)]],
      fontFamily: ['Al-QuranAlKareem', [Validators.required]],
      videoQuality: ['1080p', [Validators.required]],
      audioQuality: ['high', [Validators.required]],
      subtitlePosition: ['bottom', [Validators.required]],
      backgroundColor: ['#000000'],
      textColor: ['#FFFFFF'],
      outlineColor: ['#000000'],
      enableShadow: [true],
      shadowColor: ['#000000']
    });
  }

  onSave(): void {
    if (this.settingsForm.valid) {
      const settings: VideoSettings = this.settingsForm.value;
      this.settingsSaved.emit(settings);
      this.onHide();
    }
  }

  onHide(): void {
    this.visible = false;
    this.visibleChange.emit(false);
  }

  onFontUpload(event: any): void {
    const file = event.files[0];
    if (file) {
      // Handle custom font upload
      console.log('Custom font uploaded:', file.name);
    }
  }

  resetToDefaults(): void {
    this.initializeForm();
  }

  async testGeminiConnection(): Promise<void> {
    const apiKey = this.settingsForm.get('geminiApiKey')?.value;
    const model = this.settingsForm.get('geminiModel')?.value;

    if (!apiKey) {
      this.connectionTestResult = 'يرجى إدخال مفتاح API أولاً';
      return;
    }

    this.isTestingConnection = true;
    this.connectionTestResult = null;

    try {
      const isConnected = await this.geminiService.testConnection(apiKey, model);

      if (isConnected) {
        this.connectionTestResult = 'تم الاتصال بنجاح! ✅';

        // Check model availability
        const availability = await this.geminiService.checkModelAvailability(model, apiKey);
        if (!availability.available) {
          this.connectionTestResult = `تحذير: ${availability.error} ⚠️`;
        }
      } else {
        this.connectionTestResult = 'فشل في الاتصال. يرجى التحقق من مفتاح API ❌';
      }
    } catch (error: any) {
      this.connectionTestResult = `خطأ: ${error.message || 'فشل في الاتصال'} ❌`;
    } finally {
      this.isTestingConnection = false;
    }
  }

  onModelChange(): void {
    const selectedModel = this.settingsForm.get('geminiModel')?.value;
    const modelInfo = this.geminiService.getModelInfo(selectedModel);

    if (modelInfo?.isPaid) {
      this.connectionTestResult = 'تنبيه: هذا النموذج مدفوع وقد يتطلب اشتراك 💰';
    } else {
      this.connectionTestResult = null;
    }
  }
}
