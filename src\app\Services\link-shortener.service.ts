import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { SupabaseService } from './supabase.service';

export interface ShortenerSite {
  id: string;
  name: string;
  url: string;
  isActive: boolean;
  description: string;
}

export interface LinkShorteningStatus {
  isRequired: boolean;
  lastShortenedAt: Date | null;
  timeRemaining: number; // in milliseconds
  canUseApp: boolean;
  currentLink?: string;
  hasWatchedAd: boolean;
  adWatchedAt: Date | null;
  adTimeRemaining: number; // in milliseconds for 3-hour access
}

export interface ShorteningAttempt {
  timestamp: Date;
  siteId: string;
  success: boolean;
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class LinkShortenerService {
  private readonly STORAGE_KEY = 'linkShorteningStatus';
  private readonly ATTEMPTS_KEY = 'shorteningAttempts';
  private readonly SHORTENING_INTERVAL = 24 * 60 * 60 * 1000; // 24 hours
  private readonly AD_ACCESS_DURATION = 3 * 60 * 60 * 1000; // 3 hours

  private statusSubject = new BehaviorSubject<LinkShorteningStatus>(this.getInitialStatus());
  public status$ = this.statusSubject.asObservable();

  private shortenerSites: ShortenerSite[] = [
    {
      id: 'cuty',
      name: 'Cuty.io',
      url: 'https://cuty.io',
      isActive: true,
      description: 'موقع اختصار روابط سريع وموثوق'
    },
    {
      id: 'linkjust',
      name: 'LinkJust.com',
      url: 'https://linkjust.com',
      isActive: true,
      description: 'خدمة اختصار روابط مع عوائد جيدة'
    },
    {
      id: 'swiftlnx',
      name: 'SwiftLnx.com',
      url: 'https://swiftlnx.com',
      isActive: true,
      description: 'منصة اختصار روابط سريعة'
    }
  ];

  private currentLinkIndex = 0;

  constructor(private supabaseService: SupabaseService) {
    this.initializeService();
    this.startStatusUpdater();
    this.loadShortenerSitesFromDatabase();
  }

  private initializeService(): void {
    const status = this.getStoredStatus();
    this.updateStatus(status);
  }

  private startStatusUpdater(): void {
    // Update status every minute
    setInterval(() => {
      const currentStatus = this.statusSubject.value;
      const updatedStatus = this.calculateStatus(currentStatus);
      
      if (this.hasStatusChanged(currentStatus, updatedStatus)) {
        this.updateStatus(updatedStatus);
      }
    }, 60000); // Every minute
  }

  private getInitialStatus(): LinkShorteningStatus {
    return {
      isRequired: true,
      lastShortenedAt: null,
      timeRemaining: 0,
      canUseApp: false,
      hasWatchedAd: false,
      adWatchedAt: null,
      adTimeRemaining: 0
    };
  }

  private getStoredStatus(): LinkShorteningStatus {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        return {
          ...parsed,
          lastShortenedAt: parsed.lastShortenedAt ? new Date(parsed.lastShortenedAt) : null,
          adWatchedAt: parsed.adWatchedAt ? new Date(parsed.adWatchedAt) : null
        };
      }
    } catch (error) {
      console.error('Error loading stored status:', error);
    }
    
    return this.getInitialStatus();
  }

  private saveStatus(status: LinkShorteningStatus): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(status));
    } catch (error) {
      console.error('Error saving status:', error);
    }
  }

  private calculateStatus(currentStatus: LinkShorteningStatus): LinkShorteningStatus {
    const now = new Date();
    let canUseApp = false;
    let timeRemaining = 0;
    let adTimeRemaining = 0;

    // Check if user has shortened a link in the last 24 hours
    if (currentStatus.lastShortenedAt) {
      const timeSinceLastShorten = now.getTime() - currentStatus.lastShortenedAt.getTime();
      timeRemaining = Math.max(0, this.SHORTENING_INTERVAL - timeSinceLastShorten);
      
      if (timeRemaining > 0) {
        canUseApp = true;
      }
    }

    // Check if user has watched an ad in the last 3 hours
    if (currentStatus.adWatchedAt) {
      const timeSinceAdWatch = now.getTime() - currentStatus.adWatchedAt.getTime();
      adTimeRemaining = Math.max(0, this.AD_ACCESS_DURATION - timeSinceAdWatch);
      
      if (adTimeRemaining > 0) {
        canUseApp = true;
      }
    }

    return {
      ...currentStatus,
      timeRemaining,
      adTimeRemaining,
      canUseApp,
      isRequired: !canUseApp,
      currentLink: this.getCurrentLink()
    };
  }

  private hasStatusChanged(old: LinkShorteningStatus, updated: LinkShorteningStatus): boolean {
    return old.canUseApp !== updated.canUseApp ||
           old.timeRemaining !== updated.timeRemaining ||
           old.adTimeRemaining !== updated.adTimeRemaining ||
           old.currentLink !== updated.currentLink;
  }

  private updateStatus(status: LinkShorteningStatus): void {
    this.statusSubject.next(status);
    this.saveStatus(status);
  }

  public getAvailableSites(): ShortenerSite[] {
    return this.shortenerSites.filter(site => site.isActive);
  }

  public getCurrentLink(): string {
    const availableSites = this.getAvailableSites();
    if (availableSites.length === 0) {
      return '';
    }

    const site = availableSites[this.currentLinkIndex % availableSites.length];
    
    // Generate a unique link for the user to shorten
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(2, 8);
    
    return `${site.url}/shorten?ref=quranvidgen&t=${timestamp}&id=${randomId}`;
  }

  public switchToNextLink(): string {
    this.currentLinkIndex++;
    const newLink = this.getCurrentLink();
    
    const currentStatus = this.statusSubject.value;
    this.updateStatus({
      ...currentStatus,
      currentLink: newLink
    });
    
    return newLink;
  }

  public reportLinkProblem(): void {
    this.recordAttempt(false, 'User reported link problem');
    this.switchToNextLink();
  }

  public reportGeneralProblem(): void {
    this.recordAttempt(false, 'User reported general problem');
    this.switchToNextLink();
  }

  public confirmLinkShortened(): void {
    const now = new Date();
    const currentStatus = this.statusSubject.value;

    this.recordAttempt(true);

    // Save to database
    const userId = this.supabaseService.getCurrentUserId();
    const expiresAt = new Date(now.getTime() + this.SHORTENING_INTERVAL);

    this.supabaseService.createUserSession({
      user_id: userId,
      session_type: 'link_shortened',
      expires_at: expiresAt.toISOString(),
      metadata: { site_id: this.getCurrentSiteId() }
    }).subscribe({
      next: (session) => {
        console.log('Session saved to database:', session);
      },
      error: (error) => {
        console.error('Error saving session:', error);
      }
    });

    const updatedStatus: LinkShorteningStatus = {
      ...currentStatus,
      lastShortenedAt: now,
      isRequired: false,
      canUseApp: true,
      timeRemaining: this.SHORTENING_INTERVAL
    };

    this.updateStatus(updatedStatus);
  }

  public confirmAdWatched(): void {
    const now = new Date();
    const currentStatus = this.statusSubject.value;

    // Save to database
    const userId = this.supabaseService.getCurrentUserId();
    const expiresAt = new Date(now.getTime() + this.AD_ACCESS_DURATION);

    this.supabaseService.createUserSession({
      user_id: userId,
      session_type: 'ad_watched',
      expires_at: expiresAt.toISOString(),
      metadata: { ad_type: 'admob_rewarded' }
    }).subscribe({
      next: (session) => {
        console.log('Ad session saved to database:', session);
      },
      error: (error) => {
        console.error('Error saving ad session:', error);
      }
    });

    const updatedStatus: LinkShorteningStatus = {
      ...currentStatus,
      hasWatchedAd: true,
      adWatchedAt: now,
      isRequired: false,
      canUseApp: true,
      adTimeRemaining: this.AD_ACCESS_DURATION
    };

    this.updateStatus(updatedStatus);
  }

  private recordAttempt(success: boolean, error?: string): void {
    try {
      const attempts = this.getStoredAttempts();
      const newAttempt: ShorteningAttempt = {
        timestamp: new Date(),
        siteId: this.getAvailableSites()[this.currentLinkIndex % this.getAvailableSites().length]?.id || 'unknown',
        success,
        error
      };
      
      attempts.push(newAttempt);
      
      // Keep only last 50 attempts
      if (attempts.length > 50) {
        attempts.splice(0, attempts.length - 50);
      }
      
      localStorage.setItem(this.ATTEMPTS_KEY, JSON.stringify(attempts));
    } catch (error) {
      console.error('Error recording attempt:', error);
    }
  }

  private getStoredAttempts(): ShorteningAttempt[] {
    try {
      const stored = localStorage.getItem(this.ATTEMPTS_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        return parsed.map((attempt: any) => ({
          ...attempt,
          timestamp: new Date(attempt.timestamp)
        }));
      }
    } catch (error) {
      console.error('Error loading stored attempts:', error);
    }
    
    return [];
  }

  public getAttemptHistory(): ShorteningAttempt[] {
    return this.getStoredAttempts();
  }

  public getCurrentStatus(): LinkShorteningStatus {
    return this.statusSubject.value;
  }

  public forceRefreshStatus(): void {
    const currentStatus = this.statusSubject.value;
    const updatedStatus = this.calculateStatus(currentStatus);
    this.updateStatus(updatedStatus);
  }

  public formatTimeRemaining(milliseconds: number): string {
    if (milliseconds <= 0) {
      return 'انتهت المدة';
    }
    
    const hours = Math.floor(milliseconds / (1000 * 60 * 60));
    const minutes = Math.floor((milliseconds % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 0) {
      return `${hours} ساعة و ${minutes} دقيقة`;
    } else {
      return `${minutes} دقيقة`;
    }
  }

  // Admin functions for managing shortener sites
  public addShortenerSite(site: Omit<ShortenerSite, 'id'>): void {
    const newSite: ShortenerSite = {
      ...site,
      id: Date.now().toString()
    };
    
    this.shortenerSites.push(newSite);
    this.saveShortenerSites();
  }

  public updateShortenerSite(id: string, updates: Partial<ShortenerSite>): void {
    const index = this.shortenerSites.findIndex(site => site.id === id);
    if (index !== -1) {
      this.shortenerSites[index] = { ...this.shortenerSites[index], ...updates };
      this.saveShortenerSites();
    }
  }

  public removeShortenerSite(id: string): void {
    this.shortenerSites = this.shortenerSites.filter(site => site.id !== id);
    this.saveShortenerSites();
  }

  private saveShortenerSites(): void {
    try {
      localStorage.setItem('shortenerSites', JSON.stringify(this.shortenerSites));
    } catch (error) {
      console.error('Error saving shortener sites:', error);
    }
  }

  private loadShortenerSitesFromDatabase(): void {
    if (this.supabaseService.isConnectedToSupabase()) {
      this.supabaseService.getShortenerLinks().subscribe({
        next: (links) => {
          if (links.length > 0) {
            this.shortenerSites = links.map(link => ({
              id: link.id!.toString(),
              name: link.site_name,
              url: link.site_url,
              isActive: link.is_active,
              description: link.description || ''
            }));
          }
        },
        error: (error) => {
          console.error('Error loading shortener sites from database:', error);
        }
      });
    }
  }

  private getCurrentSiteId(): string {
    const availableSites = this.getAvailableSites();
    if (availableSites.length === 0) {
      return 'unknown';
    }
    return availableSites[this.currentLinkIndex % availableSites.length]?.id || 'unknown';
  }

  // Database integration methods
  public syncWithDatabase(): void {
    if (this.supabaseService.isConnectedToSupabase()) {
      const userId = this.supabaseService.getCurrentUserId();

      // Load active sessions from database
      this.supabaseService.getUserActiveSessions(userId).subscribe({
        next: (sessions) => {
          const currentStatus = this.statusSubject.value;
          let hasActiveSession = false;
          let lastShortenedAt: Date | null = null;
          let adWatchedAt: Date | null = null;

          sessions.forEach(session => {
            if (session.session_type === 'link_shortened') {
              hasActiveSession = true;
              lastShortenedAt = new Date(session.created_at!);
            } else if (session.session_type === 'ad_watched') {
              hasActiveSession = true;
              adWatchedAt = new Date(session.created_at!);
            }
          });

          if (hasActiveSession) {
            const updatedStatus = {
              ...currentStatus,
              lastShortenedAt,
              adWatchedAt,
              hasWatchedAd: !!adWatchedAt
            };

            this.updateStatus(this.calculateStatus(updatedStatus));
          }
        },
        error: (error) => {
          console.error('Error syncing with database:', error);
        }
      });
    }
  }
}
