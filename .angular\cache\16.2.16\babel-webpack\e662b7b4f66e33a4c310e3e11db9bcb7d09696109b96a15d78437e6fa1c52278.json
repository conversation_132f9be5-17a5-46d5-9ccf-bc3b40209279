{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/\\u062A\\u0637\\u0628\\u064A\\u0642 \\u0646\\u0627\\u0634\\u0631 \\u0627\\u064A\\u0627\\u062A \\u0642\\u0626\\u0627\\u0646\\u064A\\u0629/QuranVidGen/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject, Observable, of } from 'rxjs';\nimport { delay } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nexport let AdMobService = /*#__PURE__*/(() => {\n  class AdMobService {\n    constructor() {\n      this.AD_CONFIG = {\n        appId: 'ca-app-pub-4373910379376809~8789974713',\n        adUnitId: 'ca-app-pub-4373910379376809/7815804390',\n        testMode: false // Set to true for testing\n      };\n\n      this.adStatusSubject = new BehaviorSubject({\n        isLoaded: false,\n        isShowing: false,\n        totalShown: 0,\n        totalRewarded: 0\n      });\n      this.adStatus$ = this.adStatusSubject.asObservable();\n      this.isInitialized = false;\n      this.initializeAdMob();\n    }\n    initializeAdMob() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        try {\n          // For web implementation, we'll simulate AdMob behavior\n          // In a real mobile app, this would initialize the actual AdMob SDK\n          if (_this.isWebEnvironment()) {\n            yield _this.initializeWebAds();\n          } else {\n            yield _this.initializeMobileAds();\n          }\n          _this.isInitialized = true;\n          console.log('AdMob initialized successfully');\n        } catch (error) {\n          console.error('Failed to initialize AdMob:', error);\n        }\n      })();\n    }\n    isWebEnvironment() {\n      return typeof window !== 'undefined' && !window.hasOwnProperty('cordova');\n    }\n    initializeWebAds() {\n      return _asyncToGenerator(function* () {\n        // Load Google AdSense script for web ads\n        return new Promise((resolve, reject) => {\n          if (document.querySelector('script[src*=\"adsbygoogle\"]')) {\n            resolve();\n            return;\n          }\n          const script = document.createElement('script');\n          script.async = true;\n          script.src = 'https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js';\n          script.crossOrigin = 'anonymous';\n          script.onload = () => {\n            // Initialize AdSense\n            try {\n              window.adsbygoogle = window.adsbygoogle || [];\n              resolve();\n            } catch (error) {\n              reject(error);\n            }\n          };\n          script.onerror = () => {\n            reject(new Error('Failed to load AdSense script'));\n          };\n          document.head.appendChild(script);\n        });\n      })();\n    }\n    initializeMobileAds() {\n      return _asyncToGenerator(function* () {\n        // This would be implemented for mobile apps using Cordova/Capacitor\n        // For now, we'll simulate the behavior\n        return Promise.resolve();\n      })();\n    }\n    loadRewardedAd() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          if (!_this2.isInitialized) {\n            yield _this2.initializeAdMob();\n          }\n          // Simulate loading time\n          yield new Promise(resolve => setTimeout(resolve, 1000));\n          const currentStatus = _this2.adStatusSubject.value;\n          _this2.adStatusSubject.next({\n            ...currentStatus,\n            isLoaded: true\n          });\n          return true;\n        } catch (error) {\n          console.error('Failed to load rewarded ad:', error);\n          return false;\n        }\n      })();\n    }\n    showRewardedAd() {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        const currentStatus = _this3.adStatusSubject.value;\n        if (!currentStatus.isLoaded) {\n          return of({\n            success: false,\n            error: 'الإعلان غير محمل. يرجى المحاولة مرة أخرى.'\n          });\n        }\n        // Update status to showing\n        _this3.adStatusSubject.next({\n          ...currentStatus,\n          isShowing: true\n        });\n        if (_this3.isWebEnvironment()) {\n          return _this3.showWebRewardedAd();\n        } else {\n          return _this3.showMobileRewardedAd();\n        }\n      })();\n    }\n    showWebRewardedAd() {\n      return new Observable(observer => {\n        // Create a modal overlay for the ad\n        const adModal = this.createAdModal();\n        document.body.appendChild(adModal);\n        // Simulate ad duration (15 seconds)\n        const adDuration = 15000;\n        let timeRemaining = adDuration / 1000;\n        const countdownElement = adModal.querySelector('.countdown');\n        const skipButton = adModal.querySelector('.skip-button');\n        const closeButton = adModal.querySelector('.close-button');\n        // Update countdown\n        const countdownInterval = setInterval(() => {\n          timeRemaining--;\n          if (countdownElement) {\n            countdownElement.textContent = `${timeRemaining}`;\n          }\n          if (timeRemaining <= 0) {\n            clearInterval(countdownInterval);\n            if (skipButton) {\n              skipButton.style.display = 'block';\n              skipButton.textContent = 'إغلاق الإعلان';\n            }\n          }\n        }, 1000);\n        // Handle skip/close\n        const handleClose = rewarded => {\n          clearInterval(countdownInterval);\n          document.body.removeChild(adModal);\n          const currentStatus = this.adStatusSubject.value;\n          this.adStatusSubject.next({\n            ...currentStatus,\n            isLoaded: false,\n            isShowing: false,\n            lastShown: new Date(),\n            totalShown: currentStatus.totalShown + 1,\n            totalRewarded: rewarded ? currentStatus.totalRewarded + 1 : currentStatus.totalRewarded\n          });\n          observer.next({\n            success: true,\n            rewardEarned: rewarded,\n            rewardAmount: rewarded ? 1 : 0,\n            rewardType: 'access_time'\n          });\n          observer.complete();\n        };\n        if (skipButton) {\n          skipButton.addEventListener('click', () => handleClose(true));\n        }\n        if (closeButton) {\n          closeButton.addEventListener('click', () => handleClose(false));\n        }\n        // Auto-close after ad duration + 5 seconds\n        setTimeout(() => {\n          if (document.body.contains(adModal)) {\n            handleClose(true);\n          }\n        }, adDuration + 5000);\n      });\n    }\n    showMobileRewardedAd() {\n      // Simulate mobile ad behavior\n      return of({\n        success: true,\n        rewardEarned: true,\n        rewardAmount: 1,\n        rewardType: 'access_time'\n      }).pipe(delay(3000)); // Simulate 3 second ad\n    }\n\n    createAdModal() {\n      const modal = document.createElement('div');\n      modal.className = 'admob-modal';\n      modal.innerHTML = `\n      <div class=\"admob-overlay\">\n        <div class=\"admob-content\">\n          <div class=\"ad-header\">\n            <span class=\"ad-label\">إعلان</span>\n            <button class=\"close-button\" style=\"display: none;\">×</button>\n          </div>\n          \n          <div class=\"ad-body\">\n            <div class=\"ad-placeholder\">\n              <div class=\"ad-icon\">📺</div>\n              <h3>إعلان قصير</h3>\n              <p>شاهد هذا الإعلان للحصول على 3 ساعات من الاستخدام المجاني</p>\n              \n              <div class=\"countdown-container\">\n                <div class=\"countdown-circle\">\n                  <span class=\"countdown\">15</span>\n                </div>\n                <p>ثانية متبقية</p>\n              </div>\n              \n              <div class=\"ad-simulation\">\n                <div class=\"loading-bar\">\n                  <div class=\"loading-progress\"></div>\n                </div>\n                <p>جاري تحميل الإعلان...</p>\n              </div>\n            </div>\n          </div>\n          \n          <div class=\"ad-footer\">\n            <button class=\"skip-button\" style=\"display: none;\">تخطي الإعلان</button>\n            <p class=\"ad-info\">الإعلانات تساعدنا في تقديم الخدمة مجاناً</p>\n          </div>\n        </div>\n      </div>\n    `;\n      // Add styles\n      const style = document.createElement('style');\n      style.textContent = `\n      .admob-modal {\n        position: fixed;\n        top: 0;\n        left: 0;\n        width: 100vw;\n        height: 100vh;\n        z-index: 10000;\n        font-family: Arial, sans-serif;\n      }\n      \n      .admob-overlay {\n        width: 100%;\n        height: 100%;\n        background: rgba(0, 0, 0, 0.9);\n        display: flex;\n        align-items: center;\n        justify-content: center;\n      }\n      \n      .admob-content {\n        background: white;\n        border-radius: 12px;\n        max-width: 400px;\n        width: 90%;\n        overflow: hidden;\n        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);\n      }\n      \n      .ad-header {\n        background: #4285f4;\n        color: white;\n        padding: 1rem;\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n      }\n      \n      .ad-label {\n        font-weight: bold;\n        font-size: 0.9rem;\n      }\n      \n      .close-button {\n        background: none;\n        border: none;\n        color: white;\n        font-size: 1.5rem;\n        cursor: pointer;\n        padding: 0;\n        width: 30px;\n        height: 30px;\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n      }\n      \n      .ad-body {\n        padding: 2rem;\n        text-align: center;\n      }\n      \n      .ad-icon {\n        font-size: 3rem;\n        margin-bottom: 1rem;\n      }\n      \n      .ad-body h3 {\n        color: #333;\n        margin-bottom: 0.5rem;\n      }\n      \n      .ad-body p {\n        color: #666;\n        margin-bottom: 1.5rem;\n      }\n      \n      .countdown-container {\n        margin: 2rem 0;\n      }\n      \n      .countdown-circle {\n        width: 80px;\n        height: 80px;\n        border: 4px solid #4285f4;\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin: 0 auto 1rem;\n        position: relative;\n      }\n      \n      .countdown {\n        font-size: 1.5rem;\n        font-weight: bold;\n        color: #4285f4;\n      }\n      \n      .ad-simulation {\n        margin: 1.5rem 0;\n      }\n      \n      .loading-bar {\n        width: 100%;\n        height: 4px;\n        background: #e0e0e0;\n        border-radius: 2px;\n        overflow: hidden;\n        margin-bottom: 0.5rem;\n      }\n      \n      .loading-progress {\n        height: 100%;\n        background: #4285f4;\n        width: 0%;\n        animation: loadingProgress 15s linear forwards;\n      }\n      \n      @keyframes loadingProgress {\n        to { width: 100%; }\n      }\n      \n      .ad-footer {\n        background: #f5f5f5;\n        padding: 1rem;\n        text-align: center;\n      }\n      \n      .skip-button {\n        background: #4285f4;\n        color: white;\n        border: none;\n        padding: 0.75rem 1.5rem;\n        border-radius: 6px;\n        cursor: pointer;\n        font-weight: bold;\n        margin-bottom: 0.5rem;\n      }\n      \n      .ad-info {\n        font-size: 0.8rem;\n        color: #666;\n        margin: 0;\n      }\n    `;\n      modal.appendChild(style);\n      return modal;\n    }\n    getAdStatus() {\n      return this.adStatusSubject.value;\n    }\n    isAdLoaded() {\n      return this.adStatusSubject.value.isLoaded;\n    }\n    preloadAd() {\n      var _this4 = this;\n      return _asyncToGenerator(function* () {\n        yield _this4.loadRewardedAd();\n      })();\n    }\n    // Configuration methods\n    updateConfig(config) {\n      Object.assign(this.AD_CONFIG, config);\n    }\n    getConfig() {\n      return {\n        ...this.AD_CONFIG\n      };\n    }\n    // Analytics methods\n    getAdStats() {\n      const status = this.adStatusSubject.value;\n      const successRate = status.totalShown > 0 ? status.totalRewarded / status.totalShown * 100 : 0;\n      return {\n        totalShown: status.totalShown,\n        totalRewarded: status.totalRewarded,\n        successRate: Math.round(successRate)\n      };\n    }\n    static {\n      this.ɵfac = function AdMobService_Factory(t) {\n        return new (t || AdMobService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AdMobService,\n        factory: AdMobService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return AdMobService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}