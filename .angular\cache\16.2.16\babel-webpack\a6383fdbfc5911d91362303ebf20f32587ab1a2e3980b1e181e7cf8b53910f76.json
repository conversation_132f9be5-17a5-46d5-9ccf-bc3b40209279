{"ast": null, "code": "import { catchError, of } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let CallServerService = /*#__PURE__*/(() => {\n  class CallServerService {\n    constructor(httpClient) {\n      this.httpClient = httpClient;\n    }\n    Get(url) {\n      try {\n        return this.httpClient.get(url).pipe(catchError(error => of([])));\n      } catch (ex) {\n        throw ex?.error?.Description;\n      }\n    }\n    static {\n      this.ɵfac = function CallServerService_Factory(t) {\n        return new (t || CallServerService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: CallServerService,\n        factory: CallServerService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return CallServerService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}