// Supabase Configuration
// يرجى تعديل هذه القيم بمعلومات مشروع Supabase الخاص بك

export const SUPABASE_CONFIG = {
  // URL مشروع Supabase
  url: 'https://olguuglufpneewlhvpdj.supabase.co',
  
  // مفتاح API العام (anon key)
  anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9sZ3V1Z2x1ZnBuZWV3bGh2cGRqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM5MTI1NDAsImV4cCI6MjA2OTQ4ODU0MH0.NqoKkISvmv1DwRY4s3yCgDVmuiYucgtjjHhUljRbwdU'
};

// SQL Scripts لإنشاء الجداول المطلوبة
export const DATABASE_SCHEMA = {
  // جدول روابط الاختصار
  shortenerLinks: `
    CREATE TABLE IF NOT EXISTS shortener_links (
      id BIGSERIAL PRIMARY KEY,
      site_name VARCHAR(100) UNIQUE NOT NULL,
      site_url VARCHAR(500) NOT NULL,
      link_template VARCHAR(1000) NOT NULL,
      is_active BOOLEAN DEFAULT true,
      description TEXT,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    -- إنشاء فهرس للبحث السريع
    CREATE INDEX IF NOT EXISTS idx_shortener_links_active ON shortener_links(is_active);
    CREATE INDEX IF NOT EXISTS idx_shortener_links_site_name ON shortener_links(site_name);
  `,

  // جدول جلسات المستخدمين
  userSessions: `
    CREATE TABLE IF NOT EXISTS user_sessions (
      id BIGSERIAL PRIMARY KEY,
      user_id VARCHAR(100) NOT NULL,
      session_type VARCHAR(50) NOT NULL CHECK (session_type IN ('link_shortened', 'ad_watched')),
      expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      metadata JSONB
    );

    -- إنشاء فهارس للبحث السريع
    CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
    CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at);
    CREATE INDEX IF NOT EXISTS idx_user_sessions_type ON user_sessions(session_type);
  `,

  // جدول سجلات الأمان
  securityLogs: `
    CREATE TABLE IF NOT EXISTS security_logs (
      id BIGSERIAL PRIMARY KEY,
      user_id VARCHAR(100) NOT NULL,
      event_type VARCHAR(50) NOT NULL CHECK (event_type IN ('vpn_detected', 'adblocker_detected', 'dns_blocked', 'access_denied')),
      details JSONB,
      ip_address INET,
      user_agent TEXT,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    -- إنشاء فهارس للبحث السريع
    CREATE INDEX IF NOT EXISTS idx_security_logs_user_id ON security_logs(user_id);
    CREATE INDEX IF NOT EXISTS idx_security_logs_event_type ON security_logs(event_type);
    CREATE INDEX IF NOT EXISTS idx_security_logs_created_at ON security_logs(created_at);
  `,

  // جدول إعدادات التطبيق
  appSettings: `
    CREATE TABLE IF NOT EXISTS app_settings (
      id BIGSERIAL PRIMARY KEY,
      setting_key VARCHAR(100) UNIQUE NOT NULL,
      setting_value JSONB NOT NULL,
      description TEXT,
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    -- إنشاء فهرس فريد للمفاتيح
    CREATE UNIQUE INDEX IF NOT EXISTS idx_app_settings_key ON app_settings(setting_key);
  `,

  // دوال مساعدة للإحصائيات
  functions: `
    -- دالة للحصول على إحصائيات المستخدم
    CREATE OR REPLACE FUNCTION get_user_stats(user_id TEXT)
    RETURNS JSON AS $$
    DECLARE
      result JSON;
    BEGIN
      SELECT json_build_object(
        'total_sessions', COUNT(*),
        'link_shortened_count', COUNT(*) FILTER (WHERE session_type = 'link_shortened'),
        'ad_watched_count', COUNT(*) FILTER (WHERE session_type = 'ad_watched'),
        'last_activity', MAX(created_at),
        'security_events', (
          SELECT COUNT(*) FROM security_logs WHERE security_logs.user_id = get_user_stats.user_id
        )
      ) INTO result
      FROM user_sessions
      WHERE user_sessions.user_id = get_user_stats.user_id;
      
      RETURN result;
    END;
    $$ LANGUAGE plpgsql;

    -- دالة للحصول على إحصائيات التطبيق العامة
    CREATE OR REPLACE FUNCTION get_app_stats()
    RETURNS JSON AS $$
    DECLARE
      result JSON;
    BEGIN
      SELECT json_build_object(
        'total_users', COUNT(DISTINCT user_id),
        'total_sessions', COUNT(*),
        'active_sessions', COUNT(*) FILTER (WHERE expires_at > NOW()),
        'total_security_events', (SELECT COUNT(*) FROM security_logs),
        'active_shortener_links', (SELECT COUNT(*) FROM shortener_links WHERE is_active = true)
      ) INTO result
      FROM user_sessions;
      
      RETURN result;
    END;
    $$ LANGUAGE plpgsql;
  `,

  // إدراج البيانات الأولية
  initialData: `
    -- إدراج روابط الاختصار الافتراضية
    INSERT INTO shortener_links (site_name, site_url, link_template, description) VALUES
    ('Cuty.io', 'https://cuty.io', 'https://cuty.io/shorten?ref=quranvidgen&t={timestamp}&id={id}', 'موقع اختصار روابط سريع وموثوق'),
    ('LinkJust.com', 'https://linkjust.com', 'https://linkjust.com/shorten?ref=quranvidgen&t={timestamp}&id={id}', 'خدمة اختصار روابط مع عوائد جيدة'),
    ('SwiftLnx.com', 'https://swiftlnx.com', 'https://swiftlnx.com/shorten?ref=quranvidgen&t={timestamp}&id={id}', 'منصة اختصار روابط سريعة')
    ON CONFLICT (site_name) DO NOTHING;

    -- إدراج الإعدادات الافتراضية
    INSERT INTO app_settings (setting_key, setting_value, description) VALUES
    ('shortening_interval_hours', '"24"', 'عدد الساعات المطلوبة بين كل اختصار رابط'),
    ('ad_access_duration_hours', '"3"', 'عدد الساعات التي يحصل عليها المستخدم بعد مشاهدة إعلان'),
    ('security_strict_mode', 'true', 'تفعيل الوضع الصارم للأمان'),
    ('max_daily_attempts', '"5"', 'الحد الأقصى لمحاولات الاختصار اليومية'),
    ('admob_app_id', '"ca-app-pub-4373910379376809~8789974713"', 'معرف تطبيق AdMob'),
    ('admob_ad_unit_id', '"ca-app-pub-4373910379376809/7815804390"', 'معرف وحدة الإعلان في AdMob')
    ON CONFLICT (setting_key) DO NOTHING;
  `,

  // تنظيف البيانات القديمة
  cleanupJobs: `
    -- دالة لتنظيف الجلسات المنتهية الصلاحية
    CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
    RETURNS INTEGER AS $$
    DECLARE
      deleted_count INTEGER;
    BEGIN
      DELETE FROM user_sessions WHERE expires_at < NOW();
      GET DIAGNOSTICS deleted_count = ROW_COUNT;
      RETURN deleted_count;
    END;
    $$ LANGUAGE plpgsql;

    -- دالة لتنظيف سجلات الأمان القديمة (أكثر من 30 يوم)
    CREATE OR REPLACE FUNCTION cleanup_old_security_logs()
    RETURNS INTEGER AS $$
    DECLARE
      deleted_count INTEGER;
    BEGIN
      DELETE FROM security_logs WHERE created_at < NOW() - INTERVAL '30 days';
      GET DIAGNOSTICS deleted_count = ROW_COUNT;
      RETURN deleted_count;
    END;
    $$ LANGUAGE plpgsql;
  `
};

// تعليمات الإعداد
export const SETUP_INSTRUCTIONS = `
تعليمات إعداد قاعدة بيانات Supabase:

1. إنشاء مشروع جديد:
   - اذهب إلى https://supabase.com
   - أنشئ حساب جديد أو سجل الدخول
   - أنشئ مشروع جديد

2. الحصول على معلومات الاتصال:
   - اذهب إلى Settings > API
   - انسخ Project URL
   - انسخ anon/public key

3. تحديث ملف التكوين:
   - افتح src/app/config/supabase-config.ts
   - استبدل YOUR_SUPABASE_URL_HERE بـ Project URL
   - استبدل YOUR_SUPABASE_ANON_KEY_HERE بـ anon key

4. إنشاء الجداول:
   - اذهب إلى SQL Editor في Supabase
   - نفذ الاستعلامات الموجودة في DATABASE_SCHEMA
   - ابدأ بـ shortenerLinks، ثم userSessions، إلخ

5. إعداد الأمان:
   - اذهب إلى Authentication > Settings
   - فعّل Row Level Security (RLS) للجداول الحساسة
   - أضف السياسات المناسبة حسب الحاجة

6. اختبار الاتصال:
   - استخدم دالة testConnection() في SupabaseService
   - تأكد من عمل جميع العمليات بشكل صحيح

ملاحظات مهمة:
- احتفظ بمفاتيح API في مكان آمن
- لا تشارك مفاتيح API في الكود المصدري العام
- استخدم متغيرات البيئة في الإنتاج
- راجع سياسات الأمان بانتظام
`;
