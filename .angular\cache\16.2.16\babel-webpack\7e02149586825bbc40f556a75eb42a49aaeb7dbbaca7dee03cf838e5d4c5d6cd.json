{"ast": null, "code": "\"use strict\";\n\nvar __importDefault = this && this.__importDefault || function (mod) {\n  return mod && mod.__esModule ? mod : {\n    \"default\": mod\n  };\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nconst PostgrestQueryBuilder_1 = __importDefault(require(\"./PostgrestQueryBuilder\"));\nconst PostgrestFilterBuilder_1 = __importDefault(require(\"./PostgrestFilterBuilder\"));\nconst constants_1 = require(\"./constants\");\n/**\n * PostgREST client.\n *\n * @typeParam Database - Types for the schema from the [type\n * generator](https://supabase.com/docs/reference/javascript/next/typescript-support)\n *\n * @typeParam SchemaName - Postgres schema to switch to. Must be a string\n * literal, the same one passed to the constructor. If the schema is not\n * `\"public\"`, this must be supplied manually.\n */\nclass PostgrestClient {\n  // TODO: Add back shouldThrowOnError once we figure out the typings\n  /**\n   * Creates a PostgREST client.\n   *\n   * @param url - URL of the PostgREST endpoint\n   * @param options - Named parameters\n   * @param options.headers - Custom headers\n   * @param options.schema - Postgres schema to switch to\n   * @param options.fetch - Custom fetch\n   */\n  constructor(url, {\n    headers = {},\n    schema,\n    fetch\n  } = {}) {\n    this.url = url;\n    this.headers = Object.assign(Object.assign({}, constants_1.DEFAULT_HEADERS), headers);\n    this.schemaName = schema;\n    this.fetch = fetch;\n  }\n  /**\n   * Perform a query on a table or a view.\n   *\n   * @param relation - The table or view name to query\n   */\n  from(relation) {\n    const url = new URL(`${this.url}/${relation}`);\n    return new PostgrestQueryBuilder_1.default(url, {\n      headers: Object.assign({}, this.headers),\n      schema: this.schemaName,\n      fetch: this.fetch\n    });\n  }\n  /**\n   * Select a schema to query or perform an function (rpc) call.\n   *\n   * The schema needs to be on the list of exposed schemas inside Supabase.\n   *\n   * @param schema - The schema to query\n   */\n  schema(schema) {\n    return new PostgrestClient(this.url, {\n      headers: this.headers,\n      schema,\n      fetch: this.fetch\n    });\n  }\n  /**\n   * Perform a function call.\n   *\n   * @param fn - The function name to call\n   * @param args - The arguments to pass to the function call\n   * @param options - Named parameters\n   * @param options.head - When set to `true`, `data` will not be returned.\n   * Useful if you only need the count.\n   * @param options.get - When set to `true`, the function will be called with\n   * read-only access mode.\n   * @param options.count - Count algorithm to use to count rows returned by the\n   * function. Only applicable for [set-returning\n   * functions](https://www.postgresql.org/docs/current/functions-srf.html).\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   */\n  rpc(fn, args = {}, {\n    head = false,\n    get = false,\n    count\n  } = {}) {\n    let method;\n    const url = new URL(`${this.url}/rpc/${fn}`);\n    let body;\n    if (head || get) {\n      method = head ? 'HEAD' : 'GET';\n      Object.entries(args)\n      // params with undefined value needs to be filtered out, otherwise it'll\n      // show up as `?param=undefined`\n      .filter(([_, value]) => value !== undefined)\n      // array values need special syntax\n      .map(([name, value]) => [name, Array.isArray(value) ? `{${value.join(',')}}` : `${value}`]).forEach(([name, value]) => {\n        url.searchParams.append(name, value);\n      });\n    } else {\n      method = 'POST';\n      body = args;\n    }\n    const headers = Object.assign({}, this.headers);\n    if (count) {\n      headers['Prefer'] = `count=${count}`;\n    }\n    return new PostgrestFilterBuilder_1.default({\n      method,\n      url,\n      headers,\n      schema: this.schemaName,\n      body,\n      fetch: this.fetch,\n      allowEmpty: false\n    });\n  }\n}\nexports.default = PostgrestClient;\n//# sourceMappingURL=PostgrestClient.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}