"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2022 Google LLC. https://angular.io/
 * License: MIT
 */!function(e){const t=e.performance;function n(e){t&&t.mark&&t.mark(e)}function r(e,n){t&&t.measure&&t.measure(e,n)}n("Zone");const s=e.__Zone_symbol_prefix||"__zone_symbol__";function o(e){return s+e}const i=!0===e[o("forceDuplicateZoneCheck")];if(e.Zone){if(i||"function"!=typeof e.Zone.__symbol__)throw new Error("Zone already loaded.");return e.Zone}class a{static{this.__symbol__=o}static assertZonePatched(){if(e.Promise!==O.ZoneAwarePromise)throw new Error("Zone.js has detected that ZoneAwarePromise `(window|global).Promise` has been overwritten.\nMost likely cause is that a Promise polyfill has been loaded after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. If you must load one, do so before loading zone.js.)")}static get root(){let e=a.current;for(;e.parent;)e=e.parent;return e}static get current(){return C.zone}static get currentTask(){return F}static __load_patch(t,s,o=!1){if(O.hasOwnProperty(t)){if(!o&&i)throw Error("Already loaded patch: "+t)}else if(!e["__Zone_disable_"+t]){const o="Zone:"+t;n(o),O[t]=s(e,a,I),r(o,o)}}get parent(){return this._parent}get name(){return this._name}constructor(e,t){this._parent=e,this._name=t?t.name||"unnamed":"<root>",this._properties=t&&t.properties||{},this._zoneDelegate=new l(this,this._parent&&this._parent._zoneDelegate,t)}get(e){const t=this.getZoneWith(e);if(t)return t._properties[e]}getZoneWith(e){let t=this;for(;t;){if(t._properties.hasOwnProperty(e))return t;t=t._parent}return null}fork(e){if(!e)throw new Error("ZoneSpec required!");return this._zoneDelegate.fork(this,e)}wrap(e,t){if("function"!=typeof e)throw new Error("Expecting function got: "+e);const n=this._zoneDelegate.intercept(this,e,t),r=this;return function(){return r.runGuarded(n,this,arguments,t)}}run(e,t,n,r){C={parent:C,zone:this};try{return this._zoneDelegate.invoke(this,e,t,n,r)}finally{C=C.parent}}runGuarded(e,t=null,n,r){C={parent:C,zone:this};try{try{return this._zoneDelegate.invoke(this,e,t,n,r)}catch(e){if(this._zoneDelegate.handleError(this,e))throw e}}finally{C=C.parent}}runTask(e,t,n){if(e.zone!=this)throw new Error("A task can only be run in the zone of creation! (Creation: "+(e.zone||g).name+"; Execution: "+this.name+")");if(e.state===S&&(e.type===D||e.type===A))return;const r=e.state!=v;r&&e._transitionTo(v,E),e.runCount++;const s=F;F=e,C={parent:C,zone:this};try{e.type==A&&e.data&&!e.data.isPeriodic&&(e.cancelFn=void 0);try{return this._zoneDelegate.invokeTask(this,e,t,n)}catch(e){if(this._zoneDelegate.handleError(this,e))throw e}}finally{e.state!==S&&e.state!==w&&(e.type==D||e.data&&e.data.isPeriodic?r&&e._transitionTo(E,v):(e.runCount=0,this._updateTaskCount(e,-1),r&&e._transitionTo(S,v,S))),C=C.parent,F=s}}scheduleTask(e){if(e.zone&&e.zone!==this){let t=this;for(;t;){if(t===e.zone)throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${e.zone.name}`);t=t.parent}}e._transitionTo(b,S);const t=[];e._zoneDelegates=t,e._zone=this;try{e=this._zoneDelegate.scheduleTask(this,e)}catch(t){throw e._transitionTo(w,b,S),this._zoneDelegate.handleError(this,t),t}return e._zoneDelegates===t&&this._updateTaskCount(e,1),e.state==b&&e._transitionTo(E,b),e}scheduleMicroTask(e,t,n,r){return this.scheduleTask(new u(Z,e,t,n,r,void 0))}scheduleMacroTask(e,t,n,r,s){return this.scheduleTask(new u(A,e,t,n,r,s))}scheduleEventTask(e,t,n,r,s){return this.scheduleTask(new u(D,e,t,n,r,s))}cancelTask(e){if(e.zone!=this)throw new Error("A task can only be cancelled in the zone of creation! (Creation: "+(e.zone||g).name+"; Execution: "+this.name+")");if(e.state===E||e.state===v){e._transitionTo(P,E,v);try{this._zoneDelegate.cancelTask(this,e)}catch(t){throw e._transitionTo(w,P),this._zoneDelegate.handleError(this,t),t}return this._updateTaskCount(e,-1),e._transitionTo(S,P),e.runCount=0,e}}_updateTaskCount(e,t){const n=e._zoneDelegates;-1==t&&(e._zoneDelegates=null);for(let r=0;r<n.length;r++)n[r]._updateTaskCount(e.type,t)}}const c={name:"",onHasTask:(e,t,n,r)=>e.hasTask(n,r),onScheduleTask:(e,t,n,r)=>e.scheduleTask(n,r),onInvokeTask:(e,t,n,r,s,o)=>e.invokeTask(n,r,s,o),onCancelTask:(e,t,n,r)=>e.cancelTask(n,r)};class l{constructor(e,t,n){this._taskCounts={microTask:0,macroTask:0,eventTask:0},this.zone=e,this._parentDelegate=t,this._forkZS=n&&(n&&n.onFork?n:t._forkZS),this._forkDlgt=n&&(n.onFork?t:t._forkDlgt),this._forkCurrZone=n&&(n.onFork?this.zone:t._forkCurrZone),this._interceptZS=n&&(n.onIntercept?n:t._interceptZS),this._interceptDlgt=n&&(n.onIntercept?t:t._interceptDlgt),this._interceptCurrZone=n&&(n.onIntercept?this.zone:t._interceptCurrZone),this._invokeZS=n&&(n.onInvoke?n:t._invokeZS),this._invokeDlgt=n&&(n.onInvoke?t:t._invokeDlgt),this._invokeCurrZone=n&&(n.onInvoke?this.zone:t._invokeCurrZone),this._handleErrorZS=n&&(n.onHandleError?n:t._handleErrorZS),this._handleErrorDlgt=n&&(n.onHandleError?t:t._handleErrorDlgt),this._handleErrorCurrZone=n&&(n.onHandleError?this.zone:t._handleErrorCurrZone),this._scheduleTaskZS=n&&(n.onScheduleTask?n:t._scheduleTaskZS),this._scheduleTaskDlgt=n&&(n.onScheduleTask?t:t._scheduleTaskDlgt),this._scheduleTaskCurrZone=n&&(n.onScheduleTask?this.zone:t._scheduleTaskCurrZone),this._invokeTaskZS=n&&(n.onInvokeTask?n:t._invokeTaskZS),this._invokeTaskDlgt=n&&(n.onInvokeTask?t:t._invokeTaskDlgt),this._invokeTaskCurrZone=n&&(n.onInvokeTask?this.zone:t._invokeTaskCurrZone),this._cancelTaskZS=n&&(n.onCancelTask?n:t._cancelTaskZS),this._cancelTaskDlgt=n&&(n.onCancelTask?t:t._cancelTaskDlgt),this._cancelTaskCurrZone=n&&(n.onCancelTask?this.zone:t._cancelTaskCurrZone),this._hasTaskZS=null,this._hasTaskDlgt=null,this._hasTaskDlgtOwner=null,this._hasTaskCurrZone=null;const r=n&&n.onHasTask;(r||t&&t._hasTaskZS)&&(this._hasTaskZS=r?n:c,this._hasTaskDlgt=t,this._hasTaskDlgtOwner=this,this._hasTaskCurrZone=e,n.onScheduleTask||(this._scheduleTaskZS=c,this._scheduleTaskDlgt=t,this._scheduleTaskCurrZone=this.zone),n.onInvokeTask||(this._invokeTaskZS=c,this._invokeTaskDlgt=t,this._invokeTaskCurrZone=this.zone),n.onCancelTask||(this._cancelTaskZS=c,this._cancelTaskDlgt=t,this._cancelTaskCurrZone=this.zone))}fork(e,t){return this._forkZS?this._forkZS.onFork(this._forkDlgt,this.zone,e,t):new a(e,t)}intercept(e,t,n){return this._interceptZS?this._interceptZS.onIntercept(this._interceptDlgt,this._interceptCurrZone,e,t,n):t}invoke(e,t,n,r,s){return this._invokeZS?this._invokeZS.onInvoke(this._invokeDlgt,this._invokeCurrZone,e,t,n,r,s):t.apply(n,r)}handleError(e,t){return!this._handleErrorZS||this._handleErrorZS.onHandleError(this._handleErrorDlgt,this._handleErrorCurrZone,e,t)}scheduleTask(e,t){let n=t;if(this._scheduleTaskZS)this._hasTaskZS&&n._zoneDelegates.push(this._hasTaskDlgtOwner),n=this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt,this._scheduleTaskCurrZone,e,t),n||(n=t);else if(t.scheduleFn)t.scheduleFn(t);else{if(t.type!=Z)throw new Error("Task is missing scheduleFn.");k(t)}return n}invokeTask(e,t,n,r){return this._invokeTaskZS?this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt,this._invokeTaskCurrZone,e,t,n,r):t.callback.apply(n,r)}cancelTask(e,t){let n;if(this._cancelTaskZS)n=this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt,this._cancelTaskCurrZone,e,t);else{if(!t.cancelFn)throw Error("Task is not cancelable");n=t.cancelFn(t)}return n}hasTask(e,t){try{this._hasTaskZS&&this._hasTaskZS.onHasTask(this._hasTaskDlgt,this._hasTaskCurrZone,e,t)}catch(t){this.handleError(e,t)}}_updateTaskCount(e,t){const n=this._taskCounts,r=n[e],s=n[e]=r+t;if(s<0)throw new Error("More tasks executed then were scheduled.");0!=r&&0!=s||this.hasTask(this.zone,{microTask:n.microTask>0,macroTask:n.macroTask>0,eventTask:n.eventTask>0,change:e})}}class u{constructor(t,n,r,s,o,i){if(this._zone=null,this.runCount=0,this._zoneDelegates=null,this._state="notScheduled",this.type=t,this.source=n,this.data=s,this.scheduleFn=o,this.cancelFn=i,!r)throw new Error("callback is not defined");this.callback=r;const a=this;this.invoke=t===D&&s&&s.useG?u.invokeTask:function(){return u.invokeTask.call(e,a,this,arguments)}}static invokeTask(e,t,n){e||(e=this),j++;try{return e.runCount++,e.zone.runTask(e,t,n)}finally{1==j&&y(),j--}}get zone(){return this._zone}get state(){return this._state}cancelScheduleRequest(){this._transitionTo(S,b)}_transitionTo(e,t,n){if(this._state!==t&&this._state!==n)throw new Error(`${this.type} '${this.source}': can not transition to '${e}', expecting state '${t}'${n?" or '"+n+"'":""}, was '${this._state}'.`);this._state=e,e==S&&(this._zoneDelegates=null)}toString(){return this.data&&void 0!==this.data.handleId?this.data.handleId.toString():Object.prototype.toString.call(this)}toJSON(){return{type:this.type,state:this.state,source:this.source,zone:this.zone.name,runCount:this.runCount}}}const h=o("setTimeout"),p=o("Promise"),d=o("then");let f,_=[],m=!1;function T(t){if(f||e[p]&&(f=e[p].resolve(0)),f){let e=f[d];e||(e=f.then),e.call(f,t)}else e[h](t,0)}function k(e){0===j&&0===_.length&&T(y),e&&_.push(e)}function y(){if(!m){for(m=!0;_.length;){const e=_;_=[];for(let t=0;t<e.length;t++){const n=e[t];try{n.zone.runTask(n,null,null)}catch(e){I.onUnhandledError(e)}}}I.microtaskDrainDone(),m=!1}}const g={name:"NO ZONE"},S="notScheduled",b="scheduling",E="scheduled",v="running",P="canceling",w="unknown",Z="microTask",A="macroTask",D="eventTask",O={},I={symbol:o,currentZoneFrame:()=>C,onUnhandledError:R,microtaskDrainDone:R,scheduleMicroTask:k,showUncaughtError:()=>!a[o("ignoreConsoleErrorUncaughtError")],patchEventTarget:()=>[],patchOnProperties:R,patchMethod:()=>R,bindArguments:()=>[],patchThen:()=>R,patchMacroTask:()=>R,patchEventPrototype:()=>R,isIEOrEdge:()=>!1,getGlobalObjects:()=>{},ObjectDefineProperty:()=>R,ObjectGetOwnPropertyDescriptor:()=>{},ObjectCreate:()=>{},ArraySlice:()=>[],patchClass:()=>R,wrapWithCurrentZone:()=>R,filterProperties:()=>[],attachOriginToPatched:()=>R,_redefineProperty:()=>R,patchCallbacks:()=>R,nativeScheduleMicroTask:T};let C={parent:null,zone:new a(null,null)},F=null,j=0;function R(){}r("Zone","Zone"),e.Zone=a}("undefined"!=typeof window&&window||"undefined"!=typeof self&&self||global);const ObjectGetOwnPropertyDescriptor=Object.getOwnPropertyDescriptor,ObjectDefineProperty=Object.defineProperty,ObjectGetPrototypeOf=Object.getPrototypeOf,ArraySlice=Array.prototype.slice,ADD_EVENT_LISTENER_STR="addEventListener",REMOVE_EVENT_LISTENER_STR="removeEventListener";Zone.__symbol__("addEventListener"),Zone.__symbol__("removeEventListener");const TRUE_STR="true",FALSE_STR="false",ZONE_SYMBOL_PREFIX=Zone.__symbol__("");function wrapWithCurrentZone(e,t){return Zone.current.wrap(e,t)}function scheduleMacroTaskWithCurrentZone(e,t,n,r,s){return Zone.current.scheduleMacroTask(e,t,n,r,s)}const zoneSymbol=Zone.__symbol__,isWindowExists="undefined"!=typeof window,internalWindow=isWindowExists?window:void 0,_global=isWindowExists&&internalWindow||"object"==typeof self&&self||global,REMOVE_ATTRIBUTE="removeAttribute";function bindArguments(e,t){for(let n=e.length-1;n>=0;n--)"function"==typeof e[n]&&(e[n]=wrapWithCurrentZone(e[n],t+"_"+n));return e}function isPropertyWritable(e){return!e||!1!==e.writable&&!("function"==typeof e.get&&void 0===e.set)}const isWebWorker="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope,isNode=!("nw"in _global)&&void 0!==_global.process&&"[object process]"==={}.toString.call(_global.process),isBrowser=!isNode&&!isWebWorker&&!(!isWindowExists||!internalWindow.HTMLElement),isMix=void 0!==_global.process&&"[object process]"==={}.toString.call(_global.process)&&!isWebWorker&&!(!isWindowExists||!internalWindow.HTMLElement),zoneSymbolEventNames$1={},wrapFn=function(e){if(!(e=e||_global.event))return;let t=zoneSymbolEventNames$1[e.type];t||(t=zoneSymbolEventNames$1[e.type]=zoneSymbol("ON_PROPERTY"+e.type));const n=this||e.target||_global,r=n[t];let s;return isBrowser&&n===internalWindow&&"error"===e.type?(s=r&&r.call(this,e.message,e.filename,e.lineno,e.colno,e.error),!0===s&&e.preventDefault()):(s=r&&r.apply(this,arguments),null==s||s||e.preventDefault()),s};function patchProperty(e,t,n){let r=ObjectGetOwnPropertyDescriptor(e,t);if(!r&&n&&ObjectGetOwnPropertyDescriptor(n,t)&&(r={enumerable:!0,configurable:!0}),!r||!r.configurable)return;const s=zoneSymbol("on"+t+"patched");if(e.hasOwnProperty(s)&&e[s])return;delete r.writable,delete r.value;const o=r.get,i=r.set,a=t.slice(2);let c=zoneSymbolEventNames$1[a];c||(c=zoneSymbolEventNames$1[a]=zoneSymbol("ON_PROPERTY"+a)),r.set=function(t){let n=this;n||e!==_global||(n=_global),n&&("function"==typeof n[c]&&n.removeEventListener(a,wrapFn),i&&i.call(n,null),n[c]=t,"function"==typeof t&&n.addEventListener(a,wrapFn,!1))},r.get=function(){let n=this;if(n||e!==_global||(n=_global),!n)return null;const s=n[c];if(s)return s;if(o){let e=o.call(this);if(e)return r.set.call(this,e),"function"==typeof n[REMOVE_ATTRIBUTE]&&n.removeAttribute(t),e}return null},ObjectDefineProperty(e,t,r),e[s]=!0}function patchOnProperties(e,t,n){if(t)for(let r=0;r<t.length;r++)patchProperty(e,"on"+t[r],n);else{const t=[];for(const n in e)"on"==n.slice(0,2)&&t.push(n);for(let r=0;r<t.length;r++)patchProperty(e,t[r],n)}}function copySymbolProperties(e,t){"function"==typeof Object.getOwnPropertySymbols&&Object.getOwnPropertySymbols(e).forEach((n=>{const r=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,{get:function(){return e[n]},set:function(t){(!r||r.writable&&"function"==typeof r.set)&&(e[n]=t)},enumerable:!r||r.enumerable,configurable:!r||r.configurable})}))}zoneSymbol("originalInstance");let shouldCopySymbolProperties=!1;function setShouldCopySymbolProperties(e){shouldCopySymbolProperties=e}function patchMethod(e,t,n){let r=e;for(;r&&!r.hasOwnProperty(t);)r=ObjectGetPrototypeOf(r);!r&&e[t]&&(r=e);const s=zoneSymbol(t);let o=null;if(r&&(!(o=r[s])||!r.hasOwnProperty(s))&&(o=r[s]=r[t],isPropertyWritable(r&&ObjectGetOwnPropertyDescriptor(r,t)))){const e=n(o,s,t);r[t]=function(){return e(this,arguments)},attachOriginToPatched(r[t],o),shouldCopySymbolProperties&&copySymbolProperties(o,r[t])}return o}function patchMacroTask(e,t,n){let r=null;function s(e){const t=e.data;return t.args[t.cbIdx]=function(){e.invoke.apply(this,arguments)},r.apply(t.target,t.args),e}r=patchMethod(e,t,(e=>function(t,r){const o=n(t,r);return o.cbIdx>=0&&"function"==typeof r[o.cbIdx]?scheduleMacroTaskWithCurrentZone(o.name,r[o.cbIdx],o,s):e.apply(t,r)}))}function patchMicroTask(e,t,n){let r=null;function s(e){const t=e.data;return t.args[t.cbIdx]=function(){e.invoke.apply(this,arguments)},r.apply(t.target,t.args),e}r=patchMethod(e,t,(e=>function(t,r){const o=n(t,r);return o.cbIdx>=0&&"function"==typeof r[o.cbIdx]?Zone.current.scheduleMicroTask(o.name,r[o.cbIdx],o,s):e.apply(t,r)}))}function attachOriginToPatched(e,t){e[zoneSymbol("OriginalDelegate")]=t}Zone.__load_patch("ZoneAwarePromise",((e,t,n)=>{const r=Object.getOwnPropertyDescriptor,s=Object.defineProperty,o=n.symbol,i=[],a=!0===e[o("DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION")],c=o("Promise"),l=o("then"),u="__creationTrace__";n.onUnhandledError=e=>{if(n.showUncaughtError()){const t=e&&e.rejection;t?console.error("Unhandled Promise rejection:",t instanceof Error?t.message:t,"; Zone:",e.zone.name,"; Task:",e.task&&e.task.source,"; Value:",t,t instanceof Error?t.stack:void 0):console.error(e)}},n.microtaskDrainDone=()=>{for(;i.length;){const e=i.shift();try{e.zone.runGuarded((()=>{if(e.throwOriginal)throw e.rejection;throw e}))}catch(e){p(e)}}};const h=o("unhandledPromiseRejectionHandler");function p(e){n.onUnhandledError(e);try{const n=t[h];"function"==typeof n&&n.call(this,e)}catch(e){}}function d(e){return e&&e.then}function f(e){return e}function _(e){return M.reject(e)}const m=o("state"),T=o("value"),k=o("finally"),y=o("parentPromiseValue"),g=o("parentPromiseState"),S="Promise.then",b=null,E=!0,v=!1,P=0;function w(e,t){return n=>{try{O(e,t,n)}catch(t){O(e,!1,t)}}}const Z=function(){let e=!1;return function t(n){return function(){e||(e=!0,n.apply(null,arguments))}}},A="Promise resolved with itself",D=o("currentTaskTrace");function O(e,r,o){const c=Z();if(e===o)throw new TypeError(A);if(e[m]===b){let l=null;try{"object"!=typeof o&&"function"!=typeof o||(l=o&&o.then)}catch(t){return c((()=>{O(e,!1,t)}))(),e}if(r!==v&&o instanceof M&&o.hasOwnProperty(m)&&o.hasOwnProperty(T)&&o[m]!==b)C(o),O(e,o[m],o[T]);else if(r!==v&&"function"==typeof l)try{l.call(o,c(w(e,r)),c(w(e,!1)))}catch(t){c((()=>{O(e,!1,t)}))()}else{e[m]=r;const c=e[T];if(e[T]=o,e[k]===k&&r===E&&(e[m]=e[g],e[T]=e[y]),r===v&&o instanceof Error){const e=t.currentTask&&t.currentTask.data&&t.currentTask.data[u];e&&s(o,D,{configurable:!0,enumerable:!1,writable:!0,value:e})}for(let t=0;t<c.length;)F(e,c[t++],c[t++],c[t++],c[t++]);if(0==c.length&&r==v){e[m]=P;let r=o;try{throw new Error("Uncaught (in promise): "+function e(t){return t&&t.toString===Object.prototype.toString?(t.constructor&&t.constructor.name||"")+": "+JSON.stringify(t):t?t.toString():Object.prototype.toString.call(t)}(o)+(o&&o.stack?"\n"+o.stack:""))}catch(e){r=e}a&&(r.throwOriginal=!0),r.rejection=o,r.promise=e,r.zone=t.current,r.task=t.currentTask,i.push(r),n.scheduleMicroTask()}}}return e}const I=o("rejectionHandledHandler");function C(e){if(e[m]===P){try{const n=t[I];n&&"function"==typeof n&&n.call(this,{rejection:e[T],promise:e})}catch(e){}e[m]=v;for(let t=0;t<i.length;t++)e===i[t].promise&&i.splice(t,1)}}function F(e,t,n,r,s){C(e);const o=e[m],i=o?"function"==typeof r?r:f:"function"==typeof s?s:_;t.scheduleMicroTask(S,(()=>{try{const r=e[T],s=!!n&&k===n[k];s&&(n[y]=r,n[g]=o);const a=t.run(i,void 0,s&&i!==_&&i!==f?[]:[r]);O(n,!0,a)}catch(e){O(n,!1,e)}}),n)}const j=function(){},R=e.AggregateError;class M{static toString(){return"function ZoneAwarePromise() { [native code] }"}static resolve(e){return O(new this(null),E,e)}static reject(e){return O(new this(null),v,e)}static any(e){if(!e||"function"!=typeof e[Symbol.iterator])return Promise.reject(new R([],"All promises were rejected"));const t=[];let n=0;try{for(let r of e)n++,t.push(M.resolve(r))}catch(e){return Promise.reject(new R([],"All promises were rejected"))}if(0===n)return Promise.reject(new R([],"All promises were rejected"));let r=!1;const s=[];return new M(((e,o)=>{for(let i=0;i<t.length;i++)t[i].then((t=>{r||(r=!0,e(t))}),(e=>{s.push(e),n--,0===n&&(r=!0,o(new R(s,"All promises were rejected")))}))}))}static race(e){let t,n,r=new this(((e,r)=>{t=e,n=r}));function s(e){t(e)}function o(e){n(e)}for(let t of e)d(t)||(t=this.resolve(t)),t.then(s,o);return r}static all(e){return M.allWithCallback(e)}static allSettled(e){return(this&&this.prototype instanceof M?this:M).allWithCallback(e,{thenCallback:e=>({status:"fulfilled",value:e}),errorCallback:e=>({status:"rejected",reason:e})})}static allWithCallback(e,t){let n,r,s=new this(((e,t)=>{n=e,r=t})),o=2,i=0;const a=[];for(let s of e){d(s)||(s=this.resolve(s));const e=i;try{s.then((r=>{a[e]=t?t.thenCallback(r):r,o--,0===o&&n(a)}),(s=>{t?(a[e]=t.errorCallback(s),o--,0===o&&n(a)):r(s)}))}catch(e){r(e)}o++,i++}return o-=2,0===o&&n(a),s}constructor(e){const t=this;if(!(t instanceof M))throw new Error("Must be an instanceof Promise.");t[m]=b,t[T]=[];try{const n=Z();e&&e(n(w(t,E)),n(w(t,v)))}catch(e){O(t,!1,e)}}get[Symbol.toStringTag](){return"Promise"}get[Symbol.species](){return M}then(e,n){let r=this.constructor?.[Symbol.species];r&&"function"==typeof r||(r=this.constructor||M);const s=new r(j),o=t.current;return this[m]==b?this[T].push(o,s,e,n):F(this,o,s,e,n),s}catch(e){return this.then(null,e)}finally(e){let n=this.constructor?.[Symbol.species];n&&"function"==typeof n||(n=M);const r=new n(j);r[k]=k;const s=t.current;return this[m]==b?this[T].push(s,r,e,e):F(this,s,r,e,e),r}}M.resolve=M.resolve,M.reject=M.reject,M.race=M.race,M.all=M.all;const z=e[c]=e.Promise;e.Promise=M;const x=o("thenPatched");function N(e){const t=e.prototype,n=r(t,"then");if(n&&(!1===n.writable||!n.configurable))return;const s=t.then;t[l]=s,e.prototype.then=function(e,t){return new M(((e,t)=>{s.call(this,e,t)})).then(e,t)},e[x]=!0}return n.patchThen=N,z&&(N(z),patchMethod(e,"fetch",(e=>function t(e){return function(t,n){let r=e.apply(t,n);if(r instanceof M)return r;let s=r.constructor;return s[x]||N(s),r}}(e)))),Promise[t.__symbol__("uncaughtPromiseErrors")]=i,M})),Zone.__load_patch("toString",(e=>{const t=Function.prototype.toString,n=zoneSymbol("OriginalDelegate"),r=zoneSymbol("Promise"),s=zoneSymbol("Error"),o=function o(){if("function"==typeof this){const o=this[n];if(o)return"function"==typeof o?t.call(o):Object.prototype.toString.call(o);if(this===Promise){const n=e[r];if(n)return t.call(n)}if(this===Error){const n=e[s];if(n)return t.call(n)}}return t.call(this)};o[n]=t,Function.prototype.toString=o;const i=Object.prototype.toString;Object.prototype.toString=function(){return"function"==typeof Promise&&this instanceof Promise?"[object Promise]":i.call(this)}})),Zone.__load_patch("node_util",((e,t,n)=>{n.patchOnProperties=patchOnProperties,n.patchMethod=patchMethod,n.bindArguments=bindArguments,n.patchMacroTask=patchMacroTask,setShouldCopySymbolProperties(!0)}));let passiveSupported=!1;if("undefined"!=typeof window)try{const e=Object.defineProperty({},"passive",{get:function(){passiveSupported=!0}});window.addEventListener("test",e,e),window.removeEventListener("test",e,e)}catch(e){passiveSupported=!1}const OPTIMIZED_ZONE_EVENT_TASK_DATA={useG:!0},zoneSymbolEventNames={},globalSources={},EVENT_NAME_SYMBOL_REGX=new RegExp("^"+ZONE_SYMBOL_PREFIX+"(\\w+)(true|false)$"),IMMEDIATE_PROPAGATION_SYMBOL=zoneSymbol("propagationStopped");function prepareEventNames(e,t){const n=(t?t(e):e)+FALSE_STR,r=(t?t(e):e)+TRUE_STR,s=ZONE_SYMBOL_PREFIX+n,o=ZONE_SYMBOL_PREFIX+r;zoneSymbolEventNames[e]={},zoneSymbolEventNames[e][FALSE_STR]=s,zoneSymbolEventNames[e][TRUE_STR]=o}function patchEventTarget(e,t,n,r){const s=r&&r.add||"addEventListener",o=r&&r.rm||"removeEventListener",i=r&&r.listeners||"eventListeners",a=r&&r.rmAll||"removeAllListeners",c=zoneSymbol(s),l="."+s+":",u="prependListener",h="."+u+":",p=function(e,t,n){if(e.isRemoved)return;const r=e.callback;let s;"object"==typeof r&&r.handleEvent&&(e.callback=e=>r.handleEvent(e),e.originalDelegate=r);try{e.invoke(e,t,[n])}catch(e){s=e}const i=e.options;return i&&"object"==typeof i&&i.once&&t[o].call(t,n.type,e.originalDelegate?e.originalDelegate:e.callback,i),s};function d(n,r,s){if(!(r=r||e.event))return;const o=n||r.target||e,i=o[zoneSymbolEventNames[r.type][s?TRUE_STR:FALSE_STR]];if(i){const e=[];if(1===i.length){const t=p(i[0],o,r);t&&e.push(t)}else{const t=i.slice();for(let n=0;n<t.length&&(!r||!0!==r[IMMEDIATE_PROPAGATION_SYMBOL]);n++){const s=p(t[n],o,r);s&&e.push(s)}}if(1===e.length)throw e[0];for(let n=0;n<e.length;n++){const r=e[n];t.nativeScheduleMicroTask((()=>{throw r}))}}}const f=function(e){return d(this,e,!1)},_=function(e){return d(this,e,!0)};function m(t,n){if(!t)return!1;let r=!0;n&&void 0!==n.useG&&(r=n.useG);const p=n&&n.vh;let d=!0;n&&void 0!==n.chkDup&&(d=n.chkDup);let m=!1;n&&void 0!==n.rt&&(m=n.rt);let T=t;for(;T&&!T.hasOwnProperty(s);)T=ObjectGetPrototypeOf(T);if(!T&&t[s]&&(T=t),!T)return!1;if(T[c])return!1;const k=n&&n.eventNameToString,y={},g=T[c]=T[s],S=T[zoneSymbol(o)]=T[o],b=T[zoneSymbol(i)]=T[i],E=T[zoneSymbol(a)]=T[a];let v;n&&n.prepend&&(v=T[zoneSymbol(n.prepend)]=T[n.prepend]);const P=r?function(e){if(!y.isExisting)return g.call(y.target,y.eventName,y.capture?_:f,y.options)}:function(e){return g.call(y.target,y.eventName,e.invoke,y.options)},w=r?function(e){if(!e.isRemoved){const t=zoneSymbolEventNames[e.eventName];let n;t&&(n=t[e.capture?TRUE_STR:FALSE_STR]);const r=n&&e.target[n];if(r)for(let t=0;t<r.length;t++)if(r[t]===e){r.splice(t,1),e.isRemoved=!0,0===r.length&&(e.allRemoved=!0,e.target[n]=null);break}}if(e.allRemoved)return S.call(e.target,e.eventName,e.capture?_:f,e.options)}:function(e){return S.call(e.target,e.eventName,e.invoke,e.options)},Z=n&&n.diff?n.diff:function(e,t){const n=typeof t;return"function"===n&&e.callback===t||"object"===n&&e.originalDelegate===t},A=Zone[zoneSymbol("UNPATCHED_EVENTS")],D=e[zoneSymbol("PASSIVE_EVENTS")],O=function(t,s,o,i,a=!1,c=!1){return function(){const l=this||e;let u=arguments[0];n&&n.transferEventName&&(u=n.transferEventName(u));let h=arguments[1];if(!h)return t.apply(this,arguments);if(isNode&&"uncaughtException"===u)return t.apply(this,arguments);let f=!1;if("function"!=typeof h){if(!h.handleEvent)return t.apply(this,arguments);f=!0}if(p&&!p(t,h,l,arguments))return;const _=passiveSupported&&!!D&&-1!==D.indexOf(u),m=function T(e,t){return!passiveSupported&&"object"==typeof e&&e?!!e.capture:passiveSupported&&t?"boolean"==typeof e?{capture:e,passive:!0}:e?"object"==typeof e&&!1!==e.passive?{...e,passive:!0}:e:{passive:!0}:e}(arguments[2],_);if(A)for(let e=0;e<A.length;e++)if(u===A[e])return _?t.call(l,u,h,m):t.apply(this,arguments);const g=!!m&&("boolean"==typeof m||m.capture),S=!(!m||"object"!=typeof m)&&m.once,b=Zone.current;let E=zoneSymbolEventNames[u];E||(prepareEventNames(u,k),E=zoneSymbolEventNames[u]);const v=E[g?TRUE_STR:FALSE_STR];let P,w=l[v],O=!1;if(w){if(O=!0,d)for(let e=0;e<w.length;e++)if(Z(w[e],h))return}else w=l[v]=[];const I=l.constructor.name,C=globalSources[I];C&&(P=C[u]),P||(P=I+s+(k?k(u):u)),y.options=m,S&&(y.options.once=!1),y.target=l,y.capture=g,y.eventName=u,y.isExisting=O;const F=r?OPTIMIZED_ZONE_EVENT_TASK_DATA:void 0;F&&(F.taskData=y);const j=b.scheduleEventTask(P,h,F,o,i);return y.target=null,F&&(F.taskData=null),S&&(m.once=!0),(passiveSupported||"boolean"!=typeof j.options)&&(j.options=m),j.target=l,j.capture=g,j.eventName=u,f&&(j.originalDelegate=h),c?w.unshift(j):w.push(j),a?l:void 0}};return T[s]=O(g,l,P,w,m),v&&(T[u]=O(v,h,(function(e){return v.call(y.target,y.eventName,e.invoke,y.options)}),w,m,!0)),T[o]=function(){const t=this||e;let r=arguments[0];n&&n.transferEventName&&(r=n.transferEventName(r));const s=arguments[2],o=!!s&&("boolean"==typeof s||s.capture),i=arguments[1];if(!i)return S.apply(this,arguments);if(p&&!p(S,i,t,arguments))return;const a=zoneSymbolEventNames[r];let c;a&&(c=a[o?TRUE_STR:FALSE_STR]);const l=c&&t[c];if(l)for(let e=0;e<l.length;e++){const n=l[e];if(Z(n,i))return l.splice(e,1),n.isRemoved=!0,0===l.length&&(n.allRemoved=!0,t[c]=null,"string"==typeof r)&&(t[ZONE_SYMBOL_PREFIX+"ON_PROPERTY"+r]=null),n.zone.cancelTask(n),m?t:void 0}return S.apply(this,arguments)},T[i]=function(){const t=this||e;let r=arguments[0];n&&n.transferEventName&&(r=n.transferEventName(r));const s=[],o=findEventTasks(t,k?k(r):r);for(let e=0;e<o.length;e++){const t=o[e];s.push(t.originalDelegate?t.originalDelegate:t.callback)}return s},T[a]=function(){const t=this||e;let r=arguments[0];if(r){n&&n.transferEventName&&(r=n.transferEventName(r));const e=zoneSymbolEventNames[r];if(e){const n=t[e[FALSE_STR]],s=t[e[TRUE_STR]];if(n){const e=n.slice();for(let t=0;t<e.length;t++){const n=e[t];this[o].call(this,r,n.originalDelegate?n.originalDelegate:n.callback,n.options)}}if(s){const e=s.slice();for(let t=0;t<e.length;t++){const n=e[t];this[o].call(this,r,n.originalDelegate?n.originalDelegate:n.callback,n.options)}}}}else{const e=Object.keys(t);for(let t=0;t<e.length;t++){const n=EVENT_NAME_SYMBOL_REGX.exec(e[t]);let r=n&&n[1];r&&"removeListener"!==r&&this[a].call(this,r)}this[a].call(this,"removeListener")}if(m)return this},attachOriginToPatched(T[s],g),attachOriginToPatched(T[o],S),E&&attachOriginToPatched(T[a],E),b&&attachOriginToPatched(T[i],b),!0}let T=[];for(let e=0;e<n.length;e++)T[e]=m(n[e],r);return T}function findEventTasks(e,t){if(!t){const n=[];for(let r in e){const s=EVENT_NAME_SYMBOL_REGX.exec(r);let o=s&&s[1];if(o&&(!t||o===t)){const t=e[r];if(t)for(let e=0;e<t.length;e++)n.push(t[e])}}return n}let n=zoneSymbolEventNames[t];n||(prepareEventNames(t),n=zoneSymbolEventNames[t]);const r=e[n[FALSE_STR]],s=e[n[TRUE_STR]];return r?s?r.concat(s):r.slice():s?s.slice():[]}function patchQueueMicrotask(e,t){t.patchMethod(e,"queueMicrotask",(e=>function(e,t){Zone.current.scheduleMicroTask("queueMicrotask",t[0])}))}Zone.__load_patch("EventEmitter",((e,t,n)=>{const r="addListener",s="removeListener",o=function(e,t){return e.callback===t||e.callback.listener===t},i=function(e){return"string"==typeof e?e:e?e.toString().replace("(","_").replace(")","_"):""};let a;try{a=require("events")}catch(e){}a&&a.EventEmitter&&function c(t){const a=patchEventTarget(e,n,[t],{useG:!1,add:r,rm:s,prepend:"prependListener",rmAll:"removeAllListeners",listeners:"listeners",chkDup:!1,rt:!0,diff:o,eventNameToString:i});a&&a[0]&&(t.on=t[r],t.off=t[s])}(a.EventEmitter.prototype)})),Zone.__load_patch("fs",(()=>{let e;try{e=require("fs")}catch(e){}e&&["access","appendFile","chmod","chown","close","exists","fchmod","fchown","fdatasync","fstat","fsync","ftruncate","futimes","lchmod","lchown","link","lstat","mkdir","mkdtemp","open","read","readdir","readFile","readlink","realpath","rename","rmdir","stat","symlink","truncate","unlink","utimes","write","writeFile"].filter((t=>!!e[t]&&"function"==typeof e[t])).forEach((t=>{patchMacroTask(e,t,((e,n)=>({name:"fs."+t,args:n,cbIdx:n.length>0?n.length-1:-1,target:e})))}))}));const taskSymbol=zoneSymbol("zoneTask");function patchTimer(e,t,n,r){let s=null,o=null;n+=r;const i={};function a(t){const n=t.data;return n.args[0]=function(){return t.invoke.apply(this,arguments)},n.handleId=s.apply(e,n.args),t}function c(t){return o.call(e,t.data.handleId)}s=patchMethod(e,t+=r,(n=>function(s,o){if("function"==typeof o[0]){const e={isPeriodic:"Interval"===r,delay:"Timeout"===r||"Interval"===r?o[1]||0:void 0,args:o},n=o[0];o[0]=function t(){try{return n.apply(this,arguments)}finally{e.isPeriodic||("number"==typeof e.handleId?delete i[e.handleId]:e.handleId&&(e.handleId[taskSymbol]=null))}};const s=scheduleMacroTaskWithCurrentZone(t,o[0],e,a,c);if(!s)return s;const l=s.data.handleId;return"number"==typeof l?i[l]=s:l&&(l[taskSymbol]=s),l&&l.ref&&l.unref&&"function"==typeof l.ref&&"function"==typeof l.unref&&(s.ref=l.ref.bind(l),s.unref=l.unref.bind(l)),"number"==typeof l||l?l:s}return n.apply(e,o)})),o=patchMethod(e,n,(t=>function(n,r){const s=r[0];let o;"number"==typeof s?o=i[s]:(o=s&&s[taskSymbol],o||(o=s)),o&&"string"==typeof o.type?"notScheduled"!==o.state&&(o.cancelFn&&o.data.isPeriodic||0===o.runCount)&&("number"==typeof s?delete i[s]:s&&(s[taskSymbol]=null),o.zone.cancelTask(o)):t.apply(e,r)}))}const set="set",clear="clear";Zone.__load_patch("node_timers",((e,t)=>{let n=!1;try{const t=require("timers");if(e.setTimeout!==t.setTimeout&&!isMix){const r=t.setTimeout;t.setTimeout=function(){return n=!0,r.apply(this,arguments)};const s=e.setTimeout((()=>{}),100);clearTimeout(s),t.setTimeout=r}patchTimer(t,set,clear,"Timeout"),patchTimer(t,set,clear,"Interval"),patchTimer(t,set,clear,"Immediate")}catch(e){}isMix||(n?(e[t.__symbol__("setTimeout")]=e.setTimeout,e[t.__symbol__("setInterval")]=e.setInterval,e[t.__symbol__("setImmediate")]=e.setImmediate):(patchTimer(e,set,clear,"Timeout"),patchTimer(e,set,clear,"Interval"),patchTimer(e,set,clear,"Immediate")))})),Zone.__load_patch("nextTick",(()=>{patchMicroTask(process,"nextTick",((e,t)=>({name:"process.nextTick",args:t,cbIdx:t.length>0&&"function"==typeof t[0]?0:-1,target:process})))})),Zone.__load_patch("handleUnhandledPromiseRejection",((e,t,n)=>{function r(e){return function(t){findEventTasks(process,e).forEach((n=>{"unhandledRejection"===e?n.invoke(t.rejection,t.promise):"rejectionHandled"===e&&n.invoke(t.promise)}))}}t[n.symbol("unhandledPromiseRejectionHandler")]=r("unhandledRejection"),t[n.symbol("rejectionHandledHandler")]=r("rejectionHandled")})),Zone.__load_patch("crypto",(()=>{let e;try{e=require("crypto")}catch(e){}e&&["randomBytes","pbkdf2"].forEach((t=>{patchMacroTask(e,t,((n,r)=>({name:"crypto."+t,args:r,cbIdx:r.length>0&&"function"==typeof r[r.length-1]?r.length-1:-1,target:e})))}))})),Zone.__load_patch("console",((e,t)=>{["dir","log","info","error","warn","assert","debug","timeEnd","trace"].forEach((e=>{const n=console[t.__symbol__(e)]=console[e];n&&(console[e]=function(){const e=ArraySlice.call(arguments);return t.current===t.root?n.apply(this,e):t.root.run(n,this,e)})}))})),Zone.__load_patch("queueMicrotask",((e,t,n)=>{patchQueueMicrotask(e,n)}));const NEWLINE="\n",IGNORE_FRAMES={},creationTrace="__creationTrace__",ERROR_TAG="STACKTRACE TRACKING",SEP_TAG="__SEP_TAG__";let sepTemplate=SEP_TAG+"@[native]";class LongStackTrace{constructor(){this.error=getStacktrace(),this.timestamp=new Date}}function getStacktraceWithUncaughtError(){return new Error(ERROR_TAG)}function getStacktraceWithCaughtError(){try{throw getStacktraceWithUncaughtError()}catch(e){return e}}const error=getStacktraceWithUncaughtError(),caughtError=getStacktraceWithCaughtError(),getStacktrace=error.stack?getStacktraceWithUncaughtError:caughtError.stack?getStacktraceWithCaughtError:getStacktraceWithUncaughtError;function getFrames(e){return e.stack?e.stack.split(NEWLINE):[]}function addErrorStack(e,t){let n=getFrames(t);for(let t=0;t<n.length;t++)IGNORE_FRAMES.hasOwnProperty(n[t])||e.push(n[t])}function renderLongStackTrace(e,t){const n=[t?t.trim():""];if(e){let t=(new Date).getTime();for(let r=0;r<e.length;r++){const s=e[r],o=s.timestamp;let i=`____________________Elapsed ${t-o.getTime()} ms; At: ${o}`;i=i.replace(/[^\w\d]/g,"_"),n.push(sepTemplate.replace(SEP_TAG,i)),addErrorStack(n,s.error),t=o.getTime()}}return n.join(NEWLINE)}function stackTracesEnabled(){return Error.stackTraceLimit>0}function captureStackTraces(e,t){t>0&&(e.push(getFrames((new LongStackTrace).error)),captureStackTraces(e,t-1))}function computeIgnoreFrames(){if(!stackTracesEnabled())return;const e=[];captureStackTraces(e,2);const t=e[0],n=e[1];for(let e=0;e<t.length;e++){const n=t[e];if(-1==n.indexOf(ERROR_TAG)){let e=n.match(/^\s*at\s+/);if(e){sepTemplate=e[0]+SEP_TAG+" (http://localhost)";break}}}for(let e=0;e<t.length;e++){const r=t[e];if(r!==n[e])break;IGNORE_FRAMES[r]=!0}}Zone.longStackTraceZoneSpec={name:"long-stack-trace",longStackTraceLimit:10,getLongStackTrace:function(e){if(!e)return;const t=e[Zone.__symbol__("currentTaskTrace")];return t?renderLongStackTrace(t,e.stack):e.stack},onScheduleTask:function(e,t,n,r){if(stackTracesEnabled()){const e=Zone.currentTask;let t=e&&e.data&&e.data[creationTrace]||[];t=[new LongStackTrace].concat(t),t.length>this.longStackTraceLimit&&(t.length=this.longStackTraceLimit),r.data||(r.data={}),"eventTask"===r.type&&(r.data={...r.data}),r.data[creationTrace]=t}return e.scheduleTask(n,r)},onHandleError:function(e,t,n,r){if(stackTracesEnabled()){const e=Zone.currentTask||r.task;if(r instanceof Error&&e){const t=renderLongStackTrace(e.data&&e.data[creationTrace],r.stack);try{r.stack=r.longStack=t}catch(e){}}}return e.handleError(n,r)}},computeIgnoreFrames();class ProxyZoneSpec{static get(){return Zone.current.get("ProxyZoneSpec")}static isLoaded(){return ProxyZoneSpec.get()instanceof ProxyZoneSpec}static assertPresent(){if(!ProxyZoneSpec.isLoaded())throw new Error("Expected to be running in 'ProxyZone', but it was not found.");return ProxyZoneSpec.get()}constructor(e=null){this.defaultSpecDelegate=e,this.name="ProxyZone",this._delegateSpec=null,this.properties={ProxyZoneSpec:this},this.propertyKeys=null,this.lastTaskState=null,this.isNeedToTriggerHasTask=!1,this.tasks=[],this.setDelegate(e)}setDelegate(e){const t=this._delegateSpec!==e;this._delegateSpec=e,this.propertyKeys&&this.propertyKeys.forEach((e=>delete this.properties[e])),this.propertyKeys=null,e&&e.properties&&(this.propertyKeys=Object.keys(e.properties),this.propertyKeys.forEach((t=>this.properties[t]=e.properties[t]))),t&&this.lastTaskState&&(this.lastTaskState.macroTask||this.lastTaskState.microTask)&&(this.isNeedToTriggerHasTask=!0)}getDelegate(){return this._delegateSpec}resetDelegate(){this.getDelegate(),this.setDelegate(this.defaultSpecDelegate)}tryTriggerHasTask(e,t,n){this.isNeedToTriggerHasTask&&this.lastTaskState&&(this.isNeedToTriggerHasTask=!1,this.onHasTask(e,t,n,this.lastTaskState))}removeFromTasks(e){if(this.tasks)for(let t=0;t<this.tasks.length;t++)if(this.tasks[t]===e)return void this.tasks.splice(t,1)}getAndClearPendingTasksInfo(){if(0===this.tasks.length)return"";const e="--Pending async tasks are: ["+this.tasks.map((e=>{const t=e.data&&Object.keys(e.data).map((t=>t+":"+e.data[t])).join(",");return`type: ${e.type}, source: ${e.source}, args: {${t}}`}))+"]";return this.tasks=[],e}onFork(e,t,n,r){return this._delegateSpec&&this._delegateSpec.onFork?this._delegateSpec.onFork(e,t,n,r):e.fork(n,r)}onIntercept(e,t,n,r,s){return this._delegateSpec&&this._delegateSpec.onIntercept?this._delegateSpec.onIntercept(e,t,n,r,s):e.intercept(n,r,s)}onInvoke(e,t,n,r,s,o,i){return this.tryTriggerHasTask(e,t,n),this._delegateSpec&&this._delegateSpec.onInvoke?this._delegateSpec.onInvoke(e,t,n,r,s,o,i):e.invoke(n,r,s,o,i)}onHandleError(e,t,n,r){return this._delegateSpec&&this._delegateSpec.onHandleError?this._delegateSpec.onHandleError(e,t,n,r):e.handleError(n,r)}onScheduleTask(e,t,n,r){return"eventTask"!==r.type&&this.tasks.push(r),this._delegateSpec&&this._delegateSpec.onScheduleTask?this._delegateSpec.onScheduleTask(e,t,n,r):e.scheduleTask(n,r)}onInvokeTask(e,t,n,r,s,o){return"eventTask"!==r.type&&this.removeFromTasks(r),this.tryTriggerHasTask(e,t,n),this._delegateSpec&&this._delegateSpec.onInvokeTask?this._delegateSpec.onInvokeTask(e,t,n,r,s,o):e.invokeTask(n,r,s,o)}onCancelTask(e,t,n,r){return"eventTask"!==r.type&&this.removeFromTasks(r),this.tryTriggerHasTask(e,t,n),this._delegateSpec&&this._delegateSpec.onCancelTask?this._delegateSpec.onCancelTask(e,t,n,r):e.cancelTask(n,r)}onHasTask(e,t,n,r){this.lastTaskState=r,this._delegateSpec&&this._delegateSpec.onHasTask?this._delegateSpec.onHasTask(e,t,n,r):e.hasTask(n,r)}}Zone.ProxyZoneSpec=ProxyZoneSpec;class SyncTestZoneSpec{constructor(e){this.runZone=Zone.current,this.name="syncTestZone for "+e}onScheduleTask(e,t,n,r){switch(r.type){case"microTask":case"macroTask":throw new Error(`Cannot call ${r.source} from within a sync test (${this.name}).`);case"eventTask":r=e.scheduleTask(n,r)}return r}}Zone.SyncTestZoneSpec=SyncTestZoneSpec,Zone.__load_patch("jasmine",((e,t,n)=>{if(!t)throw new Error("Missing: zone.js");if("undefined"!=typeof jest)return;if("undefined"==typeof jasmine||jasmine.__zone_patch__)return;jasmine.__zone_patch__=!0;const r=t.SyncTestZoneSpec,s=t.ProxyZoneSpec;if(!r)throw new Error("Missing: SyncTestZoneSpec");if(!s)throw new Error("Missing: ProxyZoneSpec");const o=t.current,i=t.__symbol__,a=!0===e[i("fakeAsyncDisablePatchingClock")],c=!a&&(!0===e[i("fakeAsyncPatchLock")]||!0===e[i("fakeAsyncAutoFakeAsyncWhenClockPatched")]);if(!0!==e[i("ignoreUnhandledRejection")]){const t=jasmine.GlobalErrors;t&&!jasmine[i("GlobalErrors")]&&(jasmine[i("GlobalErrors")]=t,jasmine.GlobalErrors=function(){const n=new t,r=n.install;return r&&!n[i("install")]&&(n[i("install")]=r,n.install=function(){const t="undefined"!=typeof process&&!!process.on,n=t?process.listeners("unhandledRejection"):e.eventListeners("unhandledrejection"),s=r.apply(this,arguments);return t?process.removeAllListeners("unhandledRejection"):e.removeAllListeners("unhandledrejection"),n&&n.forEach((n=>{t?process.on("unhandledRejection",n):e.addEventListener("unhandledrejection",n)})),s}),n})}const l=jasmine.getEnv();if(["describe","xdescribe","fdescribe"].forEach((e=>{let t=l[e];l[e]=function(e,n){return t.call(this,e,function s(e,t){return function(){return o.fork(new r(`jasmine.describe#${e}`)).run(t,this,arguments)}}(e,n))}})),["it","xit","fit"].forEach((e=>{let t=l[e];l[i(e)]=t,l[e]=function(e,n,r){return arguments[1]=h(n),t.apply(this,arguments)}})),["beforeEach","afterEach","beforeAll","afterAll"].forEach((e=>{let t=l[e];l[i(e)]=t,l[e]=function(e,n){return arguments[0]=h(e),t.apply(this,arguments)}})),!a){const e=jasmine[i("clock")]=jasmine.clock;jasmine.clock=function(){const n=e.apply(this,arguments);if(!n[i("patched")]){n[i("patched")]=i("patched");const e=n[i("tick")]=n.tick;n.tick=function(){const n=t.current.get("FakeAsyncTestZoneSpec");return n?n.tick.apply(n,arguments):e.apply(this,arguments)};const r=n[i("mockDate")]=n.mockDate;n.mockDate=function(){const e=t.current.get("FakeAsyncTestZoneSpec");if(e){const t=arguments.length>0?arguments[0]:new Date;return e.setFakeBaseSystemTime.apply(e,t&&"function"==typeof t.getTime?[t.getTime()]:arguments)}return r.apply(this,arguments)},c&&["install","uninstall"].forEach((e=>{const r=n[i(e)]=n[e];n[e]=function(){if(!t.FakeAsyncTestZoneSpec)return r.apply(this,arguments);jasmine[i("clockInstalled")]="install"===e}}))}return n}}if(!jasmine[t.__symbol__("createSpyObj")]){const e=jasmine.createSpyObj;jasmine[t.__symbol__("createSpyObj")]=e,jasmine.createSpyObj=function(){const t=Array.prototype.slice.call(arguments);let n;if(t.length>=3&&t[2]){const r=Object.defineProperty;Object.defineProperty=function(e,t,n){return r.call(this,e,t,{...n,configurable:!0,enumerable:!0})};try{n=e.apply(this,t)}finally{Object.defineProperty=r}}else n=e.apply(this,t);return n}}function u(e,n,r,s){const o=!!jasmine[i("clockInstalled")],a=r.testProxyZone;if(o&&c){const n=t[t.__symbol__("fakeAsyncTest")];n&&"function"==typeof n.fakeAsync&&(e=n.fakeAsync(e))}return s?a.run(e,n,[s]):a.run(e,n)}function h(e){return e&&(e.length?function(t){return u(e,this,this.queueRunner,t)}:function(){return u(e,this,this.queueRunner)})}const p=jasmine.QueueRunner;jasmine.QueueRunner=function(n){function r(r){r.onComplete&&(r.onComplete=(e=>()=>{this.testProxyZone=null,this.testProxyZoneSpec=null,o.scheduleMicroTask("jasmine.onComplete",e)})(r.onComplete));const s=e[t.__symbol__("setTimeout")],i=e[t.__symbol__("clearTimeout")];s&&(r.timeout={setTimeout:s||e.setTimeout,clearTimeout:i||e.clearTimeout}),jasmine.UserContext?(r.userContext||(r.userContext=new jasmine.UserContext),r.userContext.queueRunner=this):(r.userContext||(r.userContext={}),r.userContext.queueRunner=this);const a=r.onException;r.onException=function(e){if(e&&"Timeout - Async callback was not invoked within timeout specified by jasmine.DEFAULT_TIMEOUT_INTERVAL."===e.message){const t=this&&this.testProxyZoneSpec;if(t){const n=t.getAndClearPendingTasksInfo();try{e.message+=n}catch(e){}}}a&&a.call(this,e)},n.call(this,r)}return function(e,t){for(const n in t)t.hasOwnProperty(n)&&(e[n]=t[n]);function n(){this.constructor=e}e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}(r,n),r.prototype.execute=function(){let e=t.current,r=!1;for(;e;){if(e===o){r=!0;break}e=e.parent}if(!r)throw new Error("Unexpected Zone: "+t.current.name);this.testProxyZoneSpec=new s,this.testProxyZone=o.fork(this.testProxyZoneSpec),t.currentTask?n.prototype.execute.call(this):t.current.scheduleMicroTask("jasmine.execute().forceTask",(()=>p.prototype.execute.call(this)))},r}(p)})),Zone.__load_patch("jest",((e,t,n)=>{if("undefined"==typeof jest||jest.__zone_patch__)return;t[n.symbol("ignoreConsoleErrorUncaughtError")]=!0,jest.__zone_patch__=!0;const r=t.ProxyZoneSpec,s=t.SyncTestZoneSpec;if(!r)throw new Error("Missing ProxyZoneSpec");const o=t.current,i=o.fork(new s("jest.describe")),a=new r,c=o.fork(a);function l(e){return function(...t){return i.run(e,this,t)}}function u(e,r=!1){if("function"!=typeof e)return e;const s=function(){if(!0===t[n.symbol("useFakeTimersCalled")]&&e&&!e.isFakeAsync){const n=t[t.__symbol__("fakeAsyncTest")];n&&"function"==typeof n.fakeAsync&&(e=n.fakeAsync(e))}return a.isTestFunc=r,c.run(e,null,arguments)};return Object.defineProperty(s,"length",{configurable:!0,writable:!0,enumerable:!1}),s.length=e.length,s}["describe","xdescribe","fdescribe"].forEach((n=>{let r=e[n];e[t.__symbol__(n)]||(e[t.__symbol__(n)]=r,e[n]=function(...e){return e[1]=l(e[1]),r.apply(this,e)},e[n].each=function s(e){return function(...t){const n=e.apply(this,t);return function(...e){return e[1]=l(e[1]),n.apply(this,e)}}}(r.each))})),e.describe.only=e.fdescribe,e.describe.skip=e.xdescribe,["it","xit","fit","test","xtest"].forEach((n=>{let r=e[n];e[t.__symbol__(n)]||(e[t.__symbol__(n)]=r,e[n]=function(...e){return e[1]=u(e[1],!0),r.apply(this,e)},e[n].each=function s(e){return function(...t){return function(...n){return n[1]=u(n[1]),e.apply(this,t).apply(this,n)}}}(r.each),e[n].todo=r.todo)})),e.it.only=e.fit,e.it.skip=e.xit,e.test.only=e.fit,e.test.skip=e.xit,["beforeEach","afterEach","beforeAll","afterAll"].forEach((n=>{let r=e[n];e[t.__symbol__(n)]||(e[t.__symbol__(n)]=r,e[n]=function(...e){return e[0]=u(e[0]),r.apply(this,e)})})),t.patchJestObject=function e(r,s=!1){function o(){return!!t.current.get("FakeAsyncTestZoneSpec")}function i(){const e=t.current.get("ProxyZoneSpec");return e&&e.isTestFunc}r[n.symbol("fakeTimers")]||(r[n.symbol("fakeTimers")]=!0,n.patchMethod(r,"_checkFakeTimers",(e=>function(t,n){return!!o()||e.apply(t,n)})),n.patchMethod(r,"useFakeTimers",(e=>function(r,o){return t[n.symbol("useFakeTimersCalled")]=!0,s||i()?e.apply(r,o):r})),n.patchMethod(r,"useRealTimers",(e=>function(r,o){return t[n.symbol("useFakeTimersCalled")]=!1,s||i()?e.apply(r,o):r})),n.patchMethod(r,"setSystemTime",(e=>function(n,r){const s=t.current.get("FakeAsyncTestZoneSpec");if(!s||!o())return e.apply(n,r);s.setFakeBaseSystemTime(r[0])})),n.patchMethod(r,"getRealSystemTime",(e=>function(n,r){const s=t.current.get("FakeAsyncTestZoneSpec");return s&&o()?s.getRealSystemTime():e.apply(n,r)})),n.patchMethod(r,"runAllTicks",(e=>function(n,r){const s=t.current.get("FakeAsyncTestZoneSpec");if(!s)return e.apply(n,r);s.flushMicrotasks()})),n.patchMethod(r,"runAllTimers",(e=>function(n,r){const s=t.current.get("FakeAsyncTestZoneSpec");if(!s)return e.apply(n,r);s.flush(100,!0)})),n.patchMethod(r,"advanceTimersByTime",(e=>function(n,r){const s=t.current.get("FakeAsyncTestZoneSpec");if(!s)return e.apply(n,r);s.tick(r[0])})),n.patchMethod(r,"runOnlyPendingTimers",(e=>function(n,r){const s=t.current.get("FakeAsyncTestZoneSpec");if(!s)return e.apply(n,r);s.flushOnlyPendingTimers()})),n.patchMethod(r,"advanceTimersToNextTimer",(e=>function(n,r){const s=t.current.get("FakeAsyncTestZoneSpec");if(!s)return e.apply(n,r);s.tickToNext(r[0])})),n.patchMethod(r,"clearAllTimers",(e=>function(n,r){const s=t.current.get("FakeAsyncTestZoneSpec");if(!s)return e.apply(n,r);s.removeAllTimers()})),n.patchMethod(r,"getTimerCount",(e=>function(n,r){const s=t.current.get("FakeAsyncTestZoneSpec");return s?s.getTimerCount():e.apply(n,r)})))}})),Zone.__load_patch("mocha",((e,t)=>{const n=e.Mocha;if(void 0===n)return;if(void 0===t)throw new Error("Missing Zone.js");const r=t.ProxyZoneSpec,s=t.SyncTestZoneSpec;if(!r)throw new Error("Missing ProxyZoneSpec");if(n.__zone_patch__)throw new Error('"Mocha" has already been patched with "Zone".');n.__zone_patch__=!0;const o=t.current,i=o.fork(new s("Mocha.describe"));let a=null;const c=o.fork(new r),l={after:e.after,afterEach:e.afterEach,before:e.before,beforeEach:e.beforeEach,describe:e.describe,it:e.it};function u(e,t,n){for(let r=0;r<e.length;r++){let s=e[r];"function"==typeof s&&(e[r]=0===s.length?t(s):n(s),e[r].toString=function(){return s.toString()})}return e}function h(e){return u(e,(function(e){return function(){return i.run(e,this,arguments)}}))}function p(e){return u(e,(function(e){return function(){return a.run(e,this)}}),(function(e){return function(t){return a.run(e,this,[t])}}))}function d(e){return u(e,(function(e){return function(){return c.run(e,this)}}),(function(e){return function(t){return c.run(e,this,[t])}}))}var f,_;e.describe=e.suite=function(){return l.describe.apply(this,h(arguments))},e.xdescribe=e.suite.skip=e.describe.skip=function(){return l.describe.skip.apply(this,h(arguments))},e.describe.only=e.suite.only=function(){return l.describe.only.apply(this,h(arguments))},e.it=e.specify=e.test=function(){return l.it.apply(this,p(arguments))},e.xit=e.xspecify=e.it.skip=function(){return l.it.skip.apply(this,p(arguments))},e.it.only=e.test.only=function(){return l.it.only.apply(this,p(arguments))},e.after=e.suiteTeardown=function(){return l.after.apply(this,d(arguments))},e.afterEach=e.teardown=function(){return l.afterEach.apply(this,p(arguments))},e.before=e.suiteSetup=function(){return l.before.apply(this,d(arguments))},e.beforeEach=e.setup=function(){return l.beforeEach.apply(this,p(arguments))},f=n.Runner.prototype.runTest,_=n.Runner.prototype.run,n.Runner.prototype.runTest=function(e){t.current.scheduleMicroTask("mocha.forceTask",(()=>{f.call(this,e)}))},n.Runner.prototype.run=function(e){return this.on("test",(e=>{a=o.fork(new r)})),this.on("fail",((e,t)=>{const n=a&&a.get("ProxyZoneSpec");if(n&&t)try{t.message+=n.getAndClearPendingTasksInfo()}catch(e){}})),_.call(this,e)}})),function(e){class t{static{this.symbolParentUnresolved=Zone.__symbol__("parentUnresolved")}constructor(t,n,r){this.finishCallback=t,this.failCallback=n,this._pendingMicroTasks=!1,this._pendingMacroTasks=!1,this._alreadyErrored=!1,this._isSync=!1,this._existingFinishTimer=null,this.entryFunction=null,this.runZone=Zone.current,this.unresolvedChainedPromiseCount=0,this.supportWaitUnresolvedChainedPromise=!1,this.name="asyncTestZone for "+r,this.properties={AsyncTestZoneSpec:this},this.supportWaitUnresolvedChainedPromise=!0===e[Zone.__symbol__("supportWaitUnResolvedChainedPromise")]}isUnresolvedChainedPromisePending(){return this.unresolvedChainedPromiseCount>0}_finishCallbackIfDone(){null!==this._existingFinishTimer&&(clearTimeout(this._existingFinishTimer),this._existingFinishTimer=null),this._pendingMicroTasks||this._pendingMacroTasks||this.supportWaitUnresolvedChainedPromise&&this.isUnresolvedChainedPromisePending()||this.runZone.run((()=>{this._existingFinishTimer=setTimeout((()=>{this._alreadyErrored||this._pendingMicroTasks||this._pendingMacroTasks||this.finishCallback()}),0)}))}patchPromiseForTest(){if(!this.supportWaitUnresolvedChainedPromise)return;const e=Promise[Zone.__symbol__("patchPromiseForTest")];e&&e()}unPatchPromiseForTest(){if(!this.supportWaitUnresolvedChainedPromise)return;const e=Promise[Zone.__symbol__("unPatchPromiseForTest")];e&&e()}onScheduleTask(e,n,r,s){return"eventTask"!==s.type&&(this._isSync=!1),"microTask"===s.type&&s.data&&s.data instanceof Promise&&!0===s.data[t.symbolParentUnresolved]&&this.unresolvedChainedPromiseCount--,e.scheduleTask(r,s)}onInvokeTask(e,t,n,r,s,o){return"eventTask"!==r.type&&(this._isSync=!1),e.invokeTask(n,r,s,o)}onCancelTask(e,t,n,r){return"eventTask"!==r.type&&(this._isSync=!1),e.cancelTask(n,r)}onInvoke(e,t,n,r,s,o,i){this.entryFunction||(this.entryFunction=r);try{return this._isSync=!0,e.invoke(n,r,s,o,i)}finally{this._isSync&&this.entryFunction===r&&this._finishCallbackIfDone()}}onHandleError(e,t,n,r){return e.handleError(n,r)&&(this.failCallback(r),this._alreadyErrored=!0),!1}onHasTask(e,t,n,r){e.hasTask(n,r),t===n&&("microTask"==r.change?(this._pendingMicroTasks=r.microTask,this._finishCallbackIfDone()):"macroTask"==r.change&&(this._pendingMacroTasks=r.macroTask,this._finishCallbackIfDone()))}}Zone.AsyncTestZoneSpec=t}("undefined"!=typeof window&&window||"undefined"!=typeof self&&self||global),Zone.__load_patch("asynctest",((e,t,n)=>{function r(e,n,r,s){const o=t.current,i=t.AsyncTestZoneSpec;if(void 0===i)throw new Error("AsyncTestZoneSpec is needed for the async() test helper but could not be found. Please make sure that your environment includes zone.js/plugins/async-test");const a=t.ProxyZoneSpec;if(!a)throw new Error("ProxyZoneSpec is needed for the async() test helper but could not be found. Please make sure that your environment includes zone.js/plugins/proxy");const c=a.get();a.assertPresent();const l=t.current.getZoneWith("ProxyZoneSpec"),u=c.getDelegate();return l.parent.run((()=>{const e=new i((()=>{c.getDelegate()==e&&c.setDelegate(u),e.unPatchPromiseForTest(),o.run((()=>{r()}))}),(t=>{c.getDelegate()==e&&c.setDelegate(u),e.unPatchPromiseForTest(),o.run((()=>{s(t)}))}),"test");c.setDelegate(e),e.patchPromiseForTest()})),t.current.runGuarded(e,n)}t[n.symbol("asyncTest")]=function t(n){return e.jasmine?function(e){e||((e=function(){}).fail=function(e){throw e}),r(n,this,e,(t=>{if("string"==typeof t)return e.fail(new Error(t));e.fail(t)}))}:function(){return new Promise(((e,t)=>{r(n,this,e,t)}))}}})),function(e){const t=e.Date;function n(){if(0===arguments.length){const e=new t;return e.setTime(n.now()),e}{const e=Array.prototype.slice.call(arguments);return new t(...e)}}n.now=function(){const e=Zone.current.get("FakeAsyncTestZoneSpec");return e?e.getFakeSystemTime():t.now.apply(this,arguments)},n.UTC=t.UTC,n.parse=t.parse;const r={setTimeout:e.setTimeout,setInterval:e.setInterval,clearTimeout:e.clearTimeout,clearInterval:e.clearInterval};class s{static{this.nextId=1}constructor(){this._schedulerQueue=[],this._currentTickTime=0,this._currentFakeBaseSystemTime=t.now(),this._currentTickRequeuePeriodicEntries=[]}getCurrentTickTime(){return this._currentTickTime}getFakeSystemTime(){return this._currentFakeBaseSystemTime+this._currentTickTime}setFakeBaseSystemTime(e){this._currentFakeBaseSystemTime=e}getRealSystemTime(){return t.now()}scheduleFunction(e,t,n){let r=(n={args:[],isPeriodic:!1,isRequestAnimationFrame:!1,id:-1,isRequeuePeriodic:!1,...n}).id<0?s.nextId++:n.id,o={endTime:this._currentTickTime+t,id:r,func:e,args:n.args,delay:t,isPeriodic:n.isPeriodic,isRequestAnimationFrame:n.isRequestAnimationFrame};n.isRequeuePeriodic&&this._currentTickRequeuePeriodicEntries.push(o);let i=0;for(;i<this._schedulerQueue.length&&!(o.endTime<this._schedulerQueue[i].endTime);i++);return this._schedulerQueue.splice(i,0,o),r}removeScheduledFunctionWithId(e){for(let t=0;t<this._schedulerQueue.length;t++)if(this._schedulerQueue[t].id==e){this._schedulerQueue.splice(t,1);break}}removeAll(){this._schedulerQueue=[]}getTimerCount(){return this._schedulerQueue.length}tickToNext(e=1,t,n){this._schedulerQueue.length<e||this.tick(this._schedulerQueue[e-1].endTime-this._currentTickTime,t,n)}tick(t=0,n,r){let s=this._currentTickTime+t,o=0;const i=(r=Object.assign({processNewMacroTasksSynchronously:!0},r)).processNewMacroTasksSynchronously?this._schedulerQueue:this._schedulerQueue.slice();if(0===i.length&&n)n(t);else{for(;i.length>0&&(this._currentTickRequeuePeriodicEntries=[],!(s<i[0].endTime));){let t=i.shift();if(!r.processNewMacroTasksSynchronously){const e=this._schedulerQueue.indexOf(t);e>=0&&this._schedulerQueue.splice(e,1)}if(o=this._currentTickTime,this._currentTickTime=t.endTime,n&&n(this._currentTickTime-o),!t.func.apply(e,t.isRequestAnimationFrame?[this._currentTickTime]:t.args))break;r.processNewMacroTasksSynchronously||this._currentTickRequeuePeriodicEntries.forEach((e=>{let t=0;for(;t<i.length&&!(e.endTime<i[t].endTime);t++);i.splice(t,0,e)}))}o=this._currentTickTime,this._currentTickTime=s,n&&n(this._currentTickTime-o)}}flushOnlyPendingTimers(e){if(0===this._schedulerQueue.length)return 0;const t=this._currentTickTime;return this.tick(this._schedulerQueue[this._schedulerQueue.length-1].endTime-t,e,{processNewMacroTasksSynchronously:!1}),this._currentTickTime-t}flush(e=20,t=!1,n){return t?this.flushPeriodic(n):this.flushNonPeriodic(e,n)}flushPeriodic(e){if(0===this._schedulerQueue.length)return 0;const t=this._currentTickTime;return this.tick(this._schedulerQueue[this._schedulerQueue.length-1].endTime-t,e),this._currentTickTime-t}flushNonPeriodic(t,n){const r=this._currentTickTime;let s=0,o=0;for(;this._schedulerQueue.length>0;){if(o++,o>t)throw new Error("flush failed after reaching the limit of "+t+" tasks. Does your code use a polling timeout?");if(0===this._schedulerQueue.filter((e=>!e.isPeriodic&&!e.isRequestAnimationFrame)).length)break;const r=this._schedulerQueue.shift();if(s=this._currentTickTime,this._currentTickTime=r.endTime,n&&n(this._currentTickTime-s),!r.func.apply(e,r.args))break}return this._currentTickTime-r}}class o{static assertInZone(){if(null==Zone.current.get("FakeAsyncTestZoneSpec"))throw new Error("The code should be running in the fakeAsync zone to call this function")}constructor(t,n=!1,r){this.trackPendingRequestAnimationFrame=n,this.macroTaskOptions=r,this._scheduler=new s,this._microtasks=[],this._lastError=null,this._uncaughtPromiseErrors=Promise[Zone.__symbol__("uncaughtPromiseErrors")],this.pendingPeriodicTimers=[],this.pendingTimers=[],this.patchDateLocked=!1,this.properties={FakeAsyncTestZoneSpec:this},this.name="fakeAsyncTestZone for "+t,this.macroTaskOptions||(this.macroTaskOptions=e[Zone.__symbol__("FakeAsyncTestMacroTask")])}_fnAndFlush(t,n){return(...r)=>(t.apply(e,r),null===this._lastError?(null!=n.onSuccess&&n.onSuccess.apply(e),this.flushMicrotasks()):null!=n.onError&&n.onError.apply(e),null===this._lastError)}static _removeTimer(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}_dequeueTimer(e){return()=>{o._removeTimer(this.pendingTimers,e)}}_requeuePeriodicTimer(e,t,n,r){return()=>{-1!==this.pendingPeriodicTimers.indexOf(r)&&this._scheduler.scheduleFunction(e,t,{args:n,isPeriodic:!0,id:r,isRequeuePeriodic:!0})}}_dequeuePeriodicTimer(e){return()=>{o._removeTimer(this.pendingPeriodicTimers,e)}}_setTimeout(e,t,n,r=!0){let o=this._dequeueTimer(s.nextId),i=this._fnAndFlush(e,{onSuccess:o,onError:o}),a=this._scheduler.scheduleFunction(i,t,{args:n,isRequestAnimationFrame:!r});return r&&this.pendingTimers.push(a),a}_clearTimeout(e){o._removeTimer(this.pendingTimers,e),this._scheduler.removeScheduledFunctionWithId(e)}_setInterval(e,t,n){let r=s.nextId,o={onSuccess:null,onError:this._dequeuePeriodicTimer(r)},i=this._fnAndFlush(e,o);return o.onSuccess=this._requeuePeriodicTimer(i,t,n,r),this._scheduler.scheduleFunction(i,t,{args:n,isPeriodic:!0}),this.pendingPeriodicTimers.push(r),r}_clearInterval(e){o._removeTimer(this.pendingPeriodicTimers,e),this._scheduler.removeScheduledFunctionWithId(e)}_resetLastErrorAndThrow(){let e=this._lastError||this._uncaughtPromiseErrors[0];throw this._uncaughtPromiseErrors.length=0,this._lastError=null,e}getCurrentTickTime(){return this._scheduler.getCurrentTickTime()}getFakeSystemTime(){return this._scheduler.getFakeSystemTime()}setFakeBaseSystemTime(e){this._scheduler.setFakeBaseSystemTime(e)}getRealSystemTime(){return this._scheduler.getRealSystemTime()}static patchDate(){e[Zone.__symbol__("disableDatePatching")]||e.Date!==n&&(e.Date=n,n.prototype=t.prototype,o.checkTimerPatch())}static resetDate(){e.Date===n&&(e.Date=t)}static checkTimerPatch(){e.setTimeout!==r.setTimeout&&(e.setTimeout=r.setTimeout,e.clearTimeout=r.clearTimeout),e.setInterval!==r.setInterval&&(e.setInterval=r.setInterval,e.clearInterval=r.clearInterval)}lockDatePatch(){this.patchDateLocked=!0,o.patchDate()}unlockDatePatch(){this.patchDateLocked=!1,o.resetDate()}tickToNext(e=1,t,n={processNewMacroTasksSynchronously:!0}){e<=0||(o.assertInZone(),this.flushMicrotasks(),this._scheduler.tickToNext(e,t,n),null!==this._lastError&&this._resetLastErrorAndThrow())}tick(e=0,t,n={processNewMacroTasksSynchronously:!0}){o.assertInZone(),this.flushMicrotasks(),this._scheduler.tick(e,t,n),null!==this._lastError&&this._resetLastErrorAndThrow()}flushMicrotasks(){for(o.assertInZone();this._microtasks.length>0;){let e=this._microtasks.shift();e.func.apply(e.target,e.args)}(()=>{(null!==this._lastError||this._uncaughtPromiseErrors.length)&&this._resetLastErrorAndThrow()})()}flush(e,t,n){o.assertInZone(),this.flushMicrotasks();const r=this._scheduler.flush(e,t,n);return null!==this._lastError&&this._resetLastErrorAndThrow(),r}flushOnlyPendingTimers(e){o.assertInZone(),this.flushMicrotasks();const t=this._scheduler.flushOnlyPendingTimers(e);return null!==this._lastError&&this._resetLastErrorAndThrow(),t}removeAllTimers(){o.assertInZone(),this._scheduler.removeAll(),this.pendingPeriodicTimers=[],this.pendingTimers=[]}getTimerCount(){return this._scheduler.getTimerCount()+this._microtasks.length}onScheduleTask(e,t,n,r){switch(r.type){case"microTask":let t,s=r.data&&r.data.args;if(s){let e=r.data.cbIdx;"number"==typeof s.length&&s.length>e+1&&(t=Array.prototype.slice.call(s,e+1))}this._microtasks.push({func:r.invoke,args:t,target:r.data&&r.data.target});break;case"macroTask":switch(r.source){case"setTimeout":r.data.handleId=this._setTimeout(r.invoke,r.data.delay,Array.prototype.slice.call(r.data.args,2));break;case"setImmediate":r.data.handleId=this._setTimeout(r.invoke,0,Array.prototype.slice.call(r.data.args,1));break;case"setInterval":r.data.handleId=this._setInterval(r.invoke,r.data.delay,Array.prototype.slice.call(r.data.args,2));break;case"XMLHttpRequest.send":throw new Error("Cannot make XHRs from within a fake async test. Request URL: "+r.data.url);case"requestAnimationFrame":case"webkitRequestAnimationFrame":case"mozRequestAnimationFrame":r.data.handleId=this._setTimeout(r.invoke,16,r.data.args,this.trackPendingRequestAnimationFrame);break;default:const e=this.findMacroTaskOption(r);if(e){const t=r.data&&r.data.args,n=t&&t.length>1?t[1]:0;let s=e.callbackArgs?e.callbackArgs:t;e.isPeriodic?(r.data.handleId=this._setInterval(r.invoke,n,s),r.data.isPeriodic=!0):r.data.handleId=this._setTimeout(r.invoke,n,s);break}throw new Error("Unknown macroTask scheduled in fake async test: "+r.source)}break;case"eventTask":r=e.scheduleTask(n,r)}return r}onCancelTask(e,t,n,r){switch(r.source){case"setTimeout":case"requestAnimationFrame":case"webkitRequestAnimationFrame":case"mozRequestAnimationFrame":return this._clearTimeout(r.data.handleId);case"setInterval":return this._clearInterval(r.data.handleId);default:const t=this.findMacroTaskOption(r);if(t){const e=r.data.handleId;return t.isPeriodic?this._clearInterval(e):this._clearTimeout(e)}return e.cancelTask(n,r)}}onInvoke(e,t,n,r,s,i,a){try{return o.patchDate(),e.invoke(n,r,s,i,a)}finally{this.patchDateLocked||o.resetDate()}}findMacroTaskOption(e){if(!this.macroTaskOptions)return null;for(let t=0;t<this.macroTaskOptions.length;t++){const n=this.macroTaskOptions[t];if(n.source===e.source)return n}return null}onHandleError(e,t,n,r){return this._lastError=r,!1}}Zone.FakeAsyncTestZoneSpec=o}("object"==typeof window&&window||"object"==typeof self&&self||global),Zone.__load_patch("fakeasync",((e,t,n)=>{const r=t&&t.FakeAsyncTestZoneSpec;function s(){return t&&t.ProxyZoneSpec}let o=null;function i(){o&&o.unlockDatePatch(),o=null,s()&&s().assertPresent().resetDelegate()}function a(){if(null==o&&(o=t.current.get("FakeAsyncTestZoneSpec"),null==o))throw new Error("The code should be running in the fakeAsync zone to call this function");return o}function c(){a().flushMicrotasks()}t[n.symbol("fakeAsyncTest")]={resetFakeAsyncZone:i,flushMicrotasks:c,discardPeriodicTasks:function l(){a().pendingPeriodicTimers.length=0},tick:function u(e=0,t=!1){a().tick(e,null,t)},flush:function h(e){return a().flush(e)},fakeAsync:function p(e){const n=function(...n){const a=s();if(!a)throw new Error("ProxyZoneSpec is needed for the async() test helper but could not be found. Please make sure that your environment includes zone.js/plugins/proxy");const l=a.assertPresent();if(t.current.get("FakeAsyncTestZoneSpec"))throw new Error("fakeAsync() calls can not be nested");try{if(!o){if(l.getDelegate()instanceof r)throw new Error("fakeAsync() calls can not be nested");o=new r}let t;const s=l.getDelegate();l.setDelegate(o),o.lockDatePatch();try{t=e.apply(this,n),c()}finally{l.setDelegate(s)}if(o.pendingPeriodicTimers.length>0)throw new Error(`${o.pendingPeriodicTimers.length} periodic timer(s) still in the queue.`);if(o.pendingTimers.length>0)throw new Error(`${o.pendingTimers.length} timer(s) still in the queue.`);return t}finally{i()}};return n.isFakeAsync=!0,n}}}),!0),Zone.__load_patch("promisefortest",((e,t,n)=>{const r=n.symbol("state"),s=n.symbol("parentUnresolved");Promise[n.symbol("patchPromiseForTest")]=function e(){let n=Promise[t.__symbol__("ZonePromiseThen")];n||(n=Promise[t.__symbol__("ZonePromiseThen")]=Promise.prototype.then,Promise.prototype.then=function(){const e=n.apply(this,arguments);if(null===this[r]){const n=t.current.get("AsyncTestZoneSpec");n&&(n.unresolvedChainedPromiseCount++,e[s]=!0)}return e})},Promise[n.symbol("unPatchPromiseForTest")]=function e(){const n=Promise[t.__symbol__("ZonePromiseThen")];n&&(Promise.prototype.then=n,Promise[t.__symbol__("ZonePromiseThen")]=void 0)}}));