"use strict";(self.webpackChunkQuranVidGen=self.webpackChunkQuranVidGen||[]).push([[937],{937:(O,x,w)=>{w.r(x),w.d(x,{FilesystemWeb:()=>q});var l=w(467),R=w(83),D=w(889);function A(p){const _=p.split("/").filter(e=>"."!==e),r=[];return _.forEach(e=>{".."===e&&r.length>0&&".."!==r[r.length-1]?r.pop():r.push(e)}),r.join("/")}let q=(()=>{class p extends R.E_{constructor(){var r;super(...arguments),r=this,this.DB_VERSION=1,this.DB_NAME="Disc",this._writeCmds=["add","put","delete"],this.downloadFile=function(){var e=(0,l.A)(function*(n){var t,s;const d=(0,R.EA)(n,n.webFetchExtra),o=yield fetch(n.url,d);let i;if(n.progress)if(o?.body){const c=o.body.getReader();let h=0;const u=[],g=o.headers.get("content-type"),v=parseInt(o.headers.get("content-length")||"0",10);for(;;){const{done:f,value:m}=yield c.read();if(f)break;u.push(m),h+=m?.length||0,r.notifyListeners("progress",{url:n.url,bytes:h,contentLength:v})}const b=new Uint8Array(h);let y=0;for(const f of u)typeof f>"u"||(b.set(f,y),y+=f.length);i=new Blob([b.buffer],{type:g||void 0})}else i=new Blob;else i=yield o.blob();return{path:(yield r.writeFile({path:n.path,directory:null!==(t=n.directory)&&void 0!==t?t:void 0,recursive:null!==(s=n.recursive)&&void 0!==s&&s,data:i})).uri,blob:i}});return function(n){return e.apply(this,arguments)}}()}initDb(){var r=this;return(0,l.A)(function*(){if(void 0!==r._db)return r._db;if(!("indexedDB"in window))throw r.unavailable("This browser doesn't support IndexedDB");return new Promise((e,n)=>{const t=indexedDB.open(r.DB_NAME,r.DB_VERSION);t.onupgradeneeded=p.doUpgrade,t.onsuccess=()=>{r._db=t.result,e(t.result)},t.onerror=()=>n(t.error),t.onblocked=()=>{console.warn("db blocked")}})})()}static doUpgrade(r){const n=r.target.result;n.objectStoreNames.contains("FileStorage")&&n.deleteObjectStore("FileStorage"),n.createObjectStore("FileStorage",{keyPath:"path"}).createIndex("by_folder","folder")}dbRequest(r,e){var n=this;return(0,l.A)(function*(){const t=-1!==n._writeCmds.indexOf(r)?"readwrite":"readonly";return n.initDb().then(s=>new Promise((d,o)=>{const c=s.transaction(["FileStorage"],t).objectStore("FileStorage")[r](...e);c.onsuccess=()=>d(c.result),c.onerror=()=>o(c.error)}))})()}dbIndexRequest(r,e,n){var t=this;return(0,l.A)(function*(){const s=-1!==t._writeCmds.indexOf(e)?"readwrite":"readonly";return t.initDb().then(d=>new Promise((o,i)=>{const u=d.transaction(["FileStorage"],s).objectStore("FileStorage").index(r)[e](...n);u.onsuccess=()=>o(u.result),u.onerror=()=>i(u.error)}))})()}getPath(r,e){const n=void 0!==e?e.replace(/^[/]+|[/]+$/g,""):"";let t="";return void 0!==r&&(t+="/"+r),""!==e&&(t+="/"+n),t}clear(){var r=this;return(0,l.A)(function*(){(yield r.initDb()).transaction(["FileStorage"],"readwrite").objectStore("FileStorage").clear()})()}readFile(r){var e=this;return(0,l.A)(function*(){const n=e.getPath(r.directory,r.path),t=yield e.dbRequest("get",[n]);if(void 0===t)throw Error("File does not exist.");return{data:t.content?t.content:""}})()}writeFile(r){var e=this;return(0,l.A)(function*(){const n=e.getPath(r.directory,r.path);let t=r.data;const s=r.encoding,d=r.recursive,o=yield e.dbRequest("get",[n]);if(o&&"directory"===o.type)throw Error("The supplied path is a directory.");const i=n.substr(0,n.lastIndexOf("/"));if(void 0===(yield e.dbRequest("get",[i]))){const u=i.indexOf("/",1);if(-1!==u){const g=i.substr(u);yield e.mkdir({path:g,directory:r.directory,recursive:d})}}if(!(s||t instanceof Blob||(t=t.indexOf(",")>=0?t.split(",")[1]:t,e.isBase64String(t))))throw Error("The supplied data is not valid base64 content.");const c=Date.now(),h={path:n,folder:i,type:"file",size:t instanceof Blob?t.size:t.length,ctime:c,mtime:c,content:t};return yield e.dbRequest("put",[h]),{uri:h.path}})()}appendFile(r){var e=this;return(0,l.A)(function*(){const n=e.getPath(r.directory,r.path);let t=r.data;const s=r.encoding,d=n.substr(0,n.lastIndexOf("/")),o=Date.now();let i=o;const a=yield e.dbRequest("get",[n]);if(a&&"directory"===a.type)throw Error("The supplied path is a directory.");if(void 0===(yield e.dbRequest("get",[d]))){const u=d.indexOf("/",1);if(-1!==u){const g=d.substr(u);yield e.mkdir({path:g,directory:r.directory,recursive:!0})}}if(!s&&!e.isBase64String(t))throw Error("The supplied data is not valid base64 content.");if(void 0!==a){if(a.content instanceof Blob)throw Error("The occupied entry contains a Blob object which cannot be appended to.");t=void 0===a.content||s?a.content+t:btoa(atob(a.content)+atob(t)),i=a.ctime}const h={path:n,folder:d,type:"file",size:t.length,ctime:i,mtime:o,content:t};yield e.dbRequest("put",[h])})()}deleteFile(r){var e=this;return(0,l.A)(function*(){const n=e.getPath(r.directory,r.path);if(void 0===(yield e.dbRequest("get",[n])))throw Error("File does not exist.");if(0!==(yield e.dbIndexRequest("by_folder","getAllKeys",[IDBKeyRange.only(n)])).length)throw Error("Folder is not empty.");yield e.dbRequest("delete",[n])})()}mkdir(r){var e=this;return(0,l.A)(function*(){const n=e.getPath(r.directory,r.path),t=r.recursive,s=n.substr(0,n.lastIndexOf("/")),d=(n.match(/\//g)||[]).length,o=yield e.dbRequest("get",[s]),i=yield e.dbRequest("get",[n]);if(1===d)throw Error("Cannot create Root directory");if(void 0!==i)throw Error("Current directory does already exist.");if(!t&&2!==d&&void 0===o)throw Error("Parent directory must exist");if(t&&2!==d&&void 0===o){const h=s.substr(s.indexOf("/",1));yield e.mkdir({path:h,directory:r.directory,recursive:t})}const a=Date.now(),c={path:n,folder:s,type:"directory",size:0,ctime:a,mtime:a};yield e.dbRequest("put",[c])})()}rmdir(r){var e=this;return(0,l.A)(function*(){const{path:n,directory:t,recursive:s}=r,d=e.getPath(t,n),o=yield e.dbRequest("get",[d]);if(void 0===o)throw Error("Folder does not exist.");if("directory"!==o.type)throw Error("Requested path is not a directory");const i=yield e.readdir({path:n,directory:t});if(0!==i.files.length&&!s)throw Error("Folder is not empty");for(const a of i.files){const c=`${n}/${a.name}`;"file"===(yield e.stat({path:c,directory:t})).type?yield e.deleteFile({path:c,directory:t}):yield e.rmdir({path:c,directory:t,recursive:s})}yield e.dbRequest("delete",[d])})()}readdir(r){var e=this;return(0,l.A)(function*(){const n=e.getPath(r.directory,r.path),t=yield e.dbRequest("get",[n]);if(""!==r.path&&void 0===t)throw Error("Folder does not exist.");const s=yield e.dbIndexRequest("by_folder","getAllKeys",[IDBKeyRange.only(n)]);return{files:yield Promise.all(s.map(function(){var o=(0,l.A)(function*(i){let a=yield e.dbRequest("get",[i]);return void 0===a&&(a=yield e.dbRequest("get",[i+"/"])),{name:i.substring(n.length+1),type:a.type,size:a.size,ctime:a.ctime,mtime:a.mtime,uri:a.path}});return function(i){return o.apply(this,arguments)}}()))}})()}getUri(r){var e=this;return(0,l.A)(function*(){const n=e.getPath(r.directory,r.path);let t=yield e.dbRequest("get",[n]);return void 0===t&&(t=yield e.dbRequest("get",[n+"/"])),{uri:t?.path||n}})()}stat(r){var e=this;return(0,l.A)(function*(){const n=e.getPath(r.directory,r.path);let t=yield e.dbRequest("get",[n]);if(void 0===t&&(t=yield e.dbRequest("get",[n+"/"])),void 0===t)throw Error("Entry does not exist.");return{type:t.type,size:t.size,ctime:t.ctime,mtime:t.mtime,uri:t.path}})()}rename(r){var e=this;return(0,l.A)(function*(){yield e._copy(r,!0)})()}copy(r){var e=this;return(0,l.A)(function*(){return e._copy(r,!1)})()}requestPermissions(){return(0,l.A)(function*(){return{publicStorage:"granted"}})()}checkPermissions(){return(0,l.A)(function*(){return{publicStorage:"granted"}})()}_copy(r){var e=this;return(0,l.A)(function*(n,t=!1){let{toDirectory:s}=n;const{to:d,from:o,directory:i}=n;if(!d||!o)throw Error("Both to and from must be provided");s||(s=i);const a=e.getPath(i,o),c=e.getPath(s,d);if(a===c)return{uri:c};if(function F(p,_){p=A(p),_=A(_);const r=p.split("/"),e=_.split("/");return p!==_&&r.every((n,t)=>n===e[t])}(a,c))throw Error("To path cannot contain the from path");let h;try{h=yield e.stat({path:d,directory:s})}catch{const y=d.split("/");y.pop();const f=y.join("/");if(y.length>0&&"directory"!==(yield e.stat({path:f,directory:s})).type)throw new Error("Parent directory of the to path is a file")}if(h&&"directory"===h.type)throw new Error("Cannot overwrite a directory with a file");const u=yield e.stat({path:o,directory:i}),g=function(){var b=(0,l.A)(function*(y,f,m){const E=e.getPath(s,y),P=yield e.dbRequest("get",[E]);P.ctime=f,P.mtime=m,yield e.dbRequest("put",[P])});return function(f,m,E){return b.apply(this,arguments)}}(),v=u.ctime?u.ctime:Date.now();switch(u.type){case"file":{const b=yield e.readFile({path:o,directory:i});let y;t&&(yield e.deleteFile({path:o,directory:i})),!(b.data instanceof Blob)&&!e.isBase64String(b.data)&&(y=D.Wi.UTF8);const f=yield e.writeFile({path:d,directory:s,data:b.data,encoding:y});return t&&(yield g(d,v,u.mtime)),f}case"directory":{if(h)throw Error("Cannot move a directory over an existing object");try{yield e.mkdir({path:d,directory:s,recursive:!1}),t&&(yield g(d,v,u.mtime))}catch{}const b=(yield e.readdir({path:o,directory:i})).files;for(const y of b)yield e._copy({from:`${o}/${y.name}`,to:`${d}/${y.name}`,directory:i,toDirectory:s},t);t&&(yield e.rmdir({path:o,directory:i}))}}return{uri:c}}).apply(this,arguments)}isBase64String(r){try{return btoa(atob(r))==r}catch{return!1}}}return p._debug=!0,p})()}}]);