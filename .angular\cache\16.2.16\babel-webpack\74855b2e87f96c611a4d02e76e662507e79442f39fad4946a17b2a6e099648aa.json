{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/\\u062A\\u0637\\u0628\\u064A\\u0642 \\u0646\\u0627\\u0634\\u0631 \\u0627\\u064A\\u0627\\u062A \\u0642\\u0626\\u0627\\u0646\\u064A\\u0629/QuranVidGen/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../Services/link-shortener.service\";\nimport * as i2 from \"../../Services/admob.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"primeng/button\";\nfunction LinkShortenerComponent_div_0_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"div\", 39)(2, \"div\", 40);\n    i0.ɵɵelement(3, \"i\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 41)(5, \"h3\");\n    i0.ɵɵtext(6, \"\\u062D\\u0627\\u0644\\u0629 \\u0627\\u0644\\u0648\\u0635\\u0648\\u0644\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r1.getTimeRemainingText());\n  }\n}\nfunction LinkShortenerComponent_div_0_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"h3\");\n    i0.ɵɵtext(2, \"\\u0627\\u0644\\u0631\\u0627\\u0628\\u0637 \\u0627\\u0644\\u0645\\u0637\\u0644\\u0648\\u0628 \\u0627\\u062E\\u062A\\u0635\\u0627\\u0631\\u0647:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 43)(4, \"div\", 44);\n    i0.ɵɵelement(5, \"i\", 45);\n    i0.ɵɵelementStart(6, \"span\", 46);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"p-button\", 47);\n    i0.ɵɵlistener(\"onClick\", function LinkShortenerComponent_div_0_div_11_Template_p_button_onClick_8_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.openCurrentLink());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate((ctx_r2.status == null ? null : ctx_r2.status.currentLink) || \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"outlined\", true);\n  }\n}\nfunction LinkShortenerComponent_div_0_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵelement(1, \"i\", 49);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.adError);\n  }\n}\nfunction LinkShortenerComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"div\", 4)(4, \"div\", 5);\n    i0.ɵɵelement(5, \"i\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h2\", 7);\n    i0.ɵɵtext(7, \"\\u0627\\u062E\\u062A\\u0635\\u0627\\u0631 \\u0631\\u0627\\u0628\\u0637 \\u0644\\u0644\\u0645\\u062A\\u0627\\u0628\\u0639\\u0629\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 8);\n    i0.ɵɵtext(9, \"\\u064A\\u0631\\u062C\\u0649 \\u0627\\u062E\\u062A\\u0635\\u0627\\u0631 \\u0631\\u0627\\u0628\\u0637 \\u0648\\u0627\\u062D\\u062F \\u0644\\u0627\\u0633\\u062A\\u062E\\u062F\\u0627\\u0645 \\u0627\\u0644\\u062A\\u0637\\u0628\\u064A\\u0642 \\u0644\\u0645\\u062F\\u0629 24 \\u0633\\u0627\\u0639\\u0629\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(10, LinkShortenerComponent_div_0_div_10_Template, 9, 1, \"div\", 9);\n    i0.ɵɵtemplate(11, LinkShortenerComponent_div_0_div_11_Template, 9, 2, \"div\", 10);\n    i0.ɵɵelementStart(12, \"div\", 11)(13, \"h3\");\n    i0.ɵɵtext(14, \"\\u062E\\u0637\\u0648\\u0627\\u062A \\u0627\\u0644\\u0627\\u062E\\u062A\\u0635\\u0627\\u0631:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"ol\", 12)(16, \"li\");\n    i0.ɵɵtext(17, \"\\u0627\\u0646\\u0642\\u0631 \\u0639\\u0644\\u0649 \\\"\\u0641\\u062A\\u062D \\u0627\\u0644\\u0631\\u0627\\u0628\\u0637\\\" \\u0623\\u0639\\u0644\\u0627\\u0647\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"li\");\n    i0.ɵɵtext(19, \"\\u0627\\u0646\\u062A\\u0638\\u0631 \\u062D\\u062A\\u0649 \\u064A\\u062A\\u0645 \\u062A\\u062D\\u0645\\u064A\\u0644 \\u0627\\u0644\\u0635\\u0641\\u062D\\u0629 (5 \\u062B\\u0648\\u0627\\u0646\\u064D)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"li\");\n    i0.ɵɵtext(21, \"\\u0627\\u0646\\u0642\\u0631 \\u0639\\u0644\\u0649 \\\"\\u0645\\u062A\\u0627\\u0628\\u0639\\u0629\\\" \\u0623\\u0648 \\\"Skip Ad\\\" \\u0641\\u064A \\u0627\\u0644\\u0635\\u0641\\u062D\\u0629\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"li\");\n    i0.ɵɵtext(23, \"\\u0627\\u0631\\u062C\\u0639 \\u0647\\u0646\\u0627 \\u0648\\u0627\\u0646\\u0642\\u0631 \\u0639\\u0644\\u0649 \\\"\\u0645\\u062A\\u0627\\u0628\\u0639\\u0629\\\" \\u0623\\u062F\\u0646\\u0627\\u0647\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 13)(25, \"p-button\", 14);\n    i0.ɵɵlistener(\"onClick\", function LinkShortenerComponent_div_0_Template_p_button_onClick_25_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onContinueClicked());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 15)(27, \"p-button\", 16);\n    i0.ɵɵlistener(\"onClick\", function LinkShortenerComponent_div_0_Template_p_button_onClick_27_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.onLinkNotWorking());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"p-button\", 17);\n    i0.ɵɵlistener(\"onClick\", function LinkShortenerComponent_div_0_Template_p_button_onClick_28_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.onProblemOccurred());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"div\", 18)(30, \"div\", 19);\n    i0.ɵɵelement(31, \"div\", 20);\n    i0.ɵɵelementStart(32, \"span\", 21);\n    i0.ɵɵtext(33, \"\\u0623\\u0648\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(34, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\", 22)(36, \"div\", 23);\n    i0.ɵɵelement(37, \"i\", 24);\n    i0.ɵɵelementStart(38, \"div\")(39, \"h4\");\n    i0.ɵɵtext(40, \"\\u0645\\u0634\\u0627\\u0647\\u062F\\u0629 \\u0625\\u0639\\u0644\\u0627\\u0646 \\u0642\\u0635\\u064A\\u0631\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"p\");\n    i0.ɵɵtext(42, \"\\u0627\\u062D\\u0635\\u0644 \\u0639\\u0644\\u0649 3 \\u0633\\u0627\\u0639\\u0627\\u062A \\u0645\\u0646 \\u0627\\u0644\\u0627\\u0633\\u062A\\u062E\\u062F\\u0627\\u0645 \\u0628\\u062F\\u0644\\u0627\\u064B \\u0645\\u0646 24 \\u0633\\u0627\\u0639\\u0629\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(43, \"p-button\", 25);\n    i0.ɵɵlistener(\"onClick\", function LinkShortenerComponent_div_0_Template_p_button_onClick_43_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onWatchAd());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(44, LinkShortenerComponent_div_0_div_44_Template, 4, 1, \"div\", 26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(45, \"div\", 27)(46, \"div\", 28);\n    i0.ɵɵelement(47, \"i\", 29);\n    i0.ɵɵelementStart(48, \"div\")(49, \"h4\");\n    i0.ɵɵtext(50, \"\\u062A\\u062D\\u062A\\u0627\\u062C \\u0645\\u0633\\u0627\\u0639\\u062F\\u0629\\u061F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"p\");\n    i0.ɵɵtext(52, \"\\u0625\\u0630\\u0627 \\u0648\\u0627\\u062C\\u0647\\u062A \\u0645\\u0634\\u0643\\u0644\\u0629\\u060C \\u064A\\u064F\\u0631\\u062C\\u0649 \\u0627\\u0644\\u0645\\u062D\\u0627\\u0648\\u0644\\u0629 \\u0628\\u0631\\u0627\\u0628\\u0637 \\u0645\\u062E\\u062A\\u0644\\u0641 \\u0623\\u0648 \\u0627\\u0644\\u062A\\u0648\\u0627\\u0635\\u0644 \\u0645\\u0639 \\u0627\\u0644\\u0645\\u0637\\u0648\\u0631\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(53, \"div\", 30)(54, \"p-button\", 31);\n    i0.ɵɵlistener(\"onClick\", function LinkShortenerComponent_div_0_Template_p_button_onClick_54_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.refreshStatus());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"p-button\", 32);\n    i0.ɵɵlistener(\"onClick\", function LinkShortenerComponent_div_0_Template_p_button_onClick_55_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.openSupportEmail());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(56, \"div\", 33)(57, \"div\", 34);\n    i0.ɵɵelement(58, \"i\", 35);\n    i0.ɵɵelementStart(59, \"span\");\n    i0.ɵɵtext(60, \"\\u0622\\u0645\\u0646 \\u0648\\u0645\\u0636\\u0645\\u0648\\u0646\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(61, \"div\", 34);\n    i0.ɵɵelement(62, \"i\", 36);\n    i0.ɵɵelementStart(63, \"span\");\n    i0.ɵɵtext(64, \"\\u0635\\u0627\\u0644\\u062D \\u0644\\u0645\\u062F\\u0629 24 \\u0633\\u0627\\u0639\\u0629\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(65, \"div\", 34);\n    i0.ɵɵelement(66, \"i\", 37);\n    i0.ɵɵelementStart(67, \"span\");\n    i0.ɵɵtext(68, \"\\u0645\\u062C\\u0627\\u0646\\u064A \\u062A\\u0645\\u0627\\u0645\\u0627\\u064B\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.status);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.status == null ? null : ctx_r0.status.currentLink);\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"loading\", ctx_r0.isProcessing);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"outlined\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"outlined\", true);\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"loading\", ctx_r0.isLoadingAd);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.adError);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"text\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"text\", true);\n  }\n}\nexport let LinkShortenerComponent = /*#__PURE__*/(() => {\n  class LinkShortenerComponent {\n    constructor(linkShortenerService, adMobService) {\n      this.linkShortenerService = linkShortenerService;\n      this.adMobService = adMobService;\n      this.status = null;\n      this.showInterface = false;\n      this.isProcessing = false;\n      this.isLoadingAd = false;\n      this.adError = '';\n    }\n    ngOnInit() {\n      this.statusSubscription = this.linkShortenerService.status$.subscribe(status => {\n        this.status = status;\n        this.showInterface = status.isRequired;\n      });\n      // Preload ad for better user experience\n      this.adMobService.preloadAd();\n    }\n    ngOnDestroy() {\n      if (this.statusSubscription) {\n        this.statusSubscription.unsubscribe();\n      }\n    }\n    openCurrentLink() {\n      if (this.status?.currentLink) {\n        window.open(this.status.currentLink, '_blank');\n      }\n    }\n    onContinueClicked() {\n      this.isProcessing = true;\n      // Simulate processing time\n      setTimeout(() => {\n        this.linkShortenerService.confirmLinkShortened();\n        this.isProcessing = false;\n      }, 1000);\n    }\n    onLinkNotWorking() {\n      this.linkShortenerService.reportLinkProblem();\n    }\n    onProblemOccurred() {\n      this.linkShortenerService.reportGeneralProblem();\n    }\n    onWatchAd() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.isLoadingAd = true;\n        _this.adError = '';\n        try {\n          // Load ad if not already loaded\n          if (!_this.adMobService.isAdLoaded()) {\n            const loaded = yield _this.adMobService.loadRewardedAd();\n            if (!loaded) {\n              throw new Error('فشل في تحميل الإعلان');\n            }\n          }\n          // Show the ad\n          const adResult$ = yield _this.adMobService.showRewardedAd();\n          adResult$.subscribe({\n            next: result => {\n              _this.isLoadingAd = false;\n              if (result.success && result.rewardEarned) {\n                // User watched the ad successfully\n                _this.linkShortenerService.confirmAdWatched();\n              } else {\n                _this.adError = result.error || 'لم يتم إكمال مشاهدة الإعلان';\n              }\n            },\n            error: error => {\n              _this.isLoadingAd = false;\n              _this.adError = 'حدث خطأ أثناء عرض الإعلان';\n              console.error('Ad error:', error);\n            }\n          });\n        } catch (error) {\n          _this.isLoadingAd = false;\n          _this.adError = 'فشل في تحميل الإعلان. يرجى المحاولة مرة أخرى.';\n          console.error('Ad loading error:', error);\n        }\n      })();\n    }\n    getTimeRemainingText() {\n      if (!this.status) return '';\n      if (this.status.adTimeRemaining > 0) {\n        return `الوقت المتبقي من مشاهدة الإعلان: ${this.linkShortenerService.formatTimeRemaining(this.status.adTimeRemaining)}`;\n      }\n      if (this.status.timeRemaining > 0) {\n        return `الوقت المتبقي: ${this.linkShortenerService.formatTimeRemaining(this.status.timeRemaining)}`;\n      }\n      return 'يجب اختصار رابط جديد';\n    }\n    openSupportEmail() {\n      const email = '<EMAIL>';\n      const subject = 'مشكلة في اختصار الروابط - مولد مقاطع القرآن الكريم';\n      const body = `السلام عليكم ورحمة الله وبركاته،\n\nأواجه مشكلة في اختصار الروابط:\n\nالرابط الحالي: ${this.status?.currentLink || 'غير متوفر'}\nآخر اختصار: ${this.status?.lastShortenedAt?.toLocaleString('ar') || 'لم يتم'}\n\nيرجى المساعدة في حل هذه المشكلة.\n\nشكراً لكم`;\n      const mailtoLink = `mailto:${email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;\n      window.open(mailtoLink, '_blank');\n    }\n    refreshStatus() {\n      this.linkShortenerService.forceRefreshStatus();\n    }\n    static {\n      this.ɵfac = function LinkShortenerComponent_Factory(t) {\n        return new (t || LinkShortenerComponent)(i0.ɵɵdirectiveInject(i1.LinkShortenerService), i0.ɵɵdirectiveInject(i2.AdMobService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: LinkShortenerComponent,\n        selectors: [[\"app-link-shortener\"]],\n        decls: 1,\n        vars: 1,\n        consts: [[\"class\", \"shortener-overlay\", 4, \"ngIf\"], [1, \"shortener-overlay\"], [1, \"shortener-container\"], [1, \"shortener-content\"], [1, \"header-section\"], [1, \"icon-container\"], [1, \"pi\", \"pi-link\"], [1, \"title\"], [1, \"subtitle\"], [\"class\", \"status-section\", 4, \"ngIf\"], [\"class\", \"link-section\", 4, \"ngIf\"], [1, \"instructions-section\"], [1, \"instructions-list\"], [1, \"action-buttons\"], [\"label\", \"\\u0645\\u062A\\u0627\\u0628\\u0639\\u0629\", \"icon\", \"pi pi-check\", \"severity\", \"success\", \"size\", \"large\", \"loadingIcon\", \"pi pi-spin pi-spinner\", \"styleClass\", \"continue-btn\", 3, \"loading\", \"onClick\"], [1, \"secondary-buttons\"], [\"label\", \"\\u0627\\u0644\\u0631\\u0627\\u0628\\u0637 \\u0644\\u0627 \\u064A\\u0639\\u0645\\u0644\", \"icon\", \"pi pi-refresh\", \"severity\", \"warning\", 3, \"outlined\", \"onClick\"], [\"label\", \"\\u062D\\u062F\\u062B\\u062A \\u0645\\u0634\\u0643\\u0644\\u0629\", \"icon\", \"pi pi-exclamation-triangle\", \"severity\", \"danger\", 3, \"outlined\", \"onClick\"], [1, \"alternative-section\"], [1, \"divider-container\"], [1, \"divider-line\"], [1, \"divider-text\"], [1, \"ad-option\"], [1, \"ad-info\"], [1, \"pi\", \"pi-video\"], [\"label\", \"\\u0645\\u0634\\u0627\\u0647\\u062F\\u0629 \\u0625\\u0639\\u0644\\u0627\\u0646\", \"icon\", \"pi pi-play\", \"severity\", \"info\", \"loadingIcon\", \"pi pi-spin pi-spinner\", \"styleClass\", \"ad-btn\", 3, \"loading\", \"onClick\"], [\"class\", \"ad-error\", 4, \"ngIf\"], [1, \"help-section\"], [1, \"help-content\"], [1, \"pi\", \"pi-question-circle\"], [1, \"help-buttons\"], [\"label\", \"\\u062A\\u062D\\u062F\\u064A\\u062B \\u0627\\u0644\\u062D\\u0627\\u0644\\u0629\", \"icon\", \"pi pi-refresh\", \"severity\", \"secondary\", \"size\", \"small\", 3, \"text\", \"onClick\"], [\"label\", \"\\u062A\\u0648\\u0627\\u0635\\u0644 \\u0645\\u0639 \\u0627\\u0644\\u0645\\u0637\\u0648\\u0631\", \"icon\", \"pi pi-envelope\", \"severity\", \"secondary\", \"size\", \"small\", 3, \"text\", \"onClick\"], [1, \"footer-info\"], [1, \"info-item\"], [1, \"pi\", \"pi-shield\"], [1, \"pi\", \"pi-clock\"], [1, \"pi\", \"pi-check\"], [1, \"status-section\"], [1, \"status-card\"], [1, \"status-icon\"], [1, \"status-text\"], [1, \"link-section\"], [1, \"link-container\"], [1, \"link-display\"], [1, \"pi\", \"pi-external-link\"], [1, \"link-text\"], [\"label\", \"\\u0641\\u062A\\u062D \\u0627\\u0644\\u0631\\u0627\\u0628\\u0637\", \"icon\", \"pi pi-external-link\", \"severity\", \"primary\", 3, \"outlined\", \"onClick\"], [1, \"ad-error\"], [1, \"pi\", \"pi-exclamation-triangle\"]],\n        template: function LinkShortenerComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, LinkShortenerComponent_div_0_Template, 69, 9, \"div\", 0);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", ctx.showInterface);\n          }\n        },\n        dependencies: [i3.NgIf, i4.Button],\n        styles: [\".shortener-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100vw;height:100vh;background:linear-gradient(135deg,rgba(102,126,234,.9) 0%,rgba(118,75,162,.9) 100%);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);z-index:9998;display:flex;align-items:center;justify-content:center;padding:1rem;overflow-y:auto}.shortener-container[_ngcontent-%COMP%]{background:white;border-radius:20px;box-shadow:0 25px 50px #00000040;max-width:600px;width:100%;max-height:95vh;overflow-y:auto;animation:_ngcontent-%COMP%_slideInUp .4s ease-out}@keyframes _ngcontent-%COMP%_slideInUp{0%{opacity:0;transform:translateY(50px) scale(.95)}to{opacity:1;transform:translateY(0) scale(1)}}.shortener-content[_ngcontent-%COMP%]{padding:2.5rem}.header-section[_ngcontent-%COMP%]{text-align:center;margin-bottom:2rem}.header-section[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);width:80px;height:80px;border-radius:50%;display:flex;align-items:center;justify-content:center;margin:0 auto 1.5rem;box-shadow:0 8px 20px #667eea4d}.header-section[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%]   .pi[_ngcontent-%COMP%]{font-size:2.5rem;color:#fff}.header-section[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{color:#2c3e50;font-size:2rem;font-weight:700;margin-bottom:.5rem}.header-section[_ngcontent-%COMP%]   .subtitle[_ngcontent-%COMP%]{color:#6c757d;font-size:1.1rem;line-height:1.5;margin:0}.status-section[_ngcontent-%COMP%]{margin-bottom:2rem}.status-card[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1rem;background:linear-gradient(135deg,#e3f2fd 0%,#bbdefb 100%);border:1px solid #2196f3;border-radius:12px;padding:1.5rem}.status-card[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%]{background:#2196f3;color:#fff;width:50px;height:50px;border-radius:50%;display:flex;align-items:center;justify-content:center;flex-shrink:0}.status-card[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%]   .pi[_ngcontent-%COMP%]{font-size:1.5rem}.status-card[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#1565c0;font-size:1.2rem;margin-bottom:.5rem}.status-card[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#1976d2;margin:0;font-weight:500}.link-section[_ngcontent-%COMP%]{margin-bottom:2rem}.link-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#495057;font-size:1.2rem;margin-bottom:1rem}.link-container[_ngcontent-%COMP%]{background:#f8f9fa;border:2px solid #e9ecef;border-radius:12px;padding:1.5rem}.link-container[_ngcontent-%COMP%]   .link-display[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.75rem;margin-bottom:1rem;padding:1rem;background:white;border-radius:8px;border:1px solid #dee2e6}.link-container[_ngcontent-%COMP%]   .link-display[_ngcontent-%COMP%]   .pi[_ngcontent-%COMP%]{color:#667eea;font-size:1.2rem;flex-shrink:0}.link-container[_ngcontent-%COMP%]   .link-display[_ngcontent-%COMP%]   .link-text[_ngcontent-%COMP%]{color:#495057;font-family:monospace;font-size:.9rem;word-break:break-all;flex:1}.link-container[_ngcontent-%COMP%]     .p-button{width:100%}.instructions-section[_ngcontent-%COMP%]{margin-bottom:2rem}.instructions-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#495057;font-size:1.2rem;margin-bottom:1rem}.instructions-list[_ngcontent-%COMP%]{background:linear-gradient(135deg,#fff3e0 0%,#ffe0b2 100%);border:1px solid #ff9800;border-radius:12px;padding:1.5rem;margin:0}.instructions-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{color:#e65100;font-weight:500;margin-bottom:.75rem;line-height:1.5}.instructions-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:last-child{margin-bottom:0}.action-buttons[_ngcontent-%COMP%]{margin-bottom:2rem}.action-buttons[_ngcontent-%COMP%]     .continue-btn .p-button{width:100%;padding:1rem 2rem;font-size:1.1rem;font-weight:600;border-radius:12px;margin-bottom:1rem;background:linear-gradient(135deg,#4caf50 0%,#45a049 100%);border:none;transition:all .3s ease}.action-buttons[_ngcontent-%COMP%]     .continue-btn .p-button:hover:not(:disabled){transform:translateY(-2px);box-shadow:0 8px 20px #4caf504d}.secondary-buttons[_ngcontent-%COMP%]{display:flex;gap:.75rem}.secondary-buttons[_ngcontent-%COMP%]     .p-button{flex:1;padding:.75rem 1rem;border-radius:8px;font-weight:500}.alternative-section[_ngcontent-%COMP%]{margin-bottom:2rem}.divider-container[_ngcontent-%COMP%]{display:flex;align-items:center;margin:1.5rem 0}.divider-container[_ngcontent-%COMP%]   .divider-line[_ngcontent-%COMP%]{flex:1;height:1px;background:#dee2e6}.divider-container[_ngcontent-%COMP%]   .divider-text[_ngcontent-%COMP%]{padding:0 1rem;color:#6c757d;font-weight:500;background:white}.ad-option[_ngcontent-%COMP%]{background:linear-gradient(135deg,#e8f5e8 0%,#c8e6c9 100%);border:1px solid #4caf50;border-radius:12px;padding:1.5rem}.ad-option[_ngcontent-%COMP%]   .ad-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1rem;margin-bottom:1rem}.ad-option[_ngcontent-%COMP%]   .ad-info[_ngcontent-%COMP%]   .pi[_ngcontent-%COMP%]{font-size:2rem;color:#4caf50;flex-shrink:0}.ad-option[_ngcontent-%COMP%]   .ad-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#2e7d32;font-size:1.1rem;margin-bottom:.5rem}.ad-option[_ngcontent-%COMP%]   .ad-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#388e3c;margin:0;font-size:.9rem}.ad-option[_ngcontent-%COMP%]     .ad-btn .p-button{width:100%;padding:.75rem 1.5rem;border-radius:8px;font-weight:600}.ad-option[_ngcontent-%COMP%]   .ad-error[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;background:#f8d7da;color:#721c24;padding:.75rem;border-radius:6px;margin-top:1rem;font-size:.9rem}.ad-option[_ngcontent-%COMP%]   .ad-error[_ngcontent-%COMP%]   .pi[_ngcontent-%COMP%]{font-size:1rem}.help-section[_ngcontent-%COMP%]{margin-bottom:2rem}.help-section[_ngcontent-%COMP%]   .help-content[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:1rem;margin-bottom:1rem}.help-section[_ngcontent-%COMP%]   .help-content[_ngcontent-%COMP%]   .pi[_ngcontent-%COMP%]{font-size:1.5rem;color:#6c757d;margin-top:.2rem;flex-shrink:0}.help-section[_ngcontent-%COMP%]   .help-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#495057;font-size:1rem;margin-bottom:.5rem}.help-section[_ngcontent-%COMP%]   .help-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#6c757d;margin:0;font-size:.9rem;line-height:1.5}.help-section[_ngcontent-%COMP%]   .help-buttons[_ngcontent-%COMP%]{display:flex;gap:1rem;justify-content:center}.help-section[_ngcontent-%COMP%]   .help-buttons[_ngcontent-%COMP%]     .p-button{font-size:.9rem}.footer-info[_ngcontent-%COMP%]{display:flex;justify-content:space-around;padding-top:1.5rem;border-top:1px solid #e9ecef}.footer-info[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:.5rem;text-align:center}.footer-info[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .pi[_ngcontent-%COMP%]{font-size:1.5rem;color:#28a745}.footer-info[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#6c757d;font-size:.85rem;font-weight:500}@media (max-width: 768px){.shortener-overlay[_ngcontent-%COMP%]{padding:.5rem}.shortener-content[_ngcontent-%COMP%]{padding:2rem 1.5rem}.header-section[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%]{width:60px;height:60px}.header-section[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%]   .pi[_ngcontent-%COMP%]{font-size:2rem}.header-section[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:1.6rem}.header-section[_ngcontent-%COMP%]   .subtitle[_ngcontent-%COMP%]{font-size:1rem}.secondary-buttons[_ngcontent-%COMP%]{flex-direction:column}.secondary-buttons[_ngcontent-%COMP%]     .p-button{width:100%}.help-buttons[_ngcontent-%COMP%]{flex-direction:column}.help-buttons[_ngcontent-%COMP%]     .p-button{width:100%}.footer-info[_ngcontent-%COMP%]{flex-direction:column;gap:1rem}}@media (max-width: 480px){.shortener-content[_ngcontent-%COMP%]{padding:1.5rem 1rem}.link-display[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start!important;gap:.5rem!important}.link-display[_ngcontent-%COMP%]   .link-text[_ngcontent-%COMP%]{font-size:.8rem}.ad-info[_ngcontent-%COMP%]{flex-direction:column!important;text-align:center}.ad-info[_ngcontent-%COMP%]   .pi[_ngcontent-%COMP%]{align-self:center!important}}\"]\n      });\n    }\n  }\n  return LinkShortenerComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}