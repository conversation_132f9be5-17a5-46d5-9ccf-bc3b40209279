::ng-deep .privacy-dialog {
  .p-dialog-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-radius: 8px 8px 0 0;
  }
  
  .p-dialog-content {
    padding: 0;
    max-height: 75vh;
    overflow-y: auto;
  }
  
  .p-dialog-footer {
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
  }
}

.privacy-container {
  padding: 2rem;
  line-height: 1.6;
  color: #333;
}

.privacy-content {
  max-width: 800px;
  margin: 0 auto;
}

.privacy-section {
  margin-bottom: 2rem;
  
  h2 {
    color: #28a745;
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
    text-align: center;
    border-bottom: 2px solid #28a745;
    padding-bottom: 0.5rem;
  }
  
  h3 {
    color: #495057;
    font-size: 1.3rem;
    margin-bottom: 1rem;
    border-right: 4px solid #28a745;
    padding-right: 1rem;
  }
  
  h4 {
    color: #495057;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
  }
  
  p {
    margin-bottom: 1rem;
    text-align: justify;
  }
}

.last-updated {
  text-align: center;
  color: #6c757d;
  font-style: italic;
  margin-bottom: 2rem !important;
}

.highlight-box {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  border: 1px solid #28a745;
  border-radius: 12px;
  padding: 2rem;
  margin: 1.5rem 0;
  
  .pi {
    font-size: 2.5rem;
    color: #28a745;
    margin-top: 0.2rem;
  }
  
  h3 {
    color: #28a745;
    margin-bottom: 0.5rem;
    border: none;
    padding: 0;
  }
  
  p {
    margin: 0;
    font-size: 1.1rem;
  }
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  margin: 1rem 0;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1.5rem;
  
  .pi {
    font-size: 1.5rem;
    margin-top: 0.2rem;
  }
  
  h4 {
    color: #495057;
    margin-bottom: 0.5rem;
  }
  
  p {
    margin: 0;
    font-size: 0.9rem;
    color: #6c757d;
  }
}

.process-steps {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin: 1rem 0;
}

.step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  border-right: 4px solid #28a745;
  
  .step-number {
    background: #28a745;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
    flex-shrink: 0;
  }
  
  .step-content {
    h4 {
      color: #28a745;
      margin-bottom: 0.5rem;
    }
    
    p {
      margin: 0;
      color: #6c757d;
    }
  }
}

.external-services {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin: 1rem 0;
}

.service-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border: 1px solid #ffc107;
  border-radius: 8px;
  padding: 1.5rem;
  
  .pi {
    font-size: 2rem;
    color: #856404;
    margin-top: 0.2rem;
  }
  
  h4 {
    color: #856404;
    margin-bottom: 0.5rem;
  }
  
  p {
    margin: 0;
    color: #6c757d;
  }
}

.security-features {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin: 1rem 0;
}

.security-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 6px;
  
  .pi {
    font-size: 1.2rem;
  }
  
  span {
    font-weight: 500;
    color: #495057;
  }
}

.contact-email {
  color: #28a745;
  text-decoration: none;
  font-weight: 500;
  
  &:hover {
    text-decoration: underline;
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;
  padding: 1rem;
}

.text-success {
  color: #28a745 !important;
}

.text-primary {
  color: #007bff !important;
}

// Responsive design
@media (max-width: 768px) {
  ::ng-deep .privacy-dialog {
    .p-dialog {
      width: 95vw !important;
      height: 95vh !important;
      margin: 0;
    }
  }
  
  .privacy-container {
    padding: 1rem;
  }
  
  .privacy-section {
    h2 {
      font-size: 1.5rem;
    }
    
    h3 {
      font-size: 1.2rem;
    }
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .highlight-box,
  .info-item,
  .service-item {
    flex-direction: column;
    text-align: center;
    
    .pi {
      align-self: center;
    }
  }
  
  .step {
    flex-direction: column;
    text-align: center;
    
    .step-number {
      align-self: center;
    }
  }
}
