.shortener-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);
  backdrop-filter: blur(10px);
  z-index: 9998;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  overflow-y: auto;
}

.shortener-container {
  background: white;
  border-radius: 20px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  max-width: 600px;
  width: 100%;
  max-height: 95vh;
  overflow-y: auto;
  animation: slideInUp 0.4s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.shortener-content {
  padding: 2.5rem;
}

.header-section {
  text-align: center;
  margin-bottom: 2rem;
  
  .icon-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
    
    .pi {
      font-size: 2.5rem;
      color: white;
    }
  }
  
  .title {
    color: #2c3e50;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
  }
  
  .subtitle {
    color: #6c757d;
    font-size: 1.1rem;
    line-height: 1.5;
    margin: 0;
  }
}

.status-section {
  margin-bottom: 2rem;
}

.status-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border: 1px solid #2196f3;
  border-radius: 12px;
  padding: 1.5rem;
  
  .status-icon {
    background: #2196f3;
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    
    .pi {
      font-size: 1.5rem;
    }
  }
  
  .status-text {
    h3 {
      color: #1565c0;
      font-size: 1.2rem;
      margin-bottom: 0.5rem;
    }
    
    p {
      color: #1976d2;
      margin: 0;
      font-weight: 500;
    }
  }
}

.link-section {
  margin-bottom: 2rem;
  
  h3 {
    color: #495057;
    font-size: 1.2rem;
    margin-bottom: 1rem;
  }
}

.link-container {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 1.5rem;
  
  .link-display {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    
    .pi {
      color: #667eea;
      font-size: 1.2rem;
      flex-shrink: 0;
    }
    
    .link-text {
      color: #495057;
      font-family: monospace;
      font-size: 0.9rem;
      word-break: break-all;
      flex: 1;
    }
  }
  
  ::ng-deep .p-button {
    width: 100%;
  }
}

.instructions-section {
  margin-bottom: 2rem;
  
  h3 {
    color: #495057;
    font-size: 1.2rem;
    margin-bottom: 1rem;
  }
}

.instructions-list {
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
  border: 1px solid #ff9800;
  border-radius: 12px;
  padding: 1.5rem;
  margin: 0;
  
  li {
    color: #e65100;
    font-weight: 500;
    margin-bottom: 0.75rem;
    line-height: 1.5;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.action-buttons {
  margin-bottom: 2rem;
  
  ::ng-deep .continue-btn .p-button {
    width: 100%;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 12px;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
    border: none;
    transition: all 0.3s ease;
    
    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(76, 175, 80, 0.3);
    }
  }
}

.secondary-buttons {
  display: flex;
  gap: 0.75rem;
  
  ::ng-deep .p-button {
    flex: 1;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    font-weight: 500;
  }
}

.alternative-section {
  margin-bottom: 2rem;
}

.divider-container {
  display: flex;
  align-items: center;
  margin: 1.5rem 0;
  
  .divider-line {
    flex: 1;
    height: 1px;
    background: #dee2e6;
  }
  
  .divider-text {
    padding: 0 1rem;
    color: #6c757d;
    font-weight: 500;
    background: white;
  }
}

.ad-option {
  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
  border: 1px solid #4caf50;
  border-radius: 12px;
  padding: 1.5rem;
  
  .ad-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    
    .pi {
      font-size: 2rem;
      color: #4caf50;
      flex-shrink: 0;
    }
    
    h4 {
      color: #2e7d32;
      font-size: 1.1rem;
      margin-bottom: 0.5rem;
    }
    
    p {
      color: #388e3c;
      margin: 0;
      font-size: 0.9rem;
    }
  }
  
  ::ng-deep .ad-btn .p-button {
    width: 100%;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
  }

  .ad-error {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: #f8d7da;
    color: #721c24;
    padding: 0.75rem;
    border-radius: 6px;
    margin-top: 1rem;
    font-size: 0.9rem;

    .pi {
      font-size: 1rem;
    }
  }
}

.help-section {
  margin-bottom: 2rem;
  
  .help-content {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1rem;
    
    .pi {
      font-size: 1.5rem;
      color: #6c757d;
      margin-top: 0.2rem;
      flex-shrink: 0;
    }
    
    h4 {
      color: #495057;
      font-size: 1rem;
      margin-bottom: 0.5rem;
    }
    
    p {
      color: #6c757d;
      margin: 0;
      font-size: 0.9rem;
      line-height: 1.5;
    }
  }
  
  .help-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    
    ::ng-deep .p-button {
      font-size: 0.9rem;
    }
  }
}

.footer-info {
  display: flex;
  justify-content: space-around;
  padding-top: 1.5rem;
  border-top: 1px solid #e9ecef;
  
  .info-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    text-align: center;
    
    .pi {
      font-size: 1.5rem;
      color: #28a745;
    }
    
    span {
      color: #6c757d;
      font-size: 0.85rem;
      font-weight: 500;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .shortener-overlay {
    padding: 0.5rem;
  }
  
  .shortener-content {
    padding: 2rem 1.5rem;
  }
  
  .header-section {
    .icon-container {
      width: 60px;
      height: 60px;
      
      .pi {
        font-size: 2rem;
      }
    }
    
    .title {
      font-size: 1.6rem;
    }
    
    .subtitle {
      font-size: 1rem;
    }
  }
  
  .secondary-buttons {
    flex-direction: column;
    
    ::ng-deep .p-button {
      width: 100%;
    }
  }
  
  .help-buttons {
    flex-direction: column;
    
    ::ng-deep .p-button {
      width: 100%;
    }
  }
  
  .footer-info {
    flex-direction: column;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .shortener-content {
    padding: 1.5rem 1rem;
  }
  
  .link-display {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 0.5rem !important;
    
    .link-text {
      font-size: 0.8rem;
    }
  }
  
  .ad-info {
    flex-direction: column !important;
    text-align: center;
    
    .pi {
      align-self: center !important;
    }
  }
}
