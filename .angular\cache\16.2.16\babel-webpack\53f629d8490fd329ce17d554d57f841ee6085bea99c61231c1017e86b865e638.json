{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/api\";\nexport let ErrorHandlerService = /*#__PURE__*/(() => {\n  class ErrorHandlerService {\n    constructor(messageService) {\n      this.messageService = messageService;\n    }\n    handleError(error, context) {\n      console.error('Error occurred:', error, 'Context:', context);\n      const errorInfo = this.parseError(error, context);\n      this.showMessage(errorInfo);\n    }\n    parseError(error, context) {\n      // YouTube API Errors\n      if (context === 'youtube-auth') {\n        return {\n          title: 'خطأ في المصادقة',\n          message: 'فشل في تسجيل الدخول إلى YouTube. يرجى التأكد من إعدادات Google API والمحاولة مرة أخرى.',\n          type: 'error'\n        };\n      }\n      if (context === 'youtube-upload') {\n        if (error.message?.includes('quota')) {\n          return {\n            title: 'تجاوز الحد المسموح',\n            message: 'تم تجاوز الحد اليومي لرفع الفيديوهات. يرجى المحاولة غداً.',\n            type: 'warning'\n          };\n        }\n        if (error.message?.includes('file size')) {\n          return {\n            title: 'حجم الملف كبير',\n            message: 'حجم الفيديو كبير جداً. الحد الأقصى المسموح هو 128GB.',\n            type: 'error'\n          };\n        }\n        return {\n          title: 'خطأ في رفع الفيديو',\n          message: 'فشل في رفع الفيديو إلى YouTube. يرجى التحقق من الاتصال بالإنترنت والمحاولة مرة أخرى.',\n          type: 'error'\n        };\n      }\n      // Gemini AI Errors\n      if (context === 'gemini-generation') {\n        if (error.message?.includes('API key')) {\n          return {\n            title: 'مفتاح API غير صحيح',\n            message: 'مفتاح Gemini API غير صحيح أو غير مفعل. يرجى التحقق من الإعدادات.',\n            type: 'error'\n          };\n        }\n        if (error.message?.includes('quota')) {\n          return {\n            title: 'تجاوز الحد المسموح',\n            message: 'تم تجاوز الحد المسموح لاستخدام Gemini AI. يرجى المحاولة لاحقاً.',\n            type: 'warning'\n          };\n        }\n        return {\n          title: 'خطأ في توليد المحتوى',\n          message: 'فشل في توليد المحتوى بالذكاء الاصطناعي. يرجى المحاولة مرة أخرى أو إدخال المحتوى يدوياً.',\n          type: 'error'\n        };\n      }\n      // Video Generation Errors\n      if (context === 'video-generation') {\n        if (error.message?.includes('FFmpeg')) {\n          return {\n            title: 'خطأ في معالجة الفيديو',\n            message: 'فشل في معالجة الفيديو. يرجى التحقق من الملفات المطلوبة والمحاولة مرة أخرى.',\n            type: 'error'\n          };\n        }\n        return {\n          title: 'خطأ في إنتاج الفيديو',\n          message: 'حدث خطأ أثناء إنتاج الفيديو. يرجى المحاولة مرة أخرى.',\n          type: 'error'\n        };\n      }\n      // Network Errors\n      if (error.message?.includes('network') || error.message?.includes('fetch')) {\n        return {\n          title: 'خطأ في الاتصال',\n          message: 'فشل في الاتصال بالخادم. يرجى التحقق من الاتصال بالإنترنت والمحاولة مرة أخرى.',\n          type: 'error'\n        };\n      }\n      // Generic Error\n      return {\n        title: 'خطأ غير متوقع',\n        message: 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى أو إعادة تحميل الصفحة.',\n        type: 'error'\n      };\n    }\n    showMessage(errorInfo) {\n      this.messageService.add({\n        severity: errorInfo.type,\n        summary: errorInfo.title,\n        detail: errorInfo.message,\n        life: errorInfo.type === 'success' ? 3000 : 5000\n      });\n    }\n    showSuccess(title, message) {\n      this.showMessage({\n        title,\n        message,\n        type: 'success'\n      });\n    }\n    showWarning(title, message) {\n      this.showMessage({\n        title,\n        message,\n        type: 'warning'\n      });\n    }\n    showInfo(title, message) {\n      this.showMessage({\n        title,\n        message,\n        type: 'info'\n      });\n    }\n    // Validation helpers\n    validateApiKeys() {\n      const missingKeys = [];\n      // Check if API keys are configured (not default values)\n      const apiKeys = {\n        'Google Client ID': 'YOUR_GOOGLE_CLIENT_ID_HERE',\n        'Google API Key': 'YOUR_GOOGLE_API_KEY_HERE',\n        'Gemini API Key': 'YOUR_GEMINI_API_KEY_HERE'\n      };\n      // This would need to be implemented based on your actual API key checking logic\n      // For now, we'll assume they need to be checked in the respective services\n      return {\n        isValid: missingKeys.length === 0,\n        missingKeys\n      };\n    }\n    static {\n      this.ɵfac = function ErrorHandlerService_Factory(t) {\n        return new (t || ErrorHandlerService)(i0.ɵɵinject(i1.MessageService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ErrorHandlerService,\n        factory: ErrorHandlerService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ErrorHandlerService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}