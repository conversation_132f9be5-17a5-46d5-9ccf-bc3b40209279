{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Directive, Optional, HostListener, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport * as i1 from '@angular/forms';\nlet InputText = /*#__PURE__*/(() => {\n  class InputText {\n    el;\n    ngModel;\n    cd;\n    filled;\n    constructor(el, ngModel, cd) {\n      this.el = el;\n      this.ngModel = ngModel;\n      this.cd = cd;\n    }\n    ngAfterViewInit() {\n      this.updateFilledState();\n      this.cd.detectChanges();\n    }\n    ngDoCheck() {\n      this.updateFilledState();\n    }\n    onInput() {\n      this.updateFilledState();\n    }\n    updateFilledState() {\n      this.filled = this.el.nativeElement.value && this.el.nativeElement.value.length || this.ngModel && this.ngModel.model;\n    }\n    static ɵfac = function InputText_Factory(t) {\n      return new (t || InputText)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.<PERSON><PERSON><PERSON><PERSON>, 8), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: InputText,\n      selectors: [[\"\", \"pInputText\", \"\"]],\n      hostAttrs: [1, \"p-inputtext\", \"p-component\", \"p-element\"],\n      hostVars: 2,\n      hostBindings: function InputText_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"input\", function InputText_input_HostBindingHandler($event) {\n            return ctx.onInput($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"p-filled\", ctx.filled);\n        }\n      }\n    });\n  }\n  return InputText;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet InputTextModule = /*#__PURE__*/(() => {\n  class InputTextModule {\n    static ɵfac = function InputTextModule_Factory(t) {\n      return new (t || InputTextModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: InputTextModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n  return InputTextModule;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { InputText, InputTextModule };\n//# sourceMappingURL=primeng-inputtext.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}