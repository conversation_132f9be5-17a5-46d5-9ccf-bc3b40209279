{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { HttpClientModule } from '@angular/common/http';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport { ButtonModule } from 'primeng/button';\nimport { ProgressBarModule } from 'primeng/progressbar';\nimport { ToastModule } from 'primeng/toast';\nimport { MessageService } from 'primeng/api';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { DialogModule } from 'primeng/dialog';\nimport { InputTextareaModule } from 'primeng/inputtextarea';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { SidebarModule } from 'primeng/sidebar';\nimport { MenuModule } from 'primeng/menu';\nimport { OverlayPanelModule } from 'primeng/overlaypanel';\nimport { PanelModule } from 'primeng/panel';\nimport { CardModule } from 'primeng/card';\nimport { DividerModule } from 'primeng/divider';\nimport { TabViewModule } from 'primeng/tabview';\nimport { SelectButtonModule } from 'primeng/selectbutton';\nimport { SliderModule } from 'primeng/slider';\nimport { FileUploadModule } from 'primeng/fileupload';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { ConfirmationService } from 'primeng/api';\nimport * as i0 from \"@angular/core\";\nexport let AppModule = /*#__PURE__*/(() => {\n  class AppModule {\n    static {\n      this.ɵfac = function AppModule_Factory(t) {\n        return new (t || AppModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: AppModule,\n        bootstrap: [AppComponent]\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        providers: [MessageService, ConfirmationService],\n        imports: [BrowserModule, AppRoutingModule, HttpClientModule, BrowserAnimationsModule, InputTextModule, CheckboxModule, RadioButtonModule, ButtonModule, ProgressBarModule, ToastModule, DropdownModule, FormsModule, ReactiveFormsModule, DialogModule, InputTextareaModule, TooltipModule, SidebarModule, MenuModule, OverlayPanelModule, PanelModule, CardModule, DividerModule, TabViewModule, SelectButtonModule, SliderModule, FileUploadModule, ConfirmDialogModule, CheckboxModule]\n      });\n    }\n  }\n  return AppModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}