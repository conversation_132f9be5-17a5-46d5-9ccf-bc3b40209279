{"ast": null, "code": "import { Immediate } from '../util/Immediate';\nconst {\n  setImmediate,\n  clearImmediate\n} = Immediate;\nexport const immediateProvider = {\n  setImmediate(...args) {\n    const {\n      delegate\n    } = immediateProvider;\n    return ((delegate === null || delegate === void 0 ? void 0 : delegate.setImmediate) || setImmediate)(...args);\n  },\n  clearImmediate(handle) {\n    const {\n      delegate\n    } = immediateProvider;\n    return ((delegate === null || delegate === void 0 ? void 0 : delegate.clearImmediate) || clearImmediate)(handle);\n  },\n  delegate: undefined\n};\n//# sourceMappingURL=immediateProvider.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}