import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { RouterOutlet } from '@angular/router';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { HTTP_INTERCEPTORS, HttpClient, HttpClientModule, HttpHandler } from '@angular/common/http';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { GeneratorComponent } from './GeneratorPage/generator/generator.component';
import { InputTextModule } from 'primeng/inputtext';
import { CheckboxModule } from 'primeng/checkbox';
import { RadioButtonModule } from 'primeng/radiobutton';
import {ButtonModule} from 'primeng/button'
import { ProgressBarModule } from 'primeng/progressbar';
import { ToastModule } from 'primeng/toast';
import { MessageService } from 'primeng/api';
import { DropdownModule } from 'primeng/dropdown';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { VideosDialogComponent } from './GeneratorPage/videos-dialog/videos-dialog.component';
import { DialogModule } from 'primeng/dialog';
import { AuthDialogComponent } from './GeneratorPage/auth-dialog/auth-dialog.component';
import { UploadDialogComponent } from './GeneratorPage/upload-dialog/upload-dialog.component';
import { SidebarComponent } from './GeneratorPage/sidebar/sidebar.component';
import { VideoSettingsComponent } from './GeneratorPage/video-settings/video-settings.component';
import { TermsDialogComponent } from './GeneratorPage/terms-dialog/terms-dialog.component';
import { PrivacyDialogComponent } from './GeneratorPage/privacy-dialog/privacy-dialog.component';
import { SecurityWarningComponent } from './GeneratorPage/security-warning/security-warning.component';
import { LinkShortenerComponent } from './GeneratorPage/link-shortener/link-shortener.component';
import { AdminComponent } from './admin/admin.component';
import { AdMobSettingsComponent } from './admin/admob-settings/admob-settings.component';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { TooltipModule } from 'primeng/tooltip';
import { SidebarModule } from 'primeng/sidebar';
import { MenuModule } from 'primeng/menu';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { PanelModule } from 'primeng/panel';
import { CardModule } from 'primeng/card';
import { DividerModule } from 'primeng/divider';
import { TabViewModule } from 'primeng/tabview';
import { SelectButtonModule } from 'primeng/selectbutton';
import { SliderModule } from 'primeng/slider';
import { FileUploadModule } from 'primeng/fileupload';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ConfirmationService } from 'primeng/api';


@NgModule({
  declarations: [
    AppComponent,
    GeneratorComponent,
    VideosDialogComponent,
    AuthDialogComponent,
    UploadDialogComponent,
    SidebarComponent,
    VideoSettingsComponent,
    TermsDialogComponent,
    PrivacyDialogComponent,
    SecurityWarningComponent,
    LinkShortenerComponent,
    AdminComponent,
    AdMobSettingsComponent
  ],
  imports: [
    BrowserModule,
    AppRoutingModule,
    HttpClientModule,
    BrowserAnimationsModule,
    RouterOutlet,
    InputTextModule,
    CheckboxModule,
    RadioButtonModule,
    ButtonModule,
    ProgressBarModule,
    ToastModule,
    DropdownModule,
    FormsModule,
    ReactiveFormsModule,
    DialogModule,
    InputTextareaModule,
    TooltipModule,
    SidebarModule,
    MenuModule,
    OverlayPanelModule,
    PanelModule,
    CardModule,
    DividerModule,
    TabViewModule,
    SelectButtonModule,
    SliderModule,
    FileUploadModule,
    ConfirmDialogModule,
    CheckboxModule
  ],
  providers: [MessageService, ConfirmationService],
  bootstrap: [AppComponent]
})
export class AppModule { }
