Write-Host "Manual Gradle Setup Helper" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green

$gradleDir = "C:\gradle"
$gradleVersion = "gradle-7.6.4"
$gradleHome = "$gradleDir\$gradleVersion"

Write-Host ""
Write-Host "MANUAL SETUP INSTRUCTIONS:" -ForegroundColor Yellow
Write-Host ""

Write-Host "1. Download Gradle manually:" -ForegroundColor Cyan
Write-Host "   Go to: https://gradle.org/releases/" -ForegroundColor White
Write-Host "   Find Gradle 7.6.4" -ForegroundColor White
Write-Host "   Download 'Binary-only' or 'Complete'" -ForegroundColor White
Write-Host ""

Write-Host "2. Extract to: C:\gradle\gradle-7.6.4" -ForegroundColor Cyan
Write-Host "   Use WinRAR, 7-Zip, or Windows built-in extractor" -ForegroundColor White
Write-Host ""

Write-Host "3. After extraction, run this script again to set environment variables" -ForegroundColor Cyan
Write-Host ""

# Check if Gradle is already extracted
if (Test-Path "$gradleHome\bin\gradle.bat") {
    Write-Host "Gradle found! Setting up environment variables..." -ForegroundColor Green
    
    # Set environment variables
    $env:GRADLE_HOME = $gradleHome
    $env:PATH = "$gradleHome\bin;$env:PATH"
    $env:JAVA_HOME = "C:\Program Files\Microsoft\jdk-*********-hotspot"
    
    Write-Host "Environment variables set:" -ForegroundColor Green
    Write-Host "GRADLE_HOME: $env:GRADLE_HOME" -ForegroundColor Cyan
    Write-Host "JAVA_HOME: $env:JAVA_HOME" -ForegroundColor Cyan
    
    # Test Gradle
    Write-Host ""
    Write-Host "Testing Gradle..." -ForegroundColor Yellow
    
    try {
        $gradleVersion = & "$gradleHome\bin\gradle.bat" --version 2>&1
        Write-Host "SUCCESS! Gradle is working:" -ForegroundColor Green
        Write-Host $gradleVersion -ForegroundColor White
        
        Write-Host ""
        Write-Host "Now you can build APK:" -ForegroundColor Green
        Write-Host "cd `"C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android`"" -ForegroundColor White
        Write-Host "gradle assembleDebug" -ForegroundColor White
        
    }
    catch {
        Write-Host "Error testing Gradle: $($_.Exception.Message)" -ForegroundColor Red
    }
    
} else {
    Write-Host "Gradle not found at: $gradleHome" -ForegroundColor Red
    Write-Host "Please follow the manual setup instructions above." -ForegroundColor Yellow
    
    Write-Host ""
    Write-Host "Alternative: Use Android Studio" -ForegroundColor Cyan
    Write-Host "1. Download from: https://developer.android.com/studio" -ForegroundColor White
    Write-Host "2. Open the 'android' folder in Android Studio" -ForegroundColor White
    Write-Host "3. Build -> Build APK" -ForegroundColor White
}

Write-Host ""
Read-Host "Press Enter to exit..."
