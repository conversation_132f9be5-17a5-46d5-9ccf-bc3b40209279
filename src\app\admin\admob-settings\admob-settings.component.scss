.admob-settings-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
}

.settings-header {
  text-align: center;
  margin-bottom: 3rem;
  
  h2 {
    color: #2c3e50;
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }
  
  p {
    color: #6c757d;
    font-size: 1.1rem;
    margin: 0;
  }
}

.settings-section, .stats-section, .guide-section, .notes-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  h3 {
    color: #2c3e50;
    font-size: 1.4rem;
    margin-bottom: 1.5rem;
    border-bottom: 2px solid #667eea;
    padding-bottom: 0.5rem;
  }
}

// Form Styles
.admob-form {
  .form-group {
    margin-bottom: 2rem;
    
    label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 600;
      color: #495057;
    }
    
    input {
      width: 100%;
      padding: 0.75rem;
      border: 2px solid #e9ecef;
      border-radius: 8px;
      font-size: 1rem;
      transition: border-color 0.3s ease;
      
      &:focus {
        border-color: #667eea;
        outline: none;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }
      
      &.error {
        border-color: #dc3545;
      }
    }
    
    .help-text {
      display: block;
      margin-top: 0.5rem;
      color: #6c757d;
      font-size: 0.9rem;
    }
    
    .error-text {
      display: block;
      margin-top: 0.5rem;
      color: #dc3545;
      font-size: 0.9rem;
      font-weight: 500;
    }
  }
  
  .checkbox-container {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
}

.save-message, .test-result {
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  
  &.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
  }
  
  &.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
  }
}

.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
  flex-wrap: wrap;
  
  ::ng-deep .p-button {
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    border-radius: 8px;
  }
}

// Statistics Styles
.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  
  h3 {
    margin-bottom: 0;
    border-bottom: none;
    padding-bottom: 0;
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  border: 1px solid #dee2e6;
  
  .stat-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .pi {
      font-size: 1.3rem;
    }
  }
  
  .stat-info {
    h4 {
      color: #2c3e50;
      font-size: 1.8rem;
      margin-bottom: 0.25rem;
      font-weight: 700;
    }
    
    p {
      color: #6c757d;
      margin: 0;
      font-size: 0.9rem;
      font-weight: 500;
    }
  }
}

// Guide Styles
.guide-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.guide-step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  
  .step-number {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.1rem;
    flex-shrink: 0;
  }
  
  .step-content {
    h4 {
      color: #2c3e50;
      margin-bottom: 0.5rem;
      font-size: 1.1rem;
    }
    
    p {
      color: #6c757d;
      margin: 0;
      line-height: 1.5;
      
      a {
        color: #667eea;
        text-decoration: none;
        
        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}

// Notes Styles
.note-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .pi {
    font-size: 1.5rem;
    margin-top: 0.2rem;
    flex-shrink: 0;
  }
  
  h4 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 1rem;
  }
  
  p {
    color: #6c757d;
    margin: 0;
    line-height: 1.5;
    font-size: 0.9rem;
  }
}

// Color classes
.text-warning { color: #ffc107 !important; }
.text-info { color: #17a2b8 !important; }
.text-success { color: #28a745 !important; }

// Responsive Design
@media (max-width: 768px) {
  .admob-settings-container {
    padding: 1rem;
  }
  
  .settings-section, .stats-section, .guide-section, .notes-section {
    padding: 1.5rem;
  }
  
  .settings-header {
    h2 {
      font-size: 1.6rem;
    }
    
    p {
      font-size: 1rem;
    }
  }
  
  .form-actions {
    flex-direction: column;
    
    ::ng-deep .p-button {
      width: 100%;
    }
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .guide-step {
    flex-direction: column;
    text-align: center;
    
    .step-number {
      align-self: center;
    }
  }
  
  .note-item {
    flex-direction: column;
    text-align: center;
    
    .pi {
      align-self: center;
    }
  }
}
