{"ast": null, "code": "import { trigger, state, style, transition, animate, query, animateChild } from '@angular/animations';\nimport * as i1 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, Output, ViewChild, Inject, ContentChildren, NgModule } from '@angular/core';\nimport * as i3 from 'primeng/api';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { CheckIcon } from 'primeng/icons/check';\nimport { ExclamationTriangleIcon } from 'primeng/icons/exclamationtriangle';\nimport { InfoCircleIcon } from 'primeng/icons/infocircle';\nimport { TimesIcon } from 'primeng/icons/times';\nimport { TimesCircleIcon } from 'primeng/icons/timescircle';\nimport * as i2 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport { UniqueComponentId, ZIndexUtils, ObjectUtils } from 'primeng/utils';\nconst _c0 = [\"container\"];\nfunction ToastItem_ng_container_3_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\");\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(\"p-toast-message-icon pi \" + ctx_r4.message.icon);\n  }\n}\nfunction ToastItem_ng_container_3_span_2_CheckIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"CheckIcon\");\n  }\n  if (rf & 2) {\n    i0.ɵɵattribute(\"aria-hidden\", true)(\"data-pc-section\", \"icon\");\n  }\n}\nfunction ToastItem_ng_container_3_span_2_InfoCircleIcon_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"InfoCircleIcon\");\n  }\n  if (rf & 2) {\n    i0.ɵɵattribute(\"aria-hidden\", true)(\"data-pc-section\", \"icon\");\n  }\n}\nfunction ToastItem_ng_container_3_span_2_TimesCircleIcon_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"TimesCircleIcon\");\n  }\n  if (rf & 2) {\n    i0.ɵɵattribute(\"aria-hidden\", true)(\"data-pc-section\", \"icon\");\n  }\n}\nfunction ToastItem_ng_container_3_span_2_ExclamationTriangleIcon_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ExclamationTriangleIcon\");\n  }\n  if (rf & 2) {\n    i0.ɵɵattribute(\"aria-hidden\", true)(\"data-pc-section\", \"icon\");\n  }\n}\nfunction ToastItem_ng_container_3_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 11);\n    i0.ɵɵelementContainerStart(1);\n    i0.ɵɵtemplate(2, ToastItem_ng_container_3_span_2_CheckIcon_2_Template, 1, 2, \"CheckIcon\", 3);\n    i0.ɵɵtemplate(3, ToastItem_ng_container_3_span_2_InfoCircleIcon_3_Template, 1, 2, \"InfoCircleIcon\", 3);\n    i0.ɵɵtemplate(4, ToastItem_ng_container_3_span_2_TimesCircleIcon_4_Template, 1, 2, \"TimesCircleIcon\", 3);\n    i0.ɵɵtemplate(5, ToastItem_ng_container_3_span_2_ExclamationTriangleIcon_5_Template, 1, 2, \"ExclamationTriangleIcon\", 3);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"aria-hidden\", true)(\"data-pc-section\", \"icon\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.message.severity === \"success\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.message.severity === \"info\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.message.severity === \"error\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.message.severity === \"warn\");\n  }\n}\nfunction ToastItem_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ToastItem_ng_container_3_span_1_Template, 1, 2, \"span\", 6);\n    i0.ɵɵtemplate(2, ToastItem_ng_container_3_span_2_Template, 6, 6, \"span\", 7);\n    i0.ɵɵelementStart(3, \"div\", 8)(4, \"div\", 9);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 10);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.message.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.message.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵattribute(\"data-pc-section\", \"text\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵattribute(\"data-pc-section\", \"summary\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r1.message.summary);\n    i0.ɵɵadvance(1);\n    i0.ɵɵattribute(\"data-pc-section\", \"detail\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r1.message.detail);\n  }\n}\nfunction ToastItem_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction ToastItem_button_5_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\");\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(\"pt-1 text-base p-toast-message-icon pi \" + ctx_r10.message.closeIcon);\n  }\n}\nfunction ToastItem_button_5_TimesIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"TimesIcon\", 14);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-toast-icon-close-icon\");\n    i0.ɵɵattribute(\"aria-hidden\", true)(\"data-pc-section\", \"closeicon\");\n  }\n}\nfunction ToastItem_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function ToastItem_button_5_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.onCloseIconClick($event));\n    })(\"keydown.enter\", function ToastItem_button_5_Template_button_keydown_enter_0_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.onCloseIconClick($event));\n    });\n    i0.ɵɵtemplate(1, ToastItem_button_5_span_1_Template, 1, 2, \"span\", 6);\n    i0.ɵɵtemplate(2, ToastItem_button_5_TimesIcon_2_Template, 1, 3, \"TimesIcon\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"aria-label\", \"Close\")(\"data-pc-section\", \"closebutton\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.message.closeIcon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.message.closeIcon);\n  }\n}\nconst _c1 = function (a0) {\n  return [a0, \"p-toast-message\"];\n};\nconst _c2 = function (a0, a1, a2, a3) {\n  return {\n    showTransformParams: a0,\n    hideTransformParams: a1,\n    showTransitionParams: a2,\n    hideTransitionParams: a3\n  };\n};\nconst _c3 = function (a1) {\n  return {\n    value: \"visible\",\n    params: a1\n  };\n};\nconst _c4 = function (a0) {\n  return {\n    $implicit: a0\n  };\n};\nfunction Toast_p_toastItem_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-toastItem\", 3);\n    i0.ɵɵlistener(\"onClose\", function Toast_p_toastItem_2_Template_p_toastItem_onClose_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onMessageClose($event));\n    })(\"@toastAnimation.start\", function Toast_p_toastItem_2_Template_p_toastItem_animation_toastAnimation_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onAnimationStart($event));\n    })(\"@toastAnimation.done\", function Toast_p_toastItem_2_Template_p_toastItem_animation_toastAnimation_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.onAnimationEnd($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const msg_r2 = ctx.$implicit;\n    const i_r3 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"message\", msg_r2)(\"index\", i_r3)(\"life\", ctx_r1.life)(\"template\", ctx_r1.template)(\"@toastAnimation\", undefined)(\"showTransformOptions\", ctx_r1.showTransformOptions)(\"hideTransformOptions\", ctx_r1.hideTransformOptions)(\"showTransitionOptions\", ctx_r1.showTransitionOptions)(\"hideTransitionOptions\", ctx_r1.hideTransitionOptions);\n  }\n}\nlet ToastItem = /*#__PURE__*/(() => {\n  class ToastItem {\n    zone;\n    message;\n    index;\n    life;\n    template;\n    showTransformOptions;\n    hideTransformOptions;\n    showTransitionOptions;\n    hideTransitionOptions;\n    onClose = new EventEmitter();\n    containerViewChild;\n    timeout;\n    constructor(zone) {\n      this.zone = zone;\n    }\n    ngAfterViewInit() {\n      this.initTimeout();\n    }\n    initTimeout() {\n      if (!this.message?.sticky) {\n        this.zone.runOutsideAngular(() => {\n          this.timeout = setTimeout(() => {\n            this.onClose.emit({\n              index: this.index,\n              message: this.message\n            });\n          }, this.message?.life || this.life || 3000);\n        });\n      }\n    }\n    clearTimeout() {\n      if (this.timeout) {\n        clearTimeout(this.timeout);\n        this.timeout = null;\n      }\n    }\n    onMouseEnter() {\n      this.clearTimeout();\n    }\n    onMouseLeave() {\n      this.initTimeout();\n    }\n    onCloseIconClick(event) {\n      this.clearTimeout();\n      this.onClose.emit({\n        index: this.index,\n        message: this.message\n      });\n      event.preventDefault();\n    }\n    ngOnDestroy() {\n      this.clearTimeout();\n    }\n    static ɵfac = function ToastItem_Factory(t) {\n      return new (t || ToastItem)(i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: ToastItem,\n      selectors: [[\"p-toastItem\"]],\n      viewQuery: function ToastItem_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.containerViewChild = _t.first);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        message: \"message\",\n        index: \"index\",\n        life: \"life\",\n        template: \"template\",\n        showTransformOptions: \"showTransformOptions\",\n        hideTransformOptions: \"hideTransformOptions\",\n        showTransitionOptions: \"showTransitionOptions\",\n        hideTransitionOptions: \"hideTransitionOptions\"\n      },\n      outputs: {\n        onClose: \"onClose\"\n      },\n      decls: 6,\n      vars: 24,\n      consts: [[\"role\", \"alert\", \"aria-live\", \"assertive\", \"aria-atomic\", \"true\", 3, \"ngClass\", \"mouseenter\", \"mouseleave\"], [\"container\", \"\"], [1, \"p-toast-message-content\", 3, \"ngClass\"], [4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"type\", \"button\", \"class\", \"p-toast-icon-close p-link\", \"pRipple\", \"\", 3, \"click\", \"keydown.enter\", 4, \"ngIf\"], [3, \"class\", 4, \"ngIf\"], [\"class\", \"p-toast-message-icon\", 4, \"ngIf\"], [1, \"p-toast-message-text\"], [1, \"p-toast-summary\"], [1, \"p-toast-detail\"], [1, \"p-toast-message-icon\"], [\"type\", \"button\", \"pRipple\", \"\", 1, \"p-toast-icon-close\", \"p-link\", 3, \"click\", \"keydown.enter\"], [3, \"styleClass\", 4, \"ngIf\"], [3, \"styleClass\"]],\n      template: function ToastItem_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0, 1);\n          i0.ɵɵlistener(\"mouseenter\", function ToastItem_Template_div_mouseenter_0_listener() {\n            return ctx.onMouseEnter();\n          })(\"mouseleave\", function ToastItem_Template_div_mouseleave_0_listener() {\n            return ctx.onMouseLeave();\n          });\n          i0.ɵɵelementStart(2, \"div\", 2);\n          i0.ɵɵtemplate(3, ToastItem_ng_container_3_Template, 8, 7, \"ng-container\", 3);\n          i0.ɵɵtemplate(4, ToastItem_ng_container_4_Template, 1, 0, \"ng-container\", 4);\n          i0.ɵɵtemplate(5, ToastItem_button_5_Template, 3, 4, \"button\", 5);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.message == null ? null : ctx.message.styleClass);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(13, _c1, \"p-toast-message-\" + (ctx.message == null ? null : ctx.message.severity)))(\"@messageState\", i0.ɵɵpureFunction1(20, _c3, i0.ɵɵpureFunction4(15, _c2, ctx.showTransformOptions, ctx.hideTransformOptions, ctx.showTransitionOptions, ctx.hideTransitionOptions)));\n          i0.ɵɵattribute(\"id\", ctx.message == null ? null : ctx.message.id)(\"data-pc-name\", \"toast\")(\"data-pc-section\", \"root\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", ctx.message == null ? null : ctx.message.contentStyleClass);\n          i0.ɵɵattribute(\"data-pc-section\", \"content\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.template);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.template)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(22, _c4, ctx.message));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", (ctx.message == null ? null : ctx.message.closable) !== false);\n        }\n      },\n      dependencies: function () {\n        return [i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, i2.Ripple, CheckIcon, InfoCircleIcon, TimesCircleIcon, ExclamationTriangleIcon, TimesIcon];\n      },\n      encapsulation: 2,\n      data: {\n        animation: [trigger('messageState', [state('visible', style({\n          transform: 'translateY(0)',\n          opacity: 1\n        })), transition('void => *', [style({\n          transform: '{{showTransformParams}}',\n          opacity: 0\n        }), animate('{{showTransitionParams}}')]), transition('* => void', [animate('{{hideTransitionParams}}', style({\n          height: 0,\n          opacity: 0,\n          transform: '{{hideTransformParams}}'\n        }))])])]\n      },\n      changeDetection: 0\n    });\n  }\n  return ToastItem;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Toast is used to display messages in an overlay.\n * @group Components\n */\nlet Toast = /*#__PURE__*/(() => {\n  class Toast {\n    document;\n    renderer;\n    messageService;\n    cd;\n    config;\n    /**\n     * Key of the message in case message is targeted to a specific toast component.\n     * @group Props\n     */\n    key;\n    /**\n     * Whether to automatically manage layering.\n     * @group Props\n     */\n    autoZIndex = true;\n    /**\n     * Base zIndex value to use in layering.\n     * @group Props\n     */\n    baseZIndex = 0;\n    /**\n     * The default time to display messages for in milliseconds.\n     * @group Props\n     */\n    life = 3000;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    style;\n    /**\n     * Inline class of the component.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Position of the toast in viewport.\n     * @group Props\n     */\n    get position() {\n      return this._position;\n    }\n    set position(value) {\n      this._position = value;\n      this.cd.markForCheck();\n    }\n    /**\n     * It does not add the new message if there is already a toast displayed with the same content\n     * @group Props\n     */\n    preventOpenDuplicates = false;\n    /**\n     * Displays only once a message with the same content.\n     * @group Props\n     */\n    preventDuplicates = false;\n    /**\n     * Transform options of the show animation.\n     * @group Props\n     */\n    showTransformOptions = 'translateY(100%)';\n    /**\n     * Transform options of the hide animation.\n     * @group Props\n     */\n    hideTransformOptions = 'translateY(-100%)';\n    /**\n     * Transition options of the show animation.\n     * @group Props\n     */\n    showTransitionOptions = '300ms ease-out';\n    /**\n     * Transition options of the hide animation.\n     * @group Props\n     */\n    hideTransitionOptions = '250ms ease-in';\n    /**\n     * Object literal to define styles per screen size.\n     * @group Props\n     */\n    breakpoints;\n    /**\n     * Callback to invoke when a message is closed.\n     * @param {ToastCloseEvent} event - custom close event.\n     * @group Emits\n     */\n    onClose = new EventEmitter();\n    containerViewChild;\n    templates;\n    messageSubscription;\n    clearSubscription;\n    messages;\n    messagesArchieve;\n    template;\n    _position = 'top-right';\n    constructor(document, renderer, messageService, cd, config) {\n      this.document = document;\n      this.renderer = renderer;\n      this.messageService = messageService;\n      this.cd = cd;\n      this.config = config;\n    }\n    styleElement;\n    id = UniqueComponentId();\n    ngOnInit() {\n      this.messageSubscription = this.messageService.messageObserver.subscribe(messages => {\n        if (messages) {\n          if (Array.isArray(messages)) {\n            const filteredMessages = messages.filter(m => this.canAdd(m));\n            this.add(filteredMessages);\n          } else if (this.canAdd(messages)) {\n            this.add([messages]);\n          }\n        }\n      });\n      this.clearSubscription = this.messageService.clearObserver.subscribe(key => {\n        if (key) {\n          if (this.key === key) {\n            this.messages = null;\n          }\n        } else {\n          this.messages = null;\n        }\n        this.cd.markForCheck();\n      });\n    }\n    ngAfterViewInit() {\n      if (this.breakpoints) {\n        this.createStyle();\n      }\n    }\n    add(messages) {\n      this.messages = this.messages ? [...this.messages, ...messages] : [...messages];\n      if (this.preventDuplicates) {\n        this.messagesArchieve = this.messagesArchieve ? [...this.messagesArchieve, ...messages] : [...messages];\n      }\n      this.cd.markForCheck();\n    }\n    canAdd(message) {\n      let allow = this.key === message.key;\n      if (allow && this.preventOpenDuplicates) {\n        allow = !this.containsMessage(this.messages, message);\n      }\n      if (allow && this.preventDuplicates) {\n        allow = !this.containsMessage(this.messagesArchieve, message);\n      }\n      return allow;\n    }\n    containsMessage(collection, message) {\n      if (!collection) {\n        return false;\n      }\n      return collection.find(m => {\n        return m.summary === message.summary && m.detail == message.detail && m.severity === message.severity;\n      }) != null;\n    }\n    ngAfterContentInit() {\n      this.templates?.forEach(item => {\n        switch (item.getType()) {\n          case 'message':\n            this.template = item.template;\n            break;\n          default:\n            this.template = item.template;\n            break;\n        }\n      });\n    }\n    onMessageClose(event) {\n      this.messages?.splice(event.index, 1);\n      this.onClose.emit({\n        message: event.message\n      });\n      this.cd.detectChanges();\n    }\n    onAnimationStart(event) {\n      if (event.fromState === 'void') {\n        this.renderer.setAttribute(this.containerViewChild?.nativeElement, this.id, '');\n        if (this.autoZIndex && this.containerViewChild?.nativeElement.style.zIndex === '') {\n          ZIndexUtils.set('modal', this.containerViewChild?.nativeElement, this.baseZIndex || this.config.zIndex.modal);\n        }\n      }\n    }\n    onAnimationEnd(event) {\n      if (event.toState === 'void') {\n        if (this.autoZIndex && ObjectUtils.isEmpty(this.messages)) {\n          ZIndexUtils.clear(this.containerViewChild?.nativeElement);\n        }\n      }\n    }\n    createStyle() {\n      if (!this.styleElement) {\n        this.styleElement = this.renderer.createElement('style');\n        this.styleElement.type = 'text/css';\n        this.renderer.appendChild(this.document.head, this.styleElement);\n        let innerHTML = '';\n        for (let breakpoint in this.breakpoints) {\n          let breakpointStyle = '';\n          for (let styleProp in this.breakpoints[breakpoint]) {\n            breakpointStyle += styleProp + ':' + this.breakpoints[breakpoint][styleProp] + ' !important;';\n          }\n          innerHTML += `\n                    @media screen and (max-width: ${breakpoint}) {\n                        .p-toast[${this.id}] {\n                           ${breakpointStyle}\n                        }\n                    }\n                `;\n        }\n        this.renderer.setProperty(this.styleElement, 'innerHTML', innerHTML);\n      }\n    }\n    destroyStyle() {\n      if (this.styleElement) {\n        this.renderer.removeChild(this.document.head, this.styleElement);\n        this.styleElement = null;\n      }\n    }\n    ngOnDestroy() {\n      if (this.messageSubscription) {\n        this.messageSubscription.unsubscribe();\n      }\n      if (this.containerViewChild && this.autoZIndex) {\n        ZIndexUtils.clear(this.containerViewChild.nativeElement);\n      }\n      if (this.clearSubscription) {\n        this.clearSubscription.unsubscribe();\n      }\n      this.destroyStyle();\n    }\n    static ɵfac = function Toast_Factory(t) {\n      return new (t || Toast)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i3.PrimeNGConfig));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: Toast,\n      selectors: [[\"p-toast\"]],\n      contentQueries: function Toast_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      viewQuery: function Toast_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.containerViewChild = _t.first);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        key: \"key\",\n        autoZIndex: \"autoZIndex\",\n        baseZIndex: \"baseZIndex\",\n        life: \"life\",\n        style: \"style\",\n        styleClass: \"styleClass\",\n        position: \"position\",\n        preventOpenDuplicates: \"preventOpenDuplicates\",\n        preventDuplicates: \"preventDuplicates\",\n        showTransformOptions: \"showTransformOptions\",\n        hideTransformOptions: \"hideTransformOptions\",\n        showTransitionOptions: \"showTransitionOptions\",\n        hideTransitionOptions: \"hideTransitionOptions\",\n        breakpoints: \"breakpoints\"\n      },\n      outputs: {\n        onClose: \"onClose\"\n      },\n      decls: 3,\n      vars: 5,\n      consts: [[1, \"p-toast\", \"p-component\", 3, \"ngClass\", \"ngStyle\"], [\"container\", \"\"], [3, \"message\", \"index\", \"life\", \"template\", \"showTransformOptions\", \"hideTransformOptions\", \"showTransitionOptions\", \"hideTransitionOptions\", \"onClose\", 4, \"ngFor\", \"ngForOf\"], [3, \"message\", \"index\", \"life\", \"template\", \"showTransformOptions\", \"hideTransformOptions\", \"showTransitionOptions\", \"hideTransitionOptions\", \"onClose\"]],\n      template: function Toast_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0, 1);\n          i0.ɵɵtemplate(2, Toast_p_toastItem_2_Template, 1, 9, \"p-toastItem\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.styleClass);\n          i0.ɵɵproperty(\"ngClass\", \"p-toast-\" + ctx._position)(\"ngStyle\", ctx.style);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.messages);\n        }\n      },\n      dependencies: [i1.NgClass, i1.NgForOf, i1.NgStyle, ToastItem],\n      styles: [\"@layer primeng{.p-toast{position:fixed;width:25rem}.p-toast-message{overflow:hidden}.p-toast-message-content{display:flex;align-items:flex-start}.p-toast-message-text{flex:1 1 auto}.p-toast-top-right{top:20px;right:20px}.p-toast-top-left{top:20px;left:20px}.p-toast-bottom-left{bottom:20px;left:20px}.p-toast-bottom-right{bottom:20px;right:20px}.p-toast-top-center{top:20px;left:50%;transform:translate(-50%)}.p-toast-bottom-center{bottom:20px;left:50%;transform:translate(-50%)}.p-toast-center{left:50%;top:50%;min-width:20vw;transform:translate(-50%,-50%)}.p-toast-icon-close{display:flex;align-items:center;justify-content:center;overflow:hidden;position:relative;flex:none}.p-toast-icon-close.p-link{cursor:pointer}}\\n\"],\n      encapsulation: 2,\n      data: {\n        animation: [trigger('toastAnimation', [transition(':enter, :leave', [query('@*', animateChild())])])]\n      },\n      changeDetection: 0\n    });\n  }\n  return Toast;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet ToastModule = /*#__PURE__*/(() => {\n  class ToastModule {\n    static ɵfac = function ToastModule_Factory(t) {\n      return new (t || ToastModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: ToastModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, RippleModule, CheckIcon, InfoCircleIcon, TimesCircleIcon, ExclamationTriangleIcon, TimesIcon, SharedModule]\n    });\n  }\n  return ToastModule;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Toast, ToastItem, ToastModule };\n//# sourceMappingURL=primeng-toast.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}