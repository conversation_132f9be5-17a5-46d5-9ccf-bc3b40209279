"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2022 Google LLC. https://angular.io/
 * License: MIT
 */Zone.__load_patch("ResizeObserver",((e,t,r)=>{const n=e.ResizeObserver;if(!n)return;const o=r.symbol("ResizeObserver");r.patchMethod(e,"ResizeObserver",(e=>(e,r)=>{const s=r.length>0?r[0]:null;return s&&(r[0]=function(e,r){const n={},l=t.current;for(let t of e){let e=t.target[o];e||(e=l);let r=n[e.name];r||(n[e.name]=r={entries:[],zone:e}),r.entries.push(t)}Object.keys(n).forEach((e=>{const o=n[e];o.zone!==t.current?o.zone.run(s,this,[o.entries,r],"ResizeObserver"):s.call(this,o.entries,r)}))}),r.length>0?new n(r[0]):new n})),r.patchMethod(n.prototype,"observe",(e=>(r,n)=>{const s=n.length>0?n[0]:null;if(!s)return e.apply(r,n);let l=r[o];return l||(l=r[o]=[]),l.push(s),s[o]=t.current,e.apply(r,n)})),r.patchMethod(n.prototype,"unobserve",(e=>(t,r)=>{const n=r.length>0?r[0]:null;if(!n)return e.apply(t,r);let s=t[o];if(s)for(let e=0;e<s.length;e++)if(s[e]===n){s.splice(e,1);break}return n[o]=void 0,e.apply(t,r)})),r.patchMethod(n.prototype,"disconnect",(e=>(t,r)=>{const n=t[o];return n&&(n.forEach((e=>{e[o]=void 0})),t[o]=void 0),e.apply(t,r)}))}));