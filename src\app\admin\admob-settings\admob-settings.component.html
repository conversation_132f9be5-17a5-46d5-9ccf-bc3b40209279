<div class="admob-settings-container">
  
  <!-- Header -->
  <div class="settings-header">
    <h2>إعدادات AdMob</h2>
    <p>إدارة إعدادات الإعلانات المكافئة</p>
  </div>

  <!-- Configuration Form -->
  <div class="settings-section">
    <h3>تكوين AdMob</h3>
    
    <form [formGroup]="settingsForm" class="admob-form">
      
      <!-- App ID -->
      <div class="form-group">
        <label for="appId">معرف التطبيق (App ID) *</label>
        <input 
          id="appId"
          type="text"
          formControlName="appId"
          pInputText 
          placeholder="ca-app-pub-4373910379376809~8789974713"
          class="w-100"
          [class.error]="appIdError">
        <small class="error-text" *ngIf="appIdError">{{ appIdError }}</small>
        <small class="help-text">معرف التطبيق من وحدة تحكم AdMob</small>
      </div>

      <!-- Ad Unit ID -->
      <div class="form-group">
        <label for="adUnitId">معرف الوحدة الإعلانية (Ad Unit ID) *</label>
        <input 
          id="adUnitId"
          type="text"
          formControlName="adUnitId"
          pInputText 
          placeholder="ca-app-pub-4373910379376809/7815804390"
          class="w-100"
          [class.error]="adUnitIdError">
        <small class="error-text" *ngIf="adUnitIdError">{{ adUnitIdError }}</small>
        <small class="help-text">معرف الوحدة الإعلانية للإعلانات المكافئة</small>
      </div>

      <!-- Test Mode -->
      <div class="form-group">
        <div class="checkbox-container">
          <p-checkbox 
            formControlName="testMode"
            label="وضع الاختبار"
            [binary]="true">
          </p-checkbox>
          <small class="help-text">تفعيل وضع الاختبار لعرض إعلانات تجريبية</small>
        </div>
      </div>

      <!-- Save Message -->
      <div class="save-message" *ngIf="saveMessage" [ngClass]="{
        'success': saveMessage.includes('✅'),
        'error': saveMessage.includes('❌')
      }">
        {{ saveMessage }}
      </div>

      <!-- Form Actions -->
      <div class="form-actions">
        <p-button 
          label="حفظ الإعدادات" 
          icon="pi pi-save" 
          [loading]="isLoading"
          [disabled]="!settingsForm.valid"
          (onClick)="onSaveSettings()">
        </p-button>
        
        <p-button 
          label="اختبار الإعلان" 
          icon="pi pi-play" 
          severity="info"
          [outlined]="true"
          [loading]="isLoading"
          (onClick)="onTestAd()">
        </p-button>
        
        <p-button 
          label="إعادة تعيين" 
          icon="pi pi-refresh" 
          severity="secondary"
          [outlined]="true"
          (onClick)="onResetSettings()">
        </p-button>
      </div>

      <!-- Test Result -->
      <div class="test-result" *ngIf="testAdResult" [ngClass]="{
        'success': testAdResult.includes('✅'),
        'error': testAdResult.includes('❌')
      }">
        <i class="pi pi-info-circle"></i>
        {{ testAdResult }}
      </div>

    </form>
  </div>

  <!-- Statistics -->
  <div class="stats-section">
    <div class="stats-header">
      <h3>إحصائيات الإعلانات</h3>
      <p-button 
        icon="pi pi-refresh" 
        severity="secondary"
        [text]="true"
        size="small"
        pTooltip="تحديث الإحصائيات"
        (onClick)="onRefreshStats()">
      </p-button>
    </div>
    
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">
          <i class="pi pi-eye"></i>
        </div>
        <div class="stat-info">
          <h4>{{ adStats.totalShown || 0 }}</h4>
          <p>إجمالي الإعلانات المعروضة</p>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <i class="pi pi-check-circle"></i>
        </div>
        <div class="stat-info">
          <h4>{{ adStats.totalRewarded || 0 }}</h4>
          <p>الإعلانات المكتملة</p>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <i class="pi pi-percentage"></i>
        </div>
        <div class="stat-info">
          <h4>{{ adStats.successRate || 0 }}%</h4>
          <p>معدل النجاح</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Integration Guide -->
  <div class="guide-section">
    <h3>دليل التكامل</h3>
    
    <div class="guide-content">
      <div class="guide-step">
        <div class="step-number">1</div>
        <div class="step-content">
          <h4>إنشاء حساب AdMob</h4>
          <p>قم بإنشاء حساب على <a href="https://admob.google.com" target="_blank">AdMob</a> وأنشئ تطبيقاً جديداً</p>
        </div>
      </div>
      
      <div class="guide-step">
        <div class="step-number">2</div>
        <div class="step-content">
          <h4>إنشاء وحدة إعلانية</h4>
          <p>أنشئ وحدة إعلانية من نوع "Rewarded" للإعلانات المكافئة</p>
        </div>
      </div>
      
      <div class="guide-step">
        <div class="step-number">3</div>
        <div class="step-content">
          <h4>نسخ المعرفات</h4>
          <p>انسخ معرف التطبيق ومعرف الوحدة الإعلانية وأدخلهما في النموذج أعلاه</p>
        </div>
      </div>
      
      <div class="guide-step">
        <div class="step-number">4</div>
        <div class="step-content">
          <h4>اختبار التكامل</h4>
          <p>استخدم زر "اختبار الإعلان" للتأكد من عمل التكامل بشكل صحيح</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Important Notes -->
  <div class="notes-section">
    <h3>ملاحظات مهمة</h3>
    
    <div class="note-item">
      <i class="pi pi-exclamation-triangle text-warning"></i>
      <div>
        <h4>وضع الاختبار</h4>
        <p>تأكد من إيقاف وضع الاختبار في الإنتاج لتجنب انتهاك سياسات AdMob</p>
      </div>
    </div>
    
    <div class="note-item">
      <i class="pi pi-shield text-info"></i>
      <div>
        <h4>سياسات AdMob</h4>
        <p>تأكد من الامتثال لسياسات AdMob وعدم النقر على الإعلانات بنفسك</p>
      </div>
    </div>
    
    <div class="note-item">
      <i class="pi pi-clock text-success"></i>
      <div>
        <h4>وقت التفعيل</h4>
        <p>قد يستغرق تفعيل الإعلانات الحقيقية بعض الوقت بعد إنشاء الحساب</p>
      </div>
    </div>
  </div>

</div>
