{"ast": null, "code": "import { isPlatformBrowser, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { TemplateRef, PLATFORM_ID, Directive, Inject, Input, HostListener, NgModule } from '@angular/core';\nimport { DomHandler, ConnectedOverlayScrollHandler } from 'primeng/dom';\nimport { UniqueComponentId, ZIndexUtils } from 'primeng/utils';\nimport * as i1 from 'primeng/api';\n\n/**\n * Tooltip directive provides advisory information for a component.\n * @group Components\n */\nlet Tooltip = /*#__PURE__*/(() => {\n  class Tooltip {\n    platformId;\n    el;\n    zone;\n    config;\n    renderer;\n    viewContainer;\n    /**\n     * Position of the tooltip.\n     * @group Props\n     */\n    tooltipPosition;\n    /**\n     * Event to show the tooltip.\n     * @group Props\n     */\n    tooltipEvent = 'hover';\n    /**\n     *  Target element to attach the overlay, valid values are \"body\", \"target\" or a local ng-F variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n     * @group Props\n     */\n    appendTo;\n    /**\n     * Type of CSS position.\n     * @group Props\n     */\n    positionStyle;\n    /**\n     * Style class of the tooltip.\n     * @group Props\n     */\n    tooltipStyleClass;\n    /**\n     * Whether the z-index should be managed automatically to always go on top or have a fixed value.\n     * @group Props\n     */\n    tooltipZIndex;\n    /**\n     * By default the tooltip contents are rendered as text. Set to false to support html tags in the content.\n     * @group Props\n     */\n    escape = true;\n    /**\n     * Delay to show the tooltip in milliseconds.\n     * @group Props\n     */\n    showDelay;\n    /**\n     * Delay to hide the tooltip in milliseconds.\n     * @group Props\n     */\n    hideDelay;\n    /**\n     * Time to wait in milliseconds to hide the tooltip even it is active.\n     * @group Props\n     */\n    life;\n    /**\n     * Specifies the additional vertical offset of the tooltip from its default position.\n     * @group Props\n     */\n    positionTop;\n    /**\n     * Specifies the additional horizontal offset of the tooltip from its default position.\n     * @group Props\n     */\n    positionLeft;\n    /**\n     * Whether to hide tooltip when hovering over tooltip content.\n     * @group Props\n     */\n    autoHide = true;\n    /**\n     * Automatically adjusts the element position when there is not enough space on the selected position.\n     * @group Props\n     */\n    fitContent = true;\n    /**\n     * Whether to hide tooltip on escape key press.\n     * @group Props\n     */\n    hideOnEscape = true;\n    /**\n     * Content of the tooltip.\n     * @group Props\n     */\n    content;\n    /**\n     * When present, it specifies that the component should be disabled.\n     * @defaultValue false\n     * @group Props\n     */\n    get disabled() {\n      return this._disabled;\n    }\n    set disabled(val) {\n      this._disabled = val;\n      this.deactivate();\n    }\n    /**\n     * Specifies the tooltip configuration options for the component.\n     * @group Props\n     */\n    tooltipOptions;\n    _tooltipOptions = {\n      tooltipLabel: null,\n      tooltipPosition: 'right',\n      tooltipEvent: 'hover',\n      appendTo: 'body',\n      positionStyle: null,\n      tooltipStyleClass: null,\n      tooltipZIndex: 'auto',\n      escape: true,\n      disabled: null,\n      showDelay: null,\n      hideDelay: null,\n      positionTop: null,\n      positionLeft: null,\n      life: null,\n      autoHide: true,\n      hideOnEscape: true,\n      id: UniqueComponentId() + '_tooltip'\n    };\n    _disabled;\n    container;\n    styleClass;\n    tooltipText;\n    showTimeout;\n    hideTimeout;\n    active;\n    mouseEnterListener;\n    mouseLeaveListener;\n    containerMouseleaveListener;\n    clickListener;\n    focusListener;\n    blurListener;\n    scrollHandler;\n    resizeListener;\n    constructor(platformId, el, zone, config, renderer, viewContainer) {\n      this.platformId = platformId;\n      this.el = el;\n      this.zone = zone;\n      this.config = config;\n      this.renderer = renderer;\n      this.viewContainer = viewContainer;\n    }\n    ngAfterViewInit() {\n      if (isPlatformBrowser(this.platformId)) {\n        this.zone.runOutsideAngular(() => {\n          if (this.getOption('tooltipEvent') === 'hover') {\n            this.mouseEnterListener = this.onMouseEnter.bind(this);\n            this.mouseLeaveListener = this.onMouseLeave.bind(this);\n            this.clickListener = this.onInputClick.bind(this);\n            this.el.nativeElement.addEventListener('mouseenter', this.mouseEnterListener);\n            this.el.nativeElement.addEventListener('click', this.clickListener);\n            this.el.nativeElement.addEventListener('mouseleave', this.mouseLeaveListener);\n          } else if (this.getOption('tooltipEvent') === 'focus') {\n            this.focusListener = this.onFocus.bind(this);\n            this.blurListener = this.onBlur.bind(this);\n            let target = this.getTarget(this.el.nativeElement);\n            target.addEventListener('focus', this.focusListener);\n            target.addEventListener('blur', this.blurListener);\n          }\n        });\n      }\n    }\n    ngOnChanges(simpleChange) {\n      if (simpleChange.tooltipPosition) {\n        this.setOption({\n          tooltipPosition: simpleChange.tooltipPosition.currentValue\n        });\n      }\n      if (simpleChange.tooltipEvent) {\n        this.setOption({\n          tooltipEvent: simpleChange.tooltipEvent.currentValue\n        });\n      }\n      if (simpleChange.appendTo) {\n        this.setOption({\n          appendTo: simpleChange.appendTo.currentValue\n        });\n      }\n      if (simpleChange.positionStyle) {\n        this.setOption({\n          positionStyle: simpleChange.positionStyle.currentValue\n        });\n      }\n      if (simpleChange.tooltipStyleClass) {\n        this.setOption({\n          tooltipStyleClass: simpleChange.tooltipStyleClass.currentValue\n        });\n      }\n      if (simpleChange.tooltipZIndex) {\n        this.setOption({\n          tooltipZIndex: simpleChange.tooltipZIndex.currentValue\n        });\n      }\n      if (simpleChange.escape) {\n        this.setOption({\n          escape: simpleChange.escape.currentValue\n        });\n      }\n      if (simpleChange.showDelay) {\n        this.setOption({\n          showDelay: simpleChange.showDelay.currentValue\n        });\n      }\n      if (simpleChange.hideDelay) {\n        this.setOption({\n          hideDelay: simpleChange.hideDelay.currentValue\n        });\n      }\n      if (simpleChange.life) {\n        this.setOption({\n          life: simpleChange.life.currentValue\n        });\n      }\n      if (simpleChange.positionTop) {\n        this.setOption({\n          positionTop: simpleChange.positionTop.currentValue\n        });\n      }\n      if (simpleChange.positionLeft) {\n        this.setOption({\n          positionLeft: simpleChange.positionLeft.currentValue\n        });\n      }\n      if (simpleChange.disabled) {\n        this.setOption({\n          disabled: simpleChange.disabled.currentValue\n        });\n      }\n      if (simpleChange.content) {\n        this.setOption({\n          tooltipLabel: simpleChange.content.currentValue\n        });\n        if (this.active) {\n          if (simpleChange.content.currentValue) {\n            if (this.container && this.container.offsetParent) {\n              this.updateText();\n              this.align();\n            } else {\n              this.show();\n            }\n          } else {\n            this.hide();\n          }\n        }\n      }\n      if (simpleChange.autoHide) {\n        this.setOption({\n          autoHide: simpleChange.autoHide.currentValue\n        });\n      }\n      if (simpleChange.id) {\n        this.setOption({\n          id: simpleChange.id.currentValue\n        });\n      }\n      if (simpleChange.tooltipOptions) {\n        this._tooltipOptions = {\n          ...this._tooltipOptions,\n          ...simpleChange.tooltipOptions.currentValue\n        };\n        this.deactivate();\n        if (this.active) {\n          if (this.getOption('tooltipLabel')) {\n            if (this.container && this.container.offsetParent) {\n              this.updateText();\n              this.align();\n            } else {\n              this.show();\n            }\n          } else {\n            this.hide();\n          }\n        }\n      }\n    }\n    isAutoHide() {\n      return this.getOption('autoHide');\n    }\n    onMouseEnter(e) {\n      if (!this.container && !this.showTimeout) {\n        this.activate();\n      }\n    }\n    onMouseLeave(e) {\n      if (!this.isAutoHide()) {\n        const valid = DomHandler.hasClass(e.relatedTarget, 'p-tooltip') || DomHandler.hasClass(e.relatedTarget, 'p-tooltip-text') || DomHandler.hasClass(e.relatedTarget, 'p-tooltip-arrow');\n        !valid && this.deactivate();\n      } else {\n        this.deactivate();\n      }\n    }\n    onFocus(e) {\n      this.activate();\n    }\n    onBlur(e) {\n      this.deactivate();\n    }\n    onInputClick(e) {\n      this.deactivate();\n    }\n    onPressEscape() {\n      if (this.hideOnEscape) {\n        this.deactivate();\n      }\n    }\n    activate() {\n      this.active = true;\n      this.clearHideTimeout();\n      if (this.getOption('showDelay')) this.showTimeout = setTimeout(() => {\n        this.show();\n      }, this.getOption('showDelay'));else this.show();\n      if (this.getOption('life')) {\n        let duration = this.getOption('showDelay') ? this.getOption('life') + this.getOption('showDelay') : this.getOption('life');\n        this.hideTimeout = setTimeout(() => {\n          this.hide();\n        }, duration);\n      }\n    }\n    deactivate() {\n      this.active = false;\n      this.clearShowTimeout();\n      if (this.getOption('hideDelay')) {\n        this.clearHideTimeout(); //life timeout\n        this.hideTimeout = setTimeout(() => {\n          this.hide();\n        }, this.getOption('hideDelay'));\n      } else {\n        this.hide();\n      }\n    }\n    create() {\n      if (this.container) {\n        this.clearHideTimeout();\n        this.remove();\n      }\n      this.container = document.createElement('div');\n      this.container.setAttribute('id', this.getOption('id'));\n      this.container.setAttribute('role', 'tooltip');\n      let tooltipArrow = document.createElement('div');\n      tooltipArrow.className = 'p-tooltip-arrow';\n      this.container.appendChild(tooltipArrow);\n      this.tooltipText = document.createElement('div');\n      this.tooltipText.className = 'p-tooltip-text';\n      this.updateText();\n      if (this.getOption('positionStyle')) {\n        this.container.style.position = this.getOption('positionStyle');\n      }\n      this.container.appendChild(this.tooltipText);\n      if (this.getOption('appendTo') === 'body') document.body.appendChild(this.container);else if (this.getOption('appendTo') === 'target') DomHandler.appendChild(this.container, this.el.nativeElement);else DomHandler.appendChild(this.container, this.getOption('appendTo'));\n      this.container.style.display = 'inline-block';\n      if (this.fitContent) {\n        this.container.style.width = 'fit-content';\n      }\n      if (this.isAutoHide()) {\n        this.container.style.pointerEvents = 'none';\n      } else {\n        this.container.style.pointerEvents = 'unset';\n        this.bindContainerMouseleaveListener();\n      }\n    }\n    bindContainerMouseleaveListener() {\n      if (!this.containerMouseleaveListener) {\n        const targetEl = this.container ?? this.container.nativeElement;\n        this.containerMouseleaveListener = this.renderer.listen(targetEl, 'mouseleave', e => {\n          this.deactivate();\n        });\n      }\n    }\n    unbindContainerMouseleaveListener() {\n      if (this.containerMouseleaveListener) {\n        this.bindContainerMouseleaveListener();\n        this.containerMouseleaveListener = null;\n      }\n    }\n    show() {\n      if (!this.getOption('tooltipLabel') || this.getOption('disabled')) {\n        return;\n      }\n      this.create();\n      this.align();\n      DomHandler.fadeIn(this.container, 250);\n      if (this.getOption('tooltipZIndex') === 'auto') ZIndexUtils.set('tooltip', this.container, this.config.zIndex.tooltip);else this.container.style.zIndex = this.getOption('tooltipZIndex');\n      this.bindDocumentResizeListener();\n      this.bindScrollListener();\n    }\n    hide() {\n      if (this.getOption('tooltipZIndex') === 'auto') {\n        ZIndexUtils.clear(this.container);\n      }\n      this.remove();\n    }\n    updateText() {\n      const content = this.getOption('tooltipLabel');\n      if (content instanceof TemplateRef) {\n        const embeddedViewRef = this.viewContainer.createEmbeddedView(content);\n        embeddedViewRef.detectChanges();\n        embeddedViewRef.rootNodes.forEach(node => this.tooltipText.appendChild(node));\n      } else if (this.getOption('escape')) {\n        this.tooltipText.innerHTML = '';\n        this.tooltipText.appendChild(document.createTextNode(content));\n      } else {\n        this.tooltipText.innerHTML = content;\n      }\n    }\n    align() {\n      let position = this.getOption('tooltipPosition');\n      switch (position) {\n        case 'top':\n          this.alignTop();\n          if (this.isOutOfBounds()) {\n            this.alignBottom();\n            if (this.isOutOfBounds()) {\n              this.alignRight();\n              if (this.isOutOfBounds()) {\n                this.alignLeft();\n              }\n            }\n          }\n          break;\n        case 'bottom':\n          this.alignBottom();\n          if (this.isOutOfBounds()) {\n            this.alignTop();\n            if (this.isOutOfBounds()) {\n              this.alignRight();\n              if (this.isOutOfBounds()) {\n                this.alignLeft();\n              }\n            }\n          }\n          break;\n        case 'left':\n          this.alignLeft();\n          if (this.isOutOfBounds()) {\n            this.alignRight();\n            if (this.isOutOfBounds()) {\n              this.alignTop();\n              if (this.isOutOfBounds()) {\n                this.alignBottom();\n              }\n            }\n          }\n          break;\n        case 'right':\n          this.alignRight();\n          if (this.isOutOfBounds()) {\n            this.alignLeft();\n            if (this.isOutOfBounds()) {\n              this.alignTop();\n              if (this.isOutOfBounds()) {\n                this.alignBottom();\n              }\n            }\n          }\n          break;\n      }\n    }\n    getHostOffset() {\n      if (this.getOption('appendTo') === 'body' || this.getOption('appendTo') === 'target') {\n        let offset = this.el.nativeElement.getBoundingClientRect();\n        let targetLeft = offset.left + DomHandler.getWindowScrollLeft();\n        let targetTop = offset.top + DomHandler.getWindowScrollTop();\n        return {\n          left: targetLeft,\n          top: targetTop\n        };\n      } else {\n        return {\n          left: 0,\n          top: 0\n        };\n      }\n    }\n    alignRight() {\n      this.preAlign('right');\n      let hostOffset = this.getHostOffset();\n      let left = hostOffset.left + DomHandler.getOuterWidth(this.el.nativeElement);\n      let top = hostOffset.top + (DomHandler.getOuterHeight(this.el.nativeElement) - DomHandler.getOuterHeight(this.container)) / 2;\n      this.container.style.left = left + this.getOption('positionLeft') + 'px';\n      this.container.style.top = top + this.getOption('positionTop') + 'px';\n    }\n    alignLeft() {\n      this.preAlign('left');\n      let hostOffset = this.getHostOffset();\n      let left = hostOffset.left - DomHandler.getOuterWidth(this.container);\n      let top = hostOffset.top + (DomHandler.getOuterHeight(this.el.nativeElement) - DomHandler.getOuterHeight(this.container)) / 2;\n      this.container.style.left = left + this.getOption('positionLeft') + 'px';\n      this.container.style.top = top + this.getOption('positionTop') + 'px';\n    }\n    alignTop() {\n      this.preAlign('top');\n      let hostOffset = this.getHostOffset();\n      let left = hostOffset.left + (DomHandler.getOuterWidth(this.el.nativeElement) - DomHandler.getOuterWidth(this.container)) / 2;\n      let top = hostOffset.top - DomHandler.getOuterHeight(this.container);\n      this.container.style.left = left + this.getOption('positionLeft') + 'px';\n      this.container.style.top = top + this.getOption('positionTop') + 'px';\n    }\n    alignBottom() {\n      this.preAlign('bottom');\n      let hostOffset = this.getHostOffset();\n      let left = hostOffset.left + (DomHandler.getOuterWidth(this.el.nativeElement) - DomHandler.getOuterWidth(this.container)) / 2;\n      let top = hostOffset.top + DomHandler.getOuterHeight(this.el.nativeElement);\n      this.container.style.left = left + this.getOption('positionLeft') + 'px';\n      this.container.style.top = top + this.getOption('positionTop') + 'px';\n    }\n    setOption(option) {\n      this._tooltipOptions = {\n        ...this._tooltipOptions,\n        ...option\n      };\n    }\n    getOption(option) {\n      return this._tooltipOptions[option];\n    }\n    getTarget(el) {\n      return DomHandler.hasClass(el, 'p-inputwrapper') ? DomHandler.findSingle(el, 'input') : el;\n    }\n    preAlign(position) {\n      this.container.style.left = -999 + 'px';\n      this.container.style.top = -999 + 'px';\n      let defaultClassName = 'p-tooltip p-component p-tooltip-' + position;\n      this.container.className = this.getOption('tooltipStyleClass') ? defaultClassName + ' ' + this.getOption('tooltipStyleClass') : defaultClassName;\n    }\n    isOutOfBounds() {\n      let offset = this.container.getBoundingClientRect();\n      let targetTop = offset.top;\n      let targetLeft = offset.left;\n      let width = DomHandler.getOuterWidth(this.container);\n      let height = DomHandler.getOuterHeight(this.container);\n      let viewport = DomHandler.getViewport();\n      return targetLeft + width > viewport.width || targetLeft < 0 || targetTop < 0 || targetTop + height > viewport.height;\n    }\n    onWindowResize(e) {\n      this.hide();\n    }\n    bindDocumentResizeListener() {\n      this.zone.runOutsideAngular(() => {\n        this.resizeListener = this.onWindowResize.bind(this);\n        window.addEventListener('resize', this.resizeListener);\n      });\n    }\n    unbindDocumentResizeListener() {\n      if (this.resizeListener) {\n        window.removeEventListener('resize', this.resizeListener);\n        this.resizeListener = null;\n      }\n    }\n    bindScrollListener() {\n      if (!this.scrollHandler) {\n        this.scrollHandler = new ConnectedOverlayScrollHandler(this.el.nativeElement, () => {\n          if (this.container) {\n            this.hide();\n          }\n        });\n      }\n      this.scrollHandler.bindScrollListener();\n    }\n    unbindScrollListener() {\n      if (this.scrollHandler) {\n        this.scrollHandler.unbindScrollListener();\n      }\n    }\n    unbindEvents() {\n      if (this.getOption('tooltipEvent') === 'hover') {\n        this.el.nativeElement.removeEventListener('mouseenter', this.mouseEnterListener);\n        this.el.nativeElement.removeEventListener('mouseleave', this.mouseLeaveListener);\n        this.el.nativeElement.removeEventListener('click', this.clickListener);\n      } else if (this.getOption('tooltipEvent') === 'focus') {\n        let target = this.getTarget(this.el.nativeElement);\n        target.removeEventListener('focus', this.focusListener);\n        target.removeEventListener('blur', this.blurListener);\n      }\n      this.unbindDocumentResizeListener();\n    }\n    remove() {\n      if (this.container && this.container.parentElement) {\n        if (this.getOption('appendTo') === 'body') document.body.removeChild(this.container);else if (this.getOption('appendTo') === 'target') this.el.nativeElement.removeChild(this.container);else DomHandler.removeChild(this.container, this.getOption('appendTo'));\n      }\n      this.unbindDocumentResizeListener();\n      this.unbindScrollListener();\n      this.unbindContainerMouseleaveListener();\n      this.clearTimeouts();\n      this.container = null;\n      this.scrollHandler = null;\n    }\n    clearShowTimeout() {\n      if (this.showTimeout) {\n        clearTimeout(this.showTimeout);\n        this.showTimeout = null;\n      }\n    }\n    clearHideTimeout() {\n      if (this.hideTimeout) {\n        clearTimeout(this.hideTimeout);\n        this.hideTimeout = null;\n      }\n    }\n    clearTimeouts() {\n      this.clearShowTimeout();\n      this.clearHideTimeout();\n    }\n    ngOnDestroy() {\n      this.unbindEvents();\n      if (this.container) {\n        ZIndexUtils.clear(this.container);\n      }\n      this.remove();\n      if (this.scrollHandler) {\n        this.scrollHandler.destroy();\n        this.scrollHandler = null;\n      }\n    }\n    static ɵfac = function Tooltip_Factory(t) {\n      return new (t || Tooltip)(i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1.PrimeNGConfig), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ViewContainerRef));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: Tooltip,\n      selectors: [[\"\", \"pTooltip\", \"\"]],\n      hostAttrs: [1, \"p-element\"],\n      hostBindings: function Tooltip_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown.escape\", function Tooltip_keydown_escape_HostBindingHandler($event) {\n            return ctx.onPressEscape($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      inputs: {\n        tooltipPosition: \"tooltipPosition\",\n        tooltipEvent: \"tooltipEvent\",\n        appendTo: \"appendTo\",\n        positionStyle: \"positionStyle\",\n        tooltipStyleClass: \"tooltipStyleClass\",\n        tooltipZIndex: \"tooltipZIndex\",\n        escape: \"escape\",\n        showDelay: \"showDelay\",\n        hideDelay: \"hideDelay\",\n        life: \"life\",\n        positionTop: \"positionTop\",\n        positionLeft: \"positionLeft\",\n        autoHide: \"autoHide\",\n        fitContent: \"fitContent\",\n        hideOnEscape: \"hideOnEscape\",\n        content: [\"pTooltip\", \"content\"],\n        disabled: [\"tooltipDisabled\", \"disabled\"],\n        tooltipOptions: \"tooltipOptions\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n  return Tooltip;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet TooltipModule = /*#__PURE__*/(() => {\n  class TooltipModule {\n    static ɵfac = function TooltipModule_Factory(t) {\n      return new (t || TooltipModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: TooltipModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n  return TooltipModule;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Tooltip, TooltipModule };\n//# sourceMappingURL=primeng-tooltip.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}