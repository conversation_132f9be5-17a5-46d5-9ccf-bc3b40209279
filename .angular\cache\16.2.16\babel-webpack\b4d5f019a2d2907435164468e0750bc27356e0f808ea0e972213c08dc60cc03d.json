{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/\\u062A\\u0637\\u0628\\u064A\\u0642 \\u0646\\u0627\\u0634\\u0631 \\u0627\\u064A\\u0627\\u062A \\u0642\\u0626\\u0627\\u0646\\u064A\\u0629/QuranVidGen/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport { _generateLinkResponse, _noResolveJsonResponse, _request, _userResponse } from './lib/fetch';\nimport { resolveFetch, validateUUID } from './lib/helpers';\nimport { SIGN_OUT_SCOPES } from './lib/types';\nimport { isAuthError } from './lib/errors';\nclass GoTrueAdminApi {\n  constructor({\n    url = '',\n    headers = {},\n    fetch\n  }) {\n    this.url = url;\n    this.headers = headers;\n    this.fetch = resolveFetch(fetch);\n    this.mfa = {\n      listFactors: this._listFactors.bind(this),\n      deleteFactor: this._deleteFactor.bind(this)\n    };\n  }\n  /**\n   * Removes a logged-in session.\n   * @param jwt A valid, logged-in JWT.\n   * @param scope The logout sope.\n   */\n  signOut(_x) {\n    var _this = this;\n    return _asyncToGenerator(function* (jwt, scope = SIGN_OUT_SCOPES[0]) {\n      if (SIGN_OUT_SCOPES.indexOf(scope) < 0) {\n        throw new Error(`@supabase/auth-js: Parameter scope must be one of ${SIGN_OUT_SCOPES.join(', ')}`);\n      }\n      try {\n        yield _request(_this.fetch, 'POST', `${_this.url}/logout?scope=${scope}`, {\n          headers: _this.headers,\n          jwt,\n          noResolveJson: true\n        });\n        return {\n          data: null,\n          error: null\n        };\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    }).apply(this, arguments);\n  }\n  /**\n   * Sends an invite link to an email address.\n   * @param email The email address of the user.\n   * @param options Additional options to be included when inviting.\n   */\n  inviteUserByEmail(_x2) {\n    var _this2 = this;\n    return _asyncToGenerator(function* (email, options = {}) {\n      try {\n        return yield _request(_this2.fetch, 'POST', `${_this2.url}/invite`, {\n          body: {\n            email,\n            data: options.data\n          },\n          headers: _this2.headers,\n          redirectTo: options.redirectTo,\n          xform: _userResponse\n        });\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: {\n              user: null\n            },\n            error\n          };\n        }\n        throw error;\n      }\n    }).apply(this, arguments);\n  }\n  /**\n   * Generates email links and OTPs to be sent via a custom email provider.\n   * @param email The user's email.\n   * @param options.password User password. For signup only.\n   * @param options.data Optional user metadata. For signup only.\n   * @param options.redirectTo The redirect url which should be appended to the generated link\n   */\n  generateLink(params) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const {\n            options\n          } = params,\n          rest = __rest(params, [\"options\"]);\n        const body = Object.assign(Object.assign({}, rest), options);\n        if ('newEmail' in rest) {\n          // replace newEmail with new_email in request body\n          body.new_email = rest === null || rest === void 0 ? void 0 : rest.newEmail;\n          delete body['newEmail'];\n        }\n        return yield _request(_this3.fetch, 'POST', `${_this3.url}/admin/generate_link`, {\n          body: body,\n          headers: _this3.headers,\n          xform: _generateLinkResponse,\n          redirectTo: options === null || options === void 0 ? void 0 : options.redirectTo\n        });\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: {\n              properties: null,\n              user: null\n            },\n            error\n          };\n        }\n        throw error;\n      }\n    })();\n  }\n  // User Admin API\n  /**\n   * Creates a new user.\n   * This function should only be called on a server. Never expose your `service_role` key in the browser.\n   */\n  createUser(attributes) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        return yield _request(_this4.fetch, 'POST', `${_this4.url}/admin/users`, {\n          body: attributes,\n          headers: _this4.headers,\n          xform: _userResponse\n        });\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: {\n              user: null\n            },\n            error\n          };\n        }\n        throw error;\n      }\n    })();\n  }\n  /**\n   * Get a list of users.\n   *\n   * This function should only be called on a server. Never expose your `service_role` key in the browser.\n   * @param params An object which supports `page` and `perPage` as numbers, to alter the paginated results.\n   */\n  listUsers(params) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      var _a, _b, _c, _d, _e, _f, _g;\n      try {\n        const pagination = {\n          nextPage: null,\n          lastPage: 0,\n          total: 0\n        };\n        const response = yield _request(_this5.fetch, 'GET', `${_this5.url}/admin/users`, {\n          headers: _this5.headers,\n          noResolveJson: true,\n          query: {\n            page: (_b = (_a = params === null || params === void 0 ? void 0 : params.page) === null || _a === void 0 ? void 0 : _a.toString()) !== null && _b !== void 0 ? _b : '',\n            per_page: (_d = (_c = params === null || params === void 0 ? void 0 : params.perPage) === null || _c === void 0 ? void 0 : _c.toString()) !== null && _d !== void 0 ? _d : ''\n          },\n          xform: _noResolveJsonResponse\n        });\n        if (response.error) throw response.error;\n        const users = yield response.json();\n        const total = (_e = response.headers.get('x-total-count')) !== null && _e !== void 0 ? _e : 0;\n        const links = (_g = (_f = response.headers.get('link')) === null || _f === void 0 ? void 0 : _f.split(',')) !== null && _g !== void 0 ? _g : [];\n        if (links.length > 0) {\n          links.forEach(link => {\n            const page = parseInt(link.split(';')[0].split('=')[1].substring(0, 1));\n            const rel = JSON.parse(link.split(';')[1].split('=')[1]);\n            pagination[`${rel}Page`] = page;\n          });\n          pagination.total = parseInt(total);\n        }\n        return {\n          data: Object.assign(Object.assign({}, users), pagination),\n          error: null\n        };\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: {\n              users: []\n            },\n            error\n          };\n        }\n        throw error;\n      }\n    })();\n  }\n  /**\n   * Get user by id.\n   *\n   * @param uid The user's unique identifier\n   *\n   * This function should only be called on a server. Never expose your `service_role` key in the browser.\n   */\n  getUserById(uid) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      validateUUID(uid);\n      try {\n        return yield _request(_this6.fetch, 'GET', `${_this6.url}/admin/users/${uid}`, {\n          headers: _this6.headers,\n          xform: _userResponse\n        });\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: {\n              user: null\n            },\n            error\n          };\n        }\n        throw error;\n      }\n    })();\n  }\n  /**\n   * Updates the user data.\n   *\n   * @param attributes The data you want to update.\n   *\n   * This function should only be called on a server. Never expose your `service_role` key in the browser.\n   */\n  updateUserById(uid, attributes) {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      validateUUID(uid);\n      try {\n        return yield _request(_this7.fetch, 'PUT', `${_this7.url}/admin/users/${uid}`, {\n          body: attributes,\n          headers: _this7.headers,\n          xform: _userResponse\n        });\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: {\n              user: null\n            },\n            error\n          };\n        }\n        throw error;\n      }\n    })();\n  }\n  /**\n   * Delete a user. Requires a `service_role` key.\n   *\n   * @param id The user id you want to remove.\n   * @param shouldSoftDelete If true, then the user will be soft-deleted from the auth schema. Soft deletion allows user identification from the hashed user ID but is not reversible.\n   * Defaults to false for backward compatibility.\n   *\n   * This function should only be called on a server. Never expose your `service_role` key in the browser.\n   */\n  deleteUser(_x3) {\n    var _this8 = this;\n    return _asyncToGenerator(function* (id, shouldSoftDelete = false) {\n      validateUUID(id);\n      try {\n        return yield _request(_this8.fetch, 'DELETE', `${_this8.url}/admin/users/${id}`, {\n          headers: _this8.headers,\n          body: {\n            should_soft_delete: shouldSoftDelete\n          },\n          xform: _userResponse\n        });\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: {\n              user: null\n            },\n            error\n          };\n        }\n        throw error;\n      }\n    }).apply(this, arguments);\n  }\n  _listFactors(params) {\n    var _this9 = this;\n    return _asyncToGenerator(function* () {\n      validateUUID(params.userId);\n      try {\n        const {\n          data,\n          error\n        } = yield _request(_this9.fetch, 'GET', `${_this9.url}/admin/users/${params.userId}/factors`, {\n          headers: _this9.headers,\n          xform: factors => {\n            return {\n              data: {\n                factors\n              },\n              error: null\n            };\n          }\n        });\n        return {\n          data,\n          error\n        };\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    })();\n  }\n  _deleteFactor(params) {\n    var _this0 = this;\n    return _asyncToGenerator(function* () {\n      validateUUID(params.userId);\n      validateUUID(params.id);\n      try {\n        const data = yield _request(_this0.fetch, 'DELETE', `${_this0.url}/admin/users/${params.userId}/factors/${params.id}`, {\n          headers: _this0.headers\n        });\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    })();\n  }\n}\n//# sourceMappingURL=GoTrueAdminApi.js.map\nexport { GoTrueAdminApi as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}