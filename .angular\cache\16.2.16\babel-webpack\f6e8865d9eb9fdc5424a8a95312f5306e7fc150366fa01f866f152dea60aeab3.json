{"ast": null, "code": "import { registerPlugin } from '@capacitor/core';\nconst Dialog = registerPlugin('Dialog', {\n  web: () => import('./web').then(m => new m.DialogWeb())\n});\nexport * from './definitions';\nexport { Dialog };", "map": {"version": 3, "names": ["registerPlugin", "Dialog", "web", "then", "m", "DialogWeb"], "sources": ["C:/Users/<USER>/Desktop/تطبيق ناشر ايات قئانية/QuranVidGen/node_modules/@capacitor/dialog/dist/esm/index.js"], "sourcesContent": ["import { registerPlugin } from '@capacitor/core';\nconst Dialog = registerPlugin('Dialog', {\n    web: () => import('./web').then(m => new m.DialogWeb()),\n});\nexport * from './definitions';\nexport { Dialog };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,iBAAiB;AAChD,MAAMC,MAAM,GAAGD,cAAc,CAAC,QAAQ,EAAE;EACpCE,GAAG,EAAEA,CAAA,KAAM,MAAM,CAAC,OAAO,CAAC,CAACC,IAAI,CAACC,CAAC,IAAI,IAAIA,CAAC,CAACC,SAAS,CAAC,CAAC;AAC1D,CAAC,CAAC;AACF,cAAc,eAAe;AAC7B,SAASJ,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}