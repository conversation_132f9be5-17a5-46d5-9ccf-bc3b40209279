🚨 حل مشكلة: Network is unreachable

═══════════════════════════════════════════════════════════════

🎯 الحل الأسرع والأضمن: Android Studio

1️⃣ نزل Android Studio من:
   https://developer.android.com/studio

2️⃣ افتح Android Studio

3️⃣ اختر "Open an existing Android Studio project"

4️⃣ انتقل إلى مجلد: android

5️⃣ Build → Build Bundle(s) / APK(s) → Build APK(s)

6️⃣ انتظر... ستحصل على APK جاهز!

═══════════════════════════════════════════════════════════════

🔧 الحل البديل: تنزيل Gradle يدوياً

1️⃣ شغل PowerShell كمدير

2️⃣ انتقل إلى مجلد المشروع:
   cd "C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية"

3️⃣ شغل:
   powershell -ExecutionPolicy Bypass -File download_gradle.ps1

4️⃣ بعد انتهاء التنزيل، شغل:
   powershell -ExecutionPolicy Bypass -File build_apk_with_gradle.ps1

═══════════════════════════════════════════════════════════════

📱 النتيجة:
ملف APK جاهز على سطح المكتب: QuranVidGen-Debug.apk

═══════════════════════════════════════════════════════════════

💡 ملاحظة:
المشروع جاهز 100% - المشكلة فقط في تنزيل Gradle
Android Studio هو الحل الأسهل والأضمن!
