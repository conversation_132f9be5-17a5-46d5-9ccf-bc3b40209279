import { Injectable } from '@angular/core';
import { Observable, BehaviorSubject, from, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { API_KEYS } from '../config/api-keys';
import { ErrorHandlerService } from './error-handler.service';

declare var gapi: any;

export interface YouTubeUploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

export interface YouTubeVideoDetails {
  title: string;
  description: string;
  tags: string[];
  privacy: 'private' | 'public' | 'unlisted';
  categoryId?: string;
}

export interface YouTubeAuthStatus {
  isAuthenticated: boolean;
  user?: {
    name: string;
    email: string;
    picture: string;
  };
}

@Injectable({
  providedIn: 'root'
})
export class YoutubeService {
  private readonly CLIENT_ID = API_KEYS.GOOGLE_CLIENT_ID;
  private readonly API_KEY = API_KEYS.GOOGLE_API_KEY;
  private readonly DISCOVERY_DOC = 'https://www.googleapis.com/discovery/v1/apis/youtube/v3/rest';
  private readonly SCOPES = 'https://www.googleapis.com/auth/youtube.upload https://www.googleapis.com/auth/youtube';

  private authStatusSubject = new BehaviorSubject<YouTubeAuthStatus>({ isAuthenticated: false });
  public authStatus$ = this.authStatusSubject.asObservable();

  private uploadProgressSubject = new BehaviorSubject<YouTubeUploadProgress>({ loaded: 0, total: 0, percentage: 0 });
  public uploadProgress$ = this.uploadProgressSubject.asObservable();

  constructor(private errorHandler: ErrorHandlerService) {
    this.initializeGapi();
  }

  private async initializeGapi(): Promise<void> {
    try {
      if (typeof gapi === 'undefined') {
        await this.loadGapiScript();
      }
      
      await gapi.load('auth2', () => {
        gapi.auth2.init({
          client_id: this.CLIENT_ID,
        });
      });

      await gapi.load('client', async () => {
        await gapi.client.init({
          apiKey: this.API_KEY,
          clientId: this.CLIENT_ID,
          discoveryDocs: [this.DISCOVERY_DOC],
          scope: this.SCOPES
        });
        
        // Check if user is already signed in
        const authInstance = gapi.auth2.getAuthInstance();
        if (authInstance.isSignedIn.get()) {
          this.updateAuthStatus(true);
        }
      });
    } catch (error) {
      console.error('Error initializing Google API:', error);
    }
  }

  private loadGapiScript(): Promise<void> {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = 'https://apis.google.com/js/api.js';
      script.onload = () => resolve();
      script.onerror = () => reject(new Error('Failed to load Google API script'));
      document.head.appendChild(script);
    });
  }

  public signIn(): Observable<boolean> {
    return from(this.performSignIn()).pipe(
      map(() => {
        this.updateAuthStatus(true);
        return true;
      }),
      catchError(error => {
        this.errorHandler.handleError(error, 'youtube-auth');
        return throwError(() => error);
      })
    );
  }

  private async performSignIn(): Promise<void> {
    const authInstance = gapi.auth2.getAuthInstance();
    await authInstance.signIn();
  }

  public signOut(): Observable<boolean> {
    return from(this.performSignOut()).pipe(
      map(() => {
        this.updateAuthStatus(false);
        return true;
      }),
      catchError(error => {
        console.error('Sign out failed:', error);
        return throwError(() => error);
      })
    );
  }

  private async performSignOut(): Promise<void> {
    const authInstance = gapi.auth2.getAuthInstance();
    await authInstance.signOut();
  }

  private updateAuthStatus(isAuthenticated: boolean): void {
    if (isAuthenticated) {
      const authInstance = gapi.auth2.getAuthInstance();
      const user = authInstance.currentUser.get();
      const profile = user.getBasicProfile();
      
      this.authStatusSubject.next({
        isAuthenticated: true,
        user: {
          name: profile.getName(),
          email: profile.getEmail(),
          picture: profile.getImageUrl()
        }
      });
    } else {
      this.authStatusSubject.next({ isAuthenticated: false });
    }
  }

  public uploadVideo(videoBlob: Blob, videoDetails: YouTubeVideoDetails): Observable<string> {
    return from(this.performVideoUpload(videoBlob, videoDetails)).pipe(
      catchError(error => {
        this.errorHandler.handleError(error, 'youtube-upload');
        return throwError(() => error);
      })
    );
  }

  private async performVideoUpload(videoBlob: Blob, videoDetails: YouTubeVideoDetails): Promise<string> {
    const metadata = {
      snippet: {
        title: videoDetails.title,
        description: videoDetails.description,
        tags: videoDetails.tags,
        categoryId: videoDetails.categoryId || '22' // People & Blogs
      },
      status: {
        privacyStatus: videoDetails.privacy
      }
    };

    const form = new FormData();
    form.append('metadata', new Blob([JSON.stringify(metadata)], { type: 'application/json' }));
    form.append('media', videoBlob);

    const xhr = new XMLHttpRequest();
    
    return new Promise((resolve, reject) => {
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const progress: YouTubeUploadProgress = {
            loaded: event.loaded,
            total: event.total,
            percentage: Math.round((event.loaded / event.total) * 100)
          };
          this.uploadProgressSubject.next(progress);
        }
      });

      xhr.addEventListener('load', () => {
        if (xhr.status === 200) {
          const response = JSON.parse(xhr.responseText);
          resolve(response.id);
        } else {
          reject(new Error(`Upload failed with status: ${xhr.status}`));
        }
      });

      xhr.addEventListener('error', () => {
        reject(new Error('Upload failed'));
      });

      const accessToken = gapi.auth2.getAuthInstance().currentUser.get().getAuthResponse().access_token;
      
      xhr.open('POST', 'https://www.googleapis.com/upload/youtube/v3/videos?uploadType=multipart&part=snippet,status');
      xhr.setRequestHeader('Authorization', `Bearer ${accessToken}`);
      xhr.send(form);
    });
  }

  public isAuthenticated(): boolean {
    return this.authStatusSubject.value.isAuthenticated;
  }

  public getCurrentUser(): any {
    return this.authStatusSubject.value.user;
  }
}
