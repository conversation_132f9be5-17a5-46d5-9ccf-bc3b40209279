import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { SupabaseService } from './supabase.service';

export interface SecurityCheck {
  isVpnDetected: boolean;
  isAdBlockerDetected: boolean;
  isDnsBlocked: boolean;
  blockedReason?: string;
  allowAccess: boolean;
}

export interface SecuritySettings {
  enableVpnDetection: boolean;
  enableAdBlockerDetection: boolean;
  enableDnsDetection: boolean;
  strictMode: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class SecurityService {
  private securityStatusSubject = new BehaviorSubject<SecurityCheck>({
    isVpnDetected: false,
    isAdBlockerDetected: false,
    isDnsBlocked: false,
    allowAccess: true
  });

  public securityStatus$ = this.securityStatusSubject.asObservable();

  private settings: SecuritySettings = {
    enableVpnDetection: true,
    enableAdBlockerDetection: true,
    enableDnsDetection: true,
    strictMode: true
  };

  // Known VPN and proxy indicators
  private vpnIndicators = [
    'vpn', 'proxy', 'tor', 'tunnel', 'anonymizer',
    'hide', 'mask', 'secure', 'private', 'shield'
  ];

  // Known ad blocker extensions
  private adBlockerIndicators = [
    'adblock', 'ublock', 'ghostery', 'disconnect',
    'privacy', 'blocker', 'guard', 'shield'
  ];

  // Blocked DNS servers
  private blockedDnsServers = [
    'dns.adguard.com',
    '************',
    '************',
    '***************',
    '***************',
    '*******', // Cloudflare (can be used for blocking)
    '*******',
    '*******', // Google DNS (can be used for blocking)
    '*******'
  ];

  constructor(private supabaseService: SupabaseService) {
    this.initializeSecurityChecks();
  }

  private async initializeSecurityChecks(): Promise<void> {
    // Run initial security checks
    await this.performSecurityCheck();
    
    // Set up periodic checks
    setInterval(() => {
      this.performSecurityCheck();
    }, 30000); // Check every 30 seconds
  }

  public async performSecurityCheck(): Promise<SecurityCheck> {
    const result: SecurityCheck = {
      isVpnDetected: false,
      isAdBlockerDetected: false,
      isDnsBlocked: false,
      allowAccess: true
    };

    try {
      // Check for VPN/Proxy
      if (this.settings.enableVpnDetection) {
        result.isVpnDetected = await this.detectVpn();
      }

      // Check for Ad Blockers
      if (this.settings.enableAdBlockerDetection) {
        result.isAdBlockerDetected = await this.detectAdBlocker();
      }

      // Check for blocked DNS
      if (this.settings.enableDnsDetection) {
        result.isDnsBlocked = await this.detectBlockedDns();
      }

      // Determine if access should be allowed
      result.allowAccess = this.shouldAllowAccess(result);
      
      if (!result.allowAccess) {
        result.blockedReason = this.getBlockedReason(result);
        this.logSecurityEvent(result);
      }

    } catch (error) {
      console.error('Security check failed:', error);
      // In case of error, allow access but log the issue
      result.allowAccess = !this.settings.strictMode;
    }

    this.securityStatusSubject.next(result);
    return result;
  }

  private async detectVpn(): Promise<boolean> {
    try {
      // Method 1: Check user agent for VPN indicators
      const userAgent = navigator.userAgent.toLowerCase();
      const hasVpnIndicator = this.vpnIndicators.some(indicator => 
        userAgent.includes(indicator)
      );

      if (hasVpnIndicator) {
        return true;
      }

      // Method 2: Check for WebRTC IP leaks
      const hasVpnIp = await this.checkWebRtcIps();
      if (hasVpnIp) {
        return true;
      }

      // Method 3: Check timezone vs expected location
      const hasVpnTimezone = this.checkTimezoneAnomaly();
      if (hasVpnTimezone) {
        return true;
      }

      return false;
    } catch (error) {
      console.error('VPN detection failed:', error);
      return false;
    }
  }

  private async detectAdBlocker(): Promise<boolean> {
    try {
      // Method 1: Try to load a test ad element
      const adTestResult = await this.testAdElement();
      if (adTestResult) {
        return true;
      }

      // Method 2: Check for ad blocker extensions
      const hasAdBlockerExtension = this.checkAdBlockerExtensions();
      if (hasAdBlockerExtension) {
        return true;
      }

      // Method 3: Test blocked requests
      const hasBlockedRequests = await this.testBlockedRequests();
      if (hasBlockedRequests) {
        return true;
      }

      return false;
    } catch (error) {
      console.error('Ad blocker detection failed:', error);
      return false;
    }
  }

  private async detectBlockedDns(): Promise<boolean> {
    try {
      // Check if we can resolve known ad domains
      const testDomains = [
        'googleads.g.doubleclick.net',
        'googlesyndication.com',
        'google-analytics.com'
      ];

      for (const domain of testDomains) {
        const isBlocked = await this.testDomainResolution(domain);
        if (isBlocked) {
          return true;
        }
      }

      return false;
    } catch (error) {
      console.error('DNS detection failed:', error);
      return false;
    }
  }

  private async checkWebRtcIps(): Promise<boolean> {
    return new Promise((resolve) => {
      try {
        const rtc = new RTCPeerConnection({
          iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
        });

        rtc.createDataChannel('');
        rtc.createOffer().then(offer => rtc.setLocalDescription(offer));

        rtc.onicecandidate = (event) => {
          if (event.candidate) {
            const ip = event.candidate.candidate.split(' ')[4];
            
            // Check for VPN/proxy IP patterns
            if (this.isVpnIp(ip)) {
              resolve(true);
              return;
            }
          }
        };

        // Timeout after 3 seconds
        setTimeout(() => {
          rtc.close();
          resolve(false);
        }, 3000);

      } catch (error) {
        resolve(false);
      }
    });
  }

  private checkTimezoneAnomaly(): boolean {
    try {
      const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      const language = navigator.language;
      
      // Simple check: if timezone doesn't match expected region for language
      // This is a basic implementation and can be enhanced
      if (language.startsWith('ar') && !timezone.includes('Asia') && !timezone.includes('Africa')) {
        return true;
      }
      
      return false;
    } catch (error) {
      return false;
    }
  }

  private async testAdElement(): Promise<boolean> {
    return new Promise((resolve) => {
      try {
        const testAd = document.createElement('div');
        testAd.innerHTML = '&nbsp;';
        testAd.className = 'adsbox';
        testAd.style.position = 'absolute';
        testAd.style.left = '-10000px';
        testAd.style.width = '1px';
        testAd.style.height = '1px';
        
        document.body.appendChild(testAd);
        
        setTimeout(() => {
          const isBlocked = testAd.offsetHeight === 0 || 
                           testAd.offsetWidth === 0 || 
                           testAd.style.display === 'none' ||
                           testAd.style.visibility === 'hidden';
          
          document.body.removeChild(testAd);
          resolve(isBlocked);
        }, 100);
        
      } catch (error) {
        resolve(false);
      }
    });
  }

  private checkAdBlockerExtensions(): boolean {
    try {
      // Check for common ad blocker extension indicators
      const extensionIndicators = [
        'chrome-extension://gighmmpiobklfepjocnamgkkbiglidom', // AdBlock
        'chrome-extension://cjpalhdlnbpafiamejdnhcphjbkeiagm', // uBlock Origin
      ];

      return extensionIndicators.some(indicator => {
        try {
          const img = new Image();
          img.src = indicator + '/icon.png';
          return true;
        } catch {
          return false;
        }
      });
    } catch (error) {
      return false;
    }
  }

  private async testBlockedRequests(): Promise<boolean> {
    try {
      const testUrl = 'https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js';
      
      const response = await fetch(testUrl, { 
        method: 'HEAD',
        mode: 'no-cors'
      });
      
      return false; // If we get here, request wasn't blocked
    } catch (error) {
      return true; // Request was likely blocked
    }
  }

  private async testDomainResolution(domain: string): Promise<boolean> {
    try {
      const testUrl = `https://${domain}/test`;
      
      await fetch(testUrl, { 
        method: 'HEAD',
        mode: 'no-cors'
      });
      
      return false; // Domain resolves
    } catch (error) {
      return true; // Domain likely blocked
    }
  }

  private isVpnIp(ip: string): boolean {
    // Check for common VPN IP patterns
    const vpnPatterns = [
      /^10\./, // Private IP ranges often used by VPNs
      /^172\.(1[6-9]|2[0-9]|3[0-1])\./, 
      /^192\.168\./,
      /^169\.254\./ // Link-local addresses
    ];

    return vpnPatterns.some(pattern => pattern.test(ip));
  }

  private shouldAllowAccess(result: SecurityCheck): boolean {
    if (this.settings.strictMode) {
      return !result.isVpnDetected && 
             !result.isAdBlockerDetected && 
             !result.isDnsBlocked;
    } else {
      // In non-strict mode, allow access but warn
      return true;
    }
  }

  private getBlockedReason(result: SecurityCheck): string {
    const reasons = [];
    
    if (result.isVpnDetected) {
      reasons.push('تم اكتشاف استخدام VPN أو بروكسي');
    }
    
    if (result.isAdBlockerDetected) {
      reasons.push('تم اكتشاف أداة حظر الإعلانات');
    }
    
    if (result.isDnsBlocked) {
      reasons.push('تم اكتشاف DNS محجوب للإعلانات');
    }
    
    return reasons.join(', ');
  }

  public updateSettings(newSettings: Partial<SecuritySettings>): void {
    this.settings = { ...this.settings, ...newSettings };
    this.performSecurityCheck(); // Re-check with new settings
  }

  public getSettings(): SecuritySettings {
    return { ...this.settings };
  }

  public async forceSecurityCheck(): Promise<SecurityCheck> {
    return await this.performSecurityCheck();
  }

  private logSecurityEvent(result: SecurityCheck): void {
    if (this.supabaseService.isConnectedToSupabase()) {
      const userId = this.supabaseService.getCurrentUserId();

      let eventType: 'vpn_detected' | 'adblocker_detected' | 'dns_blocked' | 'access_denied' = 'access_denied';

      if (result.isVpnDetected) {
        eventType = 'vpn_detected';
      } else if (result.isAdBlockerDetected) {
        eventType = 'adblocker_detected';
      } else if (result.isDnsBlocked) {
        eventType = 'dns_blocked';
      }

      this.supabaseService.logSecurityEvent({
        user_id: userId,
        event_type: eventType,
        details: {
          vpn_detected: result.isVpnDetected,
          adblocker_detected: result.isAdBlockerDetected,
          dns_blocked: result.isDnsBlocked,
          blocked_reason: result.blockedReason,
          timestamp: new Date().toISOString()
        },
        ip_address: this.getCurrentIP(),
        user_agent: navigator.userAgent
      }).subscribe({
        next: (log) => {
          console.log('Security event logged:', log);
        },
        error: (error) => {
          console.error('Error logging security event:', error);
        }
      });
    }
  }

  private getCurrentIP(): string {
    // This would need to be implemented with an external service
    // For now, return a placeholder
    return '0.0.0.0';
  }
}
