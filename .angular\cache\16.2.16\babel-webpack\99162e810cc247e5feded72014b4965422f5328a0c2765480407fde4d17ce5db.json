{"ast": null, "code": "import { trigger, transition, style, animate } from '@angular/animations';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Optional, Input, Output, ContentChildren, NgModule } from '@angular/core';\nimport * as i1 from 'primeng/api';\nimport { PrimeTemplate } from 'primeng/api';\nimport { CheckIcon } from 'primeng/icons/check';\nimport { ExclamationTriangleIcon } from 'primeng/icons/exclamationtriangle';\nimport { InfoCircleIcon } from 'primeng/icons/infocircle';\nimport { TimesIcon } from 'primeng/icons/times';\nimport { TimesCircleIcon } from 'primeng/icons/timescircle';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport { timer } from 'rxjs';\n\n/**\n * Messages is used to display alerts inline.\n * @group Components\n */\nfunction Messages_ng_container_1_div_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\");\n  }\n  if (rf & 2) {\n    const msg_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassMap(\"p-message-icon pi \" + msg_r4.icon);\n    i0.ɵɵattribute(\"data-pc-section\", \"icon\");\n  }\n}\nfunction Messages_ng_container_1_div_1_span_3_CheckIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"CheckIcon\");\n  }\n  if (rf & 2) {\n    i0.ɵɵattribute(\"data-pc-section\", \"icon\");\n  }\n}\nfunction Messages_ng_container_1_div_1_span_3_InfoCircleIcon_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"InfoCircleIcon\");\n  }\n  if (rf & 2) {\n    i0.ɵɵattribute(\"data-pc-section\", \"icon\");\n  }\n}\nfunction Messages_ng_container_1_div_1_span_3_TimesCircleIcon_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"TimesCircleIcon\");\n  }\n  if (rf & 2) {\n    i0.ɵɵattribute(\"data-pc-section\", \"icon\");\n  }\n}\nfunction Messages_ng_container_1_div_1_span_3_ExclamationTriangleIcon_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ExclamationTriangleIcon\");\n  }\n  if (rf & 2) {\n    i0.ɵɵattribute(\"data-pc-section\", \"icon\");\n  }\n}\nfunction Messages_ng_container_1_div_1_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 10);\n    i0.ɵɵelementContainerStart(1);\n    i0.ɵɵtemplate(2, Messages_ng_container_1_div_1_span_3_CheckIcon_2_Template, 1, 1, \"CheckIcon\", 11);\n    i0.ɵɵtemplate(3, Messages_ng_container_1_div_1_span_3_InfoCircleIcon_3_Template, 1, 1, \"InfoCircleIcon\", 11);\n    i0.ɵɵtemplate(4, Messages_ng_container_1_div_1_span_3_TimesCircleIcon_4_Template, 1, 1, \"TimesCircleIcon\", 11);\n    i0.ɵɵtemplate(5, Messages_ng_container_1_div_1_span_3_ExclamationTriangleIcon_5_Template, 1, 1, \"ExclamationTriangleIcon\", 11);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const msg_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", msg_r4.severity === \"success\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", msg_r4.severity === \"info\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", msg_r4.severity === \"error\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", msg_r4.severity === \"warn\");\n  }\n}\nfunction Messages_ng_container_1_div_1_ng_container_4_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 14);\n  }\n  if (rf & 2) {\n    const msg_r4 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"innerHTML\", msg_r4.summary, i0.ɵɵsanitizeHtml);\n    i0.ɵɵattribute(\"data-pc-section\", \"summary\");\n  }\n}\nfunction Messages_ng_container_1_div_1_ng_container_4_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 15);\n  }\n  if (rf & 2) {\n    const msg_r4 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"innerHTML\", msg_r4.detail, i0.ɵɵsanitizeHtml);\n    i0.ɵɵattribute(\"data-pc-section\", \"detail\");\n  }\n}\nfunction Messages_ng_container_1_div_1_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Messages_ng_container_1_div_1_ng_container_4_span_1_Template, 1, 2, \"span\", 12);\n    i0.ɵɵtemplate(2, Messages_ng_container_1_div_1_ng_container_4_span_2_Template, 1, 2, \"span\", 13);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const msg_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", msg_r4.summary);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", msg_r4.detail);\n  }\n}\nfunction Messages_ng_container_1_div_1_ng_template_5_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 18);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const msg_r4 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵattribute(\"data-pc-section\", \"summary\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(msg_r4.summary);\n  }\n}\nfunction Messages_ng_container_1_div_1_ng_template_5_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const msg_r4 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵattribute(\"data-pc-section\", \"detail\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(msg_r4.detail);\n  }\n}\nfunction Messages_ng_container_1_div_1_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Messages_ng_container_1_div_1_ng_template_5_span_0_Template, 2, 2, \"span\", 16);\n    i0.ɵɵtemplate(1, Messages_ng_container_1_div_1_ng_template_5_span_1_Template, 2, 2, \"span\", 17);\n  }\n  if (rf & 2) {\n    const msg_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngIf\", msg_r4.summary);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", msg_r4.detail);\n  }\n}\nfunction Messages_ng_container_1_div_1_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function Messages_ng_container_1_div_1_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const i_r5 = i0.ɵɵnextContext().index;\n      const ctx_r28 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r28.removeMessage(i_r5));\n    });\n    i0.ɵɵelement(1, \"TimesIcon\", 21);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵattribute(\"aria-label\", \"Close\")(\"data-pc-section\", \"closebutton\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"styleClass\", \"p-message-close-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"closeicon\");\n  }\n}\nconst _c0 = function (a0, a1) {\n  return {\n    showTransitionParams: a0,\n    hideTransitionParams: a1\n  };\n};\nconst _c1 = function (a1) {\n  return {\n    value: \"visible\",\n    params: a1\n  };\n};\nfunction Messages_ng_container_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 5);\n    i0.ɵɵtemplate(2, Messages_ng_container_1_div_1_span_2_Template, 1, 3, \"span\", 6);\n    i0.ɵɵtemplate(3, Messages_ng_container_1_div_1_span_3_Template, 6, 4, \"span\", 7);\n    i0.ɵɵtemplate(4, Messages_ng_container_1_div_1_ng_container_4_Template, 3, 2, \"ng-container\", 1);\n    i0.ɵɵtemplate(5, Messages_ng_container_1_div_1_ng_template_5_Template, 2, 2, \"ng-template\", null, 8, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(7, Messages_ng_container_1_div_1_button_7_Template, 2, 4, \"button\", 9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const msg_r4 = ctx.$implicit;\n    const _r9 = i0.ɵɵreference(6);\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(\"p-message p-message-\" + msg_r4.severity);\n    i0.ɵɵproperty(\"@messageAnimation\", i0.ɵɵpureFunction1(12, _c1, i0.ɵɵpureFunction2(9, _c0, ctx_r3.showTransitionOptions, ctx_r3.hideTransitionOptions)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵattribute(\"data-pc-section\", \"wrapper\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", msg_r4.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !msg_r4.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.escape)(\"ngIfElse\", _r9);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.closable);\n  }\n}\nfunction Messages_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Messages_ng_container_1_div_1_Template, 8, 14, \"div\", 3);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.messages);\n  }\n}\nfunction Messages_ng_template_2_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Messages_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 5);\n    i0.ɵɵtemplate(2, Messages_ng_template_2_ng_container_2_Template, 1, 0, \"ng-container\", 23);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", \"p-message p-message-\" + ctx_r2.severity);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.contentTemplate);\n  }\n}\nlet Messages = /*#__PURE__*/(() => {\n  class Messages {\n    messageService;\n    el;\n    cd;\n    /**\n     * An array of messages to display.\n     * @group Props\n     */\n    set value(messages) {\n      this.messages = messages;\n      this.startMessageLifes(this.messages);\n    }\n    /**\n     * Defines if message box can be closed by the click icon.\n     * @group Props\n     */\n    closable = true;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    style;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Whether displaying services messages are enabled.\n     * @group Props\n     */\n    enableService = true;\n    /**\n     * Id to match the key of the message to enable scoping in service based messaging.\n     * @group Props\n     */\n    key;\n    /**\n     * Whether displaying messages would be escaped or not.\n     * @group Props\n     */\n    escape = true;\n    /**\n     * Severity level of the message.\n     * @group Props\n     */\n    severity;\n    /**\n     * Transition options of the show animation.\n     * @group Props\n     */\n    showTransitionOptions = '300ms ease-out';\n    /**\n     * Transition options of the hide animation.\n     * @group Props\n     */\n    hideTransitionOptions = '200ms cubic-bezier(0.86, 0, 0.07, 1)';\n    /**\n     * This function is executed when the value changes.\n     * @param {Message[]} value - messages value.\n     * @group Emits\n     */\n    valueChange = new EventEmitter();\n    templates;\n    messages;\n    messageSubscription;\n    clearSubscription;\n    timerSubscriptions = [];\n    contentTemplate;\n    constructor(messageService, el, cd) {\n      this.messageService = messageService;\n      this.el = el;\n      this.cd = cd;\n    }\n    ngAfterContentInit() {\n      this.templates?.forEach(item => {\n        switch (item.getType()) {\n          case 'content':\n            this.contentTemplate = item.template;\n            break;\n          default:\n            this.contentTemplate = item.template;\n            break;\n        }\n      });\n      if (this.messageService && this.enableService && !this.contentTemplate) {\n        this.messageSubscription = this.messageService.messageObserver.subscribe(messages => {\n          if (messages) {\n            if (!Array.isArray(messages)) {\n              messages = [messages];\n            }\n            const filteredMessages = messages.filter(m => this.key === m.key);\n            this.messages = this.messages ? [...this.messages, ...filteredMessages] : [...filteredMessages];\n            this.startMessageLifes(filteredMessages);\n            this.cd.markForCheck();\n          }\n        });\n        this.clearSubscription = this.messageService.clearObserver.subscribe(key => {\n          if (key) {\n            if (this.key === key) {\n              this.messages = null;\n            }\n          } else {\n            this.messages = null;\n          }\n          this.cd.markForCheck();\n        });\n      }\n    }\n    hasMessages() {\n      let parentEl = this.el.nativeElement.parentElement;\n      if (parentEl && parentEl.offsetParent) {\n        return this.contentTemplate != null || this.messages && this.messages.length > 0;\n      }\n      return false;\n    }\n    clear() {\n      this.messages = [];\n      this.valueChange.emit(this.messages);\n    }\n    removeMessage(i) {\n      this.messages = this.messages?.filter((msg, index) => index !== i);\n      this.valueChange.emit(this.messages);\n    }\n    get icon() {\n      const severity = this.severity || (this.hasMessages() ? this.messages[0].severity : null);\n      if (this.hasMessages()) {\n        switch (severity) {\n          case 'success':\n            return 'pi-check';\n          case 'info':\n            return 'pi-info-circle';\n          case 'error':\n            return 'pi-times';\n          case 'warn':\n            return 'pi-exclamation-triangle';\n          default:\n            return 'pi-info-circle';\n        }\n      }\n      return null;\n    }\n    ngOnDestroy() {\n      if (this.messageSubscription) {\n        this.messageSubscription.unsubscribe();\n      }\n      if (this.clearSubscription) {\n        this.clearSubscription.unsubscribe();\n      }\n      this.timerSubscriptions?.forEach(subscription => subscription.unsubscribe());\n    }\n    startMessageLifes(messages) {\n      messages?.forEach(message => message.life && this.startMessageLife(message));\n    }\n    startMessageLife(message) {\n      const timerSubsctiption = timer(message.life).subscribe(() => {\n        this.messages = this.messages?.filter(msgEl => msgEl !== message);\n        this.timerSubscriptions = this.timerSubscriptions?.filter(timerEl => timerEl !== timerSubsctiption);\n        this.valueChange.emit(this.messages);\n        this.cd.markForCheck();\n      });\n      this.timerSubscriptions.push(timerSubsctiption);\n    }\n    static ɵfac = function Messages_Factory(t) {\n      return new (t || Messages)(i0.ɵɵdirectiveInject(i1.MessageService, 8), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: Messages,\n      selectors: [[\"p-messages\"]],\n      contentQueries: function Messages_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        value: \"value\",\n        closable: \"closable\",\n        style: \"style\",\n        styleClass: \"styleClass\",\n        enableService: \"enableService\",\n        key: \"key\",\n        escape: \"escape\",\n        severity: \"severity\",\n        showTransitionOptions: \"showTransitionOptions\",\n        hideTransitionOptions: \"hideTransitionOptions\"\n      },\n      outputs: {\n        valueChange: \"valueChange\"\n      },\n      decls: 4,\n      vars: 8,\n      consts: [[\"role\", \"alert\", 1, \"p-messages\", \"p-component\", 3, \"ngStyle\"], [4, \"ngIf\", \"ngIfElse\"], [\"staticMessage\", \"\"], [\"role\", \"alert\", 3, \"class\", 4, \"ngFor\", \"ngForOf\"], [\"role\", \"alert\"], [1, \"p-message-wrapper\"], [3, \"class\", 4, \"ngIf\"], [\"class\", \"p-message-icon\", 4, \"ngIf\"], [\"escapeOut\", \"\"], [\"class\", \"p-message-close p-link\", \"type\", \"button\", \"pRipple\", \"\", 3, \"click\", 4, \"ngIf\"], [1, \"p-message-icon\"], [4, \"ngIf\"], [\"class\", \"p-message-summary\", 3, \"innerHTML\", 4, \"ngIf\"], [\"class\", \"p-message-detail\", 3, \"innerHTML\", 4, \"ngIf\"], [1, \"p-message-summary\", 3, \"innerHTML\"], [1, \"p-message-detail\", 3, \"innerHTML\"], [\"class\", \"p-message-summary\", 4, \"ngIf\"], [\"class\", \"p-message-detail\", 4, \"ngIf\"], [1, \"p-message-summary\"], [1, \"p-message-detail\"], [\"type\", \"button\", \"pRipple\", \"\", 1, \"p-message-close\", \"p-link\", 3, \"click\"], [3, \"styleClass\"], [\"role\", \"alert\", 3, \"ngClass\"], [4, \"ngTemplateOutlet\"]],\n      template: function Messages_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, Messages_ng_container_1_Template, 2, 1, \"ng-container\", 1);\n          i0.ɵɵtemplate(2, Messages_ng_template_2_Template, 3, 2, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          const _r1 = i0.ɵɵreference(3);\n          i0.ɵɵclassMap(ctx.styleClass);\n          i0.ɵɵproperty(\"ngStyle\", ctx.style);\n          i0.ɵɵattribute(\"aria-atomic\", true)(\"aria-live\", \"assertive\")(\"data-pc-name\", \"message\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.contentTemplate)(\"ngIfElse\", _r1);\n        }\n      },\n      dependencies: function () {\n        return [i2.NgClass, i2.NgForOf, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i3.Ripple, CheckIcon, InfoCircleIcon, TimesCircleIcon, ExclamationTriangleIcon, TimesIcon];\n      },\n      styles: [\"@layer primeng{.p-message-wrapper{display:flex;align-items:center}.p-message-close{display:flex;align-items:center;justify-content:center;flex:none}.p-message-close.p-link{margin-left:auto;overflow:hidden;position:relative}.p-messages .p-message.ng-animating{overflow:hidden}}\\n\"],\n      encapsulation: 2,\n      data: {\n        animation: [trigger('messageAnimation', [transition(':enter', [style({\n          opacity: 0,\n          transform: 'translateY(-25%)'\n        }), animate('{{showTransitionParams}}')]), transition(':leave', [animate('{{hideTransitionParams}}', style({\n          height: 0,\n          marginTop: 0,\n          marginBottom: 0,\n          marginLeft: 0,\n          marginRight: 0,\n          opacity: 0\n        }))])])]\n      },\n      changeDetection: 0\n    });\n  }\n  return Messages;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MessagesModule = /*#__PURE__*/(() => {\n  class MessagesModule {\n    static ɵfac = function MessagesModule_Factory(t) {\n      return new (t || MessagesModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MessagesModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, RippleModule, CheckIcon, InfoCircleIcon, TimesCircleIcon, ExclamationTriangleIcon, TimesIcon]\n    });\n  }\n  return MessagesModule;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Messages, MessagesModule };\n//# sourceMappingURL=primeng-messages.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}