{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../Services/supabase.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"primeng/inputtext\";\nimport * as i5 from \"primeng/checkbox\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"primeng/inputtextarea\";\nimport * as i8 from \"primeng/tooltip\";\nimport * as i9 from \"./admob-settings/admob-settings.component\";\nfunction AdminComponent_div_0_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵelement(1, \"i\", 13);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.loginError, \" \");\n  }\n}\nfunction AdminComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n    i0.ɵɵelement(3, \"i\", 5);\n    i0.ɵɵelementStart(4, \"h2\");\n    i0.ɵɵtext(5, \"\\u0644\\u0648\\u062D\\u0629 \\u062A\\u062D\\u0643\\u0645 \\u0627\\u0644\\u0625\\u062F\\u0627\\u0631\\u0629\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"\\u064A\\u0631\\u062C\\u0649 \\u0625\\u062F\\u062E\\u0627\\u0644 \\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631 \\u0644\\u0644\\u0648\\u0635\\u0648\\u0644\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"form\", 6);\n    i0.ɵɵlistener(\"ngSubmit\", function AdminComponent_div_0_Template_form_ngSubmit_8_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onLogin());\n    });\n    i0.ɵɵelementStart(9, \"div\", 7)(10, \"label\", 8);\n    i0.ɵɵtext(11, \"\\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"input\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, AdminComponent_div_0_div_13_Template, 3, 1, \"div\", 10);\n    i0.ɵɵelement(14, \"p-button\", 11);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.loginForm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.loginError);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.loginForm.valid);\n  }\n}\nfunction AdminComponent_div_1_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 32)(2, \"div\", 33)(3, \"div\", 34);\n    i0.ɵɵelement(4, \"i\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 36)(6, \"h3\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9, \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\\u064A\\u0646\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 33)(11, \"div\", 34);\n    i0.ɵɵelement(12, \"i\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 36)(14, \"h3\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"p\");\n    i0.ɵɵtext(17, \"\\u0627\\u0644\\u062C\\u0644\\u0633\\u0627\\u062A \\u0627\\u0644\\u0646\\u0634\\u0637\\u0629\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 33)(19, \"div\", 34);\n    i0.ɵɵelement(20, \"i\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 36)(22, \"h3\");\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"p\");\n    i0.ɵɵtext(25, \"\\u0631\\u0648\\u0627\\u0628\\u0637 \\u0627\\u0644\\u0627\\u062E\\u062A\\u0635\\u0627\\u0631 \\u0627\\u0644\\u0646\\u0634\\u0637\\u0629\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"div\", 33)(27, \"div\", 34);\n    i0.ɵɵelement(28, \"i\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 36)(30, \"h3\");\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"p\");\n    i0.ɵɵtext(33, \"\\u0623\\u062D\\u062F\\u0627\\u062B \\u0627\\u0644\\u0623\\u0645\\u0627\\u0646\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r5.appStats.total_users || 0);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r5.appStats.active_sessions || 0);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r5.appStats.active_shortener_links || 0);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r5.appStats.total_security_events || 0);\n  }\n}\nfunction AdminComponent_div_1_div_30_p_button_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 59);\n    i0.ɵɵlistener(\"onClick\", function AdminComponent_div_1_div_30_p_button_27_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r14 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r14.onAddLink());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"loading\", ctx_r10.isLoading)(\"disabled\", !ctx_r10.linkForm.valid);\n  }\n}\nfunction AdminComponent_div_1_div_30_p_button_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 60);\n    i0.ɵɵlistener(\"onClick\", function AdminComponent_div_1_div_30_p_button_28_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r16.onUpdateLink());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"loading\", ctx_r11.isLoading)(\"disabled\", !ctx_r11.linkForm.valid);\n  }\n}\nfunction AdminComponent_div_1_div_30_p_button_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 61);\n    i0.ɵɵlistener(\"onClick\", function AdminComponent_div_1_div_30_p_button_29_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r18.cancelEditLink());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"outlined\", true);\n  }\n}\nfunction AdminComponent_div_1_div_30_tr_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\")(4, \"a\", 62);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"td\")(7, \"span\", 63);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\")(12, \"div\", 64)(13, \"p-button\", 65);\n    i0.ɵɵlistener(\"onClick\", function AdminComponent_div_1_div_30_tr_48_Template_p_button_onClick_13_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r22);\n      const link_r20 = restoredCtx.$implicit;\n      const ctx_r21 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r21.onEditLink(link_r20));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"p-button\", 66);\n    i0.ɵɵlistener(\"onClick\", function AdminComponent_div_1_div_30_tr_48_Template_p_button_onClick_14_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r22);\n      const link_r20 = restoredCtx.$implicit;\n      const ctx_r23 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r23.onDeleteLink(link_r20));\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const link_r20 = ctx.$implicit;\n    const ctx_r13 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(link_r20.site_name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"href\", link_r20.site_url, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", link_r20.site_url, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"active\", link_r20.is_active)(\"inactive\", !link_r20.is_active);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", link_r20.is_active ? \"\\u0646\\u0634\\u0637\" : \"\\u063A\\u064A\\u0631 \\u0646\\u0634\\u0637\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r13.formatDate(link_r20.created_at));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"text\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"text\", true);\n  }\n}\nfunction AdminComponent_div_1_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 38)(2, \"h3\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"form\", 39)(5, \"div\", 40)(6, \"div\", 7)(7, \"label\", 41);\n    i0.ɵɵtext(8, \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0648\\u0642\\u0639 *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(9, \"input\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 7)(11, \"label\", 43);\n    i0.ɵɵtext(12, \"\\u0631\\u0627\\u0628\\u0637 \\u0627\\u0644\\u0645\\u0648\\u0642\\u0639 *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"input\", 44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 7)(15, \"label\", 45);\n    i0.ɵɵtext(16, \"\\u0642\\u0627\\u0644\\u0628 \\u0627\\u0644\\u0631\\u0627\\u0628\\u0637 *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(17, \"input\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 7)(19, \"label\", 47);\n    i0.ɵɵtext(20, \"\\u0627\\u0644\\u0648\\u0635\\u0641\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"textarea\", 48);\n    i0.ɵɵtext(22, \"            \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 7)(24, \"div\", 49);\n    i0.ɵɵelement(25, \"p-checkbox\", 50);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 51);\n    i0.ɵɵtemplate(27, AdminComponent_div_1_div_30_p_button_27_Template, 1, 2, \"p-button\", 52);\n    i0.ɵɵtemplate(28, AdminComponent_div_1_div_30_p_button_28_Template, 1, 2, \"p-button\", 53);\n    i0.ɵɵtemplate(29, AdminComponent_div_1_div_30_p_button_29_Template, 1, 1, \"p-button\", 54);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(30, \"div\", 55)(31, \"h3\");\n    i0.ɵɵtext(32, \"\\u0631\\u0648\\u0627\\u0628\\u0637 \\u0627\\u0644\\u0627\\u062E\\u062A\\u0635\\u0627\\u0631 \\u0627\\u0644\\u062D\\u0627\\u0644\\u064A\\u0629\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"div\", 56)(34, \"table\", 57)(35, \"thead\")(36, \"tr\")(37, \"th\");\n    i0.ɵɵtext(38, \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0648\\u0642\\u0639\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"th\");\n    i0.ɵɵtext(40, \"\\u0631\\u0627\\u0628\\u0637 \\u0627\\u0644\\u0645\\u0648\\u0642\\u0639\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"th\");\n    i0.ɵɵtext(42, \"\\u0627\\u0644\\u062D\\u0627\\u0644\\u0629\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"th\");\n    i0.ɵɵtext(44, \"\\u062A\\u0627\\u0631\\u064A\\u062E \\u0627\\u0644\\u0625\\u0646\\u0634\\u0627\\u0621\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"th\");\n    i0.ɵɵtext(46, \"\\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(47, \"tbody\");\n    i0.ɵɵtemplate(48, AdminComponent_div_1_div_30_tr_48_Template, 15, 11, \"tr\", 58);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r6.editingLink ? \"\\u062A\\u0639\\u062F\\u064A\\u0644 \\u0631\\u0627\\u0628\\u0637 \\u0627\\u0644\\u0627\\u062E\\u062A\\u0635\\u0627\\u0631\" : \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0631\\u0627\\u0628\\u0637 \\u0627\\u062E\\u062A\\u0635\\u0627\\u0631 \\u062C\\u062F\\u064A\\u062F\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r6.linkForm);\n    i0.ɵɵadvance(21);\n    i0.ɵɵproperty(\"binary\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.editingLink);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.editingLink);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.editingLink);\n    i0.ɵɵadvance(19);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.shortenerLinks);\n  }\n}\nfunction AdminComponent_div_1_div_31_tr_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"div\", 67);\n    i0.ɵɵelement(3, \"i\");\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\")(9, \"pre\", 68);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const log_r25 = ctx.$implicit;\n    const ctx_r24 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(ctx_r24.getSecurityEventColor(log_r25.event_type));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(log_r25.event_type);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(log_r25.user_id);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r24.formatJson(log_r25.details));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r24.formatDate(log_r25.created_at));\n  }\n}\nfunction AdminComponent_div_1_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 55)(2, \"h3\");\n    i0.ɵɵtext(3, \"\\u0633\\u062C\\u0644\\u0627\\u062A \\u0627\\u0644\\u0623\\u0645\\u0627\\u0646 (\\u0622\\u062E\\u0631 50 \\u062D\\u062F\\u062B)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 56)(5, \"table\", 57)(6, \"thead\")(7, \"tr\")(8, \"th\");\n    i0.ɵɵtext(9, \"\\u0646\\u0648\\u0639 \\u0627\\u0644\\u062D\\u062F\\u062B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"\\u0645\\u0639\\u0631\\u0641 \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"\\u0627\\u0644\\u062A\\u0641\\u0627\\u0635\\u064A\\u0644\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\");\n    i0.ɵɵtext(15, \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"tbody\");\n    i0.ɵɵtemplate(17, AdminComponent_div_1_div_31_tr_17_Template, 13, 6, \"tr\", 58);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.securityLogs);\n  }\n}\nfunction AdminComponent_div_1_div_32_p_button_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 61);\n    i0.ɵɵlistener(\"onClick\", function AdminComponent_div_1_div_32_p_button_21_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r28 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r28.cancelEditSetting());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"outlined\", true);\n  }\n}\nfunction AdminComponent_div_1_div_32_tr_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"code\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\")(5, \"code\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\")(12, \"p-button\", 65);\n    i0.ɵɵlistener(\"onClick\", function AdminComponent_div_1_div_32_tr_40_Template_p_button_onClick_12_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r32);\n      const setting_r30 = restoredCtx.$implicit;\n      const ctx_r31 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r31.onEditSetting(setting_r30));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const setting_r30 = ctx.$implicit;\n    const ctx_r27 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(setting_r30.setting_key);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r27.formatJson(setting_r30.setting_value));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(setting_r30.description || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r27.formatDate(setting_r30.updated_at));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"text\", true);\n  }\n}\nfunction AdminComponent_div_1_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 38)(2, \"h3\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"form\", 69)(5, \"div\", 40)(6, \"div\", 7)(7, \"label\", 70);\n    i0.ɵɵtext(8, \"\\u0645\\u0641\\u062A\\u0627\\u062D \\u0627\\u0644\\u0625\\u0639\\u062F\\u0627\\u062F *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(9, \"input\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 7)(11, \"label\", 72);\n    i0.ɵɵtext(12, \"\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0625\\u0639\\u062F\\u0627\\u062F *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"input\", 73);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 7)(15, \"label\", 74);\n    i0.ɵɵtext(16, \"\\u0627\\u0644\\u0648\\u0635\\u0641\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"textarea\", 75);\n    i0.ɵɵtext(18, \"            \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 51)(20, \"p-button\", 76);\n    i0.ɵɵlistener(\"onClick\", function AdminComponent_div_1_div_32_Template_p_button_onClick_20_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r33 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r33.onAddSetting());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, AdminComponent_div_1_div_32_p_button_21_Template, 1, 1, \"p-button\", 54);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"div\", 55)(23, \"h3\");\n    i0.ɵɵtext(24, \"\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u062A\\u0637\\u0628\\u064A\\u0642 \\u0627\\u0644\\u062D\\u0627\\u0644\\u064A\\u0629\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 56)(26, \"table\", 57)(27, \"thead\")(28, \"tr\")(29, \"th\");\n    i0.ɵɵtext(30, \"\\u0645\\u0641\\u062A\\u0627\\u062D \\u0627\\u0644\\u0625\\u0639\\u062F\\u0627\\u062F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"th\");\n    i0.ɵɵtext(32, \"\\u0627\\u0644\\u0642\\u064A\\u0645\\u0629\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"th\");\n    i0.ɵɵtext(34, \"\\u0627\\u0644\\u0648\\u0635\\u0641\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"th\");\n    i0.ɵɵtext(36, \"\\u0622\\u062E\\u0631 \\u062A\\u062D\\u062F\\u064A\\u062B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"th\");\n    i0.ɵɵtext(38, \"\\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(39, \"tbody\");\n    i0.ɵɵtemplate(40, AdminComponent_div_1_div_32_tr_40_Template, 13, 5, \"tr\", 58);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r8.editingSetting ? \"\\u062A\\u0639\\u062F\\u064A\\u0644 \\u0625\\u0639\\u062F\\u0627\\u062F\" : \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0625\\u0639\\u062F\\u0627\\u062F \\u062C\\u062F\\u064A\\u062F\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r8.settingForm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"readonly\", !!ctx_r8.editingSetting);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"loading\", ctx_r8.isLoading)(\"disabled\", !ctx_r8.settingForm.valid);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.editingSetting);\n    i0.ɵɵadvance(19);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.appSettings);\n  }\n}\nfunction AdminComponent_div_1_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵelement(1, \"app-admob-settings\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AdminComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r36 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"div\", 16)(3, \"div\", 17)(4, \"h1\");\n    i0.ɵɵtext(5, \"\\u0644\\u0648\\u062D\\u0629 \\u062A\\u062D\\u0643\\u0645 \\u0627\\u0644\\u0625\\u062F\\u0627\\u0631\\u0629\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0631\\u0648\\u0627\\u0628\\u0637 \\u0627\\u0644\\u0627\\u062E\\u062A\\u0635\\u0627\\u0631 \\u0648\\u0645\\u0631\\u0627\\u0642\\u0628\\u0629 \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 18)(9, \"p-button\", 19);\n    i0.ɵɵlistener(\"onClick\", function AdminComponent_div_1_Template_p_button_onClick_9_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r35 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r35.refreshData());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p-button\", 20);\n    i0.ɵɵlistener(\"onClick\", function AdminComponent_div_1_Template_p_button_onClick_10_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r37 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r37.onLogout());\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(11, \"div\", 21)(12, \"div\", 22)(13, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function AdminComponent_div_1_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.setActiveTab(\"dashboard\"));\n    });\n    i0.ɵɵelement(14, \"i\", 24);\n    i0.ɵɵtext(15, \" \\u0644\\u0648\\u062D\\u0629 \\u0627\\u0644\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function AdminComponent_div_1_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r39 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r39.setActiveTab(\"links\"));\n    });\n    i0.ɵɵelement(17, \"i\", 25);\n    i0.ɵɵtext(18, \" \\u0631\\u0648\\u0627\\u0628\\u0637 \\u0627\\u0644\\u0627\\u062E\\u062A\\u0635\\u0627\\u0631 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function AdminComponent_div_1_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r40 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r40.setActiveTab(\"security\"));\n    });\n    i0.ɵɵelement(20, \"i\", 26);\n    i0.ɵɵtext(21, \" \\u0633\\u062C\\u0644\\u0627\\u062A \\u0627\\u0644\\u0623\\u0645\\u0627\\u0646 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function AdminComponent_div_1_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r41 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r41.setActiveTab(\"settings\"));\n    });\n    i0.ɵɵelement(23, \"i\", 27);\n    i0.ɵɵtext(24, \" \\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u062A\\u0637\\u0628\\u064A\\u0642 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function AdminComponent_div_1_Template_button_click_25_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r42 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r42.setActiveTab(\"admob\"));\n    });\n    i0.ɵɵelement(26, \"i\", 28);\n    i0.ɵɵtext(27, \" \\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A AdMob \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"div\", 29);\n    i0.ɵɵtemplate(29, AdminComponent_div_1_div_29_Template, 34, 4, \"div\", 30);\n    i0.ɵɵtemplate(30, AdminComponent_div_1_div_30_Template, 49, 7, \"div\", 30);\n    i0.ɵɵtemplate(31, AdminComponent_div_1_div_31_Template, 18, 1, \"div\", 30);\n    i0.ɵɵtemplate(32, AdminComponent_div_1_div_32_Template, 41, 7, \"div\", 30);\n    i0.ɵɵtemplate(33, AdminComponent_div_1_div_33_Template, 2, 0, \"div\", 30);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"outlined\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"outlined\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"active\", ctx_r1.activeTab === \"dashboard\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"active\", ctx_r1.activeTab === \"links\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"active\", ctx_r1.activeTab === \"security\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"active\", ctx_r1.activeTab === \"settings\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"active\", ctx_r1.activeTab === \"admob\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.activeTab === \"dashboard\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.activeTab === \"links\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.activeTab === \"security\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.activeTab === \"settings\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.activeTab === \"admob\");\n  }\n}\nexport let AdminComponent = /*#__PURE__*/(() => {\n  class AdminComponent {\n    constructor(fb, supabaseService) {\n      this.fb = fb;\n      this.supabaseService = supabaseService;\n      // Authentication\n      this.isAuthenticated = false;\n      this.loginError = '';\n      // Data\n      this.shortenerLinks = [];\n      this.securityLogs = [];\n      this.appSettings = [];\n      this.appStats = {};\n      // UI State\n      this.activeTab = 'dashboard';\n      this.isLoading = false;\n      this.editingLink = null;\n      this.editingSetting = null;\n      // Admin credentials (in production, this should be more secure)\n      this.ADMIN_PASSWORD = 'QuranAdmin2025!';\n    }\n    ngOnInit() {\n      this.initializeForms();\n      this.checkAuthStatus();\n    }\n    initializeForms() {\n      this.loginForm = this.fb.group({\n        password: ['', [Validators.required]]\n      });\n      this.linkForm = this.fb.group({\n        site_name: ['', [Validators.required]],\n        site_url: ['', [Validators.required, Validators.pattern('https?://.+')]],\n        link_template: ['', [Validators.required]],\n        description: [''],\n        is_active: [true]\n      });\n      this.settingForm = this.fb.group({\n        setting_key: ['', [Validators.required]],\n        setting_value: ['', [Validators.required]],\n        description: ['']\n      });\n    }\n    checkAuthStatus() {\n      const authStatus = localStorage.getItem('admin_authenticated');\n      if (authStatus === 'true') {\n        this.isAuthenticated = true;\n        this.loadDashboardData();\n      }\n    }\n    // Authentication\n    onLogin() {\n      if (this.loginForm.valid) {\n        const password = this.loginForm.get('password')?.value;\n        if (password === this.ADMIN_PASSWORD) {\n          this.isAuthenticated = true;\n          localStorage.setItem('admin_authenticated', 'true');\n          this.loginError = '';\n          this.loadDashboardData();\n        } else {\n          this.loginError = 'كلمة المرور غير صحيحة';\n        }\n      }\n    }\n    onLogout() {\n      this.isAuthenticated = false;\n      localStorage.removeItem('admin_authenticated');\n      this.loginForm.reset();\n    }\n    // Data Loading\n    loadDashboardData() {\n      this.loadShortenerLinks();\n      this.loadSecurityLogs();\n      this.loadAppSettings();\n      this.loadAppStats();\n    }\n    loadShortenerLinks() {\n      this.supabaseService.getShortenerLinks().subscribe({\n        next: links => {\n          this.shortenerLinks = links;\n        },\n        error: error => {\n          console.error('Error loading shortener links:', error);\n        }\n      });\n    }\n    loadSecurityLogs() {\n      this.supabaseService.getSecurityLogs(undefined, 50).subscribe({\n        next: logs => {\n          this.securityLogs = logs;\n        },\n        error: error => {\n          console.error('Error loading security logs:', error);\n        }\n      });\n    }\n    loadAppSettings() {\n      this.supabaseService.getAllAppSettings().subscribe({\n        next: settings => {\n          this.appSettings = settings;\n        },\n        error: error => {\n          console.error('Error loading app settings:', error);\n        }\n      });\n    }\n    loadAppStats() {\n      this.supabaseService.getAppStats().subscribe({\n        next: stats => {\n          this.appStats = stats;\n        },\n        error: error => {\n          console.error('Error loading app stats:', error);\n        }\n      });\n    }\n    // Shortener Links Management\n    onAddLink() {\n      if (this.linkForm.valid) {\n        this.isLoading = true;\n        const linkData = this.linkForm.value;\n        this.supabaseService.addShortenerLink(linkData).subscribe({\n          next: newLink => {\n            this.shortenerLinks.push(newLink);\n            this.linkForm.reset();\n            this.linkForm.patchValue({\n              is_active: true\n            });\n            this.isLoading = false;\n          },\n          error: error => {\n            console.error('Error adding link:', error);\n            this.isLoading = false;\n          }\n        });\n      }\n    }\n    onEditLink(link) {\n      this.editingLink = link;\n      this.linkForm.patchValue(link);\n    }\n    onUpdateLink() {\n      if (this.linkForm.valid && this.editingLink) {\n        this.isLoading = true;\n        const updates = this.linkForm.value;\n        this.supabaseService.updateShortenerLink(this.editingLink.id, updates).subscribe({\n          next: updatedLink => {\n            const index = this.shortenerLinks.findIndex(l => l.id === updatedLink.id);\n            if (index !== -1) {\n              this.shortenerLinks[index] = updatedLink;\n            }\n            this.cancelEditLink();\n            this.isLoading = false;\n          },\n          error: error => {\n            console.error('Error updating link:', error);\n            this.isLoading = false;\n          }\n        });\n      }\n    }\n    onDeleteLink(link) {\n      if (confirm(`هل أنت متأكد من حذف الرابط: ${link.site_name}؟`)) {\n        this.supabaseService.deleteShortenerLink(link.id).subscribe({\n          next: () => {\n            this.shortenerLinks = this.shortenerLinks.filter(l => l.id !== link.id);\n          },\n          error: error => {\n            console.error('Error deleting link:', error);\n          }\n        });\n      }\n    }\n    cancelEditLink() {\n      this.editingLink = null;\n      this.linkForm.reset();\n      this.linkForm.patchValue({\n        is_active: true\n      });\n    }\n    // App Settings Management\n    onAddSetting() {\n      if (this.settingForm.valid) {\n        this.isLoading = true;\n        const {\n          setting_key,\n          setting_value,\n          description\n        } = this.settingForm.value;\n        this.supabaseService.setAppSetting(setting_key, setting_value, description).subscribe({\n          next: newSetting => {\n            const existingIndex = this.appSettings.findIndex(s => s.setting_key === setting_key);\n            if (existingIndex !== -1) {\n              this.appSettings[existingIndex] = newSetting;\n            } else {\n              this.appSettings.push(newSetting);\n            }\n            this.settingForm.reset();\n            this.isLoading = false;\n          },\n          error: error => {\n            console.error('Error adding/updating setting:', error);\n            this.isLoading = false;\n          }\n        });\n      }\n    }\n    onEditSetting(setting) {\n      this.editingSetting = setting;\n      this.settingForm.patchValue({\n        setting_key: setting.setting_key,\n        setting_value: JSON.stringify(setting.setting_value),\n        description: setting.description\n      });\n    }\n    cancelEditSetting() {\n      this.editingSetting = null;\n      this.settingForm.reset();\n    }\n    // Utility Methods\n    setActiveTab(tab) {\n      this.activeTab = tab;\n    }\n    formatDate(dateString) {\n      return new Date(dateString).toLocaleString('ar');\n    }\n    formatJson(value) {\n      return JSON.stringify(value, null, 2);\n    }\n    getSecurityEventIcon(eventType) {\n      switch (eventType) {\n        case 'vpn_detected':\n          return 'pi-shield';\n        case 'adblocker_detected':\n          return 'pi-ban';\n        case 'dns_blocked':\n          return 'pi-globe';\n        case 'access_denied':\n          return 'pi-times-circle';\n        default:\n          return 'pi-exclamation-triangle';\n      }\n    }\n    getSecurityEventColor(eventType) {\n      switch (eventType) {\n        case 'vpn_detected':\n          return 'text-warning';\n        case 'adblocker_detected':\n          return 'text-danger';\n        case 'dns_blocked':\n          return 'text-info';\n        case 'access_denied':\n          return 'text-danger';\n        default:\n          return 'text-secondary';\n      }\n    }\n    refreshData() {\n      this.loadDashboardData();\n    }\n    static {\n      this.ɵfac = function AdminComponent_Factory(t) {\n        return new (t || AdminComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.SupabaseService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AdminComponent,\n        selectors: [[\"app-admin\"]],\n        decls: 2,\n        vars: 2,\n        consts: [[\"class\", \"login-container\", 4, \"ngIf\"], [\"class\", \"admin-container\", 4, \"ngIf\"], [1, \"login-container\"], [1, \"login-card\"], [1, \"login-header\"], [1, \"pi\", \"pi-lock\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"form-group\"], [\"for\", \"password\"], [\"id\", \"password\", \"type\", \"password\", \"formControlName\", \"password\", \"pInputText\", \"\", \"placeholder\", \"\\u0623\\u062F\\u062E\\u0644 \\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631\", 1, \"w-100\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"label\", \"\\u062F\\u062E\\u0648\\u0644\", \"icon\", \"pi pi-sign-in\", \"type\", \"submit\", \"styleClass\", \"w-100\", 3, \"disabled\"], [1, \"error-message\"], [1, \"pi\", \"pi-exclamation-triangle\"], [1, \"admin-container\"], [1, \"admin-header\"], [1, \"header-content\"], [1, \"header-left\"], [1, \"header-right\"], [\"label\", \"\\u062A\\u062D\\u062F\\u064A\\u062B \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A\", \"icon\", \"pi pi-refresh\", \"severity\", \"secondary\", 3, \"outlined\", \"onClick\"], [\"label\", \"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062E\\u0631\\u0648\\u062C\", \"icon\", \"pi pi-sign-out\", \"severity\", \"danger\", 3, \"outlined\", \"onClick\"], [1, \"admin-nav\"], [1, \"nav-tabs\"], [1, \"nav-tab\", 3, \"click\"], [1, \"pi\", \"pi-chart-line\"], [1, \"pi\", \"pi-link\"], [1, \"pi\", \"pi-shield\"], [1, \"pi\", \"pi-cog\"], [1, \"pi\", \"pi-dollar\"], [1, \"admin-content\"], [\"class\", \"tab-content\", 4, \"ngIf\"], [1, \"tab-content\"], [1, \"stats-grid\"], [1, \"stat-card\"], [1, \"stat-icon\"], [1, \"pi\", \"pi-users\"], [1, \"stat-info\"], [1, \"pi\", \"pi-clock\"], [1, \"form-section\"], [1, \"link-form\", 3, \"formGroup\"], [1, \"form-row\"], [\"for\", \"site_name\"], [\"id\", \"site_name\", \"formControlName\", \"site_name\", \"pInputText\", \"\", \"placeholder\", \"\\u0645\\u062B\\u0627\\u0644: Cuty.io\"], [\"for\", \"site_url\"], [\"id\", \"site_url\", \"formControlName\", \"site_url\", \"pInputText\", \"\", \"placeholder\", \"https://example.com\"], [\"for\", \"link_template\"], [\"id\", \"link_template\", \"formControlName\", \"link_template\", \"pInputText\", \"\", \"placeholder\", \"https://example.com/shorten?ref=quranvidgen&t={timestamp}&id={id}\"], [\"for\", \"description\"], [\"id\", \"description\", \"formControlName\", \"description\", \"pInputTextarea\", \"\", \"rows\", \"3\", \"placeholder\", \"\\u0648\\u0635\\u0641 \\u0627\\u0644\\u0645\\u0648\\u0642\\u0639...\"], [1, \"checkbox-container\"], [\"formControlName\", \"is_active\", \"label\", \"\\u0646\\u0634\\u0637\", 3, \"binary\"], [1, \"form-actions\"], [\"label\", \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0627\\u0644\\u0631\\u0627\\u0628\\u0637\", \"icon\", \"pi pi-plus\", 3, \"loading\", \"disabled\", \"onClick\", 4, \"ngIf\"], [\"label\", \"\\u062A\\u062D\\u062F\\u064A\\u062B \\u0627\\u0644\\u0631\\u0627\\u0628\\u0637\", \"icon\", \"pi pi-check\", 3, \"loading\", \"disabled\", \"onClick\", 4, \"ngIf\"], [\"label\", \"\\u0625\\u0644\\u063A\\u0627\\u0621\", \"icon\", \"pi pi-times\", \"severity\", \"secondary\", 3, \"outlined\", \"onClick\", 4, \"ngIf\"], [1, \"table-section\"], [1, \"table-container\"], [1, \"data-table\"], [4, \"ngFor\", \"ngForOf\"], [\"label\", \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0627\\u0644\\u0631\\u0627\\u0628\\u0637\", \"icon\", \"pi pi-plus\", 3, \"loading\", \"disabled\", \"onClick\"], [\"label\", \"\\u062A\\u062D\\u062F\\u064A\\u062B \\u0627\\u0644\\u0631\\u0627\\u0628\\u0637\", \"icon\", \"pi pi-check\", 3, \"loading\", \"disabled\", \"onClick\"], [\"label\", \"\\u0625\\u0644\\u063A\\u0627\\u0621\", \"icon\", \"pi pi-times\", \"severity\", \"secondary\", 3, \"outlined\", \"onClick\"], [\"target\", \"_blank\", 1, \"link\", 3, \"href\"], [1, \"status-badge\"], [1, \"action-buttons\"], [\"icon\", \"pi pi-pencil\", \"severity\", \"info\", \"size\", \"small\", \"pTooltip\", \"\\u062A\\u0639\\u062F\\u064A\\u0644\", 3, \"text\", \"onClick\"], [\"icon\", \"pi pi-trash\", \"severity\", \"danger\", \"size\", \"small\", \"pTooltip\", \"\\u062D\\u0630\\u0641\", 3, \"text\", \"onClick\"], [1, \"event-type\"], [1, \"details-text\"], [1, \"setting-form\", 3, \"formGroup\"], [\"for\", \"setting_key\"], [\"id\", \"setting_key\", \"formControlName\", \"setting_key\", \"pInputText\", \"\", \"placeholder\", \"\\u0645\\u062B\\u0627\\u0644: max_daily_attempts\", 3, \"readonly\"], [\"for\", \"setting_value\"], [\"id\", \"setting_value\", \"formControlName\", \"setting_value\", \"pInputText\", \"\", \"placeholder\", \"\\u0645\\u062B\\u0627\\u0644: 5\"], [\"for\", \"setting_description\"], [\"id\", \"setting_description\", \"formControlName\", \"description\", \"pInputTextarea\", \"\", \"rows\", \"2\", \"placeholder\", \"\\u0648\\u0635\\u0641 \\u0627\\u0644\\u0625\\u0639\\u062F\\u0627\\u062F...\"], [\"label\", \"\\u062D\\u0641\\u0638 \\u0627\\u0644\\u0625\\u0639\\u062F\\u0627\\u062F\", \"icon\", \"pi pi-save\", 3, \"loading\", \"disabled\", \"onClick\"]],\n        template: function AdminComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, AdminComponent_div_0_Template, 15, 3, \"div\", 0);\n            i0.ɵɵtemplate(1, AdminComponent_div_1_Template, 34, 17, \"div\", 1);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", !ctx.isAuthenticated);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isAuthenticated);\n          }\n        },\n        dependencies: [i3.NgForOf, i3.NgIf, i4.InputText, i5.Checkbox, i6.Button, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i7.InputTextarea, i8.Tooltip, i9.AdMobSettingsComponent],\n        styles: [\".login-container[_ngcontent-%COMP%]{min-height:100vh;display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);padding:1rem}.login-card[_ngcontent-%COMP%]{background:white;border-radius:16px;padding:3rem;box-shadow:0 20px 40px #0000001a;max-width:400px;width:100%;text-align:center}.login-header[_ngcontent-%COMP%]{margin-bottom:2rem}.login-header[_ngcontent-%COMP%]   .pi[_ngcontent-%COMP%]{font-size:3rem;color:#667eea;margin-bottom:1rem}.login-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:#2c3e50;margin-bottom:.5rem}.login-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#6c757d;margin:0}.form-group[_ngcontent-%COMP%]{margin-bottom:1.5rem;text-align:left}.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:block;margin-bottom:.5rem;font-weight:600;color:#495057}.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{width:100%;padding:.75rem;border:2px solid #e9ecef;border-radius:8px;font-size:1rem;transition:border-color .3s ease}.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus, .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]:focus{border-color:#667eea;outline:none;box-shadow:0 0 0 3px #667eea1a}.error-message[_ngcontent-%COMP%]{background:#f8d7da;color:#721c24;padding:.75rem;border-radius:6px;margin-bottom:1rem;display:flex;align-items:center;gap:.5rem}.error-message[_ngcontent-%COMP%]   .pi[_ngcontent-%COMP%]{font-size:1.1rem}.admin-container[_ngcontent-%COMP%]{min-height:100vh;background:#f8f9fa}.admin-header[_ngcontent-%COMP%]{background:white;border-bottom:1px solid #e9ecef;padding:1.5rem 2rem}.admin-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;max-width:1400px;margin:0 auto}.admin-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{color:#2c3e50;margin-bottom:.5rem;font-size:1.8rem}.admin-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#6c757d;margin:0}.admin-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]{display:flex;gap:1rem}.admin-nav[_ngcontent-%COMP%]{background:white;border-bottom:1px solid #e9ecef;padding:0 2rem}.admin-nav[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]{display:flex;max-width:1400px;margin:0 auto}.admin-nav[_ngcontent-%COMP%]   .nav-tab[_ngcontent-%COMP%]{background:none;border:none;padding:1rem 1.5rem;cursor:pointer;display:flex;align-items:center;gap:.5rem;color:#6c757d;font-weight:500;border-bottom:3px solid transparent;transition:all .3s ease}.admin-nav[_ngcontent-%COMP%]   .nav-tab[_ngcontent-%COMP%]:hover{color:#495057;background:#f8f9fa}.admin-nav[_ngcontent-%COMP%]   .nav-tab.active[_ngcontent-%COMP%]{color:#667eea;border-bottom-color:#667eea;background:#f8f9fa}.admin-nav[_ngcontent-%COMP%]   .nav-tab[_ngcontent-%COMP%]   .pi[_ngcontent-%COMP%]{font-size:1.1rem}.admin-content[_ngcontent-%COMP%]{max-width:1400px;margin:0 auto;padding:2rem}.tab-content[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeIn .3s ease-in}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}.stats-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:1.5rem;margin-bottom:2rem}.stat-card[_ngcontent-%COMP%]{background:white;border-radius:12px;padding:2rem;box-shadow:0 2px 8px #0000001a;display:flex;align-items:center;gap:1.5rem}.stat-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);color:#fff;width:60px;height:60px;border-radius:12px;display:flex;align-items:center;justify-content:center}.stat-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]   .pi[_ngcontent-%COMP%]{font-size:1.5rem}.stat-card[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#2c3e50;font-size:2rem;margin-bottom:.5rem;font-weight:700}.stat-card[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#6c757d;margin:0;font-weight:500}.form-section[_ngcontent-%COMP%]{background:white;border-radius:12px;padding:2rem;margin-bottom:2rem;box-shadow:0 2px 8px #0000001a}.form-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#2c3e50;margin-bottom:1.5rem;font-size:1.3rem}.form-row[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 1fr;gap:1.5rem;margin-bottom:1.5rem}.checkbox-container[_ngcontent-%COMP%]{display:flex;align-items:center;padding-top:1.5rem}.form-actions[_ngcontent-%COMP%]{display:flex;gap:1rem;margin-top:2rem}.form-actions[_ngcontent-%COMP%]     .p-button{padding:.75rem 1.5rem;font-weight:600}.table-section[_ngcontent-%COMP%]{background:white;border-radius:12px;padding:2rem;box-shadow:0 2px 8px #0000001a}.table-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#2c3e50;margin-bottom:1.5rem;font-size:1.3rem}.table-container[_ngcontent-%COMP%]{overflow-x:auto}.data-table[_ngcontent-%COMP%]{width:100%;border-collapse:collapse}.data-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .data-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{padding:1rem;text-align:right;border-bottom:1px solid #e9ecef}.data-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{background:#f8f9fa;font-weight:600;color:#495057;position:sticky;top:0}.data-table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover{background:#f8f9fa}.data-table[_ngcontent-%COMP%]   .link[_ngcontent-%COMP%]{color:#667eea;text-decoration:none}.data-table[_ngcontent-%COMP%]   .link[_ngcontent-%COMP%]:hover{text-decoration:underline}.data-table[_ngcontent-%COMP%]   code[_ngcontent-%COMP%]{background:#f8f9fa;padding:.25rem .5rem;border-radius:4px;font-family:Courier New,monospace;font-size:.9rem}.status-badge[_ngcontent-%COMP%]{padding:.25rem .75rem;border-radius:20px;font-size:.85rem;font-weight:600}.status-badge.active[_ngcontent-%COMP%]{background:#d4edda;color:#155724}.status-badge.inactive[_ngcontent-%COMP%]{background:#f8d7da;color:#721c24}.action-buttons[_ngcontent-%COMP%]{display:flex;gap:.5rem;justify-content:center}.event-type[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}.event-type[_ngcontent-%COMP%]   .pi[_ngcontent-%COMP%]{font-size:1.1rem}.details-text[_ngcontent-%COMP%]{background:#f8f9fa;padding:.5rem;border-radius:4px;font-size:.8rem;max-width:300px;overflow-x:auto;white-space:pre-wrap;margin:0}.text-warning[_ngcontent-%COMP%]{color:#ffc107!important}.text-danger[_ngcontent-%COMP%]{color:#dc3545!important}.text-info[_ngcontent-%COMP%]{color:#17a2b8!important}.text-secondary[_ngcontent-%COMP%]{color:#6c757d!important}@media (max-width: 768px){.admin-header[_ngcontent-%COMP%]{padding:1rem}.admin-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{flex-direction:column;gap:1rem;text-align:center}.admin-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]{flex-direction:column;width:100%}.admin-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]     .p-button{width:100%}.admin-nav[_ngcontent-%COMP%]{padding:0 1rem}.admin-nav[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]{flex-wrap:wrap}.admin-nav[_ngcontent-%COMP%]   .nav-tab[_ngcontent-%COMP%]{flex:1;min-width:120px;justify-content:center}.admin-content[_ngcontent-%COMP%]{padding:1rem}.form-row[_ngcontent-%COMP%], .stats-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}.form-actions[_ngcontent-%COMP%]{flex-direction:column}.form-actions[_ngcontent-%COMP%]     .p-button{width:100%}.data-table[_ngcontent-%COMP%]{font-size:.9rem}.data-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .data-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{padding:.75rem .5rem}.login-card[_ngcontent-%COMP%]{padding:2rem 1.5rem}}\"]\n      });\n    }\n  }\n  return AdminComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}