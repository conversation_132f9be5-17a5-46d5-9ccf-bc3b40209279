{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, Directive, Optional, Input, Output, HostListener, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport * as i1 from '@angular/forms';\n\n/**\n * InputTextarea adds styling and autoResize functionality to standard textarea element.\n * @group Components\n */\nlet InputTextarea = /*#__PURE__*/(() => {\n  class InputTextarea {\n    el;\n    ngModel;\n    control;\n    cd;\n    /**\n     * When present, textarea size changes as being typed.\n     * @group Props\n     */\n    autoResize;\n    /**\n     * Callback to invoke on textarea resize.\n     * @param {(Event | {})} event - Custom resize event.\n     * @group Emits\n     */\n    onResize = new EventEmitter();\n    filled;\n    cachedScrollHeight;\n    ngModelSubscription;\n    ngControlSubscription;\n    constructor(el, ngModel, control, cd) {\n      this.el = el;\n      this.ngModel = ngModel;\n      this.control = control;\n      this.cd = cd;\n    }\n    ngOnInit() {\n      if (this.ngModel) {\n        this.ngModelSubscription = this.ngModel.valueChanges.subscribe(() => {\n          this.updateState();\n        });\n      }\n      if (this.control) {\n        this.ngControlSubscription = this.control.valueChanges.subscribe(() => {\n          this.updateState();\n        });\n      }\n    }\n    ngAfterViewChecked() {\n      this.updateState();\n    }\n    ngAfterViewInit() {\n      if (this.autoResize) this.resize();\n      this.updateFilledState();\n      this.cd.detectChanges();\n    }\n    onInput(e) {\n      this.updateState();\n    }\n    updateFilledState() {\n      this.filled = this.el.nativeElement.value && this.el.nativeElement.value.length;\n    }\n    resize(event) {\n      this.el.nativeElement.style.height = 'auto';\n      this.el.nativeElement.style.height = this.el.nativeElement.scrollHeight + 'px';\n      if (parseFloat(this.el.nativeElement.style.height) >= parseFloat(this.el.nativeElement.style.maxHeight)) {\n        this.el.nativeElement.style.overflowY = 'scroll';\n        this.el.nativeElement.style.height = this.el.nativeElement.style.maxHeight;\n      } else {\n        this.el.nativeElement.style.overflow = 'hidden';\n      }\n      this.onResize.emit(event || {});\n    }\n    updateState() {\n      this.updateFilledState();\n      if (this.autoResize) {\n        this.resize();\n      }\n    }\n    ngOnDestroy() {\n      if (this.ngModelSubscription) {\n        this.ngModelSubscription.unsubscribe();\n      }\n      if (this.ngControlSubscription) {\n        this.ngControlSubscription.unsubscribe();\n      }\n    }\n    static ɵfac = function InputTextarea_Factory(t) {\n      return new (t || InputTextarea)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.NgModel, 8), i0.ɵɵdirectiveInject(i1.NgControl, 8), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: InputTextarea,\n      selectors: [[\"\", \"pInputTextarea\", \"\"]],\n      hostAttrs: [1, \"p-inputtextarea\", \"p-inputtext\", \"p-component\", \"p-element\"],\n      hostVars: 4,\n      hostBindings: function InputTextarea_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"input\", function InputTextarea_input_HostBindingHandler($event) {\n            return ctx.onInput($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"p-filled\", ctx.filled)(\"p-inputtextarea-resizable\", ctx.autoResize);\n        }\n      },\n      inputs: {\n        autoResize: \"autoResize\"\n      },\n      outputs: {\n        onResize: \"onResize\"\n      }\n    });\n  }\n  return InputTextarea;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet InputTextareaModule = /*#__PURE__*/(() => {\n  class InputTextareaModule {\n    static ɵfac = function InputTextareaModule_Factory(t) {\n      return new (t || InputTextareaModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: InputTextareaModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n  return InputTextareaModule;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { InputTextarea, InputTextareaModule };\n//# sourceMappingURL=primeng-inputtextarea.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}