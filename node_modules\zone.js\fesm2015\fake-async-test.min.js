"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2022 Google LLC. https://angular.io/
 * License: MIT
 */!function(e){const t=e.Date;function s(){if(0===arguments.length){const e=new t;return e.setTime(s.now()),e}{const e=Array.prototype.slice.call(arguments);return new t(...e)}}s.now=function(){const e=Zone.current.get("FakeAsyncTestZoneSpec");return e?e.getFakeSystemTime():t.now.apply(this,arguments)},s.UTC=t.UTC,s.parse=t.parse;const r={setTimeout:e.setTimeout,setInterval:e.setInterval,clearTimeout:e.clearTimeout,clearInterval:e.clearInterval};class i{static{this.nextId=1}constructor(){this._schedulerQueue=[],this._currentTickTime=0,this._currentFakeBaseSystemTime=t.now(),this._currentTickRequeuePeriodicEntries=[]}getCurrentTickTime(){return this._currentTickTime}getFakeSystemTime(){return this._currentFakeBaseSystemTime+this._currentTickTime}setFakeBaseSystemTime(e){this._currentFakeBaseSystemTime=e}getRealSystemTime(){return t.now()}scheduleFunction(e,t,s){let r=(s={args:[],isPeriodic:!1,isRequestAnimationFrame:!1,id:-1,isRequeuePeriodic:!1,...s}).id<0?i.nextId++:s.id,n={endTime:this._currentTickTime+t,id:r,func:e,args:s.args,delay:t,isPeriodic:s.isPeriodic,isRequestAnimationFrame:s.isRequestAnimationFrame};s.isRequeuePeriodic&&this._currentTickRequeuePeriodicEntries.push(n);let c=0;for(;c<this._schedulerQueue.length&&!(n.endTime<this._schedulerQueue[c].endTime);c++);return this._schedulerQueue.splice(c,0,n),r}removeScheduledFunctionWithId(e){for(let t=0;t<this._schedulerQueue.length;t++)if(this._schedulerQueue[t].id==e){this._schedulerQueue.splice(t,1);break}}removeAll(){this._schedulerQueue=[]}getTimerCount(){return this._schedulerQueue.length}tickToNext(e=1,t,s){this._schedulerQueue.length<e||this.tick(this._schedulerQueue[e-1].endTime-this._currentTickTime,t,s)}tick(t=0,s,r){let i=this._currentTickTime+t,n=0;const c=(r=Object.assign({processNewMacroTasksSynchronously:!0},r)).processNewMacroTasksSynchronously?this._schedulerQueue:this._schedulerQueue.slice();if(0===c.length&&s)s(t);else{for(;c.length>0&&(this._currentTickRequeuePeriodicEntries=[],!(i<c[0].endTime));){let t=c.shift();if(!r.processNewMacroTasksSynchronously){const e=this._schedulerQueue.indexOf(t);e>=0&&this._schedulerQueue.splice(e,1)}if(n=this._currentTickTime,this._currentTickTime=t.endTime,s&&s(this._currentTickTime-n),!t.func.apply(e,t.isRequestAnimationFrame?[this._currentTickTime]:t.args))break;r.processNewMacroTasksSynchronously||this._currentTickRequeuePeriodicEntries.forEach((e=>{let t=0;for(;t<c.length&&!(e.endTime<c[t].endTime);t++);c.splice(t,0,e)}))}n=this._currentTickTime,this._currentTickTime=i,s&&s(this._currentTickTime-n)}}flushOnlyPendingTimers(e){if(0===this._schedulerQueue.length)return 0;const t=this._currentTickTime;return this.tick(this._schedulerQueue[this._schedulerQueue.length-1].endTime-t,e,{processNewMacroTasksSynchronously:!1}),this._currentTickTime-t}flush(e=20,t=!1,s){return t?this.flushPeriodic(s):this.flushNonPeriodic(e,s)}flushPeriodic(e){if(0===this._schedulerQueue.length)return 0;const t=this._currentTickTime;return this.tick(this._schedulerQueue[this._schedulerQueue.length-1].endTime-t,e),this._currentTickTime-t}flushNonPeriodic(t,s){const r=this._currentTickTime;let i=0,n=0;for(;this._schedulerQueue.length>0;){if(n++,n>t)throw new Error("flush failed after reaching the limit of "+t+" tasks. Does your code use a polling timeout?");if(0===this._schedulerQueue.filter((e=>!e.isPeriodic&&!e.isRequestAnimationFrame)).length)break;const r=this._schedulerQueue.shift();if(i=this._currentTickTime,this._currentTickTime=r.endTime,s&&s(this._currentTickTime-i),!r.func.apply(e,r.args))break}return this._currentTickTime-r}}class n{static assertInZone(){if(null==Zone.current.get("FakeAsyncTestZoneSpec"))throw new Error("The code should be running in the fakeAsync zone to call this function")}constructor(t,s=!1,r){this.trackPendingRequestAnimationFrame=s,this.macroTaskOptions=r,this._scheduler=new i,this._microtasks=[],this._lastError=null,this._uncaughtPromiseErrors=Promise[Zone.__symbol__("uncaughtPromiseErrors")],this.pendingPeriodicTimers=[],this.pendingTimers=[],this.patchDateLocked=!1,this.properties={FakeAsyncTestZoneSpec:this},this.name="fakeAsyncTestZone for "+t,this.macroTaskOptions||(this.macroTaskOptions=e[Zone.__symbol__("FakeAsyncTestMacroTask")])}_fnAndFlush(t,s){return(...r)=>(t.apply(e,r),null===this._lastError?(null!=s.onSuccess&&s.onSuccess.apply(e),this.flushMicrotasks()):null!=s.onError&&s.onError.apply(e),null===this._lastError)}static _removeTimer(e,t){let s=e.indexOf(t);s>-1&&e.splice(s,1)}_dequeueTimer(e){return()=>{n._removeTimer(this.pendingTimers,e)}}_requeuePeriodicTimer(e,t,s,r){return()=>{-1!==this.pendingPeriodicTimers.indexOf(r)&&this._scheduler.scheduleFunction(e,t,{args:s,isPeriodic:!0,id:r,isRequeuePeriodic:!0})}}_dequeuePeriodicTimer(e){return()=>{n._removeTimer(this.pendingPeriodicTimers,e)}}_setTimeout(e,t,s,r=!0){let n=this._dequeueTimer(i.nextId),c=this._fnAndFlush(e,{onSuccess:n,onError:n}),a=this._scheduler.scheduleFunction(c,t,{args:s,isRequestAnimationFrame:!r});return r&&this.pendingTimers.push(a),a}_clearTimeout(e){n._removeTimer(this.pendingTimers,e),this._scheduler.removeScheduledFunctionWithId(e)}_setInterval(e,t,s){let r=i.nextId,n={onSuccess:null,onError:this._dequeuePeriodicTimer(r)},c=this._fnAndFlush(e,n);return n.onSuccess=this._requeuePeriodicTimer(c,t,s,r),this._scheduler.scheduleFunction(c,t,{args:s,isPeriodic:!0}),this.pendingPeriodicTimers.push(r),r}_clearInterval(e){n._removeTimer(this.pendingPeriodicTimers,e),this._scheduler.removeScheduledFunctionWithId(e)}_resetLastErrorAndThrow(){let e=this._lastError||this._uncaughtPromiseErrors[0];throw this._uncaughtPromiseErrors.length=0,this._lastError=null,e}getCurrentTickTime(){return this._scheduler.getCurrentTickTime()}getFakeSystemTime(){return this._scheduler.getFakeSystemTime()}setFakeBaseSystemTime(e){this._scheduler.setFakeBaseSystemTime(e)}getRealSystemTime(){return this._scheduler.getRealSystemTime()}static patchDate(){e[Zone.__symbol__("disableDatePatching")]||e.Date!==s&&(e.Date=s,s.prototype=t.prototype,n.checkTimerPatch())}static resetDate(){e.Date===s&&(e.Date=t)}static checkTimerPatch(){e.setTimeout!==r.setTimeout&&(e.setTimeout=r.setTimeout,e.clearTimeout=r.clearTimeout),e.setInterval!==r.setInterval&&(e.setInterval=r.setInterval,e.clearInterval=r.clearInterval)}lockDatePatch(){this.patchDateLocked=!0,n.patchDate()}unlockDatePatch(){this.patchDateLocked=!1,n.resetDate()}tickToNext(e=1,t,s={processNewMacroTasksSynchronously:!0}){e<=0||(n.assertInZone(),this.flushMicrotasks(),this._scheduler.tickToNext(e,t,s),null!==this._lastError&&this._resetLastErrorAndThrow())}tick(e=0,t,s={processNewMacroTasksSynchronously:!0}){n.assertInZone(),this.flushMicrotasks(),this._scheduler.tick(e,t,s),null!==this._lastError&&this._resetLastErrorAndThrow()}flushMicrotasks(){for(n.assertInZone();this._microtasks.length>0;){let e=this._microtasks.shift();e.func.apply(e.target,e.args)}(()=>{(null!==this._lastError||this._uncaughtPromiseErrors.length)&&this._resetLastErrorAndThrow()})()}flush(e,t,s){n.assertInZone(),this.flushMicrotasks();const r=this._scheduler.flush(e,t,s);return null!==this._lastError&&this._resetLastErrorAndThrow(),r}flushOnlyPendingTimers(e){n.assertInZone(),this.flushMicrotasks();const t=this._scheduler.flushOnlyPendingTimers(e);return null!==this._lastError&&this._resetLastErrorAndThrow(),t}removeAllTimers(){n.assertInZone(),this._scheduler.removeAll(),this.pendingPeriodicTimers=[],this.pendingTimers=[]}getTimerCount(){return this._scheduler.getTimerCount()+this._microtasks.length}onScheduleTask(e,t,s,r){switch(r.type){case"microTask":let t,i=r.data&&r.data.args;if(i){let e=r.data.cbIdx;"number"==typeof i.length&&i.length>e+1&&(t=Array.prototype.slice.call(i,e+1))}this._microtasks.push({func:r.invoke,args:t,target:r.data&&r.data.target});break;case"macroTask":switch(r.source){case"setTimeout":r.data.handleId=this._setTimeout(r.invoke,r.data.delay,Array.prototype.slice.call(r.data.args,2));break;case"setImmediate":r.data.handleId=this._setTimeout(r.invoke,0,Array.prototype.slice.call(r.data.args,1));break;case"setInterval":r.data.handleId=this._setInterval(r.invoke,r.data.delay,Array.prototype.slice.call(r.data.args,2));break;case"XMLHttpRequest.send":throw new Error("Cannot make XHRs from within a fake async test. Request URL: "+r.data.url);case"requestAnimationFrame":case"webkitRequestAnimationFrame":case"mozRequestAnimationFrame":r.data.handleId=this._setTimeout(r.invoke,16,r.data.args,this.trackPendingRequestAnimationFrame);break;default:const e=this.findMacroTaskOption(r);if(e){const t=r.data&&r.data.args,s=t&&t.length>1?t[1]:0;let i=e.callbackArgs?e.callbackArgs:t;e.isPeriodic?(r.data.handleId=this._setInterval(r.invoke,s,i),r.data.isPeriodic=!0):r.data.handleId=this._setTimeout(r.invoke,s,i);break}throw new Error("Unknown macroTask scheduled in fake async test: "+r.source)}break;case"eventTask":r=e.scheduleTask(s,r)}return r}onCancelTask(e,t,s,r){switch(r.source){case"setTimeout":case"requestAnimationFrame":case"webkitRequestAnimationFrame":case"mozRequestAnimationFrame":return this._clearTimeout(r.data.handleId);case"setInterval":return this._clearInterval(r.data.handleId);default:const t=this.findMacroTaskOption(r);if(t){const e=r.data.handleId;return t.isPeriodic?this._clearInterval(e):this._clearTimeout(e)}return e.cancelTask(s,r)}}onInvoke(e,t,s,r,i,c,a){try{return n.patchDate(),e.invoke(s,r,i,c,a)}finally{this.patchDateLocked||n.resetDate()}}findMacroTaskOption(e){if(!this.macroTaskOptions)return null;for(let t=0;t<this.macroTaskOptions.length;t++){const s=this.macroTaskOptions[t];if(s.source===e.source)return s}return null}onHandleError(e,t,s,r){return this._lastError=r,!1}}Zone.FakeAsyncTestZoneSpec=n}("object"==typeof window&&window||"object"==typeof self&&self||global),Zone.__load_patch("fakeasync",((e,t,s)=>{const r=t&&t.FakeAsyncTestZoneSpec;function i(){return t&&t.ProxyZoneSpec}let n=null;function c(){n&&n.unlockDatePatch(),n=null,i()&&i().assertPresent().resetDelegate()}function a(){if(null==n&&(n=t.current.get("FakeAsyncTestZoneSpec"),null==n))throw new Error("The code should be running in the fakeAsync zone to call this function");return n}function o(){a().flushMicrotasks()}t[s.symbol("fakeAsyncTest")]={resetFakeAsyncZone:c,flushMicrotasks:o,discardPeriodicTasks:function u(){a().pendingPeriodicTimers.length=0},tick:function h(e=0,t=!1){a().tick(e,null,t)},flush:function l(e){return a().flush(e)},fakeAsync:function d(e){const s=function(...s){const a=i();if(!a)throw new Error("ProxyZoneSpec is needed for the async() test helper but could not be found. Please make sure that your environment includes zone.js/plugins/proxy");const u=a.assertPresent();if(t.current.get("FakeAsyncTestZoneSpec"))throw new Error("fakeAsync() calls can not be nested");try{if(!n){if(u.getDelegate()instanceof r)throw new Error("fakeAsync() calls can not be nested");n=new r}let t;const i=u.getDelegate();u.setDelegate(n),n.lockDatePatch();try{t=e.apply(this,s),o()}finally{u.setDelegate(i)}if(n.pendingPeriodicTimers.length>0)throw new Error(`${n.pendingPeriodicTimers.length} periodic timer(s) still in the queue.`);if(n.pendingTimers.length>0)throw new Error(`${n.pendingTimers.length} timer(s) still in the queue.`);return t}finally{c()}};return s.isFakeAsync=!0,s}}}),!0);