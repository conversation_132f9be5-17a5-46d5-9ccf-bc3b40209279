.security-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.security-warning-container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.warning-content {
  padding: 2rem;
  text-align: center;
}

.warning-icon {
  margin-bottom: 1.5rem;
  
  .pi {
    font-size: 4rem;
    color: #dc3545;
    animation: pulse 2s infinite;
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.warning-title {
  color: #dc3545;
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.warning-message {
  color: #495057;
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  white-space: pre-line;
}

.security-details {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  text-align: left;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .pi {
    font-size: 1.2rem;
  }
  
  span {
    font-weight: 500;
    color: #495057;
  }
}

.instructions-section {
  text-align: left;
  margin-bottom: 2rem;
  
  h3 {
    color: #495057;
    font-size: 1.3rem;
    margin-bottom: 1rem;
    text-align: center;
  }
}

.instructions-list {
  background: #e3f2fd;
  border-radius: 8px;
  padding: 1.5rem;
  margin: 0;
  
  li {
    margin-bottom: 0.75rem;
    color: #1565c0;
    font-weight: 500;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.important-notice {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border: 1px solid #ffc107;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  text-align: left;
  
  .pi {
    font-size: 1.5rem;
    color: #856404;
    margin-top: 0.2rem;
    flex-shrink: 0;
  }
  
  h4 {
    color: #856404;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
  }
  
  p {
    color: #6c757d;
    margin: 0;
    line-height: 1.5;
  }
}

.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  
  ::ng-deep .p-button {
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }
}

.help-text {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  
  p {
    margin: 0;
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.5;
  }
}

.text-danger {
  color: #dc3545 !important;
}

// Responsive design
@media (max-width: 768px) {
  .security-overlay {
    padding: 0.5rem;
  }
  
  .warning-content {
    padding: 1.5rem;
  }
  
  .warning-icon .pi {
    font-size: 3rem;
  }
  
  .warning-title {
    font-size: 1.5rem;
  }
  
  .warning-message {
    font-size: 1rem;
  }
  
  .action-buttons {
    flex-direction: column;
    
    ::ng-deep .p-button {
      width: 100%;
    }
  }
  
  .important-notice {
    flex-direction: column;
    text-align: center;
    
    .pi {
      align-self: center;
    }
  }
}

@media (max-width: 480px) {
  .security-warning-container {
    margin: 0.5rem;
    border-radius: 12px;
  }
  
  .warning-content {
    padding: 1rem;
  }
  
  .security-details,
  .instructions-list,
  .important-notice {
    padding: 1rem;
  }
}
