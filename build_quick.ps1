# بناء سريع لـ APK - أمر واحد
Write-Host "بناء APK سريع..." -ForegroundColor Green

# تعيين متغيرات البيئة
$env:GRADLE_HOME = "C:\gradle\gradle-7.6.4"
$env:PATH = "$env:GRADLE_HOME\bin;$env:PATH"
$env:JAVA_HOME = "C:\Program Files\Microsoft\jdk-*********-hotspot"

# بناء Angular
Write-Host "بناء Angular..." -ForegroundColor Yellow
npm run build

# نسخ index.html إذا لزم الأمر
if (-not (Test-Path "dist\quran-vid-gen\index.html")) {
    Copy-Item "src\index.html" "dist\quran-vid-gen\index.html"
}

# بناء APK
Write-Host "بناء APK..." -ForegroundColor Yellow
npx cap run android --no-open

# نسخ APK إلى سطح المكتب
if (Test-Path "android\app\build\outputs\apk\debug\app-debug.apk") {
    $timestamp = Get-Date -Format "yyyy-MM-dd_HH-mm"
    Copy-Item "android\app\build\outputs\apk\debug\app-debug.apk" "$env:USERPROFILE\Desktop\QuranVidGen_$timestamp.apk"
    Write-Host "✅ APK جاهز على سطح المكتب: QuranVidGen_$timestamp.apk" -ForegroundColor Green
} else {
    Write-Host "❌ لم يتم العثور على APK" -ForegroundColor Red
}
