// ملف تكوين مفاتيح API
// يرجى تعديل هذه القيم بمفاتيحك الفعلية

export const API_KEYS = {
  // Google API Configuration
  GOOGLE_CLIENT_ID: 'YOUR_GOOGLE_CLIENT_ID_HERE',
  GOOGLE_API_KEY: 'YOUR_GOOGLE_API_KEY_HERE',
  
  // Gemini AI Configuration
  GEMINI_API_KEY: 'YOUR_GEMINI_API_KEY_HERE'
};

// تعليمات الحصول على المفاتيح:

/*
1. Google Client ID & API Key:
   - اذهب إلى Google Cloud Console: https://console.cloud.google.com/
   - أنشئ مشروع جديد أو اختر مشروع موجود
   - فعّل YouTube Data API v3
   - أنشئ credentials (OAuth 2.0 Client ID و API Key)
   - أضف domain الخاص بك في Authorized JavaScript origins

2. Gemini API Key:
   - اذه<PERSON> إلى Google AI Studio: https://aistudio.google.com/
   - أنشئ API key جديد
   - انسخ المفتاح وضعه هنا

ملاحظة: لا تشارك هذه المفاتيح مع أحد ولا ترفعها إلى GitHub
*/
