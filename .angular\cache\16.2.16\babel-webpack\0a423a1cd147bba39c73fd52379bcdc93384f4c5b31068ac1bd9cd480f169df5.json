{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ContentChild, ContentChildren, NgModule } from '@angular/core';\nimport { Header, Footer, PrimeTemplate, SharedModule } from 'primeng/api';\n\n/**\n * Card is a flexible container component.\n * @group Components\n */\nfunction Card_div_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Card_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵprojection(1, 1);\n    i0.ɵɵtemplate(2, Card_div_1_ng_container_2_Template, 1, 0, \"ng-container\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.headerTemplate);\n  }\n}\nfunction Card_div_3_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Card_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, Card_div_3_ng_container_2_Template, 1, 0, \"ng-container\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.header, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.titleTemplate);\n  }\n}\nfunction Card_div_4_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Card_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, Card_div_4_ng_container_2_Template, 1, 0, \"ng-container\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.subheader, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.subtitleTemplate);\n  }\n}\nfunction Card_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Card_div_8_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Card_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵprojection(1, 2);\n    i0.ɵɵtemplate(2, Card_div_8_ng_container_2_Template, 1, 0, \"ng-container\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r4.footerTemplate);\n  }\n}\nconst _c0 = [\"*\", [[\"p-header\"]], [[\"p-footer\"]]];\nconst _c1 = [\"*\", \"p-header\", \"p-footer\"];\nlet Card = /*#__PURE__*/(() => {\n  class Card {\n    el;\n    /**\n     * Header of the card.\n     * @group Props\n     */\n    header;\n    /**\n     * Subheader of the card.\n     * @group Props\n     */\n    subheader;\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    style;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    styleClass;\n    headerFacet;\n    footerFacet;\n    templates;\n    headerTemplate;\n    titleTemplate;\n    subtitleTemplate;\n    contentTemplate;\n    footerTemplate;\n    constructor(el) {\n      this.el = el;\n    }\n    ngAfterContentInit() {\n      this.templates.forEach(item => {\n        switch (item.getType()) {\n          case 'header':\n            this.headerTemplate = item.template;\n            break;\n          case 'title':\n            this.titleTemplate = item.template;\n            break;\n          case 'subtitle':\n            this.subtitleTemplate = item.template;\n            break;\n          case 'content':\n            this.contentTemplate = item.template;\n            break;\n          case 'footer':\n            this.footerTemplate = item.template;\n            break;\n          default:\n            this.contentTemplate = item.template;\n            break;\n        }\n      });\n    }\n    getBlockableElement() {\n      return this.el.nativeElement.children[0];\n    }\n    static ɵfac = function Card_Factory(t) {\n      return new (t || Card)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: Card,\n      selectors: [[\"p-card\"]],\n      contentQueries: function Card_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, Header, 5);\n          i0.ɵɵcontentQuery(dirIndex, Footer, 5);\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerFacet = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.footerFacet = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        header: \"header\",\n        subheader: \"subheader\",\n        style: \"style\",\n        styleClass: \"styleClass\"\n      },\n      ngContentSelectors: _c1,\n      decls: 9,\n      vars: 10,\n      consts: [[3, \"ngClass\", \"ngStyle\"], [\"class\", \"p-card-header\", 4, \"ngIf\"], [1, \"p-card-body\"], [\"class\", \"p-card-title\", 4, \"ngIf\"], [\"class\", \"p-card-subtitle\", 4, \"ngIf\"], [1, \"p-card-content\"], [4, \"ngTemplateOutlet\"], [\"class\", \"p-card-footer\", 4, \"ngIf\"], [1, \"p-card-header\"], [1, \"p-card-title\"], [1, \"p-card-subtitle\"], [1, \"p-card-footer\"]],\n      template: function Card_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c0);\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, Card_div_1_Template, 3, 1, \"div\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2);\n          i0.ɵɵtemplate(3, Card_div_3_Template, 3, 2, \"div\", 3);\n          i0.ɵɵtemplate(4, Card_div_4_Template, 3, 2, \"div\", 4);\n          i0.ɵɵelementStart(5, \"div\", 5);\n          i0.ɵɵprojection(6);\n          i0.ɵɵtemplate(7, Card_ng_container_7_Template, 1, 0, \"ng-container\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, Card_div_8_Template, 3, 1, \"div\", 7);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.styleClass);\n          i0.ɵɵproperty(\"ngClass\", \"p-card p-component\")(\"ngStyle\", ctx.style);\n          i0.ɵɵattribute(\"data-pc-name\", \"card\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.headerFacet || ctx.headerTemplate);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.header || ctx.titleTemplate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.subheader || ctx.subtitleTemplate);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.contentTemplate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.footerFacet || ctx.footerTemplate);\n        }\n      },\n      dependencies: [i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle],\n      styles: [\"@layer primeng{.p-card-header img{width:100%}}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return Card;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet CardModule = /*#__PURE__*/(() => {\n  class CardModule {\n    static ɵfac = function CardModule_Factory(t) {\n      return new (t || CardModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: CardModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, SharedModule]\n    });\n  }\n  return CardModule;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Card, CardModule };\n//# sourceMappingURL=primeng-card.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}