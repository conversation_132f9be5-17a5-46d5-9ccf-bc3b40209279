import { Component, OnInit } from '@angular/core';
import { QuranService } from './Services/quran.service';
import { catchError, of } from 'rxjs';
import { Ayah } from './Interfaces/ayah';
import { Surah } from './Interfaces/surah';
import { Capacitor } from '@capacitor/core';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit {
  title = 'QuranVidGen';
  isNativeApp = false;

  constructor(private quranService: QuranService) {
    this.isNativeApp = Capacitor.isNativePlatform();
  }

  ngOnInit() {
    console.log('App Component initialized');
    console.log('Is Native App:', this.isNativeApp);
    console.log('Platform:', Capacitor.getPlatform());

    // التحقق من حالة التطبيق
    if (this.isNativeApp) {
      console.log('Running on native platform');
    } else {
      console.log('Running on web platform');
    }
  }
}
