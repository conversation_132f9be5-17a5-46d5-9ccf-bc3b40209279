// Login Screen Styles
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 1rem;
}

.login-card {
  background: white;
  border-radius: 16px;
  padding: 3rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  max-width: 400px;
  width: 100%;
  text-align: center;
}

.login-header {
  margin-bottom: 2rem;
  
  .pi {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1rem;
  }
  
  h2 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
  }
  
  p {
    color: #6c757d;
    margin: 0;
  }
}

.form-group {
  margin-bottom: 1.5rem;
  text-align: left;
  
  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #495057;
  }
  
  input, textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    
    &:focus {
      border-color: #667eea;
      outline: none;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
  }
}

.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
  .pi {
    font-size: 1.1rem;
  }
}

// Admin Dashboard Styles
.admin-container {
  min-height: 100vh;
  background: #f8f9fa;
}

.admin-header {
  background: white;
  border-bottom: 1px solid #e9ecef;
  padding: 1.5rem 2rem;
  
  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
  }
  
  .header-left {
    h1 {
      color: #2c3e50;
      margin-bottom: 0.5rem;
      font-size: 1.8rem;
    }
    
    p {
      color: #6c757d;
      margin: 0;
    }
  }
  
  .header-right {
    display: flex;
    gap: 1rem;
  }
}

.admin-nav {
  background: white;
  border-bottom: 1px solid #e9ecef;
  padding: 0 2rem;
  
  .nav-tabs {
    display: flex;
    max-width: 1400px;
    margin: 0 auto;
  }
  
  .nav-tab {
    background: none;
    border: none;
    padding: 1rem 1.5rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6c757d;
    font-weight: 500;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    
    &:hover {
      color: #495057;
      background: #f8f9fa;
    }
    
    &.active {
      color: #667eea;
      border-bottom-color: #667eea;
      background: #f8f9fa;
    }
    
    .pi {
      font-size: 1.1rem;
    }
  }
}

.admin-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
}

.tab-content {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

// Dashboard Stats
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1.5rem;
  
  .stat-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .pi {
      font-size: 1.5rem;
    }
  }
  
  .stat-info {
    h3 {
      color: #2c3e50;
      font-size: 2rem;
      margin-bottom: 0.5rem;
      font-weight: 700;
    }
    
    p {
      color: #6c757d;
      margin: 0;
      font-weight: 500;
    }
  }
}

// Form Sections
.form-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  h3 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
  }
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.checkbox-container {
  display: flex;
  align-items: center;
  padding-top: 1.5rem;
}

.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
  
  ::ng-deep .p-button {
    padding: 0.75rem 1.5rem;
    font-weight: 600;
  }
}

// Table Sections
.table-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  h3 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
  }
}

.table-container {
  overflow-x: auto;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  
  th, td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid #e9ecef;
  }
  
  th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
    position: sticky;
    top: 0;
  }
  
  tr:hover {
    background: #f8f9fa;
  }
  
  .link {
    color: #667eea;
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }
  
  code {
    background: #f8f9fa;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
  }
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  
  &.active {
    background: #d4edda;
    color: #155724;
  }
  
  &.inactive {
    background: #f8d7da;
    color: #721c24;
  }
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

.event-type {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
  .pi {
    font-size: 1.1rem;
  }
}

.details-text {
  background: #f8f9fa;
  padding: 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  max-width: 300px;
  overflow-x: auto;
  white-space: pre-wrap;
  margin: 0;
}

// Color classes
.text-warning { color: #ffc107 !important; }
.text-danger { color: #dc3545 !important; }
.text-info { color: #17a2b8 !important; }
.text-secondary { color: #6c757d !important; }

// Responsive Design
@media (max-width: 768px) {
  .admin-header {
    padding: 1rem;
    
    .header-content {
      flex-direction: column;
      gap: 1rem;
      text-align: center;
    }
    
    .header-right {
      flex-direction: column;
      width: 100%;
      
      ::ng-deep .p-button {
        width: 100%;
      }
    }
  }
  
  .admin-nav {
    padding: 0 1rem;
    
    .nav-tabs {
      flex-wrap: wrap;
    }
    
    .nav-tab {
      flex: 1;
      min-width: 120px;
      justify-content: center;
    }
  }
  
  .admin-content {
    padding: 1rem;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
    
    ::ng-deep .p-button {
      width: 100%;
    }
  }
  
  .data-table {
    font-size: 0.9rem;
    
    th, td {
      padding: 0.75rem 0.5rem;
    }
  }
  
  .login-card {
    padding: 2rem 1.5rem;
  }
}
