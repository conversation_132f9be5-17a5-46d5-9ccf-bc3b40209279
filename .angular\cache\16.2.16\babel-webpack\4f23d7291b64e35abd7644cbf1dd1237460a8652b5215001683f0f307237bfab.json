{"ast": null, "code": "import { <PERSON><PERSON><PERSON><PERSON> } from 'primeng/dom';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Directive, Input, HostListener, NgModule } from '@angular/core';\n\n/**\n * Focus Trap keeps focus within a certain DOM element while tabbing.\n * @group Components\n */\nlet FocusTrap = /*#__PURE__*/(() => {\n  class FocusTrap {\n    el;\n    /**\n     * When set as true, focus wouldn't be managed.\n     * @group Props\n     */\n    pFocusTrapDisabled = false;\n    constructor(el) {\n      this.el = el;\n    }\n    onkeydown(e) {\n      if (this.pFocusTrapDisabled !== true) {\n        e.preventDefault();\n        const focusableElement = DomHandler.getNextFocusableElement(this.el.nativeElement, e.shiftKey);\n        if (focusableElement) {\n          focusableElement.focus();\n          focusableElement.select?.();\n        }\n      }\n    }\n    static ɵfac = function FocusTrap_Factory(t) {\n      return new (t || FocusTrap)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: FocusTrap,\n      selectors: [[\"\", \"pFocusTrap\", \"\"]],\n      hostAttrs: [1, \"p-element\"],\n      hostBindings: function FocusTrap_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown.tab\", function FocusTrap_keydown_tab_HostBindingHandler($event) {\n            return ctx.onkeydown($event);\n          })(\"keydown.shift.tab\", function FocusTrap_keydown_shift_tab_HostBindingHandler($event) {\n            return ctx.onkeydown($event);\n          });\n        }\n      },\n      inputs: {\n        pFocusTrapDisabled: \"pFocusTrapDisabled\"\n      }\n    });\n  }\n  return FocusTrap;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet FocusTrapModule = /*#__PURE__*/(() => {\n  class FocusTrapModule {\n    static ɵfac = function FocusTrapModule_Factory(t) {\n      return new (t || FocusTrapModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: FocusTrapModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n  return FocusTrapModule;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { FocusTrap, FocusTrapModule };\n//# sourceMappingURL=primeng-focustrap.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}