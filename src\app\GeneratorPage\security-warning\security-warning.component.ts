import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import { SecurityService, SecurityCheck } from '../../Services/security.service';

@Component({
  selector: 'app-security-warning',
  templateUrl: './security-warning.component.html',
  styleUrls: ['./security-warning.component.scss']
})
export class SecurityWarningComponent implements OnInit, OnDestroy {
  securityCheck: SecurityCheck | null = null;
  showWarning = false;
  isCheckingAgain = false;
  
  private securitySubscription?: Subscription;

  constructor(private securityService: SecurityService) {}

  ngOnInit(): void {
    this.securitySubscription = this.securityService.securityStatus$.subscribe(
      (check) => {
        this.securityCheck = check;
        this.showWarning = !check.allowAccess;
      }
    );
  }

  ngOnDestroy(): void {
    if (this.securitySubscription) {
      this.securitySubscription.unsubscribe();
    }
  }

  async recheckSecurity(): Promise<void> {
    this.isCheckingAgain = true;
    try {
      await this.securityService.forceSecurityCheck();
    } finally {
      this.isCheckingAgain = false;
    }
  }

  getWarningTitle(): string {
    if (!this.securityCheck) return '';
    
    if (this.securityCheck.isVpnDetected) {
      return 'تم اكتشاف VPN أو بروكسي';
    }
    
    if (this.securityCheck.isAdBlockerDetected) {
      return 'تم اكتشاف أداة حظر الإعلانات';
    }
    
    if (this.securityCheck.isDnsBlocked) {
      return 'تم اكتشاف DNS محجوب';
    }
    
    return 'مشكلة في الأمان';
  }

  getWarningMessage(): string {
    if (!this.securityCheck) return '';
    
    const messages = [];
    
    if (this.securityCheck.isVpnDetected) {
      messages.push('يرجى إيقاف تشغيل VPN أو البروكسي للمتابعة');
    }
    
    if (this.securityCheck.isAdBlockerDetected) {
      messages.push('يرجى إيقاف تشغيل أداة حظر الإعلانات للمتابعة');
    }
    
    if (this.securityCheck.isDnsBlocked) {
      messages.push('يرجى تغيير إعدادات DNS إلى الإعدادات الافتراضية');
    }
    
    return messages.join('\n');
  }

  getInstructions(): string[] {
    if (!this.securityCheck) return [];
    
    const instructions = [];
    
    if (this.securityCheck.isVpnDetected) {
      instructions.push(
        'إيقاف تشغيل أي تطبيق VPN أو بروكسي',
        'إعادة تشغيل المتصفح',
        'التأكد من عدم استخدام أي إضافات للخصوصية'
      );
    }
    
    if (this.securityCheck.isAdBlockerDetected) {
      instructions.push(
        'إيقاف تشغيل AdBlock أو uBlock Origin',
        'إضافة هذا الموقع إلى القائمة البيضاء',
        'إعادة تحميل الصفحة'
      );
    }
    
    if (this.securityCheck.isDnsBlocked) {
      instructions.push(
        'تغيير DNS إلى 8.8.8.8 أو إعدادات مزود الخدمة',
        'تجنب استخدام dns.adguard.com',
        'إعادة تشغيل الاتصال بالإنترنت'
      );
    }
    
    return instructions;
  }

  openSupportEmail(): void {
    const email = '<EMAIL>';
    const subject = 'مشكلة في الوصول - مولد مقاطع القرآن الكريم';
    const body = `السلام عليكم ورحمة الله وبركاته،

أواجه مشكلة في الوصول للتطبيق:

السبب المكتشف: ${this.securityCheck?.blockedReason || 'غير محدد'}

تفاصيل إضافية:
- VPN مكتشف: ${this.securityCheck?.isVpnDetected ? 'نعم' : 'لا'}
- أداة حظر إعلانات مكتشفة: ${this.securityCheck?.isAdBlockerDetected ? 'نعم' : 'لا'}
- DNS محجوب: ${this.securityCheck?.isDnsBlocked ? 'نعم' : 'لا'}

يرجى المساعدة في حل هذه المشكلة.

شكراً لكم`;
    
    const mailtoLink = `mailto:${email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.open(mailtoLink, '_blank');
  }
}
