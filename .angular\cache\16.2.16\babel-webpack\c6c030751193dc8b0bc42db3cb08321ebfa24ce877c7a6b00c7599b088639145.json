{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function refCount() {\n  return operate((source, subscriber) => {\n    let connection = null;\n    source._refCount++;\n    const refCounter = createOperatorSubscriber(subscriber, undefined, undefined, undefined, () => {\n      if (!source || source._refCount <= 0 || 0 < --source._refCount) {\n        connection = null;\n        return;\n      }\n      const sharedConnection = source._connection;\n      const conn = connection;\n      connection = null;\n      if (sharedConnection && (!conn || sharedConnection === conn)) {\n        sharedConnection.unsubscribe();\n      }\n      subscriber.unsubscribe();\n    });\n    source.subscribe(refCounter);\n    if (!refCounter.closed) {\n      connection = source.connect();\n    }\n  });\n}\n//# sourceMappingURL=refCount.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}