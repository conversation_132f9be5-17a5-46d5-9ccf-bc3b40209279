import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { delay, map } from 'rxjs/operators';

export interface AdConfig {
  appId: string;
  adUnitId: string;
  testMode: boolean;
}

export interface AdResult {
  success: boolean;
  error?: string;
  rewardEarned?: boolean;
  rewardAmount?: number;
  rewardType?: string;
}

export interface AdStatus {
  isLoaded: boolean;
  isShowing: boolean;
  lastShown?: Date;
  totalShown: number;
  totalRewarded: number;
}

@Injectable({
  providedIn: 'root'
})
export class AdMobService {
  private readonly AD_CONFIG: AdConfig = {
    appId: 'ca-app-pub-4373910379376809~8789974713',
    adUnitId: 'ca-app-pub-4373910379376809/7815804390',
    testMode: false // Set to true for testing
  };

  private adStatusSubject = new BehaviorSubject<AdStatus>({
    isLoaded: false,
    isShowing: false,
    totalShown: 0,
    totalRewarded: 0
  });

  public adStatus$ = this.adStatusSubject.asObservable();
  private isInitialized = false;

  constructor() {
    this.initializeAdMob();
  }

  private async initializeAdMob(): Promise<void> {
    try {
      // For web implementation, we'll simulate AdMob behavior
      // In a real mobile app, this would initialize the actual AdMob SDK
      
      if (this.isWebEnvironment()) {
        await this.initializeWebAds();
      } else {
        await this.initializeMobileAds();
      }
      
      this.isInitialized = true;
      console.log('AdMob initialized successfully');
    } catch (error) {
      console.error('Failed to initialize AdMob:', error);
    }
  }

  private isWebEnvironment(): boolean {
    return typeof window !== 'undefined' && !window.hasOwnProperty('cordova');
  }

  private async initializeWebAds(): Promise<void> {
    // Load Google AdSense script for web ads
    return new Promise((resolve, reject) => {
      if (document.querySelector('script[src*="adsbygoogle"]')) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.async = true;
      script.src = 'https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js';
      script.crossOrigin = 'anonymous';
      
      script.onload = () => {
        // Initialize AdSense
        try {
          (window as any).adsbygoogle = (window as any).adsbygoogle || [];
          resolve();
        } catch (error) {
          reject(error);
        }
      };
      
      script.onerror = () => {
        reject(new Error('Failed to load AdSense script'));
      };
      
      document.head.appendChild(script);
    });
  }

  private async initializeMobileAds(): Promise<void> {
    // This would be implemented for mobile apps using Cordova/Capacitor
    // For now, we'll simulate the behavior
    return Promise.resolve();
  }

  public async loadRewardedAd(): Promise<boolean> {
    try {
      if (!this.isInitialized) {
        await this.initializeAdMob();
      }

      // Simulate loading time
      await new Promise(resolve => setTimeout(resolve, 1000));

      const currentStatus = this.adStatusSubject.value;
      this.adStatusSubject.next({
        ...currentStatus,
        isLoaded: true
      });

      return true;
    } catch (error) {
      console.error('Failed to load rewarded ad:', error);
      return false;
    }
  }

  public async showRewardedAd(): Promise<Observable<AdResult>> {
    const currentStatus = this.adStatusSubject.value;

    if (!currentStatus.isLoaded) {
      return of({
        success: false,
        error: 'الإعلان غير محمل. يرجى المحاولة مرة أخرى.'
      });
    }

    // Update status to showing
    this.adStatusSubject.next({
      ...currentStatus,
      isShowing: true
    });

    if (this.isWebEnvironment()) {
      return this.showWebRewardedAd();
    } else {
      return this.showMobileRewardedAd();
    }
  }

  private showWebRewardedAd(): Observable<AdResult> {
    return new Observable(observer => {
      // Create a modal overlay for the ad
      const adModal = this.createAdModal();
      document.body.appendChild(adModal);

      // Simulate ad duration (15 seconds)
      const adDuration = 15000;
      let timeRemaining = adDuration / 1000;
      
      const countdownElement = adModal.querySelector('.countdown') as HTMLElement;
      const skipButton = adModal.querySelector('.skip-button') as HTMLButtonElement;
      const closeButton = adModal.querySelector('.close-button') as HTMLButtonElement;

      // Update countdown
      const countdownInterval = setInterval(() => {
        timeRemaining--;
        if (countdownElement) {
          countdownElement.textContent = `${timeRemaining}`;
        }

        if (timeRemaining <= 0) {
          clearInterval(countdownInterval);
          if (skipButton) {
            skipButton.style.display = 'block';
            skipButton.textContent = 'إغلاق الإعلان';
          }
        }
      }, 1000);

      // Handle skip/close
      const handleClose = (rewarded: boolean) => {
        clearInterval(countdownInterval);
        document.body.removeChild(adModal);

        const currentStatus = this.adStatusSubject.value;
        this.adStatusSubject.next({
          ...currentStatus,
          isLoaded: false,
          isShowing: false,
          lastShown: new Date(),
          totalShown: currentStatus.totalShown + 1,
          totalRewarded: rewarded ? currentStatus.totalRewarded + 1 : currentStatus.totalRewarded
        });

        observer.next({
          success: true,
          rewardEarned: rewarded,
          rewardAmount: rewarded ? 1 : 0,
          rewardType: 'access_time'
        });
        observer.complete();
      };

      if (skipButton) {
        skipButton.addEventListener('click', () => handleClose(true));
      }

      if (closeButton) {
        closeButton.addEventListener('click', () => handleClose(false));
      }

      // Auto-close after ad duration + 5 seconds
      setTimeout(() => {
        if (document.body.contains(adModal)) {
          handleClose(true);
        }
      }, adDuration + 5000);
    });
  }

  private showMobileRewardedAd(): Observable<AdResult> {
    // Simulate mobile ad behavior
    return of({
      success: true,
      rewardEarned: true,
      rewardAmount: 1,
      rewardType: 'access_time'
    }).pipe(delay(3000)); // Simulate 3 second ad
  }

  private createAdModal(): HTMLElement {
    const modal = document.createElement('div');
    modal.className = 'admob-modal';
    modal.innerHTML = `
      <div class="admob-overlay">
        <div class="admob-content">
          <div class="ad-header">
            <span class="ad-label">إعلان</span>
            <button class="close-button" style="display: none;">×</button>
          </div>
          
          <div class="ad-body">
            <div class="ad-placeholder">
              <div class="ad-icon">📺</div>
              <h3>إعلان قصير</h3>
              <p>شاهد هذا الإعلان للحصول على 3 ساعات من الاستخدام المجاني</p>
              
              <div class="countdown-container">
                <div class="countdown-circle">
                  <span class="countdown">15</span>
                </div>
                <p>ثانية متبقية</p>
              </div>
              
              <div class="ad-simulation">
                <div class="loading-bar">
                  <div class="loading-progress"></div>
                </div>
                <p>جاري تحميل الإعلان...</p>
              </div>
            </div>
          </div>
          
          <div class="ad-footer">
            <button class="skip-button" style="display: none;">تخطي الإعلان</button>
            <p class="ad-info">الإعلانات تساعدنا في تقديم الخدمة مجاناً</p>
          </div>
        </div>
      </div>
    `;

    // Add styles
    const style = document.createElement('style');
    style.textContent = `
      .admob-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        z-index: 10000;
        font-family: Arial, sans-serif;
      }
      
      .admob-overlay {
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      .admob-content {
        background: white;
        border-radius: 12px;
        max-width: 400px;
        width: 90%;
        overflow: hidden;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
      }
      
      .ad-header {
        background: #4285f4;
        color: white;
        padding: 1rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .ad-label {
        font-weight: bold;
        font-size: 0.9rem;
      }
      
      .close-button {
        background: none;
        border: none;
        color: white;
        font-size: 1.5rem;
        cursor: pointer;
        padding: 0;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      .ad-body {
        padding: 2rem;
        text-align: center;
      }
      
      .ad-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
      }
      
      .ad-body h3 {
        color: #333;
        margin-bottom: 0.5rem;
      }
      
      .ad-body p {
        color: #666;
        margin-bottom: 1.5rem;
      }
      
      .countdown-container {
        margin: 2rem 0;
      }
      
      .countdown-circle {
        width: 80px;
        height: 80px;
        border: 4px solid #4285f4;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        position: relative;
      }
      
      .countdown {
        font-size: 1.5rem;
        font-weight: bold;
        color: #4285f4;
      }
      
      .ad-simulation {
        margin: 1.5rem 0;
      }
      
      .loading-bar {
        width: 100%;
        height: 4px;
        background: #e0e0e0;
        border-radius: 2px;
        overflow: hidden;
        margin-bottom: 0.5rem;
      }
      
      .loading-progress {
        height: 100%;
        background: #4285f4;
        width: 0%;
        animation: loadingProgress 15s linear forwards;
      }
      
      @keyframes loadingProgress {
        to { width: 100%; }
      }
      
      .ad-footer {
        background: #f5f5f5;
        padding: 1rem;
        text-align: center;
      }
      
      .skip-button {
        background: #4285f4;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 6px;
        cursor: pointer;
        font-weight: bold;
        margin-bottom: 0.5rem;
      }
      
      .ad-info {
        font-size: 0.8rem;
        color: #666;
        margin: 0;
      }
    `;
    
    modal.appendChild(style);
    return modal;
  }

  public getAdStatus(): AdStatus {
    return this.adStatusSubject.value;
  }

  public isAdLoaded(): boolean {
    return this.adStatusSubject.value.isLoaded;
  }

  public async preloadAd(): Promise<void> {
    await this.loadRewardedAd();
  }

  // Configuration methods
  public updateConfig(config: Partial<AdConfig>): void {
    Object.assign(this.AD_CONFIG, config);
  }

  public getConfig(): AdConfig {
    return { ...this.AD_CONFIG };
  }

  // Analytics methods
  public getAdStats(): { totalShown: number; totalRewarded: number; successRate: number } {
    const status = this.adStatusSubject.value;
    const successRate = status.totalShown > 0 ? (status.totalRewarded / status.totalShown) * 100 : 0;
    
    return {
      totalShown: status.totalShown,
      totalRewarded: status.totalRewarded,
      successRate: Math.round(successRate)
    };
  }
}
