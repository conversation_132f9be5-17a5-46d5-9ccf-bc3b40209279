.field {
  margin-bottom: 1rem;
}

.block {
  display: block;
}

.w-100 {
  width: 100% !important;
}

.flex {
  display: flex;
}

.align-items-center {
  align-items: center;
}

.justify-content-between {
  justify-content: space-between;
}

.justify-content-end {
  justify-content: flex-end;
}

.gap-2 {
  gap: 0.5rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-3 {
  margin-bottom: 1rem;
}

.mb-4 {
  margin-bottom: 1.5rem;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-4 {
  margin-top: 1.5rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.me-1 {
  margin-right: 0.25rem;
}

.me-2 {
  margin-right: 0.5rem;
}

.text-danger {
  color: #dc3545 !important;
}

.text-muted {
  color: #6c757d !important;
}

.text-primary {
  color: #007bff !important;
}

.alert {
  padding: 0.75rem 1rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: 0.375rem;
}

.alert-danger {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

.alert-success {
  color: #155724;
  background-color: #d4edda;
  border-color: #c3e6cb;
}

.p-inputgroup {
  display: flex;
  align-items: stretch;
  width: 100%;
}

.p-inputgroup input,
.p-inputgroup textarea {
  flex: 1;
}

textarea {
  font-family: inherit;
  resize: vertical;
  min-height: 120px;
}

small {
  font-size: 0.875em;
  line-height: 1.25;
}

label {
  font-weight: 500;
  margin-bottom: 0.5rem;
}

// Custom styles for PrimeNG components
::ng-deep {
  .p-dropdown {
    width: 100%;
  }
  
  .p-checkbox {
    margin-right: 0.5rem;
  }
  
  .p-button-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
  }
  
  .p-progressbar {
    height: 1rem;
    background-color: #e9ecef;
    border-radius: 0.375rem;
    overflow: hidden;
  }
  
  .p-progressbar-value {
    background-color: #007bff;
    transition: width 0.3s ease;
  }
}
