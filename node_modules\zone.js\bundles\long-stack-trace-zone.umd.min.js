"use strict";var __assign=this&&this.__assign||function(){return __assign=Object.assign||function(t){for(var a,n=1,r=arguments.length;n<r;n++)for(var e in a=arguments[n])Object.prototype.hasOwnProperty.call(a,e)&&(t[e]=a[e]);return t},__assign.apply(this,arguments)};
/**
 * @license Angular v<unknown>
 * (c) 2010-2022 Google LLC. https://angular.io/
 * License: MIT
 */!function(t){"function"==typeof define&&define.amd?define(t):t()}((function(){var t="\n",a={},n="__creationTrace__",r="STACKTRACE TRACKING",e="__SEP_TAG__",c=e+"@[native]",i=function i(){this.error=u(),this.timestamp=new Date};function o(){return new Error(r)}function s(){try{throw o()}catch(t){return t}}var _=o(),f=s(),u=_.stack?o:f.stack?s:o;function h(a){return a.stack?a.stack.split(t):[]}function l(t,n){for(var r=h(n),e=0;e<r.length;e++)a.hasOwnProperty(r[e])||t.push(r[e])}function g(a,n){var r=[n?n.trim():""];if(a)for(var i=(new Date).getTime(),o=0;o<a.length;o++){var s=a[o],_=s.timestamp,f="____________________Elapsed ".concat(i-_.getTime()," ms; At: ").concat(_);f=f.replace(/[^\w\d]/g,"_"),r.push(c.replace(e,f)),l(r,s.error),i=_.getTime()}return r.join(t)}function k(){return Error.stackTraceLimit>0}function T(t,a){a>0&&(t.push(h((new i).error)),T(t,a-1))}Zone.longStackTraceZoneSpec={name:"long-stack-trace",longStackTraceLimit:10,getLongStackTrace:function(t){if(t){var a=t[Zone.__symbol__("currentTaskTrace")];return a?g(a,t.stack):t.stack}},onScheduleTask:function(t,a,r,e){if(k()){var c=Zone.currentTask,o=c&&c.data&&c.data[n]||[];(o=[new i].concat(o)).length>this.longStackTraceLimit&&(o.length=this.longStackTraceLimit),e.data||(e.data={}),"eventTask"===e.type&&(e.data=__assign({},e.data)),e.data[n]=o}return t.scheduleTask(r,e)},onHandleError:function(t,a,r,e){if(k()){var c=Zone.currentTask||e.task;if(e instanceof Error&&c){var i=g(c.data&&c.data[n],e.stack);try{e.stack=e.longStack=i}catch(t){}}}return t.handleError(r,e)}},function d(){if(k()){var t=[];T(t,2);for(var n=t[0],i=t[1],o=0;o<n.length;o++)if(-1==(_=n[o]).indexOf(r)){var s=_.match(/^\s*at\s+/);if(s){c=s[0]+e+" (http://localhost)";break}}for(o=0;o<n.length;o++){var _;if((_=n[o])!==i[o])break;a[_]=!0}}}()}));