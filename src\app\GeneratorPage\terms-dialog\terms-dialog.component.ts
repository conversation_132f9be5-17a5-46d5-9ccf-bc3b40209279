import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'app-terms-dialog',
  templateUrl: './terms-dialog.component.html',
  styleUrls: ['./terms-dialog.component.scss']
})
export class TermsDialogComponent {
  @Input() visible: boolean = false;
  @Output() visibleChange = new EventEmitter<boolean>();

  onHide(): void {
    this.visible = false;
    this.visibleChange.emit(false);
  }

  onAccept(): void {
    // Handle terms acceptance
    localStorage.setItem('termsAccepted', 'true');
    this.onHide();
  }
}
