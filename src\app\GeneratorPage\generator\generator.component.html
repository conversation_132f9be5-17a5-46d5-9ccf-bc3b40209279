<div class="main-container">
  <!-- Header with menu button -->
  <div class="app-header">
    <div class="header-content">
      <div class="header-left">
        <h3 class="app-title">مولد مقاطع القرآن الكريم</h3>
        <p class="app-subtitle">إنشاء ونشر مقاطع قرآنية احترافية</p>
      </div>
      <div class="header-right">
        <p-button
          icon="pi pi-bars"
          severity="secondary"
          [text]="true"
          [rounded]="true"
          size="large"
          (onClick)="sidebarVisible = true"
          pTooltip="القائمة الرئيسية"
          tooltipPosition="bottom">
        </p-button>
      </div>
    </div>
  </div>
<div *ngIf="loaded;else progress">
  <div class="content-wrapper">
    <div class="form-container">


      <div class="form-group">
        <label class="form-label">
          <i class="pi pi-book"></i>
          اختر السورة
        </label>
        <p-dropdown
          [options]="suras"
          [optionLabel]="'surahName'"
          [(ngModel)]="currentSurah"
          placeholder="اختر السورة"
          (onChange)="helper.SurahNumberRestrict(GetCurrentSurahNumber(),start,end)"
          [dropdownIcon]="'pi pi-chevron-down'"
          [optionValue]="'surahName'"
          [filter]="true"
          styleClass="custom-dropdown">
        </p-dropdown>
      </div>

      <div class="form-group">
        <label class="form-label">
          <i class="pi pi-user"></i>
          اختر القارئ
        </label>
        <p-dropdown
          [options]="reciters"
          [optionLabel]="'name'"
          [(ngModel)]="currentReciterId"
          placeholder="اختر القارئ"
          [dropdownIcon]="'pi pi-chevron-down'"
          [optionValue]="'id'"
          styleClass="custom-dropdown">
        </p-dropdown>
      </div>

      <div class="form-group">
        <label class="form-label">
          <i class="pi pi-list"></i>
          نطاق الآيات
        </label>
        <div class="ayah-range-container">
          <div class="ayah-input">
            <label class="ayah-label">من الآية</label>
            <input
              #start
              type="number"
              [min]="1"
              (input)="helper.InputNumberRestrict(start)"
              pInputText
              placeholder="1"
              class="ayah-number-input" />
          </div>
          <div class="range-separator">
            <i class="pi pi-arrow-left"></i>
          </div>
          <div class="ayah-input">
            <label class="ayah-label">إلى الآية</label>
            <input
              #end
              type="number"
              [min]="1"
              (input)="helper.InputNumberRestrict(end)"
              pInputText
              placeholder="1"
              class="ayah-number-input" />
          </div>
        </div>
      </div>
      <div class="form-row">
        <div class="form-group half-width">
          <label class="form-label">
            <i class="pi pi-font"></i>
            حجم الخط: {{fontSize}}
          </label>
          <p-slider
            [(ngModel)]="fontSize"
            [min]="12"
            [max]="48"
            [step]="1"
            styleClass="custom-slider">
          </p-slider>
        </div>

        <div class="form-group half-width">
          <label class="form-label">
            <i class="pi pi-video"></i>
            فيديو الخلفية
          </label>
          <p-button
            [label]="getPickedVideo() ?? 'فيديو عشوائي'"
            severity="primary"
            [outlined]="true"
            icon="pi pi-images"
            (onClick)="videoPickerVisible = true"
            styleClass="video-picker-btn">
          </p-button>
        </div>
      </div>



      <div class="generate-section">
        <p-button
          label="إنشاء الفيديو"
          [disabled]="currentReciterId == '' || currentSurah == '' || !start.value || !end.value"
          icon="pi pi-play"
          severity="primary"
          size="large"
          [loading]="ffmpegExecuting"
          loadingIcon="pi pi-spin pi-spinner"
          (onClick)="GetAyahsAndLoadThem(GetCurrentSurahNumber(),currentReciterId,start.value,end.value)"
          styleClass="generate-btn">
        </p-button>
      </div>
    </div>
  </div>


  <div *ngIf="(loadedAudio || firstLoad) && !ffmpegExecuting;" class="row my-5 justify-content-center mx-auto" style="width: 90%;">

    <div *ngIf="videoURL != ''" class="col-12 d-flex flex-column align-items-center">
      <video [src]="videoURL" class="col-10 col-md-6 col-lg-4 mb-3" autoplay controls></video>

      <!-- YouTube Upload Buttons -->
      <div class="d-flex gap-2 flex-wrap justify-content-center">
        <p-button
          *ngIf="!youtubeService.isAuthenticated()"
          label="تسجيل الدخول إلى YouTube"
          icon="pi pi-youtube"
          styleClass="p-button-danger"
          (onClick)="showAuthDialog()">
        </p-button>

        <p-button
          *ngIf="youtubeService.isAuthenticated()"
          label="نشر على YouTube"
          icon="pi pi-upload"
          styleClass="p-button-danger"
          (onClick)="showUploadDialog()">
        </p-button>

        <p-button
          *ngIf="youtubeService.isAuthenticated()"
          [label]="'مرحباً، ' + (youtubeService.getCurrentUser()?.name || 'المستخدم')"
          icon="pi pi-user"
          styleClass="p-button-outlined p-button-secondary"
          (onClick)="showAuthDialog()">
        </p-button>
      </div>
    </div>
  </div>

</div>
<ng-template #progress>
  <div class="d-flex justify-content-center align-items-center" style="height: 85dvh;width: 100dvw;">

    <div class="mx-auto w-50">
      <span class="app-title">Loading {{currentLoading.name}} </span>
      <p-toast></p-toast>
      <p-progressBar  [value]="currentLoading.value"></p-progressBar>
  </div>
  </div>
</ng-template>


<div *ngIf="ffmpegExecuting" class="row my-5 justify-content-center mx-auto" style="width: 90%">

  <div class="col-10 col-md-6 col-lg-4">
    <div class="d-flex justify-content-between"><span>{{this.executingProgressLabel()}}</span> <span>Elapsed Time <span class="fw-bold app-title">{{this.executingTime + ' s'}}</span></span></div>
    <p-toast></p-toast>
    <p-progressBar [ariaValueMin]="0" [ariaValueMax]="100" [value]="executingProgress()"></p-progressBar>
</div>
</div>



<p-dialog header="Background Video Gallery" [(visible)]="videoPickerVisible" [resizable]="true" [breakpoints]="{ '960px': '75vw' }" [style]="{width: '50vw'}">
  <app-videos-dialog (pickedVideo)="pickedVideo = $event;videoPickerVisible=false"></app-videos-dialog>
</p-dialog>

<!-- YouTube Authentication Dialog -->
<app-auth-dialog
  [(visible)]="authDialogVisible"
  (authSuccess)="onAuthSuccess()">
</app-auth-dialog>

<!-- YouTube Upload Dialog -->
<app-upload-dialog
  [(visible)]="uploadDialogVisible"
  [videoBlob]="currentVideoBlob"
  [surahInfo]="currentSurahInfo"
  (uploadComplete)="onUploadComplete($event)">
</app-upload-dialog>

<!-- Sidebar -->
<app-sidebar
  [(visible)]="sidebarVisible"
  (menuItemClick)="onSidebarMenuClick($event)">
</app-sidebar>

<!-- Video Settings Dialog -->
<app-video-settings
  [(visible)]="videoSettingsVisible"
  (settingsSaved)="onVideoSettingsSaved($event)">
</app-video-settings>

<!-- Terms Dialog -->
<app-terms-dialog
  [(visible)]="termsVisible">
</app-terms-dialog>

<!-- Privacy Dialog -->
<app-privacy-dialog
  [(visible)]="privacyVisible">
</app-privacy-dialog>

<!-- Security Warning -->
<app-security-warning></app-security-warning>

<!-- Link Shortener -->
<app-link-shortener></app-link-shortener>

</div>
