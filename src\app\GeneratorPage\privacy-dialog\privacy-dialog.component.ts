import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'app-privacy-dialog',
  templateUrl: './privacy-dialog.component.html',
  styleUrls: ['./privacy-dialog.component.scss']
})
export class PrivacyDialogComponent {
  @Input() visible: boolean = false;
  @Output() visibleChange = new EventEmitter<boolean>();

  onHide(): void {
    this.visible = false;
    this.visibleChange.emit(false);
  }
}
