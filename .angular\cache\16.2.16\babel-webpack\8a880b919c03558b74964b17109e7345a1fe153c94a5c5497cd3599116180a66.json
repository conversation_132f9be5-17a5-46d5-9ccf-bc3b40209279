{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"primeng/sidebar\";\nfunction SidebarComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"h3\", 10);\n    i0.ɵɵtext(2, \"\\u0627\\u0644\\u0642\\u0627\\u0626\\u0645\\u0629 \\u0627\\u0644\\u0631\\u0626\\u064A\\u0633\\u064A\\u0629\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SidebarComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_div_4_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r4);\n      const item_r2 = restoredCtx.$implicit;\n      return i0.ɵɵresetView(item_r2.command());\n    });\n    i0.ɵɵelement(1, \"i\", 12);\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"i\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(item_r2.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r2.label);\n  }\n}\nexport let SidebarComponent = /*#__PURE__*/(() => {\n  class SidebarComponent {\n    constructor() {\n      this.visible = false;\n      this.visibleChange = new EventEmitter();\n      this.menuItemClick = new EventEmitter();\n      this.menuItems = [{\n        id: 'video-settings',\n        label: 'إعدادات الفيديو',\n        icon: 'pi pi-cog',\n        command: () => this.onMenuClick('video-settings')\n      }, {\n        id: 'privacy-policy',\n        label: 'سياسة الخصوصية',\n        icon: 'pi pi-lock',\n        command: () => this.onMenuClick('privacy-policy')\n      }, {\n        id: 'support',\n        label: 'مساعد ودعم',\n        icon: 'pi pi-comments',\n        command: () => this.onMenuClick('support')\n      }, {\n        id: 'terms',\n        label: 'شروط الاستخدام',\n        icon: 'pi pi-file',\n        command: () => this.onMenuClick('terms')\n      }];\n    }\n    onMenuClick(itemId) {\n      this.menuItemClick.emit(itemId);\n      this.visible = false;\n      this.visibleChange.emit(false);\n    }\n    onHide() {\n      this.visible = false;\n      this.visibleChange.emit(false);\n    }\n    static {\n      this.ɵfac = function SidebarComponent_Factory(t) {\n        return new (t || SidebarComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SidebarComponent,\n        selectors: [[\"app-sidebar\"]],\n        inputs: {\n          visible: \"visible\"\n        },\n        outputs: {\n          visibleChange: \"visibleChange\",\n          menuItemClick: \"menuItemClick\"\n        },\n        decls: 11,\n        vars: 4,\n        consts: [[\"position\", \"right\", \"styleClass\", \"custom-sidebar\", 3, \"visible\", \"modal\", \"dismissible\", \"visibleChange\", \"onHide\"], [\"pTemplate\", \"header\"], [1, \"sidebar-content\"], [1, \"menu-items\"], [\"class\", \"menu-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"sidebar-footer\"], [1, \"app-info\"], [1, \"pi\", \"pi-info-circle\"], [1, \"version\"], [1, \"sidebar-header\"], [1, \"sidebar-title\"], [1, \"menu-item\", 3, \"click\"], [1, \"menu-icon\"], [1, \"menu-label\"], [1, \"pi\", \"pi-chevron-left\", \"menu-arrow\"]],\n        template: function SidebarComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"p-sidebar\", 0);\n            i0.ɵɵlistener(\"visibleChange\", function SidebarComponent_Template_p_sidebar_visibleChange_0_listener($event) {\n              return ctx.visible = $event;\n            })(\"onHide\", function SidebarComponent_Template_p_sidebar_onHide_0_listener() {\n              return ctx.onHide();\n            });\n            i0.ɵɵtemplate(1, SidebarComponent_ng_template_1_Template, 3, 0, \"ng-template\", 1);\n            i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3);\n            i0.ɵɵtemplate(4, SidebarComponent_div_4_Template, 5, 3, \"div\", 4);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"div\", 5)(6, \"p\", 6);\n            i0.ɵɵelement(7, \"i\", 7);\n            i0.ɵɵtext(8, \" \\u0645\\u0648\\u0644\\u062F \\u0645\\u0642\\u0627\\u0637\\u0639 \\u0627\\u0644\\u0642\\u0631\\u0622\\u0646 \\u0627\\u0644\\u0643\\u0631\\u064A\\u0645 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"p\", 8);\n            i0.ɵɵtext(10, \"\\u0627\\u0644\\u0625\\u0635\\u062F\\u0627\\u0631 2.0\");\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"visible\", ctx.visible)(\"modal\", true)(\"dismissible\", true);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngForOf\", ctx.menuItems);\n          }\n        },\n        dependencies: [i1.NgForOf, i2.PrimeTemplate, i3.Sidebar],\n        styles: [\".custom-sidebar{width:320px!important;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);color:#fff}  .custom-sidebar .p-sidebar-header{background:rgba(255,255,255,.1);border-bottom:1px solid rgba(255,255,255,.2);padding:1.5rem}  .custom-sidebar .p-sidebar-content{padding:0;height:100%;display:flex;flex-direction:column}.sidebar-header[_ngcontent-%COMP%]{text-align:center}.sidebar-header[_ngcontent-%COMP%]   .sidebar-title[_ngcontent-%COMP%]{margin:0;font-size:1.5rem;font-weight:600;color:#fff}.sidebar-content[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;justify-content:space-between;padding:1rem 0}.menu-items[_ngcontent-%COMP%]{flex:1}.menu-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:1rem 1.5rem;cursor:pointer;transition:all .3s ease;border-bottom:1px solid rgba(255,255,255,.1)}.menu-item[_ngcontent-%COMP%]:hover{background:rgba(255,255,255,.1);transform:translate(-5px)}.menu-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%]{font-size:1.2rem;margin-left:1rem;color:gold}.menu-item[_ngcontent-%COMP%]   .menu-label[_ngcontent-%COMP%]{flex:1;font-size:1.1rem;font-weight:500}.menu-item[_ngcontent-%COMP%]   .menu-arrow[_ngcontent-%COMP%]{font-size:.9rem;opacity:.7}.sidebar-footer[_ngcontent-%COMP%]{padding:1.5rem;text-align:center;border-top:1px solid rgba(255,255,255,.2)}.sidebar-footer[_ngcontent-%COMP%]   .app-info[_ngcontent-%COMP%]{margin:0 0 .5rem;font-size:.9rem;opacity:.8}.sidebar-footer[_ngcontent-%COMP%]   .app-info[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-left:.5rem}.sidebar-footer[_ngcontent-%COMP%]   .version[_ngcontent-%COMP%]{margin:0;font-size:.8rem;opacity:.6}\"]\n      });\n    }\n  }\n  return SidebarComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}