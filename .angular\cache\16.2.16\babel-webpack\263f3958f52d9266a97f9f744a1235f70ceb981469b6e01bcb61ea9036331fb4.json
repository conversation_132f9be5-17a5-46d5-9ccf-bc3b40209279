{"ast": null, "code": "import { FFMessageType } from \"./const.js\";\nimport { getMessageID } from \"./utils.js\";\nimport { ERROR_TERMINATED, ERROR_NOT_LOADED } from \"./errors.js\";\n/**\n * Provides APIs to interact with ffmpeg web worker.\n *\n * @example\n * ```ts\n * const ffmpeg = new FFmpeg();\n * ```\n */\nexport class FFmpeg {\n  #worker = null;\n  /**\n   * #resolves and #rejects tracks Promise resolves and rejects to\n   * be called when we receive message from web worker.\n   */\n  #resolves = {};\n  #rejects = {};\n  #logEventCallbacks = [];\n  #progressEventCallbacks = [];\n  loaded = false;\n  /**\n   * register worker message event handlers.\n   */\n  #registerHandlers = () => {\n    if (this.#worker) {\n      this.#worker.onmessage = ({\n        data: {\n          id,\n          type,\n          data\n        }\n      }) => {\n        switch (type) {\n          case FFMessageType.LOAD:\n            this.loaded = true;\n            this.#resolves[id](data);\n            break;\n          case FFMessageType.MOUNT:\n          case FFMessageType.UNMOUNT:\n          case FFMessageType.EXEC:\n          case FFMessageType.FFPROBE:\n          case FFMessageType.WRITE_FILE:\n          case FFMessageType.READ_FILE:\n          case FFMessageType.DELETE_FILE:\n          case FFMessageType.RENAME:\n          case FFMessageType.CREATE_DIR:\n          case FFMessageType.LIST_DIR:\n          case FFMessageType.DELETE_DIR:\n            this.#resolves[id](data);\n            break;\n          case FFMessageType.LOG:\n            this.#logEventCallbacks.forEach(f => f(data));\n            break;\n          case FFMessageType.PROGRESS:\n            this.#progressEventCallbacks.forEach(f => f(data));\n            break;\n          case FFMessageType.ERROR:\n            this.#rejects[id](data);\n            break;\n        }\n        delete this.#resolves[id];\n        delete this.#rejects[id];\n      };\n    }\n  };\n  /**\n   * Generic function to send messages to web worker.\n   */\n  #send = ({\n    type,\n    data\n  }, trans = [], signal) => {\n    if (!this.#worker) {\n      return Promise.reject(ERROR_NOT_LOADED);\n    }\n    return new Promise((resolve, reject) => {\n      const id = getMessageID();\n      this.#worker && this.#worker.postMessage({\n        id,\n        type,\n        data\n      }, trans);\n      this.#resolves[id] = resolve;\n      this.#rejects[id] = reject;\n      signal?.addEventListener(\"abort\", () => {\n        reject(new DOMException(`Message # ${id} was aborted`, \"AbortError\"));\n      }, {\n        once: true\n      });\n    });\n  };\n  on(event, callback) {\n    if (event === \"log\") {\n      this.#logEventCallbacks.push(callback);\n    } else if (event === \"progress\") {\n      this.#progressEventCallbacks.push(callback);\n    }\n  }\n  off(event, callback) {\n    if (event === \"log\") {\n      this.#logEventCallbacks = this.#logEventCallbacks.filter(f => f !== callback);\n    } else if (event === \"progress\") {\n      this.#progressEventCallbacks = this.#progressEventCallbacks.filter(f => f !== callback);\n    }\n  }\n  /**\n   * Loads ffmpeg-core inside web worker. It is required to call this method first\n   * as it initializes WebAssembly and other essential variables.\n   *\n   * @category FFmpeg\n   * @returns `true` if ffmpeg core is loaded for the first time.\n   */\n  load = ({\n    classWorkerURL,\n    ...config\n  } = {}, {\n    signal\n  } = {}) => {\n    if (!this.#worker) {\n      this.#worker = classWorkerURL ? new Worker(new URL(classWorkerURL, import.meta.url), {\n        type: \"module\"\n      }) :\n      // We need to duplicated the code here to enable webpack\n      // to bundle worekr.js here.\n      new Worker(new URL(\"./worker.js\", import.meta.url), {\n        type: \"module\"\n      });\n      this.#registerHandlers();\n    }\n    return this.#send({\n      type: FFMessageType.LOAD,\n      data: config\n    }, undefined, signal);\n  };\n  /**\n   * Execute ffmpeg command.\n   *\n   * @remarks\n   * To avoid common I/O issues, [\"-nostdin\", \"-y\"] are prepended to the args\n   * by default.\n   *\n   * @example\n   * ```ts\n   * const ffmpeg = new FFmpeg();\n   * await ffmpeg.load();\n   * await ffmpeg.writeFile(\"video.avi\", ...);\n   * // ffmpeg -i video.avi video.mp4\n   * await ffmpeg.exec([\"-i\", \"video.avi\", \"video.mp4\"]);\n   * const data = ffmpeg.readFile(\"video.mp4\");\n   * ```\n   *\n   * @returns `0` if no error, `!= 0` if timeout (1) or error.\n   * @category FFmpeg\n   */\n  exec = ( /** ffmpeg command line args */\n  args,\n  /**\n   * milliseconds to wait before stopping the command execution.\n   *\n   * @defaultValue -1\n   */\n  timeout = -1, {\n    signal\n  } = {}) => this.#send({\n    type: FFMessageType.EXEC,\n    data: {\n      args,\n      timeout\n    }\n  }, undefined, signal);\n  /**\n   * Execute ffprobe command.\n   *\n   * @example\n   * ```ts\n   * const ffmpeg = new FFmpeg();\n   * await ffmpeg.load();\n   * await ffmpeg.writeFile(\"video.avi\", ...);\n   * // Getting duration of a video in seconds: ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 video.avi -o output.txt\n   * await ffmpeg.ffprobe([\"-v\", \"error\", \"-show_entries\", \"format=duration\", \"-of\", \"default=noprint_wrappers=1:nokey=1\", \"video.avi\", \"-o\", \"output.txt\"]);\n   * const data = ffmpeg.readFile(\"output.txt\");\n   * ```\n   *\n   * @returns `0` if no error, `!= 0` if timeout (1) or error.\n   * @category FFmpeg\n   */\n  ffprobe = ( /** ffprobe command line args */\n  args,\n  /**\n   * milliseconds to wait before stopping the command execution.\n   *\n   * @defaultValue -1\n   */\n  timeout = -1, {\n    signal\n  } = {}) => this.#send({\n    type: FFMessageType.FFPROBE,\n    data: {\n      args,\n      timeout\n    }\n  }, undefined, signal);\n  /**\n   * Terminate all ongoing API calls and terminate web worker.\n   * `FFmpeg.load()` must be called again before calling any other APIs.\n   *\n   * @category FFmpeg\n   */\n  terminate = () => {\n    const ids = Object.keys(this.#rejects);\n    // rejects all incomplete Promises.\n    for (const id of ids) {\n      this.#rejects[id](ERROR_TERMINATED);\n      delete this.#rejects[id];\n      delete this.#resolves[id];\n    }\n    if (this.#worker) {\n      this.#worker.terminate();\n      this.#worker = null;\n      this.loaded = false;\n    }\n  };\n  /**\n   * Write data to ffmpeg.wasm.\n   *\n   * @example\n   * ```ts\n   * const ffmpeg = new FFmpeg();\n   * await ffmpeg.load();\n   * await ffmpeg.writeFile(\"video.avi\", await fetchFile(\"../video.avi\"));\n   * await ffmpeg.writeFile(\"text.txt\", \"hello world\");\n   * ```\n   *\n   * @category File System\n   */\n  writeFile = (path, data, {\n    signal\n  } = {}) => {\n    const trans = [];\n    if (data instanceof Uint8Array) {\n      trans.push(data.buffer);\n    }\n    return this.#send({\n      type: FFMessageType.WRITE_FILE,\n      data: {\n        path,\n        data\n      }\n    }, trans, signal);\n  };\n  mount = (fsType, options, mountPoint) => {\n    const trans = [];\n    return this.#send({\n      type: FFMessageType.MOUNT,\n      data: {\n        fsType,\n        options,\n        mountPoint\n      }\n    }, trans);\n  };\n  unmount = mountPoint => {\n    const trans = [];\n    return this.#send({\n      type: FFMessageType.UNMOUNT,\n      data: {\n        mountPoint\n      }\n    }, trans);\n  };\n  /**\n   * Read data from ffmpeg.wasm.\n   *\n   * @example\n   * ```ts\n   * const ffmpeg = new FFmpeg();\n   * await ffmpeg.load();\n   * const data = await ffmpeg.readFile(\"video.mp4\");\n   * ```\n   *\n   * @category File System\n   */\n  readFile = (path,\n  /**\n   * File content encoding, supports two encodings:\n   * - utf8: read file as text file, return data in string type.\n   * - binary: read file as binary file, return data in Uint8Array type.\n   *\n   * @defaultValue binary\n   */\n  encoding = \"binary\", {\n    signal\n  } = {}) => this.#send({\n    type: FFMessageType.READ_FILE,\n    data: {\n      path,\n      encoding\n    }\n  }, undefined, signal);\n  /**\n   * Delete a file.\n   *\n   * @category File System\n   */\n  deleteFile = (path, {\n    signal\n  } = {}) => this.#send({\n    type: FFMessageType.DELETE_FILE,\n    data: {\n      path\n    }\n  }, undefined, signal);\n  /**\n   * Rename a file or directory.\n   *\n   * @category File System\n   */\n  rename = (oldPath, newPath, {\n    signal\n  } = {}) => this.#send({\n    type: FFMessageType.RENAME,\n    data: {\n      oldPath,\n      newPath\n    }\n  }, undefined, signal);\n  /**\n   * Create a directory.\n   *\n   * @category File System\n   */\n  createDir = (path, {\n    signal\n  } = {}) => this.#send({\n    type: FFMessageType.CREATE_DIR,\n    data: {\n      path\n    }\n  }, undefined, signal);\n  /**\n   * List directory contents.\n   *\n   * @category File System\n   */\n  listDir = (path, {\n    signal\n  } = {}) => this.#send({\n    type: FFMessageType.LIST_DIR,\n    data: {\n      path\n    }\n  }, undefined, signal);\n  /**\n   * Delete an empty directory.\n   *\n   * @category File System\n   */\n  deleteDir = (path, {\n    signal\n  } = {}) => this.#send({\n    type: FFMessageType.DELETE_DIR,\n    data: {\n      path\n    }\n  }, undefined, signal);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}