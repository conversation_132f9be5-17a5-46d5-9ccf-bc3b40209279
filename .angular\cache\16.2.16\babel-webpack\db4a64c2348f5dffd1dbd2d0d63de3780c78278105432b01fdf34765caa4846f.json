{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/\\u062A\\u0637\\u0628\\u064A\\u0642 \\u0646\\u0627\\u0634\\u0631 \\u0627\\u064A\\u0627\\u062A \\u0642\\u0626\\u0627\\u0646\\u064A\\u0629/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { from, throwError } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport { GoogleGenerativeAI } from '@google/generative-ai';\nimport { API_KEYS } from '../config/api-keys';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./error-handler.service\";\nexport let GeminiService = /*#__PURE__*/(() => {\n  class GeminiService {\n    constructor(errorHandler) {\n      this.errorHandler = errorHandler;\n      this.API_KEY = API_KEYS.GEMINI_API_KEY;\n      this.currentModel = 'gemini-2.5-flash';\n      // Available Gemini models\n      this.availableModels = [{\n        id: 'gemini-2.5-pro',\n        name: 'Gemini 2.5 Pro',\n        description: 'النموذج الأكثر تقدماً مع قدرات محسنة',\n        isPaid: true,\n        isAvailable: true\n      }, {\n        id: 'gemini-2.5-flash',\n        name: 'Gemini 2.5 Flash',\n        description: 'نموذج سريع ومجاني مع أداء جيد',\n        isPaid: false,\n        isAvailable: true\n      }, {\n        id: 'gemini-2.0-flash-exp',\n        name: 'Gemini 2.0 Flash (تجريبي)',\n        description: 'نموذج تجريبي مجاني',\n        isPaid: false,\n        isAvailable: true\n      }];\n      this.initializeService();\n    }\n    initializeService() {\n      if (this.isApiKeyConfigured()) {\n        this.genAI = new GoogleGenerativeAI(this.API_KEY);\n        this.updateModel(this.currentModel);\n      }\n    }\n    updateApiKey(apiKey) {\n      if (apiKey && apiKey.trim().length > 0) {\n        this.genAI = new GoogleGenerativeAI(apiKey);\n        this.updateModel(this.currentModel);\n      }\n    }\n    updateModel(modelId) {\n      try {\n        this.currentModel = modelId;\n        this.model = this.genAI.getGenerativeModel({\n          model: modelId\n        });\n      } catch (error) {\n        console.error('Error updating model:', error);\n        throw new Error(`فشل في تحديث النموذج: ${modelId}`);\n      }\n    }\n    generateVideoContent(surahInfo) {\n      return from(this.performContentGeneration(surahInfo)).pipe(catchError(error => {\n        this.errorHandler.handleError(error, 'gemini-generation');\n        return throwError(() => error);\n      }));\n    }\n    performContentGeneration(surahInfo) {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        const prompt = _this.buildPrompt(surahInfo);\n        try {\n          const result = yield _this.model.generateContent(prompt);\n          const response = yield result.response;\n          const text = response.text();\n          return _this.parseGeneratedContent(text);\n        } catch (error) {\n          throw new Error(`Failed to generate content: ${error}`);\n        }\n      })();\n    }\n    buildPrompt(surahInfo) {\n      const ayahRange = surahInfo.startAyah === surahInfo.endAyah ? `الآية ${surahInfo.startAyah}` : `الآيات ${surahInfo.startAyah} إلى ${surahInfo.endAyah}`;\n      const ayahTexts = surahInfo.ayahTexts.join(' ');\n      return `\nأنت خبير في القرآن الكريم ومتخصص في إنشاء محتوى إسلامي جذاب لمنصات التواصل الاجتماعي.\n\nمعلومات المقطع:\n- السورة: ${surahInfo.surahName}\n- رقم السورة: ${surahInfo.surahNumber}\n- ${ayahRange}\n- نص الآيات: ${ayahTexts}\n\nالمطلوب منك إنشاء:\n1. عنوان جذاب ومناسب للفيديو (لا يزيد عن 100 حرف)\n2. وصف تفصيلي للفيديو (200-500 كلمة)\n3. علامات هاشتاج مناسبة (10-15 علامة)\n\nيجب أن يكون المحتوى:\n- محترم ومناسب للمحتوى الديني\n- جذاب للمشاهدين\n- يحتوي على معلومات مفيدة عن السورة والآيات\n- مناسب لمنصة يوتيوب\n\nيرجى تنسيق الإجابة كما يلي:\nTITLE: [العنوان هنا]\nDESCRIPTION: [الوصف هنا]\nTAGS: [العلامات مفصولة بفواصل]\n`;\n    }\n    parseGeneratedContent(text) {\n      const lines = text.split('\\n');\n      let title = '';\n      let description = '';\n      let tags = [];\n      for (const line of lines) {\n        if (line.startsWith('TITLE:')) {\n          title = line.replace('TITLE:', '').trim();\n        } else if (line.startsWith('DESCRIPTION:')) {\n          description = line.replace('DESCRIPTION:', '').trim();\n        } else if (line.startsWith('TAGS:')) {\n          const tagString = line.replace('TAGS:', '').trim();\n          tags = tagString.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);\n        }\n      }\n      // Fallback parsing if the format is not followed exactly\n      if (!title || !description) {\n        const fallback = this.fallbackParsing(text);\n        title = title || fallback.title;\n        description = description || fallback.description;\n        tags = tags.length > 0 ? tags : fallback.tags;\n      }\n      return {\n        title: title || 'مقطع قرآني مميز',\n        description: description || 'استمع إلى تلاوة عذبة من القرآن الكريم',\n        tags: tags.length > 0 ? tags : ['القرآن', 'تلاوة', 'إسلام', 'قرآن_كريم']\n      };\n    }\n    fallbackParsing(text) {\n      // Simple fallback parsing\n      const lines = text.split('\\n').filter(line => line.trim().length > 0);\n      let title = lines[0] || 'مقطع قرآني مميز';\n      if (title.length > 100) {\n        title = title.substring(0, 97) + '...';\n      }\n      let description = lines.slice(1, -1).join(' ') || 'استمع إلى تلاوة عذبة من القرآن الكريم';\n      const defaultTags = ['القرآن', 'تلاوة', 'إسلام', 'قرآن_كريم', 'آيات', 'دين', 'مسلم'];\n      return {\n        title,\n        description,\n        tags: defaultTags\n      };\n    }\n    generateTitleOnly(surahInfo) {\n      const prompt = `\nأنشئ عنوان جذاب لفيديو قرآني يحتوي على:\n- السورة: ${surahInfo.surahName}\n- الآيات: ${surahInfo.startAyah} إلى ${surahInfo.endAyah}\n\nالعنوان يجب أن يكون:\n- لا يزيد عن 100 حرف\n- جذاب ومناسب\n- يحتوي على اسم السورة\n\nأعطني العنوان فقط بدون أي إضافات.\n`;\n      return from(this.model.generateContent(prompt)).pipe(map(result => result.response.text().trim()), catchError(error => {\n        console.error('Title generation failed:', error);\n        return throwError(() => error);\n      }));\n    }\n    generateDescriptionOnly(surahInfo) {\n      const prompt = `\nأنشئ وصف تفصيلي لفيديو قرآني يحتوي على:\n- السورة: ${surahInfo.surahName}\n- الآيات: ${surahInfo.startAyah} إلى ${surahInfo.endAyah}\n- نص الآيات: ${surahInfo.ayahTexts.join(' ')}\n\nالوصف يجب أن يكون:\n- بين 200-500 كلمة\n- يحتوي على معلومات مفيدة عن السورة\n- مناسب لمنصة يوتيوب\n- محترم ومناسب للمحتوى الديني\n\nأعطني الوصف فقط بدون أي إضافات.\n`;\n      return from(this.model.generateContent(prompt)).pipe(map(result => result.response.text().trim()), catchError(error => {\n        console.error('Description generation failed:', error);\n        return throwError(() => error);\n      }));\n    }\n    isApiKeyConfigured() {\n      return this.API_KEY !== 'YOUR_GEMINI_API_KEY_HERE' && this.API_KEY.length > 0;\n    }\n    getAvailableModels() {\n      return this.availableModels;\n    }\n    getCurrentModel() {\n      return this.currentModel;\n    }\n    checkModelAvailability(modelId, apiKey) {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          const testGenAI = apiKey ? new GoogleGenerativeAI(apiKey) : _this2.genAI;\n          const testModel = testGenAI.getGenerativeModel({\n            model: modelId\n          });\n          // Test with a simple prompt\n          const result = yield testModel.generateContent('Test');\n          const response = yield result.response;\n          response.text(); // Ensure we can get text\n          return {\n            model: modelId,\n            available: true\n          };\n        } catch (error) {\n          let errorMessage = 'خطأ غير معروف';\n          if (error.message?.includes('API_KEY_INVALID')) {\n            errorMessage = 'مفتاح API غير صحيح';\n          } else if (error.message?.includes('PERMISSION_DENIED')) {\n            errorMessage = 'ليس لديك صلاحية للوصول لهذا النموذج';\n          } else if (error.message?.includes('MODEL_NOT_FOUND')) {\n            errorMessage = 'النموذج غير متوفر';\n          } else if (error.message?.includes('QUOTA_EXCEEDED')) {\n            errorMessage = 'تم تجاوز الحد المسموح للاستخدام';\n          }\n          return {\n            model: modelId,\n            available: false,\n            error: errorMessage\n          };\n        }\n      })();\n    }\n    testConnection(apiKey, modelId) {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          const testApiKey = apiKey || _this3.API_KEY;\n          const testModelId = modelId || _this3.currentModel;\n          if (!testApiKey || testApiKey === 'YOUR_GEMINI_API_KEY_HERE') {\n            throw new Error('مفتاح API غير صحيح');\n          }\n          const availability = yield _this3.checkModelAvailability(testModelId, testApiKey);\n          return availability.available;\n        } catch (error) {\n          console.error('Connection test failed:', error);\n          return false;\n        }\n      })();\n    }\n    getModelInfo(modelId) {\n      return this.availableModels.find(model => model.id === modelId);\n    }\n    isModelPaid(modelId) {\n      const model = this.getModelInfo(modelId);\n      return model?.isPaid || false;\n    }\n    static {\n      this.ɵfac = function GeminiService_Factory(t) {\n        return new (t || GeminiService)(i0.ɵɵinject(i1.ErrorHandlerService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: GeminiService,\n        factory: GeminiService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return GeminiService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}