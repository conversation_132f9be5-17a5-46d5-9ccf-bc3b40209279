import { Injectable } from '@angular/core';
import { MessageService } from 'primeng/api';

export interface ErrorInfo {
  title: string;
  message: string;
  type: 'error' | 'warning' | 'info' | 'success';
}

@Injectable({
  providedIn: 'root'
})
export class ErrorHandlerService {

  constructor(private messageService: MessageService) { }

  handleError(error: any, context?: string): void {
    console.error('Error occurred:', error, 'Context:', context);
    
    const errorInfo = this.parseError(error, context);
    this.showMessage(errorInfo);
  }

  private parseError(error: any, context?: string): ErrorInfo {
    // YouTube API Errors
    if (context === 'youtube-auth') {
      return {
        title: 'خطأ في المصادقة',
        message: 'فشل في تسجيل الدخول إلى YouTube. يرجى التأكد من إعدادات Google API والمحاولة مرة أخرى.',
        type: 'error'
      };
    }

    if (context === 'youtube-upload') {
      if (error.message?.includes('quota')) {
        return {
          title: 'تجاوز الحد المسموح',
          message: 'تم تجاوز الحد اليومي لرفع الفيديوهات. يرجى المحاولة غداً.',
          type: 'warning'
        };
      }
      
      if (error.message?.includes('file size')) {
        return {
          title: 'حجم الملف كبير',
          message: 'حجم الفيديو كبير جداً. الحد الأقصى المسموح هو 128GB.',
          type: 'error'
        };
      }

      return {
        title: 'خطأ في رفع الفيديو',
        message: 'فشل في رفع الفيديو إلى YouTube. يرجى التحقق من الاتصال بالإنترنت والمحاولة مرة أخرى.',
        type: 'error'
      };
    }

    // Gemini AI Errors
    if (context === 'gemini-generation') {
      if (error.message?.includes('API key')) {
        return {
          title: 'مفتاح API غير صحيح',
          message: 'مفتاح Gemini API غير صحيح أو غير مفعل. يرجى التحقق من الإعدادات.',
          type: 'error'
        };
      }

      if (error.message?.includes('quota')) {
        return {
          title: 'تجاوز الحد المسموح',
          message: 'تم تجاوز الحد المسموح لاستخدام Gemini AI. يرجى المحاولة لاحقاً.',
          type: 'warning'
        };
      }

      return {
        title: 'خطأ في توليد المحتوى',
        message: 'فشل في توليد المحتوى بالذكاء الاصطناعي. يرجى المحاولة مرة أخرى أو إدخال المحتوى يدوياً.',
        type: 'error'
      };
    }

    // Video Generation Errors
    if (context === 'video-generation') {
      if (error.message?.includes('FFmpeg')) {
        return {
          title: 'خطأ في معالجة الفيديو',
          message: 'فشل في معالجة الفيديو. يرجى التحقق من الملفات المطلوبة والمحاولة مرة أخرى.',
          type: 'error'
        };
      }

      return {
        title: 'خطأ في إنتاج الفيديو',
        message: 'حدث خطأ أثناء إنتاج الفيديو. يرجى المحاولة مرة أخرى.',
        type: 'error'
      };
    }

    // Network Errors
    if (error.message?.includes('network') || error.message?.includes('fetch')) {
      return {
        title: 'خطأ في الاتصال',
        message: 'فشل في الاتصال بالخادم. يرجى التحقق من الاتصال بالإنترنت والمحاولة مرة أخرى.',
        type: 'error'
      };
    }

    // Generic Error
    return {
      title: 'خطأ غير متوقع',
      message: 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى أو إعادة تحميل الصفحة.',
      type: 'error'
    };
  }

  showMessage(errorInfo: ErrorInfo): void {
    this.messageService.add({
      severity: errorInfo.type,
      summary: errorInfo.title,
      detail: errorInfo.message,
      life: errorInfo.type === 'success' ? 3000 : 5000
    });
  }

  showSuccess(title: string, message: string): void {
    this.showMessage({
      title,
      message,
      type: 'success'
    });
  }

  showWarning(title: string, message: string): void {
    this.showMessage({
      title,
      message,
      type: 'warning'
    });
  }

  showInfo(title: string, message: string): void {
    this.showMessage({
      title,
      message,
      type: 'info'
    });
  }

  // Validation helpers
  validateApiKeys(): { isValid: boolean; missingKeys: string[] } {
    const missingKeys: string[] = [];
    
    // Check if API keys are configured (not default values)
    const apiKeys = {
      'Google Client ID': 'YOUR_GOOGLE_CLIENT_ID_HERE',
      'Google API Key': 'YOUR_GOOGLE_API_KEY_HERE',
      'Gemini API Key': 'YOUR_GEMINI_API_KEY_HERE'
    };

    // This would need to be implemented based on your actual API key checking logic
    // For now, we'll assume they need to be checked in the respective services

    return {
      isValid: missingKeys.length === 0,
      missingKeys
    };
  }
}
