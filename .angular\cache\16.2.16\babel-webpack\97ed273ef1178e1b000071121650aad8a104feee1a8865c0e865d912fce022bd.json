{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/\\u062A\\u0637\\u0628\\u064A\\u0642 \\u0646\\u0627\\u0634\\u0631 \\u0627\\u064A\\u0627\\u062A \\u0642\\u0626\\u0627\\u0646\\u064A\\u0629/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../Services/security.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"primeng/button\";\nfunction SecurityWarningComponent_div_0_div_9_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵelement(1, \"i\", 21);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"\\u062A\\u0645 \\u0627\\u0643\\u062A\\u0634\\u0627\\u0641 VPN \\u0623\\u0648 \\u0628\\u0631\\u0648\\u0643\\u0633\\u064A\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SecurityWarningComponent_div_0_div_9_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵelement(1, \"i\", 22);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"\\u062A\\u0645 \\u0627\\u0643\\u062A\\u0634\\u0627\\u0641 \\u0623\\u062F\\u0627\\u0629 \\u062D\\u0638\\u0631 \\u0627\\u0644\\u0625\\u0639\\u0644\\u0627\\u0646\\u0627\\u062A\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SecurityWarningComponent_div_0_div_9_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵelement(1, \"i\", 23);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"\\u062A\\u0645 \\u0627\\u0643\\u062A\\u0634\\u0627\\u0641 DNS \\u0645\\u062D\\u062C\\u0648\\u0628 \\u0644\\u0644\\u0625\\u0639\\u0644\\u0627\\u0646\\u0627\\u062A\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SecurityWarningComponent_div_0_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtemplate(1, SecurityWarningComponent_div_0_div_9_div_1_Template, 4, 0, \"div\", 19);\n    i0.ɵɵtemplate(2, SecurityWarningComponent_div_0_div_9_div_2_Template, 4, 0, \"div\", 19);\n    i0.ɵɵtemplate(3, SecurityWarningComponent_div_0_div_9_div_3_Template, 4, 0, \"div\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.securityCheck.isVpnDetected);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.securityCheck.isAdBlockerDetected);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.securityCheck.isDnsBlocked);\n  }\n}\nfunction SecurityWarningComponent_div_0_li_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const instruction_r6 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", instruction_r6, \" \");\n  }\n}\nfunction SecurityWarningComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"div\", 4);\n    i0.ɵɵelement(4, \"i\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h2\", 6);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 7);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, SecurityWarningComponent_div_0_div_9_Template, 4, 3, \"div\", 8);\n    i0.ɵɵelementStart(10, \"div\", 9)(11, \"h3\");\n    i0.ɵɵtext(12, \"\\u062E\\u0637\\u0648\\u0627\\u062A \\u0627\\u0644\\u062D\\u0644:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"ol\", 10);\n    i0.ɵɵtemplate(14, SecurityWarningComponent_div_0_li_14_Template, 2, 1, \"li\", 11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 12);\n    i0.ɵɵelement(16, \"i\", 13);\n    i0.ɵɵelementStart(17, \"div\")(18, \"h4\");\n    i0.ɵɵtext(19, \"\\u0644\\u0645\\u0627\\u0630\\u0627 \\u0647\\u0630\\u0647 \\u0627\\u0644\\u0642\\u064A\\u0648\\u062F\\u061F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"p\");\n    i0.ɵɵtext(21, \" \\u0647\\u0630\\u0647 \\u0627\\u0644\\u0642\\u064A\\u0648\\u062F \\u0636\\u0631\\u0648\\u0631\\u064A\\u0629 \\u0644\\u0644\\u062D\\u0641\\u0627\\u0638 \\u0639\\u0644\\u0649 \\u0627\\u0633\\u062A\\u062F\\u0627\\u0645\\u0629 \\u0627\\u0644\\u062E\\u062F\\u0645\\u0629 \\u0648\\u062A\\u063A\\u0637\\u064A\\u0629 \\u062A\\u0643\\u0627\\u0644\\u064A\\u0641 \\u0627\\u0644\\u062A\\u0634\\u063A\\u064A\\u0644. \\u0627\\u0644\\u0625\\u0639\\u0644\\u0627\\u0646\\u0627\\u062A \\u062A\\u0633\\u0627\\u0639\\u062F\\u0646\\u0627 \\u0641\\u064A \\u062A\\u0642\\u062F\\u064A\\u0645 \\u0647\\u0630\\u0647 \\u0627\\u0644\\u062E\\u062F\\u0645\\u0629 \\u0645\\u062C\\u0627\\u0646\\u0627\\u064B \\u0644\\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\\u064A\\u0646. \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"div\", 14)(23, \"p-button\", 15);\n    i0.ɵɵlistener(\"onClick\", function SecurityWarningComponent_div_0_Template_p_button_onClick_23_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.recheckSecurity());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"p-button\", 16);\n    i0.ɵɵlistener(\"onClick\", function SecurityWarningComponent_div_0_Template_p_button_onClick_24_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.openSupportEmail());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 17)(26, \"p\");\n    i0.ɵɵtext(27, \" \\u0625\\u0630\\u0627 \\u0643\\u0646\\u062A \\u062A\\u0648\\u0627\\u062C\\u0647 \\u0635\\u0639\\u0648\\u0628\\u0629 \\u0641\\u064A \\u062D\\u0644 \\u0647\\u0630\\u0647 \\u0627\\u0644\\u0645\\u0634\\u0643\\u0644\\u0629\\u060C \\u064A\\u0631\\u062C\\u0649 \\u0627\\u0644\\u062A\\u0648\\u0627\\u0635\\u0644 \\u0645\\u0639 \\u0641\\u0631\\u064A\\u0642 \\u0627\\u0644\\u062F\\u0639\\u0645 \\u0648\\u0633\\u0646\\u0633\\u0627\\u0639\\u062F\\u0643 \\u0641\\u064A \\u0623\\u0642\\u0631\\u0628 \\u0648\\u0642\\u062A \\u0645\\u0645\\u0643\\u0646. \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r0.getWarningTitle());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getWarningMessage());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.securityCheck);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.getInstructions());\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"loading\", ctx_r0.isCheckingAgain);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"outlined\", true);\n  }\n}\nexport let SecurityWarningComponent = /*#__PURE__*/(() => {\n  class SecurityWarningComponent {\n    constructor(securityService) {\n      this.securityService = securityService;\n      this.securityCheck = null;\n      this.showWarning = false;\n      this.isCheckingAgain = false;\n    }\n    ngOnInit() {\n      this.securitySubscription = this.securityService.securityStatus$.subscribe(check => {\n        this.securityCheck = check;\n        this.showWarning = !check.allowAccess;\n      });\n    }\n    ngOnDestroy() {\n      if (this.securitySubscription) {\n        this.securitySubscription.unsubscribe();\n      }\n    }\n    recheckSecurity() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.isCheckingAgain = true;\n        try {\n          yield _this.securityService.forceSecurityCheck();\n        } finally {\n          _this.isCheckingAgain = false;\n        }\n      })();\n    }\n    getWarningTitle() {\n      if (!this.securityCheck) return '';\n      if (this.securityCheck.isVpnDetected) {\n        return 'تم اكتشاف VPN أو بروكسي';\n      }\n      if (this.securityCheck.isAdBlockerDetected) {\n        return 'تم اكتشاف أداة حظر الإعلانات';\n      }\n      if (this.securityCheck.isDnsBlocked) {\n        return 'تم اكتشاف DNS محجوب';\n      }\n      return 'مشكلة في الأمان';\n    }\n    getWarningMessage() {\n      if (!this.securityCheck) return '';\n      const messages = [];\n      if (this.securityCheck.isVpnDetected) {\n        messages.push('يرجى إيقاف تشغيل VPN أو البروكسي للمتابعة');\n      }\n      if (this.securityCheck.isAdBlockerDetected) {\n        messages.push('يرجى إيقاف تشغيل أداة حظر الإعلانات للمتابعة');\n      }\n      if (this.securityCheck.isDnsBlocked) {\n        messages.push('يرجى تغيير إعدادات DNS إلى الإعدادات الافتراضية');\n      }\n      return messages.join('\\n');\n    }\n    getInstructions() {\n      if (!this.securityCheck) return [];\n      const instructions = [];\n      if (this.securityCheck.isVpnDetected) {\n        instructions.push('إيقاف تشغيل أي تطبيق VPN أو بروكسي', 'إعادة تشغيل المتصفح', 'التأكد من عدم استخدام أي إضافات للخصوصية');\n      }\n      if (this.securityCheck.isAdBlockerDetected) {\n        instructions.push('إيقاف تشغيل AdBlock أو uBlock Origin', 'إضافة هذا الموقع إلى القائمة البيضاء', 'إعادة تحميل الصفحة');\n      }\n      if (this.securityCheck.isDnsBlocked) {\n        instructions.push('تغيير DNS إلى 8.8.8.8 أو إعدادات مزود الخدمة', 'تجنب استخدام dns.adguard.com', 'إعادة تشغيل الاتصال بالإنترنت');\n      }\n      return instructions;\n    }\n    openSupportEmail() {\n      const email = '<EMAIL>';\n      const subject = 'مشكلة في الوصول - مولد مقاطع القرآن الكريم';\n      const body = `السلام عليكم ورحمة الله وبركاته،\n\nأواجه مشكلة في الوصول للتطبيق:\n\nالسبب المكتشف: ${this.securityCheck?.blockedReason || 'غير محدد'}\n\nتفاصيل إضافية:\n- VPN مكتشف: ${this.securityCheck?.isVpnDetected ? 'نعم' : 'لا'}\n- أداة حظر إعلانات مكتشفة: ${this.securityCheck?.isAdBlockerDetected ? 'نعم' : 'لا'}\n- DNS محجوب: ${this.securityCheck?.isDnsBlocked ? 'نعم' : 'لا'}\n\nيرجى المساعدة في حل هذه المشكلة.\n\nشكراً لكم`;\n      const mailtoLink = `mailto:${email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;\n      window.open(mailtoLink, '_blank');\n    }\n    static {\n      this.ɵfac = function SecurityWarningComponent_Factory(t) {\n        return new (t || SecurityWarningComponent)(i0.ɵɵdirectiveInject(i1.SecurityService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SecurityWarningComponent,\n        selectors: [[\"app-security-warning\"]],\n        decls: 1,\n        vars: 1,\n        consts: [[\"class\", \"security-overlay\", 4, \"ngIf\"], [1, \"security-overlay\"], [1, \"security-warning-container\"], [1, \"warning-content\"], [1, \"warning-icon\"], [1, \"pi\", \"pi-exclamation-triangle\"], [1, \"warning-title\"], [1, \"warning-message\"], [\"class\", \"security-details\", 4, \"ngIf\"], [1, \"instructions-section\"], [1, \"instructions-list\"], [4, \"ngFor\", \"ngForOf\"], [1, \"important-notice\"], [1, \"pi\", \"pi-info-circle\"], [1, \"action-buttons\"], [\"label\", \"\\u0625\\u0639\\u0627\\u062F\\u0629 \\u0641\\u062D\\u0635\", \"icon\", \"pi pi-refresh\", \"severity\", \"primary\", \"loadingIcon\", \"pi pi-spin pi-spinner\", 3, \"loading\", \"onClick\"], [\"label\", \"\\u062A\\u0648\\u0627\\u0635\\u0644 \\u0645\\u0639 \\u0627\\u0644\\u062F\\u0639\\u0645\", \"icon\", \"pi pi-envelope\", \"severity\", \"secondary\", 3, \"outlined\", \"onClick\"], [1, \"help-text\"], [1, \"security-details\"], [\"class\", \"detail-item\", 4, \"ngIf\"], [1, \"detail-item\"], [1, \"pi\", \"pi-shield\", \"text-danger\"], [1, \"pi\", \"pi-ban\", \"text-danger\"], [1, \"pi\", \"pi-globe\", \"text-danger\"]],\n        template: function SecurityWarningComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, SecurityWarningComponent_div_0_Template, 28, 6, \"div\", 0);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", ctx.showWarning);\n          }\n        },\n        dependencies: [i2.NgForOf, i2.NgIf, i3.Button],\n        styles: [\".security-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100vw;height:100vh;background:rgba(0,0,0,.8);-webkit-backdrop-filter:blur(5px);backdrop-filter:blur(5px);z-index:9999;display:flex;align-items:center;justify-content:center;padding:1rem}.security-warning-container[_ngcontent-%COMP%]{background:white;border-radius:16px;box-shadow:0 20px 40px #0000004d;max-width:600px;width:100%;max-height:90vh;overflow-y:auto;animation:_ngcontent-%COMP%_slideIn .3s ease-out}@keyframes _ngcontent-%COMP%_slideIn{0%{opacity:0;transform:translateY(-50px) scale(.9)}to{opacity:1;transform:translateY(0) scale(1)}}.warning-content[_ngcontent-%COMP%]{padding:2rem;text-align:center}.warning-icon[_ngcontent-%COMP%]{margin-bottom:1.5rem}.warning-icon[_ngcontent-%COMP%]   .pi[_ngcontent-%COMP%]{font-size:4rem;color:#dc3545;animation:_ngcontent-%COMP%_pulse 2s infinite}@keyframes _ngcontent-%COMP%_pulse{0%,to{transform:scale(1)}50%{transform:scale(1.1)}}.warning-title[_ngcontent-%COMP%]{color:#dc3545;font-size:1.8rem;font-weight:700;margin-bottom:1rem}.warning-message[_ngcontent-%COMP%]{color:#495057;font-size:1.1rem;line-height:1.6;margin-bottom:2rem;white-space:pre-line}.security-details[_ngcontent-%COMP%]{background:#f8f9fa;border-radius:8px;padding:1.5rem;margin-bottom:2rem;text-align:left}.detail-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.75rem;margin-bottom:.75rem}.detail-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.detail-item[_ngcontent-%COMP%]   .pi[_ngcontent-%COMP%]{font-size:1.2rem}.detail-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:500;color:#495057}.instructions-section[_ngcontent-%COMP%]{text-align:left;margin-bottom:2rem}.instructions-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#495057;font-size:1.3rem;margin-bottom:1rem;text-align:center}.instructions-list[_ngcontent-%COMP%]{background:#e3f2fd;border-radius:8px;padding:1.5rem;margin:0}.instructions-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin-bottom:.75rem;color:#1565c0;font-weight:500}.instructions-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:last-child{margin-bottom:0}.important-notice[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:1rem;background:linear-gradient(135deg,#fff3cd 0%,#ffeaa7 100%);border:1px solid #ffc107;border-radius:8px;padding:1.5rem;margin-bottom:2rem;text-align:left}.important-notice[_ngcontent-%COMP%]   .pi[_ngcontent-%COMP%]{font-size:1.5rem;color:#856404;margin-top:.2rem;flex-shrink:0}.important-notice[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#856404;font-size:1.1rem;margin-bottom:.5rem}.important-notice[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#6c757d;margin:0;line-height:1.5}.action-buttons[_ngcontent-%COMP%]{display:flex;gap:1rem;justify-content:center;margin-bottom:1.5rem;flex-wrap:wrap}.action-buttons[_ngcontent-%COMP%]     .p-button{padding:.75rem 1.5rem;font-weight:600;border-radius:8px;transition:all .3s ease}.action-buttons[_ngcontent-%COMP%]     .p-button:hover{transform:translateY(-2px);box-shadow:0 4px 12px #00000026}.help-text[_ngcontent-%COMP%]{background:#f8f9fa;border-radius:8px;padding:1rem}.help-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#6c757d;font-size:.9rem;line-height:1.5}.text-danger[_ngcontent-%COMP%]{color:#dc3545!important}@media (max-width: 768px){.security-overlay[_ngcontent-%COMP%]{padding:.5rem}.warning-content[_ngcontent-%COMP%]{padding:1.5rem}.warning-icon[_ngcontent-%COMP%]   .pi[_ngcontent-%COMP%]{font-size:3rem}.warning-title[_ngcontent-%COMP%]{font-size:1.5rem}.warning-message[_ngcontent-%COMP%]{font-size:1rem}.action-buttons[_ngcontent-%COMP%]{flex-direction:column}.action-buttons[_ngcontent-%COMP%]     .p-button{width:100%}.important-notice[_ngcontent-%COMP%]{flex-direction:column;text-align:center}.important-notice[_ngcontent-%COMP%]   .pi[_ngcontent-%COMP%]{align-self:center}}@media (max-width: 480px){.security-warning-container[_ngcontent-%COMP%]{margin:.5rem;border-radius:12px}.warning-content[_ngcontent-%COMP%], .security-details[_ngcontent-%COMP%], .instructions-list[_ngcontent-%COMP%], .important-notice[_ngcontent-%COMP%]{padding:1rem}}\"]\n      });\n    }\n  }\n  return SecurityWarningComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}