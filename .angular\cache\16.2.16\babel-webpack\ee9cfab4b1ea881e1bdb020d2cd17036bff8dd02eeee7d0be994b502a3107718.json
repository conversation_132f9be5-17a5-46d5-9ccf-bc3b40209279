{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component } from '@angular/core';\nimport { BaseIcon } from 'primeng/baseicon';\nimport { UniqueComponentId } from 'primeng/utils';\nlet InfoCircleIcon = /*#__PURE__*/(() => {\n  class InfoCircleIcon extends BaseIcon {\n    pathId;\n    ngOnInit() {\n      this.pathId = 'url(#' + UniqueComponentId() + ')';\n    }\n    static ɵfac = /* @__PURE__ */function () {\n      let ɵInfoCircleIcon_BaseFactory;\n      return function InfoCircleIcon_Factory(t) {\n        return (ɵInfoCircleIcon_BaseFactory || (ɵInfoCircleIcon_BaseFactory = i0.ɵɵgetInheritedFactory(InfoCircleIcon)))(t || InfoCircleIcon);\n      };\n    }();\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: InfoCircleIcon,\n      selectors: [[\"InfoCircleIcon\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 6,\n      vars: 7,\n      consts: [[\"width\", \"14\", \"height\", \"14\", \"viewBox\", \"0 0 14 14\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"fill-rule\", \"evenodd\", \"clip-rule\", \"evenodd\", \"d\", \"M3.11101 12.8203C4.26215 13.5895 5.61553 14 7 14C8.85652 14 10.637 13.2625 11.9497 11.9497C13.2625 10.637 14 8.85652 14 7C14 5.61553 13.5895 4.26215 12.8203 3.11101C12.0511 1.95987 10.9579 1.06266 9.67879 0.532846C8.3997 0.00303296 6.99224 -0.13559 5.63437 0.134506C4.2765 0.404603 3.02922 1.07129 2.05026 2.05026C1.07129 3.02922 0.404603 4.2765 0.134506 5.63437C-0.13559 6.99224 0.00303296 8.3997 0.532846 9.67879C1.06266 10.9579 1.95987 12.0511 3.11101 12.8203ZM3.75918 2.14976C4.71846 1.50879 5.84628 1.16667 7 1.16667C8.5471 1.16667 10.0308 1.78125 11.1248 2.87521C12.2188 3.96918 12.8333 5.45291 12.8333 7C12.8333 8.15373 12.4912 9.28154 11.8502 10.2408C11.2093 11.2001 10.2982 11.9478 9.23232 12.3893C8.16642 12.8308 6.99353 12.9463 5.86198 12.7212C4.73042 12.4962 3.69102 11.9406 2.87521 11.1248C2.05941 10.309 1.50384 9.26958 1.27876 8.13803C1.05367 7.00647 1.16919 5.83358 1.61071 4.76768C2.05222 3.70178 2.79989 2.79074 3.75918 2.14976ZM7.00002 4.8611C6.84594 4.85908 6.69873 4.79698 6.58977 4.68801C6.48081 4.57905 6.4187 4.43185 6.41669 4.27776V3.88888C6.41669 3.73417 6.47815 3.58579 6.58754 3.4764C6.69694 3.367 6.84531 3.30554 7.00002 3.30554C7.15473 3.30554 7.3031 3.367 7.4125 3.4764C7.52189 3.58579 7.58335 3.73417 7.58335 3.88888V4.27776C7.58134 4.43185 7.51923 4.57905 7.41027 4.68801C7.30131 4.79698 7.1541 4.85908 7.00002 4.8611ZM7.00002 10.6945C6.84594 10.6925 6.69873 10.6304 6.58977 10.5214C6.48081 10.4124 6.4187 10.2652 6.41669 10.1111V6.22225C6.41669 6.06754 6.47815 5.91917 6.58754 5.80977C6.69694 5.70037 6.84531 5.63892 7.00002 5.63892C7.15473 5.63892 7.3031 5.70037 7.4125 5.80977C7.52189 5.91917 7.58335 6.06754 7.58335 6.22225V10.1111C7.58134 10.2652 7.51923 10.4124 7.41027 10.5214C7.30131 10.6304 7.1541 10.6925 7.00002 10.6945Z\", \"fill\", \"currentColor\"], [3, \"id\"], [\"width\", \"14\", \"height\", \"14\", \"fill\", \"white\"]],\n      template: function InfoCircleIcon_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(0, \"svg\", 0)(1, \"g\");\n          i0.ɵɵelement(2, \"path\", 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"defs\")(4, \"clipPath\", 2);\n          i0.ɵɵelement(5, \"rect\", 3);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.getClassNames());\n          i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel)(\"aria-hidden\", ctx.ariaHidden)(\"role\", ctx.role);\n          i0.ɵɵadvance(1);\n          i0.ɵɵattribute(\"clip-path\", ctx.pathId);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"id\", ctx.pathId);\n        }\n      },\n      encapsulation: 2\n    });\n  }\n  return InfoCircleIcon;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { InfoCircleIcon };\n//# sourceMappingURL=primeng-icons-infocircle.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}