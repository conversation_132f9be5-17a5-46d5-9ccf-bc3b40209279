<!-- Login Screen -->
<div class="login-container" *ngIf="!isAuthenticated">
  <div class="login-card">
    <div class="login-header">
      <i class="pi pi-lock"></i>
      <h2>لوحة تحكم الإدارة</h2>
      <p>يرجى إدخال كلمة المرور للوصول</p>
    </div>
    
    <form [formGroup]="loginForm" (ngSubmit)="onLogin()">
      <div class="form-group">
        <label for="password">كلمة المرور</label>
        <input 
          id="password"
          type="password" 
          formControlName="password"
          pInputText 
          placeholder="أدخل كلمة المرور"
          class="w-100">
      </div>
      
      <div class="error-message" *ngIf="loginError">
        <i class="pi pi-exclamation-triangle"></i>
        {{ loginError }}
      </div>
      
      <p-button 
        label="دخول" 
        icon="pi pi-sign-in" 
        type="submit"
        [disabled]="!loginForm.valid"
        styleClass="w-100">
      </p-button>
    </form>
  </div>
</div>

<!-- Admin Dashboard -->
<div class="admin-container" *ngIf="isAuthenticated">
  
  <!-- Header -->
  <div class="admin-header">
    <div class="header-content">
      <div class="header-left">
        <h1>لوحة تحكم الإدارة</h1>
        <p>إدارة روابط الاختصار ومراقبة النظام</p>
      </div>
      <div class="header-right">
        <p-button 
          label="تحديث البيانات" 
          icon="pi pi-refresh" 
          severity="secondary"
          [outlined]="true"
          (onClick)="refreshData()">
        </p-button>
        <p-button 
          label="تسجيل الخروج" 
          icon="pi pi-sign-out" 
          severity="danger"
          [outlined]="true"
          (onClick)="onLogout()">
        </p-button>
      </div>
    </div>
  </div>

  <!-- Navigation Tabs -->
  <div class="admin-nav">
    <div class="nav-tabs">
      <button 
        class="nav-tab"
        [class.active]="activeTab === 'dashboard'"
        (click)="setActiveTab('dashboard')">
        <i class="pi pi-chart-line"></i>
        لوحة المعلومات
      </button>
      <button 
        class="nav-tab"
        [class.active]="activeTab === 'links'"
        (click)="setActiveTab('links')">
        <i class="pi pi-link"></i>
        روابط الاختصار
      </button>
      <button 
        class="nav-tab"
        [class.active]="activeTab === 'security'"
        (click)="setActiveTab('security')">
        <i class="pi pi-shield"></i>
        سجلات الأمان
      </button>
      <button
        class="nav-tab"
        [class.active]="activeTab === 'settings'"
        (click)="setActiveTab('settings')">
        <i class="pi pi-cog"></i>
        إعدادات التطبيق
      </button>
      <button
        class="nav-tab"
        [class.active]="activeTab === 'admob'"
        (click)="setActiveTab('admob')">
        <i class="pi pi-dollar"></i>
        إعدادات AdMob
      </button>
    </div>
  </div>

  <!-- Content Area -->
  <div class="admin-content">
    
    <!-- Dashboard Tab -->
    <div class="tab-content" *ngIf="activeTab === 'dashboard'">
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">
            <i class="pi pi-users"></i>
          </div>
          <div class="stat-info">
            <h3>{{ appStats.total_users || 0 }}</h3>
            <p>إجمالي المستخدمين</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">
            <i class="pi pi-clock"></i>
          </div>
          <div class="stat-info">
            <h3>{{ appStats.active_sessions || 0 }}</h3>
            <p>الجلسات النشطة</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">
            <i class="pi pi-link"></i>
          </div>
          <div class="stat-info">
            <h3>{{ appStats.active_shortener_links || 0 }}</h3>
            <p>روابط الاختصار النشطة</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">
            <i class="pi pi-exclamation-triangle"></i>
          </div>
          <div class="stat-info">
            <h3>{{ appStats.total_security_events || 0 }}</h3>
            <p>أحداث الأمان</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Links Management Tab -->
    <div class="tab-content" *ngIf="activeTab === 'links'">
      
      <!-- Add/Edit Link Form -->
      <div class="form-section">
        <h3>{{ editingLink ? 'تعديل رابط الاختصار' : 'إضافة رابط اختصار جديد' }}</h3>
        
        <form [formGroup]="linkForm" class="link-form">
          <div class="form-row">
            <div class="form-group">
              <label for="site_name">اسم الموقع *</label>
              <input 
                id="site_name"
                formControlName="site_name"
                pInputText 
                placeholder="مثال: Cuty.io">
            </div>
            
            <div class="form-group">
              <label for="site_url">رابط الموقع *</label>
              <input 
                id="site_url"
                formControlName="site_url"
                pInputText 
                placeholder="https://example.com">
            </div>
          </div>
          
          <div class="form-group">
            <label for="link_template">قالب الرابط *</label>
            <input 
              id="link_template"
              formControlName="link_template"
              pInputText 
              placeholder="https://example.com/shorten?ref=quranvidgen&t={timestamp}&id={id}">
          </div>
          
          <div class="form-group">
            <label for="description">الوصف</label>
            <textarea 
              id="description"
              formControlName="description"
              pInputTextarea 
              rows="3"
              placeholder="وصف الموقع...">
            </textarea>
          </div>
          
          <div class="form-group">
            <div class="checkbox-container">
              <p-checkbox
                formControlName="is_active"
                label="نشط"
                [binary]="true">
              </p-checkbox>
            </div>
          </div>
          
          <div class="form-actions">
            <p-button 
              *ngIf="!editingLink"
              label="إضافة الرابط" 
              icon="pi pi-plus" 
              [loading]="isLoading"
              [disabled]="!linkForm.valid"
              (onClick)="onAddLink()">
            </p-button>
            
            <p-button 
              *ngIf="editingLink"
              label="تحديث الرابط" 
              icon="pi pi-check" 
              [loading]="isLoading"
              [disabled]="!linkForm.valid"
              (onClick)="onUpdateLink()">
            </p-button>
            
            <p-button 
              *ngIf="editingLink"
              label="إلغاء" 
              icon="pi pi-times" 
              severity="secondary"
              [outlined]="true"
              (onClick)="cancelEditLink()">
            </p-button>
          </div>
        </form>
      </div>

      <!-- Links List -->
      <div class="table-section">
        <h3>روابط الاختصار الحالية</h3>
        
        <div class="table-container">
          <table class="data-table">
            <thead>
              <tr>
                <th>اسم الموقع</th>
                <th>رابط الموقع</th>
                <th>الحالة</th>
                <th>تاريخ الإنشاء</th>
                <th>الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let link of shortenerLinks">
                <td>{{ link.site_name }}</td>
                <td>
                  <a [href]="link.site_url" target="_blank" class="link">
                    {{ link.site_url }}
                  </a>
                </td>
                <td>
                  <span class="status-badge" [class.active]="link.is_active" [class.inactive]="!link.is_active">
                    {{ link.is_active ? 'نشط' : 'غير نشط' }}
                  </span>
                </td>
                <td>{{ formatDate(link.created_at!) }}</td>
                <td>
                  <div class="action-buttons">
                    <p-button 
                      icon="pi pi-pencil" 
                      severity="info"
                      [text]="true"
                      size="small"
                      pTooltip="تعديل"
                      (onClick)="onEditLink(link)">
                    </p-button>
                    <p-button 
                      icon="pi pi-trash" 
                      severity="danger"
                      [text]="true"
                      size="small"
                      pTooltip="حذف"
                      (onClick)="onDeleteLink(link)">
                    </p-button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Security Logs Tab -->
    <div class="tab-content" *ngIf="activeTab === 'security'">
      <div class="table-section">
        <h3>سجلات الأمان (آخر 50 حدث)</h3>
        
        <div class="table-container">
          <table class="data-table">
            <thead>
              <tr>
                <th>نوع الحدث</th>
                <th>معرف المستخدم</th>
                <th>التفاصيل</th>
                <th>التاريخ</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let log of securityLogs">
                <td>
                  <div class="event-type">
                    <i [class]="'pi ' + getSecurityEventIcon(log.event_type)" 
                       [class]="getSecurityEventColor(log.event_type)"></i>
                    <span>{{ log.event_type }}</span>
                  </div>
                </td>
                <td>{{ log.user_id }}</td>
                <td>
                  <pre class="details-text">{{ formatJson(log.details) }}</pre>
                </td>
                <td>{{ formatDate(log.created_at!) }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Settings Tab -->
    <div class="tab-content" *ngIf="activeTab === 'settings'">
      
      <!-- Add/Edit Setting Form -->
      <div class="form-section">
        <h3>{{ editingSetting ? 'تعديل إعداد' : 'إضافة إعداد جديد' }}</h3>
        
        <form [formGroup]="settingForm" class="setting-form">
          <div class="form-row">
            <div class="form-group">
              <label for="setting_key">مفتاح الإعداد *</label>
              <input 
                id="setting_key"
                formControlName="setting_key"
                pInputText 
                placeholder="مثال: max_daily_attempts"
                [readonly]="!!editingSetting">
            </div>
            
            <div class="form-group">
              <label for="setting_value">قيمة الإعداد *</label>
              <input 
                id="setting_value"
                formControlName="setting_value"
                pInputText 
                placeholder="مثال: 5">
            </div>
          </div>
          
          <div class="form-group">
            <label for="setting_description">الوصف</label>
            <textarea 
              id="setting_description"
              formControlName="description"
              pInputTextarea 
              rows="2"
              placeholder="وصف الإعداد...">
            </textarea>
          </div>
          
          <div class="form-actions">
            <p-button 
              label="حفظ الإعداد" 
              icon="pi pi-save" 
              [loading]="isLoading"
              [disabled]="!settingForm.valid"
              (onClick)="onAddSetting()">
            </p-button>
            
            <p-button 
              *ngIf="editingSetting"
              label="إلغاء" 
              icon="pi pi-times" 
              severity="secondary"
              [outlined]="true"
              (onClick)="cancelEditSetting()">
            </p-button>
          </div>
        </form>
      </div>

      <!-- Settings List -->
      <div class="table-section">
        <h3>إعدادات التطبيق الحالية</h3>
        
        <div class="table-container">
          <table class="data-table">
            <thead>
              <tr>
                <th>مفتاح الإعداد</th>
                <th>القيمة</th>
                <th>الوصف</th>
                <th>آخر تحديث</th>
                <th>الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let setting of appSettings">
                <td><code>{{ setting.setting_key }}</code></td>
                <td><code>{{ formatJson(setting.setting_value) }}</code></td>
                <td>{{ setting.description || '-' }}</td>
                <td>{{ formatDate(setting.updated_at!) }}</td>
                <td>
                  <p-button 
                    icon="pi pi-pencil" 
                    severity="info"
                    [text]="true"
                    size="small"
                    pTooltip="تعديل"
                    (onClick)="onEditSetting(setting)">
                  </p-button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- AdMob Settings Tab -->
    <div class="tab-content" *ngIf="activeTab === 'admob'">
      <app-admob-settings></app-admob-settings>
    </div>

  </div>
</div>
