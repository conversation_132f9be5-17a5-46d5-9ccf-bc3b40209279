{"ast": null, "code": "// ملف تكوين مفاتيح API\n// يرجى تعديل هذه القيم بمفاتيحك الفعلية\nexport const API_KEYS = {\n  // Google API Configuration\n  GOOGLE_CLIENT_ID: 'YOUR_GOOGLE_CLIENT_ID_HERE',\n  GOOGLE_API_KEY: 'YOUR_GOOGLE_API_KEY_HERE',\n  // Gemini AI Configuration\n  GEMINI_API_KEY: 'YOUR_GEMINI_API_KEY_HERE'\n};\n// تعليمات الحصول على المفاتيح:\n/*\n1. Google Client ID & API Key:\n   - اذهب إلى Google Cloud Console: https://console.cloud.google.com/\n   - أنشئ مشروع جديد أو اختر مشروع موجود\n   - فعّل YouTube Data API v3\n   - أنشئ credentials (OAuth 2.0 Client ID و API Key)\n   - أضف domain الخاص بك في Authorized JavaScript origins\n\n2. Gemini API Key:\n   - اذه<PERSON> إلى Google AI Studio: https://aistudio.google.com/\n   - أنشئ API key جديد\n   - انسخ المفتاح وضعه هنا\n\nملاحظة: لا تشارك هذه المفاتيح مع أحد ولا ترفعها إلى GitHub\n*/", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}