"use strict";var __assign=this&&this.__assign||function(){return __assign=Object.assign||function(e){for(var t,r=1,i=arguments.length;r<i;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},__assign.apply(this,arguments)},__spreadArray=this&&this.__spreadArray||function(e,t,r){if(r||2===arguments.length)for(var i,n=0,s=t.length;n<s;n++)!i&&n in t||(i||(i=Array.prototype.slice.call(t,0,n)),i[n]=t[n]);return e.concat(i||Array.prototype.slice.call(t))};
/**
 * @license Angular v<unknown>
 * (c) 2010-2022 Google LLC. https://angular.io/
 * License: MIT
 */
!function(e){"function"==typeof define&&define.amd?define(e):e()}((function(){!function(e){var t,r=e.Date;function i(){if(0===arguments.length){var e=new r;return e.setTime(i.now()),e}var t=Array.prototype.slice.call(arguments);return new(r.bind.apply(r,__spreadArray([void 0],t,!1)))}i.now=function(){var e=Zone.current.get("FakeAsyncTestZoneSpec");return e?e.getFakeSystemTime():r.now.apply(this,arguments)},i.UTC=r.UTC,i.parse=r.parse;var n={setTimeout:e.setTimeout,setInterval:e.setInterval,clearTimeout:e.clearTimeout,clearInterval:e.clearInterval},s=function(){function i(){this._schedulerQueue=[],this._currentTickTime=0,this._currentFakeBaseSystemTime=r.now(),this._currentTickRequeuePeriodicEntries=[]}return i.prototype.getCurrentTickTime=function(){return this._currentTickTime},i.prototype.getFakeSystemTime=function(){return this._currentFakeBaseSystemTime+this._currentTickTime},i.prototype.setFakeBaseSystemTime=function(e){this._currentFakeBaseSystemTime=e},i.prototype.getRealSystemTime=function(){return r.now()},i.prototype.scheduleFunction=function(e,r,i){var n=(i=__assign({args:[],isPeriodic:!1,isRequestAnimationFrame:!1,id:-1,isRequeuePeriodic:!1},i)).id<0?t.nextId++:i.id,s={endTime:this._currentTickTime+r,id:n,func:e,args:i.args,delay:r,isPeriodic:i.isPeriodic,isRequestAnimationFrame:i.isRequestAnimationFrame};i.isRequeuePeriodic&&this._currentTickRequeuePeriodicEntries.push(s);for(var o=0;o<this._schedulerQueue.length&&!(s.endTime<this._schedulerQueue[o].endTime);o++);return this._schedulerQueue.splice(o,0,s),n},i.prototype.removeScheduledFunctionWithId=function(e){for(var t=0;t<this._schedulerQueue.length;t++)if(this._schedulerQueue[t].id==e){this._schedulerQueue.splice(t,1);break}},i.prototype.removeAll=function(){this._schedulerQueue=[]},i.prototype.getTimerCount=function(){return this._schedulerQueue.length},i.prototype.tickToNext=function(e,t,r){void 0===e&&(e=1),this._schedulerQueue.length<e||this.tick(this._schedulerQueue[e-1].endTime-this._currentTickTime,t,r)},i.prototype.tick=function(t,r,i){void 0===t&&(t=0);var n=this._currentTickTime+t,s=0,o=(i=Object.assign({processNewMacroTasksSynchronously:!0},i)).processNewMacroTasksSynchronously?this._schedulerQueue:this._schedulerQueue.slice();if(0===o.length&&r)r(t);else{for(;o.length>0&&(this._currentTickRequeuePeriodicEntries=[],!(n<o[0].endTime));){var a=o.shift();if(!i.processNewMacroTasksSynchronously){var c=this._schedulerQueue.indexOf(a);c>=0&&this._schedulerQueue.splice(c,1)}if(s=this._currentTickTime,this._currentTickTime=a.endTime,r&&r(this._currentTickTime-s),!a.func.apply(e,a.isRequestAnimationFrame?[this._currentTickTime]:a.args))break;i.processNewMacroTasksSynchronously||this._currentTickRequeuePeriodicEntries.forEach((function(e){for(var t=0;t<o.length&&!(e.endTime<o[t].endTime);t++);o.splice(t,0,e)}))}s=this._currentTickTime,this._currentTickTime=n,r&&r(this._currentTickTime-s)}},i.prototype.flushOnlyPendingTimers=function(e){if(0===this._schedulerQueue.length)return 0;var t=this._currentTickTime;return this.tick(this._schedulerQueue[this._schedulerQueue.length-1].endTime-t,e,{processNewMacroTasksSynchronously:!1}),this._currentTickTime-t},i.prototype.flush=function(e,t,r){return void 0===e&&(e=20),void 0===t&&(t=!1),t?this.flushPeriodic(r):this.flushNonPeriodic(e,r)},i.prototype.flushPeriodic=function(e){if(0===this._schedulerQueue.length)return 0;var t=this._currentTickTime;return this.tick(this._schedulerQueue[this._schedulerQueue.length-1].endTime-t,e),this._currentTickTime-t},i.prototype.flushNonPeriodic=function(t,r){for(var i=this._currentTickTime,n=0,s=0;this._schedulerQueue.length>0;){if(++s>t)throw new Error("flush failed after reaching the limit of "+t+" tasks. Does your code use a polling timeout?");if(0===this._schedulerQueue.filter((function(e){return!e.isPeriodic&&!e.isRequestAnimationFrame})).length)break;var o=this._schedulerQueue.shift();if(n=this._currentTickTime,this._currentTickTime=o.endTime,r&&r(this._currentTickTime-n),!o.func.apply(e,o.args))break}return this._currentTickTime-i},i}();(t=s).nextId=1;var o=function(){function t(t,r,i){void 0===r&&(r=!1),this.trackPendingRequestAnimationFrame=r,this.macroTaskOptions=i,this._scheduler=new s,this._microtasks=[],this._lastError=null,this._uncaughtPromiseErrors=Promise[Zone.__symbol__("uncaughtPromiseErrors")],this.pendingPeriodicTimers=[],this.pendingTimers=[],this.patchDateLocked=!1,this.properties={FakeAsyncTestZoneSpec:this},this.name="fakeAsyncTestZone for "+t,this.macroTaskOptions||(this.macroTaskOptions=e[Zone.__symbol__("FakeAsyncTestMacroTask")])}return t.assertInZone=function(){if(null==Zone.current.get("FakeAsyncTestZoneSpec"))throw new Error("The code should be running in the fakeAsync zone to call this function")},t.prototype._fnAndFlush=function(t,r){var i=this;return function(){for(var n=[],s=0;s<arguments.length;s++)n[s]=arguments[s];return t.apply(e,n),null===i._lastError?(null!=r.onSuccess&&r.onSuccess.apply(e),i.flushMicrotasks()):null!=r.onError&&r.onError.apply(e),null===i._lastError}},t._removeTimer=function(e,t){var r=e.indexOf(t);r>-1&&e.splice(r,1)},t.prototype._dequeueTimer=function(e){var r=this;return function(){t._removeTimer(r.pendingTimers,e)}},t.prototype._requeuePeriodicTimer=function(e,t,r,i){var n=this;return function(){-1!==n.pendingPeriodicTimers.indexOf(i)&&n._scheduler.scheduleFunction(e,t,{args:r,isPeriodic:!0,id:i,isRequeuePeriodic:!0})}},t.prototype._dequeuePeriodicTimer=function(e){var r=this;return function(){t._removeTimer(r.pendingPeriodicTimers,e)}},t.prototype._setTimeout=function(e,t,r,i){void 0===i&&(i=!0);var n=this._dequeueTimer(s.nextId),o=this._fnAndFlush(e,{onSuccess:n,onError:n}),a=this._scheduler.scheduleFunction(o,t,{args:r,isRequestAnimationFrame:!i});return i&&this.pendingTimers.push(a),a},t.prototype._clearTimeout=function(e){t._removeTimer(this.pendingTimers,e),this._scheduler.removeScheduledFunctionWithId(e)},t.prototype._setInterval=function(e,t,r){var i=s.nextId,n={onSuccess:null,onError:this._dequeuePeriodicTimer(i)},o=this._fnAndFlush(e,n);return n.onSuccess=this._requeuePeriodicTimer(o,t,r,i),this._scheduler.scheduleFunction(o,t,{args:r,isPeriodic:!0}),this.pendingPeriodicTimers.push(i),i},t.prototype._clearInterval=function(e){t._removeTimer(this.pendingPeriodicTimers,e),this._scheduler.removeScheduledFunctionWithId(e)},t.prototype._resetLastErrorAndThrow=function(){var e=this._lastError||this._uncaughtPromiseErrors[0];throw this._uncaughtPromiseErrors.length=0,this._lastError=null,e},t.prototype.getCurrentTickTime=function(){return this._scheduler.getCurrentTickTime()},t.prototype.getFakeSystemTime=function(){return this._scheduler.getFakeSystemTime()},t.prototype.setFakeBaseSystemTime=function(e){this._scheduler.setFakeBaseSystemTime(e)},t.prototype.getRealSystemTime=function(){return this._scheduler.getRealSystemTime()},t.patchDate=function(){e[Zone.__symbol__("disableDatePatching")]||e.Date!==i&&(e.Date=i,i.prototype=r.prototype,t.checkTimerPatch())},t.resetDate=function(){e.Date===i&&(e.Date=r)},t.checkTimerPatch=function(){e.setTimeout!==n.setTimeout&&(e.setTimeout=n.setTimeout,e.clearTimeout=n.clearTimeout),e.setInterval!==n.setInterval&&(e.setInterval=n.setInterval,e.clearInterval=n.clearInterval)},t.prototype.lockDatePatch=function(){this.patchDateLocked=!0,t.patchDate()},t.prototype.unlockDatePatch=function(){this.patchDateLocked=!1,t.resetDate()},t.prototype.tickToNext=function(e,r,i){void 0===e&&(e=1),void 0===i&&(i={processNewMacroTasksSynchronously:!0}),e<=0||(t.assertInZone(),this.flushMicrotasks(),this._scheduler.tickToNext(e,r,i),null!==this._lastError&&this._resetLastErrorAndThrow())},t.prototype.tick=function(e,r,i){void 0===e&&(e=0),void 0===i&&(i={processNewMacroTasksSynchronously:!0}),t.assertInZone(),this.flushMicrotasks(),this._scheduler.tick(e,r,i),null!==this._lastError&&this._resetLastErrorAndThrow()},t.prototype.flushMicrotasks=function(){var e=this;for(t.assertInZone();this._microtasks.length>0;){var r=this._microtasks.shift();r.func.apply(r.target,r.args)}(null!==e._lastError||e._uncaughtPromiseErrors.length)&&e._resetLastErrorAndThrow()},t.prototype.flush=function(e,r,i){t.assertInZone(),this.flushMicrotasks();var n=this._scheduler.flush(e,r,i);return null!==this._lastError&&this._resetLastErrorAndThrow(),n},t.prototype.flushOnlyPendingTimers=function(e){t.assertInZone(),this.flushMicrotasks();var r=this._scheduler.flushOnlyPendingTimers(e);return null!==this._lastError&&this._resetLastErrorAndThrow(),r},t.prototype.removeAllTimers=function(){t.assertInZone(),this._scheduler.removeAll(),this.pendingPeriodicTimers=[],this.pendingTimers=[]},t.prototype.getTimerCount=function(){return this._scheduler.getTimerCount()+this._microtasks.length},t.prototype.onScheduleTask=function(e,t,r,i){switch(i.type){case"microTask":var n=i.data&&i.data.args,s=void 0;if(n){var o=i.data.cbIdx;"number"==typeof n.length&&n.length>o+1&&(s=Array.prototype.slice.call(n,o+1))}this._microtasks.push({func:i.invoke,args:s,target:i.data&&i.data.target});break;case"macroTask":switch(i.source){case"setTimeout":i.data.handleId=this._setTimeout(i.invoke,i.data.delay,Array.prototype.slice.call(i.data.args,2));break;case"setImmediate":i.data.handleId=this._setTimeout(i.invoke,0,Array.prototype.slice.call(i.data.args,1));break;case"setInterval":i.data.handleId=this._setInterval(i.invoke,i.data.delay,Array.prototype.slice.call(i.data.args,2));break;case"XMLHttpRequest.send":throw new Error("Cannot make XHRs from within a fake async test. Request URL: "+i.data.url);case"requestAnimationFrame":case"webkitRequestAnimationFrame":case"mozRequestAnimationFrame":i.data.handleId=this._setTimeout(i.invoke,16,i.data.args,this.trackPendingRequestAnimationFrame);break;default:var a=this.findMacroTaskOption(i);if(a){var c=i.data&&i.data.args,u=c&&c.length>1?c[1]:0,h=a.callbackArgs?a.callbackArgs:c;a.isPeriodic?(i.data.handleId=this._setInterval(i.invoke,u,h),i.data.isPeriodic=!0):i.data.handleId=this._setTimeout(i.invoke,u,h);break}throw new Error("Unknown macroTask scheduled in fake async test: "+i.source)}break;case"eventTask":i=e.scheduleTask(r,i)}return i},t.prototype.onCancelTask=function(e,t,r,i){switch(i.source){case"setTimeout":case"requestAnimationFrame":case"webkitRequestAnimationFrame":case"mozRequestAnimationFrame":return this._clearTimeout(i.data.handleId);case"setInterval":return this._clearInterval(i.data.handleId);default:var n=this.findMacroTaskOption(i);if(n){var s=i.data.handleId;return n.isPeriodic?this._clearInterval(s):this._clearTimeout(s)}return e.cancelTask(r,i)}},t.prototype.onInvoke=function(e,r,i,n,s,o,a){try{return t.patchDate(),e.invoke(i,n,s,o,a)}finally{this.patchDateLocked||t.resetDate()}},t.prototype.findMacroTaskOption=function(e){if(!this.macroTaskOptions)return null;for(var t=0;t<this.macroTaskOptions.length;t++){var r=this.macroTaskOptions[t];if(r.source===e.source)return r}return null},t.prototype.onHandleError=function(e,t,r,i){return this._lastError=i,!1},t}();Zone.FakeAsyncTestZoneSpec=o}("object"==typeof window&&window||"object"==typeof self&&self||global),Zone.__load_patch("fakeasync",(function(e,t,r){var i=t&&t.FakeAsyncTestZoneSpec;function n(){return t&&t.ProxyZoneSpec}var s=null;function o(){s&&s.unlockDatePatch(),s=null,n()&&n().assertPresent().resetDelegate()}function a(){if(null==s&&null==(s=t.current.get("FakeAsyncTestZoneSpec")))throw new Error("The code should be running in the fakeAsync zone to call this function");return s}function c(){a().flushMicrotasks()}t[r.symbol("fakeAsyncTest")]={resetFakeAsyncZone:o,flushMicrotasks:c,discardPeriodicTasks:function u(){a().pendingPeriodicTimers.length=0},tick:function h(e,t){void 0===e&&(e=0),void 0===t&&(t=!1),a().tick(e,null,t)},flush:function l(e){return a().flush(e)},fakeAsync:function d(e){var r=function(){for(var r=[],a=0;a<arguments.length;a++)r[a]=arguments[a];var u=n();if(!u)throw new Error("ProxyZoneSpec is needed for the async() test helper but could not be found. Please make sure that your environment includes zone.js/plugins/proxy");var h=u.assertPresent();if(t.current.get("FakeAsyncTestZoneSpec"))throw new Error("fakeAsync() calls can not be nested");try{if(!s){if(h.getDelegate()instanceof i)throw new Error("fakeAsync() calls can not be nested");s=new i}var l=void 0,d=h.getDelegate();h.setDelegate(s),s.lockDatePatch();try{l=e.apply(this,r),c()}finally{h.setDelegate(d)}if(s.pendingPeriodicTimers.length>0)throw new Error("".concat(s.pendingPeriodicTimers.length," ")+"periodic timer(s) still in the queue.");if(s.pendingTimers.length>0)throw new Error("".concat(s.pendingTimers.length," timer(s) still in the queue."));return l}finally{o()}};return r.isFakeAsync=!0,r}}}),!0)}));