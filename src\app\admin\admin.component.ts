import { Component, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { SupabaseService, ShortenerLink, SecurityLog, AppSettings } from '../Services/supabase.service';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-admin',
  templateUrl: './admin.component.html',
  styleUrls: ['./admin.component.scss']
})
export class AdminComponent implements OnInit {
  // Authentication
  isAuthenticated = false;
  loginForm!: FormGroup;
  loginError = '';

  // Data
  shortenerLinks: ShortenerLink[] = [];
  securityLogs: SecurityLog[] = [];
  appSettings: AppSettings[] = [];
  appStats: any = {};

  // Forms
  linkForm!: FormGroup;
  settingForm!: FormGroup;

  // UI State
  activeTab = 'dashboard';
  isLoading = false;
  editingLink: ShortenerLink | null = null;
  editingSetting: AppSettings | null = null;

  // Admin credentials (in production, this should be more secure)
  private readonly ADMIN_PASSWORD = 'QuranAdmin2025!';

  constructor(
    private fb: FormBuilder,
    private supabaseService: SupabaseService
  ) {}

  ngOnInit(): void {
    this.initializeForms();
    this.checkAuthStatus();
  }

  private initializeForms(): void {
    this.loginForm = this.fb.group({
      password: ['', [Validators.required]]
    });

    this.linkForm = this.fb.group({
      site_name: ['', [Validators.required]],
      site_url: ['', [Validators.required, Validators.pattern('https?://.+')]],
      link_template: ['', [Validators.required]],
      description: [''],
      is_active: [true]
    });

    this.settingForm = this.fb.group({
      setting_key: ['', [Validators.required]],
      setting_value: ['', [Validators.required]],
      description: ['']
    });
  }

  private checkAuthStatus(): void {
    const authStatus = localStorage.getItem('admin_authenticated');
    if (authStatus === 'true') {
      this.isAuthenticated = true;
      this.loadDashboardData();
    }
  }

  // Authentication
  onLogin(): void {
    if (this.loginForm.valid) {
      const password = this.loginForm.get('password')?.value;
      
      if (password === this.ADMIN_PASSWORD) {
        this.isAuthenticated = true;
        localStorage.setItem('admin_authenticated', 'true');
        this.loginError = '';
        this.loadDashboardData();
      } else {
        this.loginError = 'كلمة المرور غير صحيحة';
      }
    }
  }

  onLogout(): void {
    this.isAuthenticated = false;
    localStorage.removeItem('admin_authenticated');
    this.loginForm.reset();
  }

  // Data Loading
  private loadDashboardData(): void {
    this.loadShortenerLinks();
    this.loadSecurityLogs();
    this.loadAppSettings();
    this.loadAppStats();
  }

  private loadShortenerLinks(): void {
    this.supabaseService.getShortenerLinks().subscribe({
      next: (links) => {
        this.shortenerLinks = links;
      },
      error: (error) => {
        console.error('Error loading shortener links:', error);
      }
    });
  }

  private loadSecurityLogs(): void {
    this.supabaseService.getSecurityLogs(undefined, 50).subscribe({
      next: (logs) => {
        this.securityLogs = logs;
      },
      error: (error) => {
        console.error('Error loading security logs:', error);
      }
    });
  }

  private loadAppSettings(): void {
    this.supabaseService.getAllAppSettings().subscribe({
      next: (settings) => {
        this.appSettings = settings;
      },
      error: (error) => {
        console.error('Error loading app settings:', error);
      }
    });
  }

  private loadAppStats(): void {
    this.supabaseService.getAppStats().subscribe({
      next: (stats) => {
        this.appStats = stats;
      },
      error: (error) => {
        console.error('Error loading app stats:', error);
      }
    });
  }

  // Shortener Links Management
  onAddLink(): void {
    if (this.linkForm.valid) {
      this.isLoading = true;
      const linkData = this.linkForm.value;
      
      this.supabaseService.addShortenerLink(linkData).subscribe({
        next: (newLink) => {
          this.shortenerLinks.push(newLink);
          this.linkForm.reset();
          this.linkForm.patchValue({ is_active: true });
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error adding link:', error);
          this.isLoading = false;
        }
      });
    }
  }

  onEditLink(link: ShortenerLink): void {
    this.editingLink = link;
    this.linkForm.patchValue(link);
  }

  onUpdateLink(): void {
    if (this.linkForm.valid && this.editingLink) {
      this.isLoading = true;
      const updates = this.linkForm.value;
      
      this.supabaseService.updateShortenerLink(this.editingLink.id!, updates).subscribe({
        next: (updatedLink) => {
          const index = this.shortenerLinks.findIndex(l => l.id === updatedLink.id);
          if (index !== -1) {
            this.shortenerLinks[index] = updatedLink;
          }
          this.cancelEditLink();
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error updating link:', error);
          this.isLoading = false;
        }
      });
    }
  }

  onDeleteLink(link: ShortenerLink): void {
    if (confirm(`هل أنت متأكد من حذف الرابط: ${link.site_name}؟`)) {
      this.supabaseService.deleteShortenerLink(link.id!).subscribe({
        next: () => {
          this.shortenerLinks = this.shortenerLinks.filter(l => l.id !== link.id);
        },
        error: (error) => {
          console.error('Error deleting link:', error);
        }
      });
    }
  }

  cancelEditLink(): void {
    this.editingLink = null;
    this.linkForm.reset();
    this.linkForm.patchValue({ is_active: true });
  }

  // App Settings Management
  onAddSetting(): void {
    if (this.settingForm.valid) {
      this.isLoading = true;
      const { setting_key, setting_value, description } = this.settingForm.value;
      
      this.supabaseService.setAppSetting(setting_key, setting_value, description).subscribe({
        next: (newSetting) => {
          const existingIndex = this.appSettings.findIndex(s => s.setting_key === setting_key);
          if (existingIndex !== -1) {
            this.appSettings[existingIndex] = newSetting;
          } else {
            this.appSettings.push(newSetting);
          }
          this.settingForm.reset();
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error adding/updating setting:', error);
          this.isLoading = false;
        }
      });
    }
  }

  onEditSetting(setting: AppSettings): void {
    this.editingSetting = setting;
    this.settingForm.patchValue({
      setting_key: setting.setting_key,
      setting_value: JSON.stringify(setting.setting_value),
      description: setting.description
    });
  }

  cancelEditSetting(): void {
    this.editingSetting = null;
    this.settingForm.reset();
  }

  // Utility Methods
  setActiveTab(tab: string): void {
    this.activeTab = tab;
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleString('ar');
  }

  formatJson(value: any): string {
    return JSON.stringify(value, null, 2);
  }

  getSecurityEventIcon(eventType: string): string {
    switch (eventType) {
      case 'vpn_detected': return 'pi-shield';
      case 'adblocker_detected': return 'pi-ban';
      case 'dns_blocked': return 'pi-globe';
      case 'access_denied': return 'pi-times-circle';
      default: return 'pi-exclamation-triangle';
    }
  }

  getSecurityEventColor(eventType: string): string {
    switch (eventType) {
      case 'vpn_detected': return 'text-warning';
      case 'adblocker_detected': return 'text-danger';
      case 'dns_blocked': return 'text-info';
      case 'access_denied': return 'text-danger';
      default: return 'text-secondary';
    }
  }

  refreshData(): void {
    this.loadDashboardData();
  }
}
