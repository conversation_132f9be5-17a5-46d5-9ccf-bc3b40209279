# دليل بناء تطبيق APK - مولد مقاطع القرآن الكريم

## 🎯 الهدف
تحويل تطبيق Angular إلى تطبيق Android APK باستخدام Capacitor

## ✅ الحالة الحالية
- ✅ مشروع Angular جاهز ومبني
- ✅ Capacitor مُعد ومُزامن
- ✅ Java 17 مثبت بنجاح
- ✅ مجلد Android موجود ومُعد
- ⚠️ مشكلة في تنزيل Gradle (مشكلة اتصال بالإنترنت)

## 🚀 طرق بناء APK

### الطريقة الأولى: Android Studio (الأسهل والأفضل)

#### 1. تنزيل وتثبيت Android Studio
```
https://developer.android.com/studio
```

#### 2. فتح المشروع في Android Studio
1. افتح Android Studio
2. اختر "Open an existing Android Studio project"
3. انتقل إلى مجلد: `تطبيق ناشر ايات قئانية\android`
4. انقر "OK"

#### 3. بناء APK
1. من القائمة: **Build** → **Build Bundle(s) / APK(s)** → **Build APK(s)**
2. انتظر حتى يكتمل البناء (قد يستغرق 5-10 دقائق في المرة الأولى)
3. ستجد APK في: `android\app\build\outputs\apk\debug\app-debug.apk`

### الطريقة الثانية: تنزيل Gradle يدوياً

#### 1. تنزيل Gradle
- اذهب إلى: https://gradle.org/releases/
- نزل Gradle 7.6.4 (Binary-only)
- استخرج الملف إلى `C:\gradle\gradle-7.6.4`

#### 2. إعداد متغيرات البيئة
```powershell
$env:GRADLE_HOME = "C:\gradle\gradle-7.6.4"
$env:PATH = "$env:GRADLE_HOME\bin;$env:PATH"
$env:JAVA_HOME = "C:\Program Files\Microsoft\jdk-*********-hotspot"
```

#### 3. بناء APK
```powershell
cd "تطبيق ناشر ايات قئانية\android"
gradle assembleDebug
```

### الطريقة الثالثة: استخدام Scripts الجاهزة

#### استخدام PowerShell Script
```powershell
powershell -ExecutionPolicy Bypass -File build_apk.ps1
```

#### استخدام Batch Script
```cmd
build_apk.bat
```

## 🛠️ الأوامر المطلوبة (إذا كان Gradle يعمل)

```powershell
# تعيين Java
$env:JAVA_HOME = "C:\Program Files\Microsoft\jdk-*********-hotspot"

# بناء Angular
npm run build

# نسخ index.html إذا لزم الأمر
copy src\index.html dist\quran-vid-gen\index.html

# مزامنة Capacitor
npx cap sync android

# بناء APK
cd android
.\gradlew.bat assembleDebug

# نسخ APK إلى سطح المكتب
copy app\build\outputs\apk\debug\app-debug.apk %USERPROFILE%\Desktop\QuranVidGen.apk
```

## 📱 معلومات التطبيق

- **اسم التطبيق:** Quran Video Generator
- **معرف التطبيق:** qurangen.pixelpigeon.com
- **الإصدار:** Debug Build
- **الحد الأدنى لـ Android:** API 22 (Android 5.1)

## 🎯 الميزات المتوفرة في APK

- ✅ واجهة محسنة بألوان جذابة
- ✅ نظام حماية من VPN وأدوات الحظر
- ✅ نظام اختصار الروابط اليومي
- ✅ تكامل مع Gemini AI
- ✅ تكامل مع Supabase
- ✅ نظام إدارة المحتوى
- ✅ دعم الخطوط العربية والقرآنية

## 🔧 حل المشاكل الشائعة

### مشكلة: "JAVA_HOME is set to an invalid directory"
**الحل:**
```powershell
$env:JAVA_HOME = "C:\Program Files\Microsoft\jdk-*********-hotspot"
```

### مشكلة: "index.html not found"
**الحل:**
```powershell
copy src\index.html dist\quran-vid-gen\index.html
```

### مشكلة: "Gradle download timeout"
**الحل:** استخدم Android Studio أو نزل Gradle يدوياً

## 📋 ملفات المشروع

- **الكود المصدري:** `src/`
- **ملفات الويب المبنية:** `dist/quran-vid-gen/`
- **مشروع Android:** `android/`
- **إعدادات Capacitor:** `capacitor.config.ts`
- **Scripts البناء:** `build_apk.ps1`, `build_apk.bat`

## 🎉 النتيجة المتوقعة

ملف APK جاهز للتثبيت على أجهزة Android مع جميع الميزات المطلوبة.

---

**💡 نصيحة:** الطريقة الأولى (Android Studio) هي الأسهل والأكثر موثوقية، خاصة للمرة الأولى.
