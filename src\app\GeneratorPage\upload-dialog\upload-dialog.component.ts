import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { YoutubeService, YouTubeVideoDetails, YouTubeUploadProgress } from '../../Services/youtube.service';
import { GeminiService, SurahInfo } from '../../Services/gemini.service';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-upload-dialog',
  templateUrl: './upload-dialog.component.html',
  styleUrls: ['./upload-dialog.component.scss']
})
export class UploadDialogComponent implements OnInit {
  @Input() visible: boolean = false;
  @Input() videoBlob: Blob | null = null;
  @Input() surahInfo: SurahInfo | null = null;
  @Output() visibleChange = new EventEmitter<boolean>();
  @Output() uploadComplete = new EventEmitter<string>();

  uploadForm: FormGroup;
  isUploading = false;
  isGenerating = false;
  uploadProgress$: Observable<YouTubeUploadProgress>;
  uploadProgress = 0;
  isUploadComplete = false;
  videoUrl = '';
  errorMessage = '';
  successMessage = '';

  privacyOptions = [
    { label: 'عام', value: 'public' },
    { label: 'غير مدرج', value: 'unlisted' },
    { label: 'خاص', value: 'private' }
  ];

  constructor(
    private fb: FormBuilder,
    private youtubeService: YoutubeService,
    private geminiService: GeminiService
  ) {
    this.uploadForm = this.createForm();
    this.uploadProgress$ = this.youtubeService.uploadProgress$;
  }

  ngOnInit(): void {
    // Initialize form when component loads
  }

  private createForm(): FormGroup {
    return this.fb.group({
      title: ['', [Validators.required, Validators.maxLength(100)]],
      description: ['', [Validators.required, Validators.maxLength(5000)]],
      tags: [''],
      privacy: ['unlisted', Validators.required],
      useAI: [true]
    });
  }

  onVisibilityChange(): void {
    if (this.visible && this.surahInfo) {
      this.resetForm();
      if (this.uploadForm.get('useAI')?.value) {
        this.generateContent();
      }
    }
  }

  private resetForm(): void {
    this.uploadForm.reset({
      title: '',
      description: '',
      tags: '',
      privacy: 'unlisted',
      useAI: true
    });
    this.errorMessage = '';
    this.successMessage = '';
  }

  generateContent(): void {
    if (!this.surahInfo || !this.geminiService.isApiKeyConfigured()) {
      this.errorMessage = 'لم يتم تكوين مفتاح Gemini API';
      return;
    }

    this.isGenerating = true;
    this.errorMessage = '';

    this.geminiService.generateVideoContent(this.surahInfo).subscribe({
      next: (content) => {
        this.uploadForm.patchValue({
          title: content.title,
          description: content.description,
          tags: content.tags.join(', ')
        });
        this.isGenerating = false;
      },
      error: (error) => {
        this.errorMessage = 'فشل في توليد المحتوى. يرجى المحاولة مرة أخرى.';
        this.isGenerating = false;
        console.error('Content generation error:', error);
      }
    });
  }

  generateTitleOnly(): void {
    if (!this.surahInfo || !this.geminiService.isApiKeyConfigured()) {
      return;
    }

    this.geminiService.generateTitleOnly(this.surahInfo).subscribe({
      next: (title) => {
        this.uploadForm.patchValue({ title });
      },
      error: (error) => {
        console.error('Title generation error:', error);
      }
    });
  }

  generateDescriptionOnly(): void {
    if (!this.surahInfo || !this.geminiService.isApiKeyConfigured()) {
      return;
    }

    this.geminiService.generateDescriptionOnly(this.surahInfo).subscribe({
      next: (description) => {
        this.uploadForm.patchValue({ description });
      },
      error: (error) => {
        console.error('Description generation error:', error);
      }
    });
  }

  onUpload(): void {
    if (!this.uploadForm.valid || !this.videoBlob) {
      this.errorMessage = 'يرجى ملء جميع الحقول المطلوبة';
      return;
    }

    if (!this.youtubeService.isAuthenticated()) {
      this.errorMessage = 'يرجى تسجيل الدخول إلى YouTube أولاً';
      return;
    }

    const formValue = this.uploadForm.value;
    const videoDetails: YouTubeVideoDetails = {
      title: formValue.title,
      description: formValue.description,
      tags: formValue.tags ? formValue.tags.split(',').map((tag: string) => tag.trim()) : [],
      privacy: formValue.privacy
    };

    this.isUploading = true;
    this.errorMessage = '';

    this.youtubeService.uploadVideo(this.videoBlob, videoDetails).subscribe({
      next: (videoId) => {
        this.isUploading = false;
        this.successMessage = `تم رفع الفيديو بنجاح! معرف الفيديو: ${videoId}`;
        this.uploadComplete.emit(videoId);
        setTimeout(() => this.closeDialog(), 3000);
      },
      error: (error) => {
        this.isUploading = false;
        this.errorMessage = 'فشل في رفع الفيديو. يرجى المحاولة مرة أخرى.';
        console.error('Upload error:', error);
      }
    });
  }

  closeDialog(): void {
    this.visible = false;
    this.visibleChange.emit(false);
    this.resetForm();
  }

  get isAIEnabled(): boolean {
    return this.geminiService.isApiKeyConfigured();
  }

  get canGenerate(): boolean {
    return this.isAIEnabled && !!this.surahInfo && !this.isGenerating;
  }

  onHide(): void {
    this.visible = false;
    this.visibleChange.emit(false);
    this.resetForm();
  }
}
