"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2022 Google LLC. https://angular.io/
 * License: MIT
 */const NEWLINE="\n",IGNORE_FRAMES={},creationTrace="__creationTrace__",ERROR_TAG="STACKTRACE TRACKING",SEP_TAG="__SEP_TAG__";let sepTemplate=SEP_TAG+"@[native]";class LongStackTrace{constructor(){this.error=getStacktrace(),this.timestamp=new Date}}function getStacktraceWithUncaughtError(){return new Error(ERROR_TAG)}function getStacktraceWithCaughtError(){try{throw getStacktraceWithUncaughtError()}catch(t){return t}}const error=getStacktraceWithUncaughtError(),caughtError=getStacktraceWithCaughtError(),getStacktrace=error.stack?getStacktraceWithUncaughtError:caughtError.stack?getStacktraceWithCaughtError:getStacktraceWithUncaughtError;function getFrames(t){return t.stack?t.stack.split(NEWLINE):[]}function addErrorStack(t,r){let e=getFrames(r);for(let r=0;r<e.length;r++)IGNORE_FRAMES.hasOwnProperty(e[r])||t.push(e[r])}function renderLongStackTrace(t,r){const e=[r?r.trim():""];if(t){let r=(new Date).getTime();for(let a=0;a<t.length;a++){const c=t[a],n=c.timestamp;let o=`____________________Elapsed ${r-n.getTime()} ms; At: ${n}`;o=o.replace(/[^\w\d]/g,"_"),e.push(sepTemplate.replace(SEP_TAG,o)),addErrorStack(e,c.error),r=n.getTime()}}return e.join(NEWLINE)}function stackTracesEnabled(){return Error.stackTraceLimit>0}function captureStackTraces(t,r){r>0&&(t.push(getFrames((new LongStackTrace).error)),captureStackTraces(t,r-1))}function computeIgnoreFrames(){if(!stackTracesEnabled())return;const t=[];captureStackTraces(t,2);const r=t[0],e=t[1];for(let t=0;t<r.length;t++){const e=r[t];if(-1==e.indexOf(ERROR_TAG)){let t=e.match(/^\s*at\s+/);if(t){sepTemplate=t[0]+SEP_TAG+" (http://localhost)";break}}}for(let t=0;t<r.length;t++){const a=r[t];if(a!==e[t])break;IGNORE_FRAMES[a]=!0}}Zone.longStackTraceZoneSpec={name:"long-stack-trace",longStackTraceLimit:10,getLongStackTrace:function(t){if(!t)return;const r=t[Zone.__symbol__("currentTaskTrace")];return r?renderLongStackTrace(r,t.stack):t.stack},onScheduleTask:function(t,r,e,a){if(stackTracesEnabled()){const t=Zone.currentTask;let r=t&&t.data&&t.data[creationTrace]||[];r=[new LongStackTrace].concat(r),r.length>this.longStackTraceLimit&&(r.length=this.longStackTraceLimit),a.data||(a.data={}),"eventTask"===a.type&&(a.data={...a.data}),a.data[creationTrace]=r}return t.scheduleTask(e,a)},onHandleError:function(t,r,e,a){if(stackTracesEnabled()){const t=Zone.currentTask||a.task;if(a instanceof Error&&t){const r=renderLongStackTrace(t.data&&t.data[creationTrace],a.stack);try{a.stack=a.longStack=r}catch(t){}}}return t.handleError(e,a)}},computeIgnoreFrames();