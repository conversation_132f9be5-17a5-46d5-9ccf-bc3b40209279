{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, PLATFORM_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ViewChild, ContentChildren, NgModule } from '@angular/core';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { SpinnerIcon } from 'primeng/icons/spinner';\n\n/**\n * Scroller is a performance-approach to handle huge data efficiently.\n * @group Components\n */\nconst _c0 = [\"element\"];\nconst _c1 = [\"content\"];\nfunction Scroller_ng_container_0_ng_container_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c2 = function (a0, a1) {\n  return {\n    $implicit: a0,\n    options: a1\n  };\n};\nfunction Scroller_ng_container_0_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_ng_container_3_ng_container_1_Template, 1, 0, \"ng-container\", 7);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r4.contentTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c2, ctx_r4.loadedItems, ctx_r4.getContentOptions()));\n  }\n}\nfunction Scroller_ng_container_0_ng_template_4_ng_container_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Scroller_ng_container_0_ng_template_4_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_ng_template_4_ng_container_2_ng_container_1_Template, 1, 0, \"ng-container\", 7);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r12 = ctx.$implicit;\n    const index_r13 = ctx.index;\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r11.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c2, item_r12, ctx_r11.getOptions(index_r13)));\n  }\n}\nconst _c3 = function (a0) {\n  return {\n    \"p-scroller-loading\": a0\n  };\n};\nfunction Scroller_ng_container_0_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8, 9);\n    i0.ɵɵtemplate(2, Scroller_ng_container_0_ng_template_4_ng_container_2_Template, 2, 5, \"ng-container\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c3, ctx_r6.d_loading))(\"ngStyle\", ctx_r6.contentStyle);\n    i0.ɵɵattribute(\"data-pc-section\", \"content\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.loadedItems)(\"ngForTrackBy\", ctx_r6._trackBy || ctx_r6.index);\n  }\n}\nfunction Scroller_ng_container_0_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r7.spacerStyle);\n    i0.ɵɵattribute(\"data-pc-section\", \"spacer\");\n  }\n}\nfunction Scroller_ng_container_0_div_7_ng_container_1_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c4 = function (a0) {\n  return {\n    numCols: a0\n  };\n};\nconst _c5 = function (a0) {\n  return {\n    options: a0\n  };\n};\nfunction Scroller_ng_container_0_div_7_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_div_7_ng_container_1_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 7);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const index_r20 = ctx.index;\n    const ctx_r18 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r18.loaderTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c5, ctx_r18.getLoaderOptions(index_r20, ctx_r18.both && i0.ɵɵpureFunction1(2, _c4, ctx_r18._numItemsInViewport.cols))));\n  }\n}\nfunction Scroller_ng_container_0_div_7_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_div_7_ng_container_1_ng_container_1_Template, 2, 6, \"ng-container\", 14);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r15.loaderArr);\n  }\n}\nfunction Scroller_ng_container_0_div_7_ng_template_2_ng_container_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c6 = function () {\n  return {\n    styleClass: \"p-scroller-loading-icon\"\n  };\n};\nfunction Scroller_ng_container_0_div_7_ng_template_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_div_7_ng_template_2_ng_container_0_ng_container_1_Template, 1, 0, \"ng-container\", 7);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r22.loaderIconTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(3, _c5, i0.ɵɵpureFunction0(2, _c6)));\n  }\n}\nfunction Scroller_ng_container_0_div_7_ng_template_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"SpinnerIcon\", 16);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-scroller-loading-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"loadingIcon\");\n  }\n}\nfunction Scroller_ng_container_0_div_7_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Scroller_ng_container_0_div_7_ng_template_2_ng_container_0_Template, 2, 5, \"ng-container\", 0);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_div_7_ng_template_2_ng_template_1_Template, 1, 2, \"ng-template\", null, 15, i0.ɵɵtemplateRefExtractor);\n  }\n  if (rf & 2) {\n    const _r23 = i0.ɵɵreference(2);\n    const ctx_r17 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r17.loaderIconTemplate)(\"ngIfElse\", _r23);\n  }\n}\nconst _c7 = function (a0) {\n  return {\n    \"p-component-overlay\": a0\n  };\n};\nfunction Scroller_ng_container_0_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_div_7_ng_container_1_Template, 2, 1, \"ng-container\", 0);\n    i0.ɵɵtemplate(2, Scroller_ng_container_0_div_7_ng_template_2_Template, 3, 2, \"ng-template\", null, 13, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const _r16 = i0.ɵɵreference(3);\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c7, !ctx_r8.loaderTemplate));\n    i0.ɵɵattribute(\"data-pc-section\", \"loader\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.loaderTemplate)(\"ngIfElse\", _r16);\n  }\n}\nconst _c8 = function (a1, a2, a3) {\n  return {\n    \"p-scroller\": true,\n    \"p-scroller-inline\": a1,\n    \"p-both-scroll\": a2,\n    \"p-horizontal-scroll\": a3\n  };\n};\nfunction Scroller_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 2, 3);\n    i0.ɵɵlistener(\"scroll\", function Scroller_ng_container_0_Template_div_scroll_1_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.onContainerScroll($event));\n    });\n    i0.ɵɵtemplate(3, Scroller_ng_container_0_ng_container_3_Template, 2, 5, \"ng-container\", 0);\n    i0.ɵɵtemplate(4, Scroller_ng_container_0_ng_template_4_Template, 3, 7, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(6, Scroller_ng_container_0_div_6_Template, 1, 2, \"div\", 5);\n    i0.ɵɵtemplate(7, Scroller_ng_container_0_div_7_Template, 4, 6, \"div\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const _r5 = i0.ɵɵreference(5);\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r0._styleClass);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r0._style)(\"ngClass\", i0.ɵɵpureFunction3(12, _c8, ctx_r0.inline, ctx_r0.both, ctx_r0.horizontal));\n    i0.ɵɵattribute(\"id\", ctx_r0._id)(\"tabindex\", ctx_r0.tabindex)(\"data-pc-name\", \"scroller\")(\"data-pc-section\", \"root\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.contentTemplate)(\"ngIfElse\", _r5);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0._showSpacer);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.loaderDisabled && ctx_r0._showLoader && ctx_r0.d_loading);\n  }\n}\nfunction Scroller_ng_template_1_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c9 = function (a0, a1) {\n  return {\n    rows: a0,\n    columns: a1\n  };\n};\nfunction Scroller_ng_template_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Scroller_ng_template_1_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 7);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r28 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r28.contentTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(5, _c2, ctx_r28.items, i0.ɵɵpureFunction2(2, _c9, ctx_r28._items, ctx_r28.loadedColumns)));\n  }\n}\nfunction Scroller_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n    i0.ɵɵtemplate(1, Scroller_ng_template_1_ng_container_1_Template, 2, 8, \"ng-container\", 17);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.contentTemplate);\n  }\n}\nconst _c10 = [\"*\"];\nclass Scroller {\n  document;\n  platformId;\n  renderer;\n  cd;\n  zone;\n  /**\n   * Unique identifier of the element.\n   * @group Props\n   */\n  get id() {\n    return this._id;\n  }\n  set id(val) {\n    this._id = val;\n  }\n  /**\n   * Inline style of the component.\n   * @group Props\n   */\n  get style() {\n    return this._style;\n  }\n  set style(val) {\n    this._style = val;\n  }\n  /**\n   * Style class of the element.\n   * @group Props\n   */\n  get styleClass() {\n    return this._styleClass;\n  }\n  set styleClass(val) {\n    this._styleClass = val;\n  }\n  /**\n   * Index of the element in tabbing order.\n   * @group Props\n   */\n  get tabindex() {\n    return this._tabindex;\n  }\n  set tabindex(val) {\n    this._tabindex = val;\n  }\n  /**\n   * An array of objects to display.\n   * @group Props\n   */\n  get items() {\n    return this._items;\n  }\n  set items(val) {\n    this._items = val;\n  }\n  /**\n   * The height/width of item according to orientation.\n   * @group Props\n   */\n  get itemSize() {\n    return this._itemSize;\n  }\n  set itemSize(val) {\n    this._itemSize = val;\n  }\n  /**\n   * Height of the scroll viewport.\n   * @group Props\n   */\n  get scrollHeight() {\n    return this._scrollHeight;\n  }\n  set scrollHeight(val) {\n    this._scrollHeight = val;\n  }\n  /**\n   * Width of the scroll viewport.\n   * @group Props\n   */\n  get scrollWidth() {\n    return this._scrollWidth;\n  }\n  set scrollWidth(val) {\n    this._scrollWidth = val;\n  }\n  /**\n   * The orientation of scrollbar.\n   * @group Props\n   */\n  get orientation() {\n    return this._orientation;\n  }\n  set orientation(val) {\n    this._orientation = val;\n  }\n  /**\n   * Used to specify how many items to load in each load method in lazy mode.\n   * @group Props\n   */\n  get step() {\n    return this._step;\n  }\n  set step(val) {\n    this._step = val;\n  }\n  /**\n   * Delay in scroll before new data is loaded.\n   * @group Props\n   */\n  get delay() {\n    return this._delay;\n  }\n  set delay(val) {\n    this._delay = val;\n  }\n  /**\n   * Delay after window's resize finishes.\n   * @group Props\n   */\n  get resizeDelay() {\n    return this._resizeDelay;\n  }\n  set resizeDelay(val) {\n    this._resizeDelay = val;\n  }\n  /**\n   * Used to append each loaded item to top without removing any items from the DOM. Using very large data may cause the browser to crash.\n   * @group Props\n   */\n  get appendOnly() {\n    return this._appendOnly;\n  }\n  set appendOnly(val) {\n    this._appendOnly = val;\n  }\n  /**\n   * Specifies whether the scroller should be displayed inline or not.\n   * @group Props\n   */\n  get inline() {\n    return this._inline;\n  }\n  set inline(val) {\n    this._inline = val;\n  }\n  /**\n   * Defines if data is loaded and interacted with in lazy manner.\n   * @group Props\n   */\n  get lazy() {\n    return this._lazy;\n  }\n  set lazy(val) {\n    this._lazy = val;\n  }\n  /**\n   * If disabled, the scroller feature is eliminated and the content is displayed directly.\n   * @group Props\n   */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(val) {\n    this._disabled = val;\n  }\n  /**\n   * Used to implement a custom loader instead of using the loader feature in the scroller.\n   * @group Props\n   */\n  get loaderDisabled() {\n    return this._loaderDisabled;\n  }\n  set loaderDisabled(val) {\n    this._loaderDisabled = val;\n  }\n  /**\n   * Columns to display.\n   * @group Props\n   */\n  get columns() {\n    return this._columns;\n  }\n  set columns(val) {\n    this._columns = val;\n  }\n  /**\n   * Used to implement a custom spacer instead of using the spacer feature in the scroller.\n   * @group Props\n   */\n  get showSpacer() {\n    return this._showSpacer;\n  }\n  set showSpacer(val) {\n    this._showSpacer = val;\n  }\n  /**\n   * Defines whether to show loader.\n   * @group Props\n   */\n  get showLoader() {\n    return this._showLoader;\n  }\n  set showLoader(val) {\n    this._showLoader = val;\n  }\n  /**\n   * Determines how many additional elements to add to the DOM outside of the view. According to the scrolls made up and down, extra items are added in a certain algorithm in the form of multiples of this number. Default value is half the number of items shown in the view.\n   * @group Props\n   */\n  get numToleratedItems() {\n    return this._numToleratedItems;\n  }\n  set numToleratedItems(val) {\n    this._numToleratedItems = val;\n  }\n  /**\n   * Defines whether the data is loaded.\n   * @group Props\n   */\n  get loading() {\n    return this._loading;\n  }\n  set loading(val) {\n    this._loading = val;\n  }\n  /**\n   * Defines whether to dynamically change the height or width of scrollable container.\n   * @group Props\n   */\n  get autoSize() {\n    return this._autoSize;\n  }\n  set autoSize(val) {\n    this._autoSize = val;\n  }\n  /**\n   * Function to optimize the dom operations by delegating to ngForTrackBy, default algoritm checks for object identity.\n   * @group Props\n   */\n  get trackBy() {\n    return this._trackBy;\n  }\n  set trackBy(val) {\n    this._trackBy = val;\n  }\n  /**\n   * Defines whether to use the scroller feature. The properties of scroller component can be used like an object in it.\n   * @group Props\n   */\n  get options() {\n    return this._options;\n  }\n  set options(val) {\n    this._options = val;\n    if (val && typeof val === 'object') {\n      //@ts-ignore\n      Object.entries(val).forEach(([k, v]) => this[`_${k}`] !== v && (this[`_${k}`] = v));\n    }\n  }\n  /**\n   * Callback to invoke in lazy mode to load new data.\n   * @param {ScrollerLazyLoadEvent} event - Custom lazy load event.\n   * @group Emits\n   */\n  onLazyLoad = new EventEmitter();\n  /**\n   * Callback to invoke when scroll position changes.\n   * @param {ScrollerScrollEvent} event - Custom scroll event.\n   * @group Emits\n   */\n  onScroll = new EventEmitter();\n  /**\n   * Callback to invoke when scroll position and item's range in view changes.\n   * @param {ScrollerScrollEvent} event - Custom scroll index change event.\n   * @group Emits\n   */\n  onScrollIndexChange = new EventEmitter();\n  elementViewChild;\n  contentViewChild;\n  templates;\n  _id;\n  _style;\n  _styleClass;\n  _tabindex = 0;\n  _items;\n  _itemSize = 0;\n  _scrollHeight;\n  _scrollWidth;\n  _orientation = 'vertical';\n  _step = 0;\n  _delay = 0;\n  _resizeDelay = 10;\n  _appendOnly = false;\n  _inline = false;\n  _lazy = false;\n  _disabled = false;\n  _loaderDisabled = false;\n  _columns;\n  _showSpacer = true;\n  _showLoader = false;\n  _numToleratedItems;\n  _loading;\n  _autoSize = false;\n  _trackBy;\n  _options;\n  d_loading = false;\n  d_numToleratedItems;\n  contentEl;\n  contentTemplate;\n  itemTemplate;\n  loaderTemplate;\n  loaderIconTemplate;\n  first = 0;\n  last = 0;\n  page = 0;\n  isRangeChanged = false;\n  numItemsInViewport = 0;\n  lastScrollPos = 0;\n  lazyLoadState = {};\n  loaderArr = [];\n  spacerStyle = {};\n  contentStyle = {};\n  scrollTimeout;\n  resizeTimeout;\n  initialized = false;\n  windowResizeListener;\n  defaultWidth;\n  defaultHeight;\n  defaultContentWidth;\n  defaultContentHeight;\n  get vertical() {\n    return this._orientation === 'vertical';\n  }\n  get horizontal() {\n    return this._orientation === 'horizontal';\n  }\n  get both() {\n    return this._orientation === 'both';\n  }\n  get loadedItems() {\n    if (this._items && !this.d_loading) {\n      if (this.both) return this._items.slice(this._appendOnly ? 0 : this.first.rows, this.last.rows).map(item => this._columns ? item : item.slice(this._appendOnly ? 0 : this.first.cols, this.last.cols));else if (this.horizontal && this._columns) return this._items;else return this._items.slice(this._appendOnly ? 0 : this.first, this.last);\n    }\n    return [];\n  }\n  get loadedRows() {\n    return this.d_loading ? this._loaderDisabled ? this.loaderArr : [] : this.loadedItems;\n  }\n  get loadedColumns() {\n    if (this._columns && (this.both || this.horizontal)) {\n      return this.d_loading && this._loaderDisabled ? this.both ? this.loaderArr[0] : this.loaderArr : this._columns.slice(this.both ? this.first.cols : this.first, this.both ? this.last.cols : this.last);\n    }\n    return this._columns;\n  }\n  get isPageChanged() {\n    return this._step ? this.page !== this.getPageByFirst() : true;\n  }\n  constructor(document, platformId, renderer, cd, zone) {\n    this.document = document;\n    this.platformId = platformId;\n    this.renderer = renderer;\n    this.cd = cd;\n    this.zone = zone;\n  }\n  ngOnInit() {\n    this.setInitialState();\n  }\n  ngOnChanges(simpleChanges) {\n    let isLoadingChanged = false;\n    if (simpleChanges.loading) {\n      const {\n        previousValue,\n        currentValue\n      } = simpleChanges.loading;\n      if (this.lazy && previousValue !== currentValue && currentValue !== this.d_loading) {\n        this.d_loading = currentValue;\n        isLoadingChanged = true;\n      }\n    }\n    if (simpleChanges.orientation) {\n      this.lastScrollPos = this.both ? {\n        top: 0,\n        left: 0\n      } : 0;\n    }\n    if (simpleChanges.numToleratedItems) {\n      const {\n        previousValue,\n        currentValue\n      } = simpleChanges.numToleratedItems;\n      if (previousValue !== currentValue && currentValue !== this.d_numToleratedItems) {\n        this.d_numToleratedItems = currentValue;\n      }\n    }\n    if (simpleChanges.options) {\n      const {\n        previousValue,\n        currentValue\n      } = simpleChanges.options;\n      if (this.lazy && previousValue?.loading !== currentValue?.loading && currentValue?.loading !== this.d_loading) {\n        this.d_loading = currentValue.loading;\n        isLoadingChanged = true;\n      }\n      if (previousValue?.numToleratedItems !== currentValue?.numToleratedItems && currentValue?.numToleratedItems !== this.d_numToleratedItems) {\n        this.d_numToleratedItems = currentValue.numToleratedItems;\n      }\n    }\n    if (this.initialized) {\n      const isChanged = !isLoadingChanged && (simpleChanges.items?.previousValue?.length !== simpleChanges.items?.currentValue?.length || simpleChanges.itemSize || simpleChanges.scrollHeight || simpleChanges.scrollWidth);\n      if (isChanged) {\n        this.init();\n        this.calculateAutoSize();\n      }\n    }\n  }\n  ngAfterContentInit() {\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'content':\n          this.contentTemplate = item.template;\n          break;\n        case 'item':\n          this.itemTemplate = item.template;\n          break;\n        case 'loader':\n          this.loaderTemplate = item.template;\n          break;\n        case 'loadericon':\n          this.loaderIconTemplate = item.template;\n          break;\n        default:\n          this.itemTemplate = item.template;\n          break;\n      }\n    });\n  }\n  ngAfterViewInit() {\n    Promise.resolve().then(() => {\n      this.viewInit();\n    });\n  }\n  ngAfterViewChecked() {\n    if (!this.initialized) {\n      this.viewInit();\n    }\n  }\n  ngOnDestroy() {\n    this.unbindResizeListener();\n    this.contentEl = null;\n    this.initialized = false;\n  }\n  viewInit() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (DomHandler.isVisible(this.elementViewChild?.nativeElement)) {\n        this.setInitialState();\n        this.setContentEl(this.contentEl);\n        this.init();\n        this.defaultWidth = DomHandler.getWidth(this.elementViewChild?.nativeElement);\n        this.defaultHeight = DomHandler.getHeight(this.elementViewChild?.nativeElement);\n        this.defaultContentWidth = DomHandler.getWidth(this.contentEl);\n        this.defaultContentHeight = DomHandler.getHeight(this.contentEl);\n        this.initialized = true;\n      }\n    }\n  }\n  init() {\n    if (!this._disabled) {\n      this.setSize();\n      this.calculateOptions();\n      this.setSpacerSize();\n      this.bindResizeListener();\n      this.cd.detectChanges();\n    }\n  }\n  setContentEl(el) {\n    this.contentEl = el || this.contentViewChild?.nativeElement || DomHandler.findSingle(this.elementViewChild?.nativeElement, '.p-scroller-content');\n  }\n  setInitialState() {\n    this.first = this.both ? {\n      rows: 0,\n      cols: 0\n    } : 0;\n    this.last = this.both ? {\n      rows: 0,\n      cols: 0\n    } : 0;\n    this.numItemsInViewport = this.both ? {\n      rows: 0,\n      cols: 0\n    } : 0;\n    this.lastScrollPos = this.both ? {\n      top: 0,\n      left: 0\n    } : 0;\n    this.d_loading = this._loading || false;\n    this.d_numToleratedItems = this._numToleratedItems;\n    this.loaderArr = [];\n    this.spacerStyle = {};\n    this.contentStyle = {};\n  }\n  getElementRef() {\n    return this.elementViewChild;\n  }\n  getPageByFirst() {\n    return Math.floor((this.first + this.d_numToleratedItems * 4) / (this._step || 1));\n  }\n  scrollTo(options) {\n    this.lastScrollPos = this.both ? {\n      top: 0,\n      left: 0\n    } : 0;\n    this.elementViewChild?.nativeElement?.scrollTo(options);\n  }\n  scrollToIndex(index, behavior = 'auto') {\n    const {\n      numToleratedItems\n    } = this.calculateNumItems();\n    const contentPos = this.getContentPosition();\n    const calculateFirst = (_index = 0, _numT) => _index <= _numT ? 0 : _index;\n    const calculateCoord = (_first, _size, _cpos) => _first * _size + _cpos;\n    const scrollTo = (left = 0, top = 0) => this.scrollTo({\n      left,\n      top,\n      behavior\n    });\n    let newFirst = 0;\n    if (this.both) {\n      newFirst = {\n        rows: calculateFirst(index[0], numToleratedItems[0]),\n        cols: calculateFirst(index[1], numToleratedItems[1])\n      };\n      scrollTo(calculateCoord(newFirst.cols, this._itemSize[1], contentPos.left), calculateCoord(newFirst.rows, this._itemSize[0], contentPos.top));\n    } else {\n      newFirst = calculateFirst(index, numToleratedItems);\n      this.horizontal ? scrollTo(calculateCoord(newFirst, this._itemSize, contentPos.left), 0) : scrollTo(0, calculateCoord(newFirst, this._itemSize, contentPos.top));\n    }\n    this.isRangeChanged = this.first !== newFirst;\n    this.first = newFirst;\n  }\n  scrollInView(index, to, behavior = 'auto') {\n    if (to) {\n      const {\n        first,\n        viewport\n      } = this.getRenderedRange();\n      const scrollTo = (left = 0, top = 0) => this.scrollTo({\n        left,\n        top,\n        behavior\n      });\n      const isToStart = to === 'to-start';\n      const isToEnd = to === 'to-end';\n      if (isToStart) {\n        if (this.both) {\n          if (viewport.first.rows - first.rows > index[0]) {\n            scrollTo(viewport.first.cols * this._itemSize[1], (viewport.first.rows - 1) * this._itemSize[0]);\n          } else if (viewport.first.cols - first.cols > index[1]) {\n            scrollTo((viewport.first.cols - 1) * this._itemSize[1], viewport.first.rows * this._itemSize[0]);\n          }\n        } else {\n          if (viewport.first - first > index) {\n            const pos = (viewport.first - 1) * this._itemSize;\n            this.horizontal ? scrollTo(pos, 0) : scrollTo(0, pos);\n          }\n        }\n      } else if (isToEnd) {\n        if (this.both) {\n          if (viewport.last.rows - first.rows <= index[0] + 1) {\n            scrollTo(viewport.first.cols * this._itemSize[1], (viewport.first.rows + 1) * this._itemSize[0]);\n          } else if (viewport.last.cols - first.cols <= index[1] + 1) {\n            scrollTo((viewport.first.cols + 1) * this._itemSize[1], viewport.first.rows * this._itemSize[0]);\n          }\n        } else {\n          if (viewport.last - first <= index + 1) {\n            const pos = (viewport.first + 1) * this._itemSize;\n            this.horizontal ? scrollTo(pos, 0) : scrollTo(0, pos);\n          }\n        }\n      }\n    } else {\n      this.scrollToIndex(index, behavior);\n    }\n  }\n  getRenderedRange() {\n    const calculateFirstInViewport = (_pos, _size) => Math.floor(_pos / (_size || _pos));\n    let firstInViewport = this.first;\n    let lastInViewport = 0;\n    if (this.elementViewChild?.nativeElement) {\n      const {\n        scrollTop,\n        scrollLeft\n      } = this.elementViewChild.nativeElement;\n      if (this.both) {\n        firstInViewport = {\n          rows: calculateFirstInViewport(scrollTop, this._itemSize[0]),\n          cols: calculateFirstInViewport(scrollLeft, this._itemSize[1])\n        };\n        lastInViewport = {\n          rows: firstInViewport.rows + this.numItemsInViewport.rows,\n          cols: firstInViewport.cols + this.numItemsInViewport.cols\n        };\n      } else {\n        const scrollPos = this.horizontal ? scrollLeft : scrollTop;\n        firstInViewport = calculateFirstInViewport(scrollPos, this._itemSize);\n        lastInViewport = firstInViewport + this.numItemsInViewport;\n      }\n    }\n    return {\n      first: this.first,\n      last: this.last,\n      viewport: {\n        first: firstInViewport,\n        last: lastInViewport\n      }\n    };\n  }\n  calculateNumItems() {\n    const contentPos = this.getContentPosition();\n    const contentWidth = (this.elementViewChild?.nativeElement ? this.elementViewChild.nativeElement.offsetWidth - contentPos.left : 0) || 0;\n    const contentHeight = (this.elementViewChild?.nativeElement ? this.elementViewChild.nativeElement.offsetHeight - contentPos.top : 0) || 0;\n    const calculateNumItemsInViewport = (_contentSize, _itemSize) => Math.ceil(_contentSize / (_itemSize || _contentSize));\n    const calculateNumToleratedItems = _numItems => Math.ceil(_numItems / 2);\n    const numItemsInViewport = this.both ? {\n      rows: calculateNumItemsInViewport(contentHeight, this._itemSize[0]),\n      cols: calculateNumItemsInViewport(contentWidth, this._itemSize[1])\n    } : calculateNumItemsInViewport(this.horizontal ? contentWidth : contentHeight, this._itemSize);\n    const numToleratedItems = this.d_numToleratedItems || (this.both ? [calculateNumToleratedItems(numItemsInViewport.rows), calculateNumToleratedItems(numItemsInViewport.cols)] : calculateNumToleratedItems(numItemsInViewport));\n    return {\n      numItemsInViewport,\n      numToleratedItems\n    };\n  }\n  calculateOptions() {\n    const {\n      numItemsInViewport,\n      numToleratedItems\n    } = this.calculateNumItems();\n    const calculateLast = (_first, _num, _numT, _isCols = false) => this.getLast(_first + _num + (_first < _numT ? 2 : 3) * _numT, _isCols);\n    const first = this.first;\n    const last = this.both ? {\n      rows: calculateLast(this.first.rows, numItemsInViewport.rows, numToleratedItems[0]),\n      cols: calculateLast(this.first.cols, numItemsInViewport.cols, numToleratedItems[1], true)\n    } : calculateLast(this.first, numItemsInViewport, numToleratedItems);\n    this.last = last;\n    this.numItemsInViewport = numItemsInViewport;\n    this.d_numToleratedItems = numToleratedItems;\n    if (this.showLoader) {\n      this.loaderArr = this.both ? Array.from({\n        length: numItemsInViewport.rows\n      }).map(() => Array.from({\n        length: numItemsInViewport.cols\n      })) : Array.from({\n        length: numItemsInViewport\n      });\n    }\n    if (this._lazy) {\n      Promise.resolve().then(() => {\n        this.lazyLoadState = {\n          first: this._step ? this.both ? {\n            rows: 0,\n            cols: first.cols\n          } : 0 : first,\n          last: Math.min(this._step ? this._step : this.last, this.items.length)\n        };\n        this.handleEvents('onLazyLoad', this.lazyLoadState);\n      });\n    }\n  }\n  calculateAutoSize() {\n    if (this._autoSize && !this.d_loading) {\n      Promise.resolve().then(() => {\n        if (this.contentEl) {\n          this.contentEl.style.minHeight = this.contentEl.style.minWidth = 'auto';\n          this.contentEl.style.position = 'relative';\n          this.elementViewChild.nativeElement.style.contain = 'none';\n          const [contentWidth, contentHeight] = [DomHandler.getWidth(this.contentEl), DomHandler.getHeight(this.contentEl)];\n          contentWidth !== this.defaultContentWidth && (this.elementViewChild.nativeElement.style.width = '');\n          contentHeight !== this.defaultContentHeight && (this.elementViewChild.nativeElement.style.height = '');\n          const [width, height] = [DomHandler.getWidth(this.elementViewChild.nativeElement), DomHandler.getHeight(this.elementViewChild.nativeElement)];\n          (this.both || this.horizontal) && (this.elementViewChild.nativeElement.style.width = width < this.defaultWidth ? width + 'px' : this._scrollWidth || this.defaultWidth + 'px');\n          (this.both || this.vertical) && (this.elementViewChild.nativeElement.style.height = height < this.defaultHeight ? height + 'px' : this._scrollHeight || this.defaultHeight + 'px');\n          this.contentEl.style.minHeight = this.contentEl.style.minWidth = '';\n          this.contentEl.style.position = '';\n          this.elementViewChild.nativeElement.style.contain = '';\n        }\n      });\n    }\n  }\n  getLast(last = 0, isCols = false) {\n    return this._items ? Math.min(isCols ? (this._columns || this._items[0]).length : this._items.length, last) : 0;\n  }\n  getContentPosition() {\n    if (this.contentEl) {\n      const style = getComputedStyle(this.contentEl);\n      const left = parseFloat(style.paddingLeft) + Math.max(parseFloat(style.left) || 0, 0);\n      const right = parseFloat(style.paddingRight) + Math.max(parseFloat(style.right) || 0, 0);\n      const top = parseFloat(style.paddingTop) + Math.max(parseFloat(style.top) || 0, 0);\n      const bottom = parseFloat(style.paddingBottom) + Math.max(parseFloat(style.bottom) || 0, 0);\n      return {\n        left,\n        right,\n        top,\n        bottom,\n        x: left + right,\n        y: top + bottom\n      };\n    }\n    return {\n      left: 0,\n      right: 0,\n      top: 0,\n      bottom: 0,\n      x: 0,\n      y: 0\n    };\n  }\n  setSize() {\n    if (this.elementViewChild?.nativeElement) {\n      const parentElement = this.elementViewChild.nativeElement.parentElement.parentElement;\n      const width = this._scrollWidth || `${this.elementViewChild.nativeElement.offsetWidth || parentElement.offsetWidth}px`;\n      const height = this._scrollHeight || `${this.elementViewChild.nativeElement.offsetHeight || parentElement.offsetHeight}px`;\n      const setProp = (_name, _value) => this.elementViewChild.nativeElement.style[_name] = _value;\n      if (this.both || this.horizontal) {\n        setProp('height', height);\n        setProp('width', width);\n      } else {\n        setProp('height', height);\n      }\n    }\n  }\n  setSpacerSize() {\n    if (this._items) {\n      const contentPos = this.getContentPosition();\n      const setProp = (_name, _value, _size, _cpos = 0) => this.spacerStyle = {\n        ...this.spacerStyle,\n        ...{\n          [`${_name}`]: (_value || []).length * _size + _cpos + 'px'\n        }\n      };\n      if (this.both) {\n        setProp('height', this._items, this._itemSize[0], contentPos.y);\n        setProp('width', this._columns || this._items[1], this._itemSize[1], contentPos.x);\n      } else {\n        this.horizontal ? setProp('width', this._columns || this._items, this._itemSize, contentPos.x) : setProp('height', this._items, this._itemSize, contentPos.y);\n      }\n    }\n  }\n  setContentPosition(pos) {\n    if (this.contentEl && !this._appendOnly) {\n      const first = pos ? pos.first : this.first;\n      const calculateTranslateVal = (_first, _size) => _first * _size;\n      const setTransform = (_x = 0, _y = 0) => this.contentStyle = {\n        ...this.contentStyle,\n        ...{\n          transform: `translate3d(${_x}px, ${_y}px, 0)`\n        }\n      };\n      if (this.both) {\n        setTransform(calculateTranslateVal(first.cols, this._itemSize[1]), calculateTranslateVal(first.rows, this._itemSize[0]));\n      } else {\n        const translateVal = calculateTranslateVal(first, this._itemSize);\n        this.horizontal ? setTransform(translateVal, 0) : setTransform(0, translateVal);\n      }\n    }\n  }\n  onScrollPositionChange(event) {\n    const target = event.target;\n    const contentPos = this.getContentPosition();\n    const calculateScrollPos = (_pos, _cpos) => _pos ? _pos > _cpos ? _pos - _cpos : _pos : 0;\n    const calculateCurrentIndex = (_pos, _size) => Math.floor(_pos / (_size || _pos));\n    const calculateTriggerIndex = (_currentIndex, _first, _last, _num, _numT, _isScrollDownOrRight) => {\n      return _currentIndex <= _numT ? _numT : _isScrollDownOrRight ? _last - _num - _numT : _first + _numT - 1;\n    };\n    const calculateFirst = (_currentIndex, _triggerIndex, _first, _last, _num, _numT, _isScrollDownOrRight) => {\n      if (_currentIndex <= _numT) return 0;else return Math.max(0, _isScrollDownOrRight ? _currentIndex < _triggerIndex ? _first : _currentIndex - _numT : _currentIndex > _triggerIndex ? _first : _currentIndex - 2 * _numT);\n    };\n    const calculateLast = (_currentIndex, _first, _last, _num, _numT, _isCols = false) => {\n      let lastValue = _first + _num + 2 * _numT;\n      if (_currentIndex >= _numT) {\n        lastValue += _numT + 1;\n      }\n      return this.getLast(lastValue, _isCols);\n    };\n    const scrollTop = calculateScrollPos(target.scrollTop, contentPos.top);\n    const scrollLeft = calculateScrollPos(target.scrollLeft, contentPos.left);\n    let newFirst = this.both ? {\n      rows: 0,\n      cols: 0\n    } : 0;\n    let newLast = this.last;\n    let isRangeChanged = false;\n    let newScrollPos = this.lastScrollPos;\n    if (this.both) {\n      const isScrollDown = this.lastScrollPos.top <= scrollTop;\n      const isScrollRight = this.lastScrollPos.left <= scrollLeft;\n      if (!this._appendOnly || this._appendOnly && (isScrollDown || isScrollRight)) {\n        const currentIndex = {\n          rows: calculateCurrentIndex(scrollTop, this._itemSize[0]),\n          cols: calculateCurrentIndex(scrollLeft, this._itemSize[1])\n        };\n        const triggerIndex = {\n          rows: calculateTriggerIndex(currentIndex.rows, this.first.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0], isScrollDown),\n          cols: calculateTriggerIndex(currentIndex.cols, this.first.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], isScrollRight)\n        };\n        newFirst = {\n          rows: calculateFirst(currentIndex.rows, triggerIndex.rows, this.first.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0], isScrollDown),\n          cols: calculateFirst(currentIndex.cols, triggerIndex.cols, this.first.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], isScrollRight)\n        };\n        newLast = {\n          rows: calculateLast(currentIndex.rows, newFirst.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0]),\n          cols: calculateLast(currentIndex.cols, newFirst.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], true)\n        };\n        isRangeChanged = newFirst.rows !== this.first.rows || newLast.rows !== this.last.rows || newFirst.cols !== this.first.cols || newLast.cols !== this.last.cols || this.isRangeChanged;\n        newScrollPos = {\n          top: scrollTop,\n          left: scrollLeft\n        };\n      }\n    } else {\n      const scrollPos = this.horizontal ? scrollLeft : scrollTop;\n      const isScrollDownOrRight = this.lastScrollPos <= scrollPos;\n      if (!this._appendOnly || this._appendOnly && isScrollDownOrRight) {\n        const currentIndex = calculateCurrentIndex(scrollPos, this._itemSize);\n        const triggerIndex = calculateTriggerIndex(currentIndex, this.first, this.last, this.numItemsInViewport, this.d_numToleratedItems, isScrollDownOrRight);\n        newFirst = calculateFirst(currentIndex, triggerIndex, this.first, this.last, this.numItemsInViewport, this.d_numToleratedItems, isScrollDownOrRight);\n        newLast = calculateLast(currentIndex, newFirst, this.last, this.numItemsInViewport, this.d_numToleratedItems);\n        isRangeChanged = newFirst !== this.first || newLast !== this.last || this.isRangeChanged;\n        newScrollPos = scrollPos;\n      }\n    }\n    return {\n      first: newFirst,\n      last: newLast,\n      isRangeChanged,\n      scrollPos: newScrollPos\n    };\n  }\n  onScrollChange(event) {\n    const {\n      first,\n      last,\n      isRangeChanged,\n      scrollPos\n    } = this.onScrollPositionChange(event);\n    if (isRangeChanged) {\n      const newState = {\n        first,\n        last\n      };\n      this.setContentPosition(newState);\n      this.first = first;\n      this.last = last;\n      this.lastScrollPos = scrollPos;\n      this.handleEvents('onScrollIndexChange', newState);\n      if (this._lazy && this.isPageChanged) {\n        const lazyLoadState = {\n          first: this._step ? Math.min(this.getPageByFirst() * this._step, this.items.length - this._step) : first,\n          last: Math.min(this._step ? (this.getPageByFirst() + 1) * this._step : last, this.items.length)\n        };\n        const isLazyStateChanged = this.lazyLoadState.first !== lazyLoadState.first || this.lazyLoadState.last !== lazyLoadState.last;\n        isLazyStateChanged && this.handleEvents('onLazyLoad', lazyLoadState);\n        this.lazyLoadState = lazyLoadState;\n      }\n    }\n  }\n  onContainerScroll(event) {\n    this.handleEvents('onScroll', {\n      originalEvent: event\n    });\n    if (this._delay && this.isPageChanged) {\n      if (this.scrollTimeout) {\n        clearTimeout(this.scrollTimeout);\n      }\n      if (!this.d_loading && this.showLoader) {\n        const {\n          isRangeChanged\n        } = this.onScrollPositionChange(event);\n        const changed = isRangeChanged || (this._step ? this.isPageChanged : false);\n        if (changed) {\n          this.d_loading = true;\n          this.cd.detectChanges();\n        }\n      }\n      this.scrollTimeout = setTimeout(() => {\n        this.onScrollChange(event);\n        if (this.d_loading && this.showLoader && (!this._lazy || this._loading === undefined)) {\n          this.d_loading = false;\n          this.page = this.getPageByFirst();\n          this.cd.detectChanges();\n        }\n      }, this._delay);\n    } else {\n      !this.d_loading && this.onScrollChange(event);\n    }\n  }\n  bindResizeListener() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (!this.windowResizeListener) {\n        this.zone.runOutsideAngular(() => {\n          const window = this.document.defaultView;\n          const event = DomHandler.isTouchDevice() ? 'orientationchange' : 'resize';\n          this.windowResizeListener = this.renderer.listen(window, event, this.onWindowResize.bind(this));\n        });\n      }\n    }\n  }\n  unbindResizeListener() {\n    if (this.windowResizeListener) {\n      this.windowResizeListener();\n      this.windowResizeListener = null;\n    }\n  }\n  onWindowResize() {\n    if (this.resizeTimeout) {\n      clearTimeout(this.resizeTimeout);\n    }\n    this.resizeTimeout = setTimeout(() => {\n      if (DomHandler.isVisible(this.elementViewChild?.nativeElement)) {\n        const [width, height] = [DomHandler.getWidth(this.elementViewChild?.nativeElement), DomHandler.getHeight(this.elementViewChild?.nativeElement)];\n        const [isDiffWidth, isDiffHeight] = [width !== this.defaultWidth, height !== this.defaultHeight];\n        const reinit = this.both ? isDiffWidth || isDiffHeight : this.horizontal ? isDiffWidth : this.vertical ? isDiffHeight : false;\n        reinit && this.zone.run(() => {\n          this.d_numToleratedItems = this._numToleratedItems;\n          this.defaultWidth = width;\n          this.defaultHeight = height;\n          this.defaultContentWidth = DomHandler.getWidth(this.contentEl);\n          this.defaultContentHeight = DomHandler.getHeight(this.contentEl);\n          this.init();\n        });\n      }\n    }, this._resizeDelay);\n  }\n  handleEvents(name, params) {\n    //@ts-ignore\n    return this.options && this.options[name] ? this.options[name](params) : this[name].emit(params);\n  }\n  getContentOptions() {\n    return {\n      contentStyleClass: `p-scroller-content ${this.d_loading ? 'p-scroller-loading' : ''}`,\n      items: this.loadedItems,\n      getItemOptions: index => this.getOptions(index),\n      loading: this.d_loading,\n      getLoaderOptions: (index, options) => this.getLoaderOptions(index, options),\n      itemSize: this._itemSize,\n      rows: this.loadedRows,\n      columns: this.loadedColumns,\n      spacerStyle: this.spacerStyle,\n      contentStyle: this.contentStyle,\n      vertical: this.vertical,\n      horizontal: this.horizontal,\n      both: this.both\n    };\n  }\n  getOptions(renderedIndex) {\n    const count = (this._items || []).length;\n    const index = this.both ? this.first.rows + renderedIndex : this.first + renderedIndex;\n    return {\n      index,\n      count,\n      first: index === 0,\n      last: index === count - 1,\n      even: index % 2 === 0,\n      odd: index % 2 !== 0\n    };\n  }\n  getLoaderOptions(index, extOptions) {\n    const count = this.loaderArr.length;\n    return {\n      index,\n      count,\n      first: index === 0,\n      last: index === count - 1,\n      even: index % 2 === 0,\n      odd: index % 2 !== 0,\n      ...extOptions\n    };\n  }\n  static ɵfac = function Scroller_Factory(t) {\n    return new (t || Scroller)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Scroller,\n    selectors: [[\"p-scroller\"]],\n    contentQueries: function Scroller_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    viewQuery: function Scroller_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.elementViewChild = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.contentViewChild = _t.first);\n      }\n    },\n    hostAttrs: [1, \"p-scroller-viewport\", \"p-element\"],\n    inputs: {\n      id: \"id\",\n      style: \"style\",\n      styleClass: \"styleClass\",\n      tabindex: \"tabindex\",\n      items: \"items\",\n      itemSize: \"itemSize\",\n      scrollHeight: \"scrollHeight\",\n      scrollWidth: \"scrollWidth\",\n      orientation: \"orientation\",\n      step: \"step\",\n      delay: \"delay\",\n      resizeDelay: \"resizeDelay\",\n      appendOnly: \"appendOnly\",\n      inline: \"inline\",\n      lazy: \"lazy\",\n      disabled: \"disabled\",\n      loaderDisabled: \"loaderDisabled\",\n      columns: \"columns\",\n      showSpacer: \"showSpacer\",\n      showLoader: \"showLoader\",\n      numToleratedItems: \"numToleratedItems\",\n      loading: \"loading\",\n      autoSize: \"autoSize\",\n      trackBy: \"trackBy\",\n      options: \"options\"\n    },\n    outputs: {\n      onLazyLoad: \"onLazyLoad\",\n      onScroll: \"onScroll\",\n      onScrollIndexChange: \"onScrollIndexChange\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c10,\n    decls: 3,\n    vars: 2,\n    consts: [[4, \"ngIf\", \"ngIfElse\"], [\"disabledContainer\", \"\"], [3, \"ngStyle\", \"ngClass\", \"scroll\"], [\"element\", \"\"], [\"buildInContent\", \"\"], [\"class\", \"p-scroller-spacer\", 3, \"ngStyle\", 4, \"ngIf\"], [\"class\", \"p-scroller-loader\", 3, \"ngClass\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"p-scroller-content\", 3, \"ngClass\", \"ngStyle\"], [\"content\", \"\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"p-scroller-spacer\", 3, \"ngStyle\"], [1, \"p-scroller-loader\", 3, \"ngClass\"], [\"buildInLoader\", \"\"], [4, \"ngFor\", \"ngForOf\"], [\"buildInLoaderIcon\", \"\"], [3, \"styleClass\"], [4, \"ngIf\"]],\n    template: function Scroller_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵtemplate(0, Scroller_ng_container_0_Template, 8, 16, \"ng-container\", 0);\n        i0.ɵɵtemplate(1, Scroller_ng_template_1_Template, 2, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        const _r1 = i0.ɵɵreference(2);\n        i0.ɵɵproperty(\"ngIf\", !ctx._disabled)(\"ngIfElse\", _r1);\n      }\n    },\n    dependencies: function () {\n      return [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, SpinnerIcon];\n    },\n    styles: [\"@layer primeng{p-scroller{flex:1;outline:0 none}.p-scroller{position:relative;overflow:auto;contain:strict;transform:translateZ(0);will-change:scroll-position;outline:0 none}.p-scroller-content{position:absolute;top:0;left:0;min-height:100%;min-width:100%;will-change:transform}.p-scroller-spacer{position:absolute;top:0;left:0;height:1px;width:1px;transform-origin:0 0;pointer-events:none}.p-scroller-loader{position:sticky;top:0;left:0;width:100%;height:100%}.p-scroller-loader.p-component-overlay{display:flex;align-items:center;justify-content:center}.p-scroller-loading-icon{scale:2}.p-scroller-inline .p-scroller-content{position:static}}\\n\"],\n    encapsulation: 2\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Scroller, [{\n    type: Component,\n    args: [{\n      selector: 'p-scroller',\n      template: `\n        <ng-container *ngIf=\"!_disabled; else disabledContainer\">\n            <div\n                #element\n                [attr.id]=\"_id\"\n                [attr.tabindex]=\"tabindex\"\n                [ngStyle]=\"_style\"\n                [class]=\"_styleClass\"\n                [ngClass]=\"{ 'p-scroller': true, 'p-scroller-inline': inline, 'p-both-scroll': both, 'p-horizontal-scroll': horizontal }\"\n                (scroll)=\"onContainerScroll($event)\"\n                [attr.data-pc-name]=\"'scroller'\"\n                [attr.data-pc-section]=\"'root'\"\n            >\n                <ng-container *ngIf=\"contentTemplate; else buildInContent\">\n                    <ng-container *ngTemplateOutlet=\"contentTemplate; context: { $implicit: loadedItems, options: getContentOptions() }\"></ng-container>\n                </ng-container>\n                <ng-template #buildInContent>\n                    <div #content class=\"p-scroller-content\" [ngClass]=\"{ 'p-scroller-loading': d_loading }\" [ngStyle]=\"contentStyle\" [attr.data-pc-section]=\"'content'\">\n                        <ng-container *ngFor=\"let item of loadedItems; let index = index; trackBy: _trackBy || index\">\n                            <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item, options: getOptions(index) }\"></ng-container>\n                        </ng-container>\n                    </div>\n                </ng-template>\n                <div *ngIf=\"_showSpacer\" class=\"p-scroller-spacer\" [ngStyle]=\"spacerStyle\" [attr.data-pc-section]=\"'spacer'\"></div>\n                <div *ngIf=\"!loaderDisabled && _showLoader && d_loading\" class=\"p-scroller-loader\" [ngClass]=\"{ 'p-component-overlay': !loaderTemplate }\" [attr.data-pc-section]=\"'loader'\">\n                    <ng-container *ngIf=\"loaderTemplate; else buildInLoader\">\n                        <ng-container *ngFor=\"let item of loaderArr; let index = index\">\n                            <ng-container *ngTemplateOutlet=\"loaderTemplate; context: { options: getLoaderOptions(index, both && { numCols: _numItemsInViewport.cols }) }\"></ng-container>\n                        </ng-container>\n                    </ng-container>\n                    <ng-template #buildInLoader>\n                        <ng-container *ngIf=\"loaderIconTemplate; else buildInLoaderIcon\">\n                            <ng-container *ngTemplateOutlet=\"loaderIconTemplate; context: { options: { styleClass: 'p-scroller-loading-icon' } }\"></ng-container>\n                        </ng-container>\n                        <ng-template #buildInLoaderIcon>\n                            <SpinnerIcon [styleClass]=\"'p-scroller-loading-icon'\" [attr.data-pc-section]=\"'loadingIcon'\" />\n                        </ng-template>\n                    </ng-template>\n                </div>\n            </div>\n        </ng-container>\n        <ng-template #disabledContainer>\n            <ng-content></ng-content>\n            <ng-container *ngIf=\"contentTemplate\">\n                <ng-container *ngTemplateOutlet=\"contentTemplate; context: { $implicit: items, options: { rows: _items, columns: loadedColumns } }\"></ng-container>\n            </ng-container>\n        </ng-template>\n    `,\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-scroller-viewport p-element'\n      },\n      styles: [\"@layer primeng{p-scroller{flex:1;outline:0 none}.p-scroller{position:relative;overflow:auto;contain:strict;transform:translateZ(0);will-change:scroll-position;outline:0 none}.p-scroller-content{position:absolute;top:0;left:0;min-height:100%;min-width:100%;will-change:transform}.p-scroller-spacer{position:absolute;top:0;left:0;height:1px;width:1px;transform-origin:0 0;pointer-events:none}.p-scroller-loader{position:sticky;top:0;left:0;width:100%;height:100%}.p-scroller-loader.p-component-overlay{display:flex;align-items:center;justify-content:center}.p-scroller-loading-icon{scale:2}.p-scroller-inline .p-scroller-content{position:static}}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: Document,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [PLATFORM_ID]\n      }]\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    id: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    tabindex: [{\n      type: Input\n    }],\n    items: [{\n      type: Input\n    }],\n    itemSize: [{\n      type: Input\n    }],\n    scrollHeight: [{\n      type: Input\n    }],\n    scrollWidth: [{\n      type: Input\n    }],\n    orientation: [{\n      type: Input\n    }],\n    step: [{\n      type: Input\n    }],\n    delay: [{\n      type: Input\n    }],\n    resizeDelay: [{\n      type: Input\n    }],\n    appendOnly: [{\n      type: Input\n    }],\n    inline: [{\n      type: Input\n    }],\n    lazy: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    loaderDisabled: [{\n      type: Input\n    }],\n    columns: [{\n      type: Input\n    }],\n    showSpacer: [{\n      type: Input\n    }],\n    showLoader: [{\n      type: Input\n    }],\n    numToleratedItems: [{\n      type: Input\n    }],\n    loading: [{\n      type: Input\n    }],\n    autoSize: [{\n      type: Input\n    }],\n    trackBy: [{\n      type: Input\n    }],\n    options: [{\n      type: Input\n    }],\n    onLazyLoad: [{\n      type: Output\n    }],\n    onScroll: [{\n      type: Output\n    }],\n    onScrollIndexChange: [{\n      type: Output\n    }],\n    elementViewChild: [{\n      type: ViewChild,\n      args: ['element']\n    }],\n    contentViewChild: [{\n      type: ViewChild,\n      args: ['content']\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\nclass ScrollerModule {\n  static ɵfac = function ScrollerModule_Factory(t) {\n    return new (t || ScrollerModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ScrollerModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, SharedModule, SpinnerIcon, SharedModule]\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, SharedModule, SpinnerIcon],\n      exports: [Scroller, SharedModule],\n      declarations: [Scroller]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Scroller, ScrollerModule };", "map": {"version": 3, "names": ["i1", "isPlatformBrowser", "DOCUMENT", "CommonModule", "i0", "EventEmitter", "PLATFORM_ID", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Inject", "Input", "Output", "ViewChild", "ContentChildren", "NgModule", "PrimeTemplate", "SharedModule", "<PERSON><PERSON><PERSON><PERSON>", "SpinnerIcon", "_c0", "_c1", "Scroller_ng_container_0_ng_container_3_ng_container_1_Template", "rf", "ctx", "ɵɵelementContainer", "_c2", "a0", "a1", "$implicit", "options", "Scroller_ng_container_0_ng_container_3_Template", "ɵɵelementContainerStart", "ɵɵtemplate", "ɵɵelementContainerEnd", "ctx_r4", "ɵɵnextContext", "ɵɵadvance", "ɵɵproperty", "contentTemplate", "ɵɵpureFunction2", "loadedItems", "getContentOptions", "Scroller_ng_container_0_ng_template_4_ng_container_2_ng_container_1_Template", "Scroller_ng_container_0_ng_template_4_ng_container_2_Template", "item_r12", "index_r13", "index", "ctx_r11", "itemTemplate", "getOptions", "_c3", "Scroller_ng_container_0_ng_template_4_Template", "ɵɵelementStart", "ɵɵelementEnd", "ctx_r6", "ɵɵpureFunction1", "d_loading", "contentStyle", "ɵɵattribute", "_trackBy", "Scroller_ng_container_0_div_6_Template", "ɵɵelement", "ctx_r7", "spacerStyle", "Scroller_ng_container_0_div_7_ng_container_1_ng_container_1_ng_container_1_Template", "_c4", "numCols", "_c5", "Scroller_ng_container_0_div_7_ng_container_1_ng_container_1_Template", "index_r20", "ctx_r18", "loaderTemplate", "getLoaderOptions", "both", "_numItemsInViewport", "cols", "Scroller_ng_container_0_div_7_ng_container_1_Template", "ctx_r15", "loaderArr", "Scroller_ng_container_0_div_7_ng_template_2_ng_container_0_ng_container_1_Template", "_c6", "styleClass", "Scroller_ng_container_0_div_7_ng_template_2_ng_container_0_Template", "ctx_r22", "loaderIconTemplate", "ɵɵpureFunction0", "Scroller_ng_container_0_div_7_ng_template_2_ng_template_1_Template", "Scroller_ng_container_0_div_7_ng_template_2_Template", "ɵɵtemplateRefExtractor", "_r23", "ɵɵreference", "ctx_r17", "_c7", "Scroller_ng_container_0_div_7_Template", "_r16", "ctx_r8", "_c8", "a2", "a3", "Scroller_ng_container_0_Template", "_r27", "ɵɵgetCurrentView", "ɵɵlistener", "Scroller_ng_container_0_Template_div_scroll_1_listener", "$event", "ɵɵrestoreView", "ctx_r26", "ɵɵresetView", "onContainerScroll", "_r5", "ctx_r0", "ɵɵclassMap", "_styleClass", "_style", "ɵɵpureFunction3", "inline", "horizontal", "_id", "tabindex", "_showSpacer", "loaderDisabled", "_showLoader", "Scroller_ng_template_1_ng_container_1_ng_container_1_Template", "_c9", "rows", "columns", "Scroller_ng_template_1_ng_container_1_Template", "ctx_r28", "items", "_items", "loadedColumns", "Scroller_ng_template_1_Template", "ɵɵprojection", "ctx_r2", "_c10", "<PERSON><PERSON><PERSON>", "document", "platformId", "renderer", "cd", "zone", "id", "val", "style", "_tabindex", "itemSize", "_itemSize", "scrollHeight", "_scrollHeight", "scrollWidth", "_scrollWidth", "orientation", "_orientation", "step", "_step", "delay", "_delay", "resizeDelay", "_resizeDelay", "appendOnly", "_appendOnly", "_inline", "lazy", "_lazy", "disabled", "_disabled", "_loaderDisabled", "_columns", "showSpacer", "<PERSON><PERSON><PERSON><PERSON>", "numToleratedItems", "_numToleratedItems", "loading", "_loading", "autoSize", "_autoSize", "trackBy", "_options", "Object", "entries", "for<PERSON>ach", "k", "v", "onLazyLoad", "onScroll", "onScrollIndexChange", "elementViewChild", "contentViewChild", "templates", "d_numToleratedItems", "contentEl", "first", "last", "page", "isRangeChanged", "numItemsInViewport", "lastScrollPos", "lazyLoadState", "scrollTimeout", "resizeTimeout", "initialized", "windowResizeListener", "defaultWidth", "defaultHeight", "defaultContentWidth", "defaultContentHeight", "vertical", "slice", "map", "item", "loadedRows", "isPageChanged", "getPageByFirst", "constructor", "ngOnInit", "setInitialState", "ngOnChanges", "simpleChanges", "isLoadingChanged", "previousValue", "currentValue", "top", "left", "isChanged", "length", "init", "calculateAutoSize", "ngAfterContentInit", "getType", "template", "ngAfterViewInit", "Promise", "resolve", "then", "viewInit", "ngAfterViewChecked", "ngOnDestroy", "unbindResizeListener", "isVisible", "nativeElement", "setContentEl", "getWidth", "getHeight", "setSize", "calculateOptions", "setSpacerSize", "bindResizeListener", "detectChanges", "el", "findSingle", "getElementRef", "Math", "floor", "scrollTo", "scrollToIndex", "behavior", "calculateNumItems", "contentPos", "getContentPosition", "calculateFirst", "_index", "_numT", "calculateCoord", "_first", "_size", "_cpos", "newFirst", "scrollInView", "to", "viewport", "getRenderedRange", "isToStart", "isToEnd", "pos", "calculateFirstInViewport", "_pos", "firstInViewport", "lastInViewport", "scrollTop", "scrollLeft", "scrollPos", "contentWidth", "offsetWidth", "contentHeight", "offsetHeight", "calculateNumItemsInViewport", "_contentSize", "ceil", "calculateNumToleratedItems", "_numItems", "calculateLast", "_num", "_isCols", "getLast", "Array", "from", "min", "handleEvents", "minHeight", "min<PERSON><PERSON><PERSON>", "position", "contain", "width", "height", "isCols", "getComputedStyle", "parseFloat", "paddingLeft", "max", "right", "paddingRight", "paddingTop", "bottom", "paddingBottom", "x", "y", "parentElement", "setProp", "_name", "_value", "setContentPosition", "calculateTranslateVal", "setTransform", "_x", "_y", "transform", "translateVal", "onScrollPositionChange", "event", "target", "calculateScrollPos", "calculateCurrentIndex", "calculateTriggerIndex", "_currentIndex", "_last", "_isScrollDownOrRight", "_triggerIndex", "lastValue", "newLast", "newScrollPos", "isScrollDown", "isScrollRight", "currentIndex", "triggerIndex", "isScrollDownOrRight", "onScrollChange", "newState", "isLazyStateChanged", "originalEvent", "clearTimeout", "changed", "setTimeout", "undefined", "runOutsideAngular", "window", "defaultView", "isTouchDevice", "listen", "onWindowResize", "bind", "isDiffWidth", "isDiffHeight", "reinit", "run", "name", "params", "emit", "contentStyleClass", "getItemOptions", "renderedIndex", "count", "even", "odd", "extOptions", "ɵfac", "Scroller_Factory", "t", "ɵɵdirectiveInject", "Renderer2", "ChangeDetectorRef", "NgZone", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "contentQueries", "Scroller_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "viewQuery", "Scroller_Query", "ɵɵviewQuery", "hostAttrs", "inputs", "outputs", "features", "ɵɵNgOnChangesFeature", "ngContentSelectors", "decls", "vars", "consts", "Scroller_Template", "ɵɵprojectionDef", "_r1", "dependencies", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "NgStyle", "styles", "encapsulation", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "changeDetection", "<PERSON><PERSON><PERSON>", "None", "host", "class", "Document", "decorators", "ScrollerModule", "ScrollerModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/Desktop/تطبيق ناشر ايات قئانية/QuranVidGen/node_modules/primeng/fesm2022/primeng-scroller.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, PLATFORM_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ViewChild, ContentChildren, NgModule } from '@angular/core';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { SpinnerIcon } from 'primeng/icons/spinner';\n\n/**\n * Scroller is a performance-approach to handle huge data efficiently.\n * @group Components\n */\nclass Scroller {\n    document;\n    platformId;\n    renderer;\n    cd;\n    zone;\n    /**\n     * Unique identifier of the element.\n     * @group Props\n     */\n    get id() {\n        return this._id;\n    }\n    set id(val) {\n        this._id = val;\n    }\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    get style() {\n        return this._style;\n    }\n    set style(val) {\n        this._style = val;\n    }\n    /**\n     * Style class of the element.\n     * @group Props\n     */\n    get styleClass() {\n        return this._styleClass;\n    }\n    set styleClass(val) {\n        this._styleClass = val;\n    }\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    get tabindex() {\n        return this._tabindex;\n    }\n    set tabindex(val) {\n        this._tabindex = val;\n    }\n    /**\n     * An array of objects to display.\n     * @group Props\n     */\n    get items() {\n        return this._items;\n    }\n    set items(val) {\n        this._items = val;\n    }\n    /**\n     * The height/width of item according to orientation.\n     * @group Props\n     */\n    get itemSize() {\n        return this._itemSize;\n    }\n    set itemSize(val) {\n        this._itemSize = val;\n    }\n    /**\n     * Height of the scroll viewport.\n     * @group Props\n     */\n    get scrollHeight() {\n        return this._scrollHeight;\n    }\n    set scrollHeight(val) {\n        this._scrollHeight = val;\n    }\n    /**\n     * Width of the scroll viewport.\n     * @group Props\n     */\n    get scrollWidth() {\n        return this._scrollWidth;\n    }\n    set scrollWidth(val) {\n        this._scrollWidth = val;\n    }\n    /**\n     * The orientation of scrollbar.\n     * @group Props\n     */\n    get orientation() {\n        return this._orientation;\n    }\n    set orientation(val) {\n        this._orientation = val;\n    }\n    /**\n     * Used to specify how many items to load in each load method in lazy mode.\n     * @group Props\n     */\n    get step() {\n        return this._step;\n    }\n    set step(val) {\n        this._step = val;\n    }\n    /**\n     * Delay in scroll before new data is loaded.\n     * @group Props\n     */\n    get delay() {\n        return this._delay;\n    }\n    set delay(val) {\n        this._delay = val;\n    }\n    /**\n     * Delay after window's resize finishes.\n     * @group Props\n     */\n    get resizeDelay() {\n        return this._resizeDelay;\n    }\n    set resizeDelay(val) {\n        this._resizeDelay = val;\n    }\n    /**\n     * Used to append each loaded item to top without removing any items from the DOM. Using very large data may cause the browser to crash.\n     * @group Props\n     */\n    get appendOnly() {\n        return this._appendOnly;\n    }\n    set appendOnly(val) {\n        this._appendOnly = val;\n    }\n    /**\n     * Specifies whether the scroller should be displayed inline or not.\n     * @group Props\n     */\n    get inline() {\n        return this._inline;\n    }\n    set inline(val) {\n        this._inline = val;\n    }\n    /**\n     * Defines if data is loaded and interacted with in lazy manner.\n     * @group Props\n     */\n    get lazy() {\n        return this._lazy;\n    }\n    set lazy(val) {\n        this._lazy = val;\n    }\n    /**\n     * If disabled, the scroller feature is eliminated and the content is displayed directly.\n     * @group Props\n     */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(val) {\n        this._disabled = val;\n    }\n    /**\n     * Used to implement a custom loader instead of using the loader feature in the scroller.\n     * @group Props\n     */\n    get loaderDisabled() {\n        return this._loaderDisabled;\n    }\n    set loaderDisabled(val) {\n        this._loaderDisabled = val;\n    }\n    /**\n     * Columns to display.\n     * @group Props\n     */\n    get columns() {\n        return this._columns;\n    }\n    set columns(val) {\n        this._columns = val;\n    }\n    /**\n     * Used to implement a custom spacer instead of using the spacer feature in the scroller.\n     * @group Props\n     */\n    get showSpacer() {\n        return this._showSpacer;\n    }\n    set showSpacer(val) {\n        this._showSpacer = val;\n    }\n    /**\n     * Defines whether to show loader.\n     * @group Props\n     */\n    get showLoader() {\n        return this._showLoader;\n    }\n    set showLoader(val) {\n        this._showLoader = val;\n    }\n    /**\n     * Determines how many additional elements to add to the DOM outside of the view. According to the scrolls made up and down, extra items are added in a certain algorithm in the form of multiples of this number. Default value is half the number of items shown in the view.\n     * @group Props\n     */\n    get numToleratedItems() {\n        return this._numToleratedItems;\n    }\n    set numToleratedItems(val) {\n        this._numToleratedItems = val;\n    }\n    /**\n     * Defines whether the data is loaded.\n     * @group Props\n     */\n    get loading() {\n        return this._loading;\n    }\n    set loading(val) {\n        this._loading = val;\n    }\n    /**\n     * Defines whether to dynamically change the height or width of scrollable container.\n     * @group Props\n     */\n    get autoSize() {\n        return this._autoSize;\n    }\n    set autoSize(val) {\n        this._autoSize = val;\n    }\n    /**\n     * Function to optimize the dom operations by delegating to ngForTrackBy, default algoritm checks for object identity.\n     * @group Props\n     */\n    get trackBy() {\n        return this._trackBy;\n    }\n    set trackBy(val) {\n        this._trackBy = val;\n    }\n    /**\n     * Defines whether to use the scroller feature. The properties of scroller component can be used like an object in it.\n     * @group Props\n     */\n    get options() {\n        return this._options;\n    }\n    set options(val) {\n        this._options = val;\n        if (val && typeof val === 'object') {\n            //@ts-ignore\n            Object.entries(val).forEach(([k, v]) => this[`_${k}`] !== v && (this[`_${k}`] = v));\n        }\n    }\n    /**\n     * Callback to invoke in lazy mode to load new data.\n     * @param {ScrollerLazyLoadEvent} event - Custom lazy load event.\n     * @group Emits\n     */\n    onLazyLoad = new EventEmitter();\n    /**\n     * Callback to invoke when scroll position changes.\n     * @param {ScrollerScrollEvent} event - Custom scroll event.\n     * @group Emits\n     */\n    onScroll = new EventEmitter();\n    /**\n     * Callback to invoke when scroll position and item's range in view changes.\n     * @param {ScrollerScrollEvent} event - Custom scroll index change event.\n     * @group Emits\n     */\n    onScrollIndexChange = new EventEmitter();\n    elementViewChild;\n    contentViewChild;\n    templates;\n    _id;\n    _style;\n    _styleClass;\n    _tabindex = 0;\n    _items;\n    _itemSize = 0;\n    _scrollHeight;\n    _scrollWidth;\n    _orientation = 'vertical';\n    _step = 0;\n    _delay = 0;\n    _resizeDelay = 10;\n    _appendOnly = false;\n    _inline = false;\n    _lazy = false;\n    _disabled = false;\n    _loaderDisabled = false;\n    _columns;\n    _showSpacer = true;\n    _showLoader = false;\n    _numToleratedItems;\n    _loading;\n    _autoSize = false;\n    _trackBy;\n    _options;\n    d_loading = false;\n    d_numToleratedItems;\n    contentEl;\n    contentTemplate;\n    itemTemplate;\n    loaderTemplate;\n    loaderIconTemplate;\n    first = 0;\n    last = 0;\n    page = 0;\n    isRangeChanged = false;\n    numItemsInViewport = 0;\n    lastScrollPos = 0;\n    lazyLoadState = {};\n    loaderArr = [];\n    spacerStyle = {};\n    contentStyle = {};\n    scrollTimeout;\n    resizeTimeout;\n    initialized = false;\n    windowResizeListener;\n    defaultWidth;\n    defaultHeight;\n    defaultContentWidth;\n    defaultContentHeight;\n    get vertical() {\n        return this._orientation === 'vertical';\n    }\n    get horizontal() {\n        return this._orientation === 'horizontal';\n    }\n    get both() {\n        return this._orientation === 'both';\n    }\n    get loadedItems() {\n        if (this._items && !this.d_loading) {\n            if (this.both)\n                return this._items.slice(this._appendOnly ? 0 : this.first.rows, this.last.rows).map((item) => (this._columns ? item : item.slice(this._appendOnly ? 0 : this.first.cols, this.last.cols)));\n            else if (this.horizontal && this._columns)\n                return this._items;\n            else\n                return this._items.slice(this._appendOnly ? 0 : this.first, this.last);\n        }\n        return [];\n    }\n    get loadedRows() {\n        return this.d_loading ? (this._loaderDisabled ? this.loaderArr : []) : this.loadedItems;\n    }\n    get loadedColumns() {\n        if (this._columns && (this.both || this.horizontal)) {\n            return this.d_loading && this._loaderDisabled ? (this.both ? this.loaderArr[0] : this.loaderArr) : this._columns.slice(this.both ? this.first.cols : this.first, this.both ? this.last.cols : this.last);\n        }\n        return this._columns;\n    }\n    get isPageChanged() {\n        return this._step ? this.page !== this.getPageByFirst() : true;\n    }\n    constructor(document, platformId, renderer, cd, zone) {\n        this.document = document;\n        this.platformId = platformId;\n        this.renderer = renderer;\n        this.cd = cd;\n        this.zone = zone;\n    }\n    ngOnInit() {\n        this.setInitialState();\n    }\n    ngOnChanges(simpleChanges) {\n        let isLoadingChanged = false;\n        if (simpleChanges.loading) {\n            const { previousValue, currentValue } = simpleChanges.loading;\n            if (this.lazy && previousValue !== currentValue && currentValue !== this.d_loading) {\n                this.d_loading = currentValue;\n                isLoadingChanged = true;\n            }\n        }\n        if (simpleChanges.orientation) {\n            this.lastScrollPos = this.both ? { top: 0, left: 0 } : 0;\n        }\n        if (simpleChanges.numToleratedItems) {\n            const { previousValue, currentValue } = simpleChanges.numToleratedItems;\n            if (previousValue !== currentValue && currentValue !== this.d_numToleratedItems) {\n                this.d_numToleratedItems = currentValue;\n            }\n        }\n        if (simpleChanges.options) {\n            const { previousValue, currentValue } = simpleChanges.options;\n            if (this.lazy && previousValue?.loading !== currentValue?.loading && currentValue?.loading !== this.d_loading) {\n                this.d_loading = currentValue.loading;\n                isLoadingChanged = true;\n            }\n            if (previousValue?.numToleratedItems !== currentValue?.numToleratedItems && currentValue?.numToleratedItems !== this.d_numToleratedItems) {\n                this.d_numToleratedItems = currentValue.numToleratedItems;\n            }\n        }\n        if (this.initialized) {\n            const isChanged = !isLoadingChanged && (simpleChanges.items?.previousValue?.length !== simpleChanges.items?.currentValue?.length || simpleChanges.itemSize || simpleChanges.scrollHeight || simpleChanges.scrollWidth);\n            if (isChanged) {\n                this.init();\n                this.calculateAutoSize();\n            }\n        }\n    }\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'content':\n                    this.contentTemplate = item.template;\n                    break;\n                case 'item':\n                    this.itemTemplate = item.template;\n                    break;\n                case 'loader':\n                    this.loaderTemplate = item.template;\n                    break;\n                case 'loadericon':\n                    this.loaderIconTemplate = item.template;\n                    break;\n                default:\n                    this.itemTemplate = item.template;\n                    break;\n            }\n        });\n    }\n    ngAfterViewInit() {\n        Promise.resolve().then(() => {\n            this.viewInit();\n        });\n    }\n    ngAfterViewChecked() {\n        if (!this.initialized) {\n            this.viewInit();\n        }\n    }\n    ngOnDestroy() {\n        this.unbindResizeListener();\n        this.contentEl = null;\n        this.initialized = false;\n    }\n    viewInit() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (DomHandler.isVisible(this.elementViewChild?.nativeElement)) {\n                this.setInitialState();\n                this.setContentEl(this.contentEl);\n                this.init();\n                this.defaultWidth = DomHandler.getWidth(this.elementViewChild?.nativeElement);\n                this.defaultHeight = DomHandler.getHeight(this.elementViewChild?.nativeElement);\n                this.defaultContentWidth = DomHandler.getWidth(this.contentEl);\n                this.defaultContentHeight = DomHandler.getHeight(this.contentEl);\n                this.initialized = true;\n            }\n        }\n    }\n    init() {\n        if (!this._disabled) {\n            this.setSize();\n            this.calculateOptions();\n            this.setSpacerSize();\n            this.bindResizeListener();\n            this.cd.detectChanges();\n        }\n    }\n    setContentEl(el) {\n        this.contentEl = el || this.contentViewChild?.nativeElement || DomHandler.findSingle(this.elementViewChild?.nativeElement, '.p-scroller-content');\n    }\n    setInitialState() {\n        this.first = this.both ? { rows: 0, cols: 0 } : 0;\n        this.last = this.both ? { rows: 0, cols: 0 } : 0;\n        this.numItemsInViewport = this.both ? { rows: 0, cols: 0 } : 0;\n        this.lastScrollPos = this.both ? { top: 0, left: 0 } : 0;\n        this.d_loading = this._loading || false;\n        this.d_numToleratedItems = this._numToleratedItems;\n        this.loaderArr = [];\n        this.spacerStyle = {};\n        this.contentStyle = {};\n    }\n    getElementRef() {\n        return this.elementViewChild;\n    }\n    getPageByFirst() {\n        return Math.floor((this.first + this.d_numToleratedItems * 4) / (this._step || 1));\n    }\n    scrollTo(options) {\n        this.lastScrollPos = this.both ? { top: 0, left: 0 } : 0;\n        this.elementViewChild?.nativeElement?.scrollTo(options);\n    }\n    scrollToIndex(index, behavior = 'auto') {\n        const { numToleratedItems } = this.calculateNumItems();\n        const contentPos = this.getContentPosition();\n        const calculateFirst = (_index = 0, _numT) => (_index <= _numT ? 0 : _index);\n        const calculateCoord = (_first, _size, _cpos) => _first * _size + _cpos;\n        const scrollTo = (left = 0, top = 0) => this.scrollTo({ left, top, behavior });\n        let newFirst = 0;\n        if (this.both) {\n            newFirst = { rows: calculateFirst(index[0], numToleratedItems[0]), cols: calculateFirst(index[1], numToleratedItems[1]) };\n            scrollTo(calculateCoord(newFirst.cols, this._itemSize[1], contentPos.left), calculateCoord(newFirst.rows, this._itemSize[0], contentPos.top));\n        }\n        else {\n            newFirst = calculateFirst(index, numToleratedItems);\n            this.horizontal ? scrollTo(calculateCoord(newFirst, this._itemSize, contentPos.left), 0) : scrollTo(0, calculateCoord(newFirst, this._itemSize, contentPos.top));\n        }\n        this.isRangeChanged = this.first !== newFirst;\n        this.first = newFirst;\n    }\n    scrollInView(index, to, behavior = 'auto') {\n        if (to) {\n            const { first, viewport } = this.getRenderedRange();\n            const scrollTo = (left = 0, top = 0) => this.scrollTo({ left, top, behavior });\n            const isToStart = to === 'to-start';\n            const isToEnd = to === 'to-end';\n            if (isToStart) {\n                if (this.both) {\n                    if (viewport.first.rows - first.rows > index[0]) {\n                        scrollTo(viewport.first.cols * this._itemSize[1], (viewport.first.rows - 1) * this._itemSize[0]);\n                    }\n                    else if (viewport.first.cols - first.cols > index[1]) {\n                        scrollTo((viewport.first.cols - 1) * this._itemSize[1], viewport.first.rows * this._itemSize[0]);\n                    }\n                }\n                else {\n                    if (viewport.first - first > index) {\n                        const pos = (viewport.first - 1) * this._itemSize;\n                        this.horizontal ? scrollTo(pos, 0) : scrollTo(0, pos);\n                    }\n                }\n            }\n            else if (isToEnd) {\n                if (this.both) {\n                    if (viewport.last.rows - first.rows <= index[0] + 1) {\n                        scrollTo(viewport.first.cols * this._itemSize[1], (viewport.first.rows + 1) * this._itemSize[0]);\n                    }\n                    else if (viewport.last.cols - first.cols <= index[1] + 1) {\n                        scrollTo((viewport.first.cols + 1) * this._itemSize[1], viewport.first.rows * this._itemSize[0]);\n                    }\n                }\n                else {\n                    if (viewport.last - first <= index + 1) {\n                        const pos = (viewport.first + 1) * this._itemSize;\n                        this.horizontal ? scrollTo(pos, 0) : scrollTo(0, pos);\n                    }\n                }\n            }\n        }\n        else {\n            this.scrollToIndex(index, behavior);\n        }\n    }\n    getRenderedRange() {\n        const calculateFirstInViewport = (_pos, _size) => Math.floor(_pos / (_size || _pos));\n        let firstInViewport = this.first;\n        let lastInViewport = 0;\n        if (this.elementViewChild?.nativeElement) {\n            const { scrollTop, scrollLeft } = this.elementViewChild.nativeElement;\n            if (this.both) {\n                firstInViewport = { rows: calculateFirstInViewport(scrollTop, this._itemSize[0]), cols: calculateFirstInViewport(scrollLeft, this._itemSize[1]) };\n                lastInViewport = { rows: firstInViewport.rows + this.numItemsInViewport.rows, cols: firstInViewport.cols + this.numItemsInViewport.cols };\n            }\n            else {\n                const scrollPos = this.horizontal ? scrollLeft : scrollTop;\n                firstInViewport = calculateFirstInViewport(scrollPos, this._itemSize);\n                lastInViewport = firstInViewport + this.numItemsInViewport;\n            }\n        }\n        return {\n            first: this.first,\n            last: this.last,\n            viewport: {\n                first: firstInViewport,\n                last: lastInViewport\n            }\n        };\n    }\n    calculateNumItems() {\n        const contentPos = this.getContentPosition();\n        const contentWidth = (this.elementViewChild?.nativeElement ? this.elementViewChild.nativeElement.offsetWidth - contentPos.left : 0) || 0;\n        const contentHeight = (this.elementViewChild?.nativeElement ? this.elementViewChild.nativeElement.offsetHeight - contentPos.top : 0) || 0;\n        const calculateNumItemsInViewport = (_contentSize, _itemSize) => Math.ceil(_contentSize / (_itemSize || _contentSize));\n        const calculateNumToleratedItems = (_numItems) => Math.ceil(_numItems / 2);\n        const numItemsInViewport = this.both\n            ? { rows: calculateNumItemsInViewport(contentHeight, this._itemSize[0]), cols: calculateNumItemsInViewport(contentWidth, this._itemSize[1]) }\n            : calculateNumItemsInViewport(this.horizontal ? contentWidth : contentHeight, this._itemSize);\n        const numToleratedItems = this.d_numToleratedItems || (this.both ? [calculateNumToleratedItems(numItemsInViewport.rows), calculateNumToleratedItems(numItemsInViewport.cols)] : calculateNumToleratedItems(numItemsInViewport));\n        return { numItemsInViewport, numToleratedItems };\n    }\n    calculateOptions() {\n        const { numItemsInViewport, numToleratedItems } = this.calculateNumItems();\n        const calculateLast = (_first, _num, _numT, _isCols = false) => this.getLast(_first + _num + (_first < _numT ? 2 : 3) * _numT, _isCols);\n        const first = this.first;\n        const last = this.both\n            ? { rows: calculateLast(this.first.rows, numItemsInViewport.rows, numToleratedItems[0]), cols: calculateLast(this.first.cols, numItemsInViewport.cols, numToleratedItems[1], true) }\n            : calculateLast(this.first, numItemsInViewport, numToleratedItems);\n        this.last = last;\n        this.numItemsInViewport = numItemsInViewport;\n        this.d_numToleratedItems = numToleratedItems;\n        if (this.showLoader) {\n            this.loaderArr = this.both ? Array.from({ length: numItemsInViewport.rows }).map(() => Array.from({ length: numItemsInViewport.cols })) : Array.from({ length: numItemsInViewport });\n        }\n        if (this._lazy) {\n            Promise.resolve().then(() => {\n                this.lazyLoadState = {\n                    first: this._step ? (this.both ? { rows: 0, cols: first.cols } : 0) : first,\n                    last: Math.min(this._step ? this._step : this.last, this.items.length)\n                };\n                this.handleEvents('onLazyLoad', this.lazyLoadState);\n            });\n        }\n    }\n    calculateAutoSize() {\n        if (this._autoSize && !this.d_loading) {\n            Promise.resolve().then(() => {\n                if (this.contentEl) {\n                    this.contentEl.style.minHeight = this.contentEl.style.minWidth = 'auto';\n                    this.contentEl.style.position = 'relative';\n                    this.elementViewChild.nativeElement.style.contain = 'none';\n                    const [contentWidth, contentHeight] = [DomHandler.getWidth(this.contentEl), DomHandler.getHeight(this.contentEl)];\n                    contentWidth !== this.defaultContentWidth && (this.elementViewChild.nativeElement.style.width = '');\n                    contentHeight !== this.defaultContentHeight && (this.elementViewChild.nativeElement.style.height = '');\n                    const [width, height] = [DomHandler.getWidth(this.elementViewChild.nativeElement), DomHandler.getHeight(this.elementViewChild.nativeElement)];\n                    (this.both || this.horizontal) && (this.elementViewChild.nativeElement.style.width = width < this.defaultWidth ? width + 'px' : this._scrollWidth || this.defaultWidth + 'px');\n                    (this.both || this.vertical) && (this.elementViewChild.nativeElement.style.height = height < this.defaultHeight ? height + 'px' : this._scrollHeight || this.defaultHeight + 'px');\n                    this.contentEl.style.minHeight = this.contentEl.style.minWidth = '';\n                    this.contentEl.style.position = '';\n                    this.elementViewChild.nativeElement.style.contain = '';\n                }\n            });\n        }\n    }\n    getLast(last = 0, isCols = false) {\n        return this._items ? Math.min(isCols ? (this._columns || this._items[0]).length : this._items.length, last) : 0;\n    }\n    getContentPosition() {\n        if (this.contentEl) {\n            const style = getComputedStyle(this.contentEl);\n            const left = parseFloat(style.paddingLeft) + Math.max(parseFloat(style.left) || 0, 0);\n            const right = parseFloat(style.paddingRight) + Math.max(parseFloat(style.right) || 0, 0);\n            const top = parseFloat(style.paddingTop) + Math.max(parseFloat(style.top) || 0, 0);\n            const bottom = parseFloat(style.paddingBottom) + Math.max(parseFloat(style.bottom) || 0, 0);\n            return { left, right, top, bottom, x: left + right, y: top + bottom };\n        }\n        return { left: 0, right: 0, top: 0, bottom: 0, x: 0, y: 0 };\n    }\n    setSize() {\n        if (this.elementViewChild?.nativeElement) {\n            const parentElement = this.elementViewChild.nativeElement.parentElement.parentElement;\n            const width = this._scrollWidth || `${this.elementViewChild.nativeElement.offsetWidth || parentElement.offsetWidth}px`;\n            const height = this._scrollHeight || `${this.elementViewChild.nativeElement.offsetHeight || parentElement.offsetHeight}px`;\n            const setProp = (_name, _value) => (this.elementViewChild.nativeElement.style[_name] = _value);\n            if (this.both || this.horizontal) {\n                setProp('height', height);\n                setProp('width', width);\n            }\n            else {\n                setProp('height', height);\n            }\n        }\n    }\n    setSpacerSize() {\n        if (this._items) {\n            const contentPos = this.getContentPosition();\n            const setProp = (_name, _value, _size, _cpos = 0) => (this.spacerStyle = { ...this.spacerStyle, ...{ [`${_name}`]: (_value || []).length * _size + _cpos + 'px' } });\n            if (this.both) {\n                setProp('height', this._items, this._itemSize[0], contentPos.y);\n                setProp('width', this._columns || this._items[1], this._itemSize[1], contentPos.x);\n            }\n            else {\n                this.horizontal ? setProp('width', this._columns || this._items, this._itemSize, contentPos.x) : setProp('height', this._items, this._itemSize, contentPos.y);\n            }\n        }\n    }\n    setContentPosition(pos) {\n        if (this.contentEl && !this._appendOnly) {\n            const first = pos ? pos.first : this.first;\n            const calculateTranslateVal = (_first, _size) => _first * _size;\n            const setTransform = (_x = 0, _y = 0) => (this.contentStyle = { ...this.contentStyle, ...{ transform: `translate3d(${_x}px, ${_y}px, 0)` } });\n            if (this.both) {\n                setTransform(calculateTranslateVal(first.cols, this._itemSize[1]), calculateTranslateVal(first.rows, this._itemSize[0]));\n            }\n            else {\n                const translateVal = calculateTranslateVal(first, this._itemSize);\n                this.horizontal ? setTransform(translateVal, 0) : setTransform(0, translateVal);\n            }\n        }\n    }\n    onScrollPositionChange(event) {\n        const target = event.target;\n        const contentPos = this.getContentPosition();\n        const calculateScrollPos = (_pos, _cpos) => (_pos ? (_pos > _cpos ? _pos - _cpos : _pos) : 0);\n        const calculateCurrentIndex = (_pos, _size) => Math.floor(_pos / (_size || _pos));\n        const calculateTriggerIndex = (_currentIndex, _first, _last, _num, _numT, _isScrollDownOrRight) => {\n            return _currentIndex <= _numT ? _numT : _isScrollDownOrRight ? _last - _num - _numT : _first + _numT - 1;\n        };\n        const calculateFirst = (_currentIndex, _triggerIndex, _first, _last, _num, _numT, _isScrollDownOrRight) => {\n            if (_currentIndex <= _numT)\n                return 0;\n            else\n                return Math.max(0, _isScrollDownOrRight ? (_currentIndex < _triggerIndex ? _first : _currentIndex - _numT) : _currentIndex > _triggerIndex ? _first : _currentIndex - 2 * _numT);\n        };\n        const calculateLast = (_currentIndex, _first, _last, _num, _numT, _isCols = false) => {\n            let lastValue = _first + _num + 2 * _numT;\n            if (_currentIndex >= _numT) {\n                lastValue += _numT + 1;\n            }\n            return this.getLast(lastValue, _isCols);\n        };\n        const scrollTop = calculateScrollPos(target.scrollTop, contentPos.top);\n        const scrollLeft = calculateScrollPos(target.scrollLeft, contentPos.left);\n        let newFirst = this.both ? { rows: 0, cols: 0 } : 0;\n        let newLast = this.last;\n        let isRangeChanged = false;\n        let newScrollPos = this.lastScrollPos;\n        if (this.both) {\n            const isScrollDown = this.lastScrollPos.top <= scrollTop;\n            const isScrollRight = this.lastScrollPos.left <= scrollLeft;\n            if (!this._appendOnly || (this._appendOnly && (isScrollDown || isScrollRight))) {\n                const currentIndex = { rows: calculateCurrentIndex(scrollTop, this._itemSize[0]), cols: calculateCurrentIndex(scrollLeft, this._itemSize[1]) };\n                const triggerIndex = {\n                    rows: calculateTriggerIndex(currentIndex.rows, this.first.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0], isScrollDown),\n                    cols: calculateTriggerIndex(currentIndex.cols, this.first.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], isScrollRight)\n                };\n                newFirst = {\n                    rows: calculateFirst(currentIndex.rows, triggerIndex.rows, this.first.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0], isScrollDown),\n                    cols: calculateFirst(currentIndex.cols, triggerIndex.cols, this.first.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], isScrollRight)\n                };\n                newLast = {\n                    rows: calculateLast(currentIndex.rows, newFirst.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0]),\n                    cols: calculateLast(currentIndex.cols, newFirst.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], true)\n                };\n                isRangeChanged = newFirst.rows !== this.first.rows || newLast.rows !== this.last.rows || newFirst.cols !== this.first.cols || newLast.cols !== this.last.cols || this.isRangeChanged;\n                newScrollPos = { top: scrollTop, left: scrollLeft };\n            }\n        }\n        else {\n            const scrollPos = this.horizontal ? scrollLeft : scrollTop;\n            const isScrollDownOrRight = this.lastScrollPos <= scrollPos;\n            if (!this._appendOnly || (this._appendOnly && isScrollDownOrRight)) {\n                const currentIndex = calculateCurrentIndex(scrollPos, this._itemSize);\n                const triggerIndex = calculateTriggerIndex(currentIndex, this.first, this.last, this.numItemsInViewport, this.d_numToleratedItems, isScrollDownOrRight);\n                newFirst = calculateFirst(currentIndex, triggerIndex, this.first, this.last, this.numItemsInViewport, this.d_numToleratedItems, isScrollDownOrRight);\n                newLast = calculateLast(currentIndex, newFirst, this.last, this.numItemsInViewport, this.d_numToleratedItems);\n                isRangeChanged = newFirst !== this.first || newLast !== this.last || this.isRangeChanged;\n                newScrollPos = scrollPos;\n            }\n        }\n        return {\n            first: newFirst,\n            last: newLast,\n            isRangeChanged,\n            scrollPos: newScrollPos\n        };\n    }\n    onScrollChange(event) {\n        const { first, last, isRangeChanged, scrollPos } = this.onScrollPositionChange(event);\n        if (isRangeChanged) {\n            const newState = { first, last };\n            this.setContentPosition(newState);\n            this.first = first;\n            this.last = last;\n            this.lastScrollPos = scrollPos;\n            this.handleEvents('onScrollIndexChange', newState);\n            if (this._lazy && this.isPageChanged) {\n                const lazyLoadState = {\n                    first: this._step ? Math.min(this.getPageByFirst() * this._step, this.items.length - this._step) : first,\n                    last: Math.min(this._step ? (this.getPageByFirst() + 1) * this._step : last, this.items.length)\n                };\n                const isLazyStateChanged = this.lazyLoadState.first !== lazyLoadState.first || this.lazyLoadState.last !== lazyLoadState.last;\n                isLazyStateChanged && this.handleEvents('onLazyLoad', lazyLoadState);\n                this.lazyLoadState = lazyLoadState;\n            }\n        }\n    }\n    onContainerScroll(event) {\n        this.handleEvents('onScroll', { originalEvent: event });\n        if (this._delay && this.isPageChanged) {\n            if (this.scrollTimeout) {\n                clearTimeout(this.scrollTimeout);\n            }\n            if (!this.d_loading && this.showLoader) {\n                const { isRangeChanged } = this.onScrollPositionChange(event);\n                const changed = isRangeChanged || (this._step ? this.isPageChanged : false);\n                if (changed) {\n                    this.d_loading = true;\n                    this.cd.detectChanges();\n                }\n            }\n            this.scrollTimeout = setTimeout(() => {\n                this.onScrollChange(event);\n                if (this.d_loading && this.showLoader && (!this._lazy || this._loading === undefined)) {\n                    this.d_loading = false;\n                    this.page = this.getPageByFirst();\n                    this.cd.detectChanges();\n                }\n            }, this._delay);\n        }\n        else {\n            !this.d_loading && this.onScrollChange(event);\n        }\n    }\n    bindResizeListener() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (!this.windowResizeListener) {\n                this.zone.runOutsideAngular(() => {\n                    const window = this.document.defaultView;\n                    const event = DomHandler.isTouchDevice() ? 'orientationchange' : 'resize';\n                    this.windowResizeListener = this.renderer.listen(window, event, this.onWindowResize.bind(this));\n                });\n            }\n        }\n    }\n    unbindResizeListener() {\n        if (this.windowResizeListener) {\n            this.windowResizeListener();\n            this.windowResizeListener = null;\n        }\n    }\n    onWindowResize() {\n        if (this.resizeTimeout) {\n            clearTimeout(this.resizeTimeout);\n        }\n        this.resizeTimeout = setTimeout(() => {\n            if (DomHandler.isVisible(this.elementViewChild?.nativeElement)) {\n                const [width, height] = [DomHandler.getWidth(this.elementViewChild?.nativeElement), DomHandler.getHeight(this.elementViewChild?.nativeElement)];\n                const [isDiffWidth, isDiffHeight] = [width !== this.defaultWidth, height !== this.defaultHeight];\n                const reinit = this.both ? isDiffWidth || isDiffHeight : this.horizontal ? isDiffWidth : this.vertical ? isDiffHeight : false;\n                reinit &&\n                    this.zone.run(() => {\n                        this.d_numToleratedItems = this._numToleratedItems;\n                        this.defaultWidth = width;\n                        this.defaultHeight = height;\n                        this.defaultContentWidth = DomHandler.getWidth(this.contentEl);\n                        this.defaultContentHeight = DomHandler.getHeight(this.contentEl);\n                        this.init();\n                    });\n            }\n        }, this._resizeDelay);\n    }\n    handleEvents(name, params) {\n        //@ts-ignore\n        return this.options && this.options[name] ? this.options[name](params) : this[name].emit(params);\n    }\n    getContentOptions() {\n        return {\n            contentStyleClass: `p-scroller-content ${this.d_loading ? 'p-scroller-loading' : ''}`,\n            items: this.loadedItems,\n            getItemOptions: (index) => this.getOptions(index),\n            loading: this.d_loading,\n            getLoaderOptions: (index, options) => this.getLoaderOptions(index, options),\n            itemSize: this._itemSize,\n            rows: this.loadedRows,\n            columns: this.loadedColumns,\n            spacerStyle: this.spacerStyle,\n            contentStyle: this.contentStyle,\n            vertical: this.vertical,\n            horizontal: this.horizontal,\n            both: this.both\n        };\n    }\n    getOptions(renderedIndex) {\n        const count = (this._items || []).length;\n        const index = this.both ? this.first.rows + renderedIndex : this.first + renderedIndex;\n        return {\n            index,\n            count,\n            first: index === 0,\n            last: index === count - 1,\n            even: index % 2 === 0,\n            odd: index % 2 !== 0\n        };\n    }\n    getLoaderOptions(index, extOptions) {\n        const count = this.loaderArr.length;\n        return {\n            index,\n            count,\n            first: index === 0,\n            last: index === count - 1,\n            even: index % 2 === 0,\n            odd: index % 2 !== 0,\n            ...extOptions\n        };\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.0\", ngImport: i0, type: Scroller, deps: [{ token: DOCUMENT }, { token: PLATFORM_ID }, { token: i0.Renderer2 }, { token: i0.ChangeDetectorRef }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.0\", type: Scroller, selector: \"p-scroller\", inputs: { id: \"id\", style: \"style\", styleClass: \"styleClass\", tabindex: \"tabindex\", items: \"items\", itemSize: \"itemSize\", scrollHeight: \"scrollHeight\", scrollWidth: \"scrollWidth\", orientation: \"orientation\", step: \"step\", delay: \"delay\", resizeDelay: \"resizeDelay\", appendOnly: \"appendOnly\", inline: \"inline\", lazy: \"lazy\", disabled: \"disabled\", loaderDisabled: \"loaderDisabled\", columns: \"columns\", showSpacer: \"showSpacer\", showLoader: \"showLoader\", numToleratedItems: \"numToleratedItems\", loading: \"loading\", autoSize: \"autoSize\", trackBy: \"trackBy\", options: \"options\" }, outputs: { onLazyLoad: \"onLazyLoad\", onScroll: \"onScroll\", onScrollIndexChange: \"onScrollIndexChange\" }, host: { classAttribute: \"p-scroller-viewport p-element\" }, queries: [{ propertyName: \"templates\", predicate: PrimeTemplate }], viewQueries: [{ propertyName: \"elementViewChild\", first: true, predicate: [\"element\"], descendants: true }, { propertyName: \"contentViewChild\", first: true, predicate: [\"content\"], descendants: true }], usesOnChanges: true, ngImport: i0, template: `\n        <ng-container *ngIf=\"!_disabled; else disabledContainer\">\n            <div\n                #element\n                [attr.id]=\"_id\"\n                [attr.tabindex]=\"tabindex\"\n                [ngStyle]=\"_style\"\n                [class]=\"_styleClass\"\n                [ngClass]=\"{ 'p-scroller': true, 'p-scroller-inline': inline, 'p-both-scroll': both, 'p-horizontal-scroll': horizontal }\"\n                (scroll)=\"onContainerScroll($event)\"\n                [attr.data-pc-name]=\"'scroller'\"\n                [attr.data-pc-section]=\"'root'\"\n            >\n                <ng-container *ngIf=\"contentTemplate; else buildInContent\">\n                    <ng-container *ngTemplateOutlet=\"contentTemplate; context: { $implicit: loadedItems, options: getContentOptions() }\"></ng-container>\n                </ng-container>\n                <ng-template #buildInContent>\n                    <div #content class=\"p-scroller-content\" [ngClass]=\"{ 'p-scroller-loading': d_loading }\" [ngStyle]=\"contentStyle\" [attr.data-pc-section]=\"'content'\">\n                        <ng-container *ngFor=\"let item of loadedItems; let index = index; trackBy: _trackBy || index\">\n                            <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item, options: getOptions(index) }\"></ng-container>\n                        </ng-container>\n                    </div>\n                </ng-template>\n                <div *ngIf=\"_showSpacer\" class=\"p-scroller-spacer\" [ngStyle]=\"spacerStyle\" [attr.data-pc-section]=\"'spacer'\"></div>\n                <div *ngIf=\"!loaderDisabled && _showLoader && d_loading\" class=\"p-scroller-loader\" [ngClass]=\"{ 'p-component-overlay': !loaderTemplate }\" [attr.data-pc-section]=\"'loader'\">\n                    <ng-container *ngIf=\"loaderTemplate; else buildInLoader\">\n                        <ng-container *ngFor=\"let item of loaderArr; let index = index\">\n                            <ng-container *ngTemplateOutlet=\"loaderTemplate; context: { options: getLoaderOptions(index, both && { numCols: _numItemsInViewport.cols }) }\"></ng-container>\n                        </ng-container>\n                    </ng-container>\n                    <ng-template #buildInLoader>\n                        <ng-container *ngIf=\"loaderIconTemplate; else buildInLoaderIcon\">\n                            <ng-container *ngTemplateOutlet=\"loaderIconTemplate; context: { options: { styleClass: 'p-scroller-loading-icon' } }\"></ng-container>\n                        </ng-container>\n                        <ng-template #buildInLoaderIcon>\n                            <SpinnerIcon [styleClass]=\"'p-scroller-loading-icon'\" [attr.data-pc-section]=\"'loadingIcon'\" />\n                        </ng-template>\n                    </ng-template>\n                </div>\n            </div>\n        </ng-container>\n        <ng-template #disabledContainer>\n            <ng-content></ng-content>\n            <ng-container *ngIf=\"contentTemplate\">\n                <ng-container *ngTemplateOutlet=\"contentTemplate; context: { $implicit: items, options: { rows: _items, columns: loadedColumns } }\"></ng-container>\n            </ng-container>\n        </ng-template>\n    `, isInline: true, styles: [\"@layer primeng{p-scroller{flex:1;outline:0 none}.p-scroller{position:relative;overflow:auto;contain:strict;transform:translateZ(0);will-change:scroll-position;outline:0 none}.p-scroller-content{position:absolute;top:0;left:0;min-height:100%;min-width:100%;will-change:transform}.p-scroller-spacer{position:absolute;top:0;left:0;height:1px;width:1px;transform-origin:0 0;pointer-events:none}.p-scroller-loader{position:sticky;top:0;left:0;width:100%;height:100%}.p-scroller-loader.p-component-overlay{display:flex;align-items:center;justify-content:center}.p-scroller-loading-icon{scale:2}.p-scroller-inline .p-scroller-content{position:static}}\\n\"], dependencies: [{ kind: \"directive\", type: i0.forwardRef(function () { return i1.NgClass; }), selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i0.forwardRef(function () { return i1.NgForOf; }), selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i0.forwardRef(function () { return i1.NgIf; }), selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i0.forwardRef(function () { return i1.NgTemplateOutlet; }), selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i0.forwardRef(function () { return i1.NgStyle; }), selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"component\", type: i0.forwardRef(function () { return SpinnerIcon; }), selector: \"SpinnerIcon\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.0\", ngImport: i0, type: Scroller, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-scroller', template: `\n        <ng-container *ngIf=\"!_disabled; else disabledContainer\">\n            <div\n                #element\n                [attr.id]=\"_id\"\n                [attr.tabindex]=\"tabindex\"\n                [ngStyle]=\"_style\"\n                [class]=\"_styleClass\"\n                [ngClass]=\"{ 'p-scroller': true, 'p-scroller-inline': inline, 'p-both-scroll': both, 'p-horizontal-scroll': horizontal }\"\n                (scroll)=\"onContainerScroll($event)\"\n                [attr.data-pc-name]=\"'scroller'\"\n                [attr.data-pc-section]=\"'root'\"\n            >\n                <ng-container *ngIf=\"contentTemplate; else buildInContent\">\n                    <ng-container *ngTemplateOutlet=\"contentTemplate; context: { $implicit: loadedItems, options: getContentOptions() }\"></ng-container>\n                </ng-container>\n                <ng-template #buildInContent>\n                    <div #content class=\"p-scroller-content\" [ngClass]=\"{ 'p-scroller-loading': d_loading }\" [ngStyle]=\"contentStyle\" [attr.data-pc-section]=\"'content'\">\n                        <ng-container *ngFor=\"let item of loadedItems; let index = index; trackBy: _trackBy || index\">\n                            <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item, options: getOptions(index) }\"></ng-container>\n                        </ng-container>\n                    </div>\n                </ng-template>\n                <div *ngIf=\"_showSpacer\" class=\"p-scroller-spacer\" [ngStyle]=\"spacerStyle\" [attr.data-pc-section]=\"'spacer'\"></div>\n                <div *ngIf=\"!loaderDisabled && _showLoader && d_loading\" class=\"p-scroller-loader\" [ngClass]=\"{ 'p-component-overlay': !loaderTemplate }\" [attr.data-pc-section]=\"'loader'\">\n                    <ng-container *ngIf=\"loaderTemplate; else buildInLoader\">\n                        <ng-container *ngFor=\"let item of loaderArr; let index = index\">\n                            <ng-container *ngTemplateOutlet=\"loaderTemplate; context: { options: getLoaderOptions(index, both && { numCols: _numItemsInViewport.cols }) }\"></ng-container>\n                        </ng-container>\n                    </ng-container>\n                    <ng-template #buildInLoader>\n                        <ng-container *ngIf=\"loaderIconTemplate; else buildInLoaderIcon\">\n                            <ng-container *ngTemplateOutlet=\"loaderIconTemplate; context: { options: { styleClass: 'p-scroller-loading-icon' } }\"></ng-container>\n                        </ng-container>\n                        <ng-template #buildInLoaderIcon>\n                            <SpinnerIcon [styleClass]=\"'p-scroller-loading-icon'\" [attr.data-pc-section]=\"'loadingIcon'\" />\n                        </ng-template>\n                    </ng-template>\n                </div>\n            </div>\n        </ng-container>\n        <ng-template #disabledContainer>\n            <ng-content></ng-content>\n            <ng-container *ngIf=\"contentTemplate\">\n                <ng-container *ngTemplateOutlet=\"contentTemplate; context: { $implicit: items, options: { rows: _items, columns: loadedColumns } }\"></ng-container>\n            </ng-container>\n        </ng-template>\n    `, changeDetection: ChangeDetectionStrategy.Default, encapsulation: ViewEncapsulation.None, host: {\n                        class: 'p-scroller-viewport p-element'\n                    }, styles: [\"@layer primeng{p-scroller{flex:1;outline:0 none}.p-scroller{position:relative;overflow:auto;contain:strict;transform:translateZ(0);will-change:scroll-position;outline:0 none}.p-scroller-content{position:absolute;top:0;left:0;min-height:100%;min-width:100%;will-change:transform}.p-scroller-spacer{position:absolute;top:0;left:0;height:1px;width:1px;transform-origin:0 0;pointer-events:none}.p-scroller-loader{position:sticky;top:0;left:0;width:100%;height:100%}.p-scroller-loader.p-component-overlay{display:flex;align-items:center;justify-content:center}.p-scroller-loading-icon{scale:2}.p-scroller-inline .p-scroller-content{position:static}}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: i0.Renderer2 }, { type: i0.ChangeDetectorRef }, { type: i0.NgZone }]; }, propDecorators: { id: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], tabindex: [{\n                type: Input\n            }], items: [{\n                type: Input\n            }], itemSize: [{\n                type: Input\n            }], scrollHeight: [{\n                type: Input\n            }], scrollWidth: [{\n                type: Input\n            }], orientation: [{\n                type: Input\n            }], step: [{\n                type: Input\n            }], delay: [{\n                type: Input\n            }], resizeDelay: [{\n                type: Input\n            }], appendOnly: [{\n                type: Input\n            }], inline: [{\n                type: Input\n            }], lazy: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], loaderDisabled: [{\n                type: Input\n            }], columns: [{\n                type: Input\n            }], showSpacer: [{\n                type: Input\n            }], showLoader: [{\n                type: Input\n            }], numToleratedItems: [{\n                type: Input\n            }], loading: [{\n                type: Input\n            }], autoSize: [{\n                type: Input\n            }], trackBy: [{\n                type: Input\n            }], options: [{\n                type: Input\n            }], onLazyLoad: [{\n                type: Output\n            }], onScroll: [{\n                type: Output\n            }], onScrollIndexChange: [{\n                type: Output\n            }], elementViewChild: [{\n                type: ViewChild,\n                args: ['element']\n            }], contentViewChild: [{\n                type: ViewChild,\n                args: ['content']\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }] } });\nclass ScrollerModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.0\", ngImport: i0, type: ScrollerModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.2.0\", ngImport: i0, type: ScrollerModule, declarations: [Scroller], imports: [CommonModule, SharedModule, SpinnerIcon], exports: [Scroller, SharedModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.2.0\", ngImport: i0, type: ScrollerModule, imports: [CommonModule, SharedModule, SpinnerIcon, SharedModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.0\", ngImport: i0, type: ScrollerModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, SharedModule, SpinnerIcon],\n                    exports: [Scroller, SharedModule],\n                    declarations: [Scroller]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Scroller, ScrollerModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,iBAAiB;AACrC,SAASC,iBAAiB,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AAC3E,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,WAAW,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,eAAe;AAC7K,SAASC,aAAa,EAAEC,YAAY,QAAQ,aAAa;AACzD,SAASC,UAAU,QAAQ,aAAa;AACxC,SAASC,WAAW,QAAQ,uBAAuB;;AAEnD;AACA;AACA;AACA;AAHA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,+DAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA03B6FnB,EAAE,CAAAqB,kBAAA,EAewD,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA,YAAAA,CAAAC,EAAA,EAAAC,EAAA;EAAA;IAAAC,SAAA,EAAAF,EAAA;IAAAG,OAAA,EAAAF;EAAA;AAAA;AAAA,SAAAG,gDAAAR,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAf3DnB,EAAE,CAAA4B,uBAAA,EAcrB,CAAC;IAdkB5B,EAAE,CAAA6B,UAAA,IAAAX,8DAAA,yBAewD,CAAC;IAf3DlB,EAAE,CAAA8B,qBAAA,CAgBjE,CAAC;EAAA;EAAA,IAAAX,EAAA;IAAA,MAAAY,MAAA,GAhB8D/B,EAAE,CAAAgC,aAAA;IAAFhC,EAAE,CAAAiC,SAAA,EAe1B,CAAC;IAfuBjC,EAAE,CAAAkC,UAAA,qBAAAH,MAAA,CAAAI,eAe1B,CAAC,4BAfuBnC,EAAE,CAAAoC,eAAA,IAAAd,GAAA,EAAAS,MAAA,CAAAM,WAAA,EAAAN,MAAA,CAAAO,iBAAA,GAe1B,CAAC;EAAA;AAAA;AAAA,SAAAC,6EAAApB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAfuBnB,EAAE,CAAAqB,kBAAA,EAoBoD,CAAC;EAAA;AAAA;AAAA,SAAAmB,8DAAArB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApBvDnB,EAAE,CAAA4B,uBAAA,EAmBsB,CAAC;IAnBzB5B,EAAE,CAAA6B,UAAA,IAAAU,4EAAA,yBAoBoD,CAAC;IApBvDvC,EAAE,CAAA8B,qBAAA,CAqBzD,CAAC;EAAA;EAAA,IAAAX,EAAA;IAAA,MAAAsB,QAAA,GAAArB,GAAA,CAAAK,SAAA;IAAA,MAAAiB,SAAA,GAAAtB,GAAA,CAAAuB,KAAA;IAAA,MAAAC,OAAA,GArBsD5C,EAAE,CAAAgC,aAAA;IAAFhC,EAAE,CAAAiC,SAAA,EAoBrB,CAAC;IApBkBjC,EAAE,CAAAkC,UAAA,qBAAAU,OAAA,CAAAC,YAoBrB,CAAC,4BApBkB7C,EAAE,CAAAoC,eAAA,IAAAd,GAAA,EAAAmB,QAAA,EAAAG,OAAA,CAAAE,UAAA,CAAAJ,SAAA,EAoBrB,CAAC;EAAA;AAAA;AAAA,MAAAK,GAAA,YAAAA,CAAAxB,EAAA;EAAA;IAAA,sBAAAA;EAAA;AAAA;AAAA,SAAAyB,+CAAA7B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApBkBnB,EAAE,CAAAiD,cAAA,eAkByE,CAAC;IAlB5EjD,EAAE,CAAA6B,UAAA,IAAAW,6DAAA,0BAqBzD,CAAC;IArBsDxC,EAAE,CAAAkD,YAAA,CAsBtE,CAAC;EAAA;EAAA,IAAA/B,EAAA;IAAA,MAAAgC,MAAA,GAtBmEnD,EAAE,CAAAgC,aAAA;IAAFhC,EAAE,CAAAkC,UAAA,YAAFlC,EAAE,CAAAoD,eAAA,IAAAL,GAAA,EAAAI,MAAA,CAAAE,SAAA,CAkBY,CAAC,YAAAF,MAAA,CAAAG,YAAD,CAAC;IAlBftD,EAAE,CAAAuD,WAAA,6BAkBwE,CAAC;IAlB3EvD,EAAE,CAAAiC,SAAA,EAmBzB,CAAC;IAnBsBjC,EAAE,CAAAkC,UAAA,YAAAiB,MAAA,CAAAd,WAmBzB,CAAC,iBAAAc,MAAA,CAAAK,QAAA,IAAAL,MAAA,CAAAR,KAAD,CAAC;EAAA;AAAA;AAAA,SAAAc,uCAAAtC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnBsBnB,EAAE,CAAA0D,SAAA,aAwBmC,CAAC;EAAA;EAAA,IAAAvC,EAAA;IAAA,MAAAwC,MAAA,GAxBtC3D,EAAE,CAAAgC,aAAA;IAAFhC,EAAE,CAAAkC,UAAA,YAAAyB,MAAA,CAAAC,WAwBN,CAAC;IAxBG5D,EAAE,CAAAuD,WAAA,4BAwB4B,CAAC;EAAA;AAAA;AAAA,SAAAM,oFAAA1C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAxB/BnB,EAAE,CAAAqB,kBAAA,EA4B0F,CAAC;EAAA;AAAA;AAAA,MAAAyC,GAAA,YAAAA,CAAAvC,EAAA;EAAA;IAAAwC,OAAA,EAAAxC;EAAA;AAAA;AAAA,MAAAyC,GAAA,YAAAA,CAAAzC,EAAA;EAAA;IAAAG,OAAA,EAAAH;EAAA;AAAA;AAAA,SAAA0C,qEAAA9C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA5B7FnB,EAAE,CAAA4B,uBAAA,EA2BR,CAAC;IA3BK5B,EAAE,CAAA6B,UAAA,IAAAgC,mFAAA,yBA4B0F,CAAC;IA5B7F7D,EAAE,CAAA8B,qBAAA,CA6BzD,CAAC;EAAA;EAAA,IAAAX,EAAA;IAAA,MAAA+C,SAAA,GAAA9C,GAAA,CAAAuB,KAAA;IAAA,MAAAwB,OAAA,GA7BsDnE,EAAE,CAAAgC,aAAA;IAAFhC,EAAE,CAAAiC,SAAA,EA4BnB,CAAC;IA5BgBjC,EAAE,CAAAkC,UAAA,qBAAAiC,OAAA,CAAAC,cA4BnB,CAAC,4BA5BgBpE,EAAE,CAAAoD,eAAA,IAAAY,GAAA,EAAAG,OAAA,CAAAE,gBAAA,CAAAH,SAAA,EAAAC,OAAA,CAAAG,IAAA,IAAFtE,EAAE,CAAAoD,eAAA,IAAAU,GAAA,EAAAK,OAAA,CAAAI,mBAAA,CAAAC,IAAA,GA4BnB,CAAC;EAAA;AAAA;AAAA,SAAAC,sDAAAtD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA5BgBnB,EAAE,CAAA4B,uBAAA,EA0BnB,CAAC;IA1BgB5B,EAAE,CAAA6B,UAAA,IAAAoC,oEAAA,0BA6BzD,CAAC;IA7BsDjE,EAAE,CAAA8B,qBAAA,CA8B7D,CAAC;EAAA;EAAA,IAAAX,EAAA;IAAA,MAAAuD,OAAA,GA9B0D1E,EAAE,CAAAgC,aAAA;IAAFhC,EAAE,CAAAiC,SAAA,EA2B3B,CAAC;IA3BwBjC,EAAE,CAAAkC,UAAA,YAAAwC,OAAA,CAAAC,SA2B3B,CAAC;EAAA;AAAA;AAAA,SAAAC,mFAAAzD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA3BwBnB,EAAE,CAAAqB,kBAAA,EAiCiE,CAAC;EAAA;AAAA;AAAA,MAAAwD,GAAA,YAAAA,CAAA;EAAA;IAAAC,UAAA;EAAA;AAAA;AAAA,SAAAC,oEAAA5D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjCpEnB,EAAE,CAAA4B,uBAAA,EAgCP,CAAC;IAhCI5B,EAAE,CAAA6B,UAAA,IAAA+C,kFAAA,yBAiCiE,CAAC;IAjCpE5E,EAAE,CAAA8B,qBAAA,CAkCzD,CAAC;EAAA;EAAA,IAAAX,EAAA;IAAA,MAAA6D,OAAA,GAlCsDhF,EAAE,CAAAgC,aAAA;IAAFhC,EAAE,CAAAiC,SAAA,EAiCf,CAAC;IAjCYjC,EAAE,CAAAkC,UAAA,qBAAA8C,OAAA,CAAAC,kBAiCf,CAAC,4BAjCYjF,EAAE,CAAAoD,eAAA,IAAAY,GAAA,EAAFhE,EAAE,CAAAkF,eAAA,IAAAL,GAAA,EAiCf,CAAC;EAAA;AAAA;AAAA,SAAAM,mEAAAhE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjCYnB,EAAE,CAAA0D,SAAA,qBAoC2B,CAAC;EAAA;EAAA,IAAAvC,EAAA;IApC9BnB,EAAE,CAAAkC,UAAA,wCAoCf,CAAC;IApCYlC,EAAE,CAAAuD,WAAA,iCAoCwB,CAAC;EAAA;AAAA;AAAA,SAAA6B,qDAAAjE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApC3BnB,EAAE,CAAA6B,UAAA,IAAAkD,mEAAA,yBAkCzD,CAAC;IAlCsD/E,EAAE,CAAA6B,UAAA,IAAAsD,kEAAA,iCAAFnF,EAAE,CAAAqF,sBAqC1D,CAAC;EAAA;EAAA,IAAAlE,EAAA;IAAA,MAAAmE,IAAA,GArCuDtF,EAAE,CAAAuF,WAAA;IAAA,MAAAC,OAAA,GAAFxF,EAAE,CAAAgC,aAAA;IAAFhC,EAAE,CAAAkC,UAAA,SAAAsD,OAAA,CAAAP,kBAgC/B,CAAC,aAAAK,IAAD,CAAC;EAAA;AAAA;AAAA,MAAAG,GAAA,YAAAA,CAAAlE,EAAA;EAAA;IAAA,uBAAAA;EAAA;AAAA;AAAA,SAAAmE,uCAAAvE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhC4BnB,EAAE,CAAAiD,cAAA,aAyB4F,CAAC;IAzB/FjD,EAAE,CAAA6B,UAAA,IAAA4C,qDAAA,yBA8B7D,CAAC;IA9B0DzE,EAAE,CAAA6B,UAAA,IAAAuD,oDAAA,iCAAFpF,EAAE,CAAAqF,sBAsC9D,CAAC;IAtC2DrF,EAAE,CAAAkD,YAAA,CAuC1E,CAAC;EAAA;EAAA,IAAA/B,EAAA;IAAA,MAAAwE,IAAA,GAvCuE3F,EAAE,CAAAuF,WAAA;IAAA,MAAAK,MAAA,GAAF5F,EAAE,CAAAgC,aAAA;IAAFhC,EAAE,CAAAkC,UAAA,YAAFlC,EAAE,CAAAoD,eAAA,IAAAqC,GAAA,GAAAG,MAAA,CAAAxB,cAAA,CAyByD,CAAC;IAzB5DpE,EAAE,CAAAuD,WAAA,4BAyB2F,CAAC;IAzB9FvD,EAAE,CAAAiC,SAAA,EA0BvC,CAAC;IA1BoCjC,EAAE,CAAAkC,UAAA,SAAA0D,MAAA,CAAAxB,cA0BvC,CAAC,aAAAuB,IAAD,CAAC;EAAA;AAAA;AAAA,MAAAE,GAAA,YAAAA,CAAArE,EAAA,EAAAsE,EAAA,EAAAC,EAAA;EAAA;IAAA;IAAA,qBAAAvE,EAAA;IAAA,iBAAAsE,EAAA;IAAA,uBAAAC;EAAA;AAAA;AAAA,SAAAC,iCAAA7E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA8E,IAAA,GA1BoCjG,EAAE,CAAAkG,gBAAA;IAAFlG,EAAE,CAAA4B,uBAAA,EAE/B,CAAC;IAF4B5B,EAAE,CAAAiD,cAAA,eAanF,CAAC;IAbgFjD,EAAE,CAAAmG,UAAA,oBAAAC,uDAAAC,MAAA;MAAFrG,EAAE,CAAAsG,aAAA,CAAAL,IAAA;MAAA,MAAAM,OAAA,GAAFvG,EAAE,CAAAgC,aAAA;MAAA,OAAFhC,EAAE,CAAAwG,WAAA,CAUrED,OAAA,CAAAE,iBAAA,CAAAJ,MAAwB,EAAC;IAAA,EAAC;IAVyCrG,EAAE,CAAA6B,UAAA,IAAAF,+CAAA,yBAgBjE,CAAC;IAhB8D3B,EAAE,CAAA6B,UAAA,IAAAmB,8CAAA,gCAAFhD,EAAE,CAAAqF,sBAuBlE,CAAC;IAvB+DrF,EAAE,CAAA6B,UAAA,IAAA4B,sCAAA,gBAwBmC,CAAC;IAxBtCzD,EAAE,CAAA6B,UAAA,IAAA6D,sCAAA,gBAuC1E,CAAC;IAvCuE1F,EAAE,CAAAkD,YAAA,CAwC9E,CAAC;IAxC2ElD,EAAE,CAAA8B,qBAAA,CAyCzE,CAAC;EAAA;EAAA,IAAAX,EAAA;IAAA,MAAAuF,GAAA,GAzCsE1G,EAAE,CAAAuF,WAAA;IAAA,MAAAoB,MAAA,GAAF3G,EAAE,CAAAgC,aAAA;IAAFhC,EAAE,CAAAiC,SAAA,EAQ3D,CAAC;IARwDjC,EAAE,CAAA4G,UAAA,CAAAD,MAAA,CAAAE,WAQ3D,CAAC;IARwD7G,EAAE,CAAAkC,UAAA,YAAAyE,MAAA,CAAAG,MAO9D,CAAC,YAP2D9G,EAAE,CAAA+G,eAAA,KAAAlB,GAAA,EAAAc,MAAA,CAAAK,MAAA,EAAAL,MAAA,CAAArC,IAAA,EAAAqC,MAAA,CAAAM,UAAA,CAO9D,CAAC;IAP2DjH,EAAE,CAAAuD,WAAA,OAAAoD,MAAA,CAAAO,GAKjE,CAAC,aAAAP,MAAA,CAAAQ,QAAD,CAAC,2BAAD,CAAC,0BAAD,CAAC;IAL8DnH,EAAE,CAAAiC,SAAA,EAc1C,CAAC;IAduCjC,EAAE,CAAAkC,UAAA,SAAAyE,MAAA,CAAAxE,eAc1C,CAAC,aAAAuE,GAAD,CAAC;IAduC1G,EAAE,CAAAiC,SAAA,EAwBzD,CAAC;IAxBsDjC,EAAE,CAAAkC,UAAA,SAAAyE,MAAA,CAAAS,WAwBzD,CAAC;IAxBsDpH,EAAE,CAAAiC,SAAA,EAyBzB,CAAC;IAzBsBjC,EAAE,CAAAkC,UAAA,UAAAyE,MAAA,CAAAU,cAAA,IAAAV,MAAA,CAAAW,WAAA,IAAAX,MAAA,CAAAtD,SAyBzB,CAAC;EAAA;AAAA;AAAA,SAAAkE,8DAAApG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzBsBnB,EAAE,CAAAqB,kBAAA,EA6CmE,CAAC;EAAA;AAAA;AAAA,MAAAmG,GAAA,YAAAA,CAAAjG,EAAA,EAAAC,EAAA;EAAA;IAAAiG,IAAA,EAAAlG,EAAA;IAAAmG,OAAA,EAAAlG;EAAA;AAAA;AAAA,SAAAmG,+CAAAxG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7CtEnB,EAAE,CAAA4B,uBAAA,EA4C9C,CAAC;IA5C2C5B,EAAE,CAAA6B,UAAA,IAAA0F,6DAAA,yBA6CmE,CAAC;IA7CtEvH,EAAE,CAAA8B,qBAAA,CA8CrE,CAAC;EAAA;EAAA,IAAAX,EAAA;IAAA,MAAAyG,OAAA,GA9CkE5H,EAAE,CAAAgC,aAAA;IAAFhC,EAAE,CAAAiC,SAAA,EA6C9B,CAAC;IA7C2BjC,EAAE,CAAAkC,UAAA,qBAAA0F,OAAA,CAAAzF,eA6C9B,CAAC,4BA7C2BnC,EAAE,CAAAoC,eAAA,IAAAd,GAAA,EAAAsG,OAAA,CAAAC,KAAA,EAAF7H,EAAE,CAAAoC,eAAA,IAAAoF,GAAA,EAAAI,OAAA,CAAAE,MAAA,EAAAF,OAAA,CAAAG,aAAA,EA6C9B,CAAC;EAAA;AAAA;AAAA,SAAAC,gCAAA7G,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7C2BnB,EAAE,CAAAiI,YAAA,EA2C3D,CAAC;IA3CwDjI,EAAE,CAAA6B,UAAA,IAAA8F,8CAAA,0BA8CrE,CAAC;EAAA;EAAA,IAAAxG,EAAA;IAAA,MAAA+G,MAAA,GA9CkElI,EAAE,CAAAgC,aAAA;IAAFhC,EAAE,CAAAiC,SAAA,EA4ChD,CAAC;IA5C6CjC,EAAE,CAAAkC,UAAA,SAAAgG,MAAA,CAAA/F,eA4ChD,CAAC;EAAA;AAAA;AAAA,MAAAgG,IAAA;AAl6BhD,MAAMC,QAAQ,CAAC;EACXC,QAAQ;EACRC,UAAU;EACVC,QAAQ;EACRC,EAAE;EACFC,IAAI;EACJ;AACJ;AACA;AACA;EACI,IAAIC,EAAEA,CAAA,EAAG;IACL,OAAO,IAAI,CAACxB,GAAG;EACnB;EACA,IAAIwB,EAAEA,CAACC,GAAG,EAAE;IACR,IAAI,CAACzB,GAAG,GAAGyB,GAAG;EAClB;EACA;AACJ;AACA;AACA;EACI,IAAIC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAAC9B,MAAM;EACtB;EACA,IAAI8B,KAAKA,CAACD,GAAG,EAAE;IACX,IAAI,CAAC7B,MAAM,GAAG6B,GAAG;EACrB;EACA;AACJ;AACA;AACA;EACI,IAAI7D,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC+B,WAAW;EAC3B;EACA,IAAI/B,UAAUA,CAAC6D,GAAG,EAAE;IAChB,IAAI,CAAC9B,WAAW,GAAG8B,GAAG;EAC1B;EACA;AACJ;AACA;AACA;EACI,IAAIxB,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC0B,SAAS;EACzB;EACA,IAAI1B,QAAQA,CAACwB,GAAG,EAAE;IACd,IAAI,CAACE,SAAS,GAAGF,GAAG;EACxB;EACA;AACJ;AACA;AACA;EACI,IAAId,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,MAAM;EACtB;EACA,IAAID,KAAKA,CAACc,GAAG,EAAE;IACX,IAAI,CAACb,MAAM,GAAGa,GAAG;EACrB;EACA;AACJ;AACA;AACA;EACI,IAAIG,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACH,GAAG,EAAE;IACd,IAAI,CAACI,SAAS,GAAGJ,GAAG;EACxB;EACA;AACJ;AACA;AACA;EACI,IAAIK,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACC,aAAa;EAC7B;EACA,IAAID,YAAYA,CAACL,GAAG,EAAE;IAClB,IAAI,CAACM,aAAa,GAAGN,GAAG;EAC5B;EACA;AACJ;AACA;AACA;EACI,IAAIO,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAID,WAAWA,CAACP,GAAG,EAAE;IACjB,IAAI,CAACQ,YAAY,GAAGR,GAAG;EAC3B;EACA;AACJ;AACA;AACA;EACI,IAAIS,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAID,WAAWA,CAACT,GAAG,EAAE;IACjB,IAAI,CAACU,YAAY,GAAGV,GAAG;EAC3B;EACA;AACJ;AACA;AACA;EACI,IAAIW,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,KAAK;EACrB;EACA,IAAID,IAAIA,CAACX,GAAG,EAAE;IACV,IAAI,CAACY,KAAK,GAAGZ,GAAG;EACpB;EACA;AACJ;AACA;AACA;EACI,IAAIa,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,MAAM;EACtB;EACA,IAAID,KAAKA,CAACb,GAAG,EAAE;IACX,IAAI,CAACc,MAAM,GAAGd,GAAG;EACrB;EACA;AACJ;AACA;AACA;EACI,IAAIe,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAID,WAAWA,CAACf,GAAG,EAAE;IACjB,IAAI,CAACgB,YAAY,GAAGhB,GAAG;EAC3B;EACA;AACJ;AACA;AACA;EACI,IAAIiB,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,WAAW;EAC3B;EACA,IAAID,UAAUA,CAACjB,GAAG,EAAE;IAChB,IAAI,CAACkB,WAAW,GAAGlB,GAAG;EAC1B;EACA;AACJ;AACA;AACA;EACI,IAAI3B,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC8C,OAAO;EACvB;EACA,IAAI9C,MAAMA,CAAC2B,GAAG,EAAE;IACZ,IAAI,CAACmB,OAAO,GAAGnB,GAAG;EACtB;EACA;AACJ;AACA;AACA;EACI,IAAIoB,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,KAAK;EACrB;EACA,IAAID,IAAIA,CAACpB,GAAG,EAAE;IACV,IAAI,CAACqB,KAAK,GAAGrB,GAAG;EACpB;EACA;AACJ;AACA;AACA;EACI,IAAIsB,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACtB,GAAG,EAAE;IACd,IAAI,CAACuB,SAAS,GAAGvB,GAAG;EACxB;EACA;AACJ;AACA;AACA;EACI,IAAItB,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAC8C,eAAe;EAC/B;EACA,IAAI9C,cAAcA,CAACsB,GAAG,EAAE;IACpB,IAAI,CAACwB,eAAe,GAAGxB,GAAG;EAC9B;EACA;AACJ;AACA;AACA;EACI,IAAIjB,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC0C,QAAQ;EACxB;EACA,IAAI1C,OAAOA,CAACiB,GAAG,EAAE;IACb,IAAI,CAACyB,QAAQ,GAAGzB,GAAG;EACvB;EACA;AACJ;AACA;AACA;EACI,IAAI0B,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACjD,WAAW;EAC3B;EACA,IAAIiD,UAAUA,CAAC1B,GAAG,EAAE;IAChB,IAAI,CAACvB,WAAW,GAAGuB,GAAG;EAC1B;EACA;AACJ;AACA;AACA;EACI,IAAI2B,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAAChD,WAAW;EAC3B;EACA,IAAIgD,UAAUA,CAAC3B,GAAG,EAAE;IAChB,IAAI,CAACrB,WAAW,GAAGqB,GAAG;EAC1B;EACA;AACJ;AACA;AACA;EACI,IAAI4B,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACC,kBAAkB;EAClC;EACA,IAAID,iBAAiBA,CAAC5B,GAAG,EAAE;IACvB,IAAI,CAAC6B,kBAAkB,GAAG7B,GAAG;EACjC;EACA;AACJ;AACA;AACA;EACI,IAAI8B,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACA,IAAID,OAAOA,CAAC9B,GAAG,EAAE;IACb,IAAI,CAAC+B,QAAQ,GAAG/B,GAAG;EACvB;EACA;AACJ;AACA;AACA;EACI,IAAIgC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAAChC,GAAG,EAAE;IACd,IAAI,CAACiC,SAAS,GAAGjC,GAAG;EACxB;EACA;AACJ;AACA;AACA;EACI,IAAIkC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACrH,QAAQ;EACxB;EACA,IAAIqH,OAAOA,CAAClC,GAAG,EAAE;IACb,IAAI,CAACnF,QAAQ,GAAGmF,GAAG;EACvB;EACA;AACJ;AACA;AACA;EACI,IAAIjH,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACoJ,QAAQ;EACxB;EACA,IAAIpJ,OAAOA,CAACiH,GAAG,EAAE;IACb,IAAI,CAACmC,QAAQ,GAAGnC,GAAG;IACnB,IAAIA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MAChC;MACAoC,MAAM,CAACC,OAAO,CAACrC,GAAG,CAAC,CAACsC,OAAO,CAAC,CAAC,CAACC,CAAC,EAAEC,CAAC,CAAC,KAAK,IAAI,CAAE,IAAGD,CAAE,EAAC,CAAC,KAAKC,CAAC,KAAK,IAAI,CAAE,IAAGD,CAAE,EAAC,CAAC,GAAGC,CAAC,CAAC,CAAC;IACvF;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIC,UAAU,GAAG,IAAInL,YAAY,CAAC,CAAC;EAC/B;AACJ;AACA;AACA;AACA;EACIoL,QAAQ,GAAG,IAAIpL,YAAY,CAAC,CAAC;EAC7B;AACJ;AACA;AACA;AACA;EACIqL,mBAAmB,GAAG,IAAIrL,YAAY,CAAC,CAAC;EACxCsL,gBAAgB;EAChBC,gBAAgB;EAChBC,SAAS;EACTvE,GAAG;EACHJ,MAAM;EACND,WAAW;EACXgC,SAAS,GAAG,CAAC;EACbf,MAAM;EACNiB,SAAS,GAAG,CAAC;EACbE,aAAa;EACbE,YAAY;EACZE,YAAY,GAAG,UAAU;EACzBE,KAAK,GAAG,CAAC;EACTE,MAAM,GAAG,CAAC;EACVE,YAAY,GAAG,EAAE;EACjBE,WAAW,GAAG,KAAK;EACnBC,OAAO,GAAG,KAAK;EACfE,KAAK,GAAG,KAAK;EACbE,SAAS,GAAG,KAAK;EACjBC,eAAe,GAAG,KAAK;EACvBC,QAAQ;EACRhD,WAAW,GAAG,IAAI;EAClBE,WAAW,GAAG,KAAK;EACnBkD,kBAAkB;EAClBE,QAAQ;EACRE,SAAS,GAAG,KAAK;EACjBpH,QAAQ;EACRsH,QAAQ;EACRzH,SAAS,GAAG,KAAK;EACjBqI,mBAAmB;EACnBC,SAAS;EACTxJ,eAAe;EACfU,YAAY;EACZuB,cAAc;EACda,kBAAkB;EAClB2G,KAAK,GAAG,CAAC;EACTC,IAAI,GAAG,CAAC;EACRC,IAAI,GAAG,CAAC;EACRC,cAAc,GAAG,KAAK;EACtBC,kBAAkB,GAAG,CAAC;EACtBC,aAAa,GAAG,CAAC;EACjBC,aAAa,GAAG,CAAC,CAAC;EAClBvH,SAAS,GAAG,EAAE;EACdf,WAAW,GAAG,CAAC,CAAC;EAChBN,YAAY,GAAG,CAAC,CAAC;EACjB6I,aAAa;EACbC,aAAa;EACbC,WAAW,GAAG,KAAK;EACnBC,oBAAoB;EACpBC,YAAY;EACZC,aAAa;EACbC,mBAAmB;EACnBC,oBAAoB;EACpB,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACtD,YAAY,KAAK,UAAU;EAC3C;EACA,IAAIpC,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACoC,YAAY,KAAK,YAAY;EAC7C;EACA,IAAI/E,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC+E,YAAY,KAAK,MAAM;EACvC;EACA,IAAIhH,WAAWA,CAAA,EAAG;IACd,IAAI,IAAI,CAACyF,MAAM,IAAI,CAAC,IAAI,CAACzE,SAAS,EAAE;MAChC,IAAI,IAAI,CAACiB,IAAI,EACT,OAAO,IAAI,CAACwD,MAAM,CAAC8E,KAAK,CAAC,IAAI,CAAC/C,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC+B,KAAK,CAACnE,IAAI,EAAE,IAAI,CAACoE,IAAI,CAACpE,IAAI,CAAC,CAACoF,GAAG,CAAEC,IAAI,IAAM,IAAI,CAAC1C,QAAQ,GAAG0C,IAAI,GAAGA,IAAI,CAACF,KAAK,CAAC,IAAI,CAAC/C,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC+B,KAAK,CAACpH,IAAI,EAAE,IAAI,CAACqH,IAAI,CAACrH,IAAI,CAAE,CAAC,CAAC,KAC3L,IAAI,IAAI,CAACyC,UAAU,IAAI,IAAI,CAACmD,QAAQ,EACrC,OAAO,IAAI,CAACtC,MAAM,CAAC,KAEnB,OAAO,IAAI,CAACA,MAAM,CAAC8E,KAAK,CAAC,IAAI,CAAC/C,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC+B,KAAK,EAAE,IAAI,CAACC,IAAI,CAAC;IAC9E;IACA,OAAO,EAAE;EACb;EACA,IAAIkB,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC1J,SAAS,GAAI,IAAI,CAAC8G,eAAe,GAAG,IAAI,CAACxF,SAAS,GAAG,EAAE,GAAI,IAAI,CAACtC,WAAW;EAC3F;EACA,IAAI0F,aAAaA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACqC,QAAQ,KAAK,IAAI,CAAC9F,IAAI,IAAI,IAAI,CAAC2C,UAAU,CAAC,EAAE;MACjD,OAAO,IAAI,CAAC5D,SAAS,IAAI,IAAI,CAAC8G,eAAe,GAAI,IAAI,CAAC7F,IAAI,GAAG,IAAI,CAACK,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAACA,SAAS,GAAI,IAAI,CAACyF,QAAQ,CAACwC,KAAK,CAAC,IAAI,CAACtI,IAAI,GAAG,IAAI,CAACsH,KAAK,CAACpH,IAAI,GAAG,IAAI,CAACoH,KAAK,EAAE,IAAI,CAACtH,IAAI,GAAG,IAAI,CAACuH,IAAI,CAACrH,IAAI,GAAG,IAAI,CAACqH,IAAI,CAAC;IAC5M;IACA,OAAO,IAAI,CAACzB,QAAQ;EACxB;EACA,IAAI4C,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACzD,KAAK,GAAG,IAAI,CAACuC,IAAI,KAAK,IAAI,CAACmB,cAAc,CAAC,CAAC,GAAG,IAAI;EAClE;EACAC,WAAWA,CAAC7E,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,EAAE,EAAEC,IAAI,EAAE;IAClD,IAAI,CAACJ,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,IAAI,GAAGA,IAAI;EACpB;EACA0E,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,eAAe,CAAC,CAAC;EAC1B;EACAC,WAAWA,CAACC,aAAa,EAAE;IACvB,IAAIC,gBAAgB,GAAG,KAAK;IAC5B,IAAID,aAAa,CAAC7C,OAAO,EAAE;MACvB,MAAM;QAAE+C,aAAa;QAAEC;MAAa,CAAC,GAAGH,aAAa,CAAC7C,OAAO;MAC7D,IAAI,IAAI,CAACV,IAAI,IAAIyD,aAAa,KAAKC,YAAY,IAAIA,YAAY,KAAK,IAAI,CAACpK,SAAS,EAAE;QAChF,IAAI,CAACA,SAAS,GAAGoK,YAAY;QAC7BF,gBAAgB,GAAG,IAAI;MAC3B;IACJ;IACA,IAAID,aAAa,CAAClE,WAAW,EAAE;MAC3B,IAAI,CAAC6C,aAAa,GAAG,IAAI,CAAC3H,IAAI,GAAG;QAAEoJ,GAAG,EAAE,CAAC;QAAEC,IAAI,EAAE;MAAE,CAAC,GAAG,CAAC;IAC5D;IACA,IAAIL,aAAa,CAAC/C,iBAAiB,EAAE;MACjC,MAAM;QAAEiD,aAAa;QAAEC;MAAa,CAAC,GAAGH,aAAa,CAAC/C,iBAAiB;MACvE,IAAIiD,aAAa,KAAKC,YAAY,IAAIA,YAAY,KAAK,IAAI,CAAC/B,mBAAmB,EAAE;QAC7E,IAAI,CAACA,mBAAmB,GAAG+B,YAAY;MAC3C;IACJ;IACA,IAAIH,aAAa,CAAC5L,OAAO,EAAE;MACvB,MAAM;QAAE8L,aAAa;QAAEC;MAAa,CAAC,GAAGH,aAAa,CAAC5L,OAAO;MAC7D,IAAI,IAAI,CAACqI,IAAI,IAAIyD,aAAa,EAAE/C,OAAO,KAAKgD,YAAY,EAAEhD,OAAO,IAAIgD,YAAY,EAAEhD,OAAO,KAAK,IAAI,CAACpH,SAAS,EAAE;QAC3G,IAAI,CAACA,SAAS,GAAGoK,YAAY,CAAChD,OAAO;QACrC8C,gBAAgB,GAAG,IAAI;MAC3B;MACA,IAAIC,aAAa,EAAEjD,iBAAiB,KAAKkD,YAAY,EAAElD,iBAAiB,IAAIkD,YAAY,EAAElD,iBAAiB,KAAK,IAAI,CAACmB,mBAAmB,EAAE;QACtI,IAAI,CAACA,mBAAmB,GAAG+B,YAAY,CAAClD,iBAAiB;MAC7D;IACJ;IACA,IAAI,IAAI,CAAC8B,WAAW,EAAE;MAClB,MAAMuB,SAAS,GAAG,CAACL,gBAAgB,KAAKD,aAAa,CAACzF,KAAK,EAAE2F,aAAa,EAAEK,MAAM,KAAKP,aAAa,CAACzF,KAAK,EAAE4F,YAAY,EAAEI,MAAM,IAAIP,aAAa,CAACxE,QAAQ,IAAIwE,aAAa,CAACtE,YAAY,IAAIsE,aAAa,CAACpE,WAAW,CAAC;MACtN,IAAI0E,SAAS,EAAE;QACX,IAAI,CAACE,IAAI,CAAC,CAAC;QACX,IAAI,CAACC,iBAAiB,CAAC,CAAC;MAC5B;IACJ;EACJ;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACvC,SAAS,CAACR,OAAO,CAAE6B,IAAI,IAAK;MAC7B,QAAQA,IAAI,CAACmB,OAAO,CAAC,CAAC;QAClB,KAAK,SAAS;UACV,IAAI,CAAC9L,eAAe,GAAG2K,IAAI,CAACoB,QAAQ;UACpC;QACJ,KAAK,MAAM;UACP,IAAI,CAACrL,YAAY,GAAGiK,IAAI,CAACoB,QAAQ;UACjC;QACJ,KAAK,QAAQ;UACT,IAAI,CAAC9J,cAAc,GAAG0I,IAAI,CAACoB,QAAQ;UACnC;QACJ,KAAK,YAAY;UACb,IAAI,CAACjJ,kBAAkB,GAAG6H,IAAI,CAACoB,QAAQ;UACvC;QACJ;UACI,IAAI,CAACrL,YAAY,GAAGiK,IAAI,CAACoB,QAAQ;UACjC;MACR;IACJ,CAAC,CAAC;EACN;EACAC,eAAeA,CAAA,EAAG;IACdC,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;MACzB,IAAI,CAACC,QAAQ,CAAC,CAAC;IACnB,CAAC,CAAC;EACN;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACnC,WAAW,EAAE;MACnB,IAAI,CAACkC,QAAQ,CAAC,CAAC;IACnB;EACJ;EACAE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAAC/C,SAAS,GAAG,IAAI;IACrB,IAAI,CAACU,WAAW,GAAG,KAAK;EAC5B;EACAkC,QAAQA,CAAA,EAAG;IACP,IAAI1O,iBAAiB,CAAC,IAAI,CAACyI,UAAU,CAAC,EAAE;MACpC,IAAIxH,UAAU,CAAC6N,SAAS,CAAC,IAAI,CAACpD,gBAAgB,EAAEqD,aAAa,CAAC,EAAE;QAC5D,IAAI,CAACxB,eAAe,CAAC,CAAC;QACtB,IAAI,CAACyB,YAAY,CAAC,IAAI,CAAClD,SAAS,CAAC;QACjC,IAAI,CAACmC,IAAI,CAAC,CAAC;QACX,IAAI,CAACvB,YAAY,GAAGzL,UAAU,CAACgO,QAAQ,CAAC,IAAI,CAACvD,gBAAgB,EAAEqD,aAAa,CAAC;QAC7E,IAAI,CAACpC,aAAa,GAAG1L,UAAU,CAACiO,SAAS,CAAC,IAAI,CAACxD,gBAAgB,EAAEqD,aAAa,CAAC;QAC/E,IAAI,CAACnC,mBAAmB,GAAG3L,UAAU,CAACgO,QAAQ,CAAC,IAAI,CAACnD,SAAS,CAAC;QAC9D,IAAI,CAACe,oBAAoB,GAAG5L,UAAU,CAACiO,SAAS,CAAC,IAAI,CAACpD,SAAS,CAAC;QAChE,IAAI,CAACU,WAAW,GAAG,IAAI;MAC3B;IACJ;EACJ;EACAyB,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAAC5D,SAAS,EAAE;MACjB,IAAI,CAAC8E,OAAO,CAAC,CAAC;MACd,IAAI,CAACC,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAACC,aAAa,CAAC,CAAC;MACpB,IAAI,CAACC,kBAAkB,CAAC,CAAC;MACzB,IAAI,CAAC3G,EAAE,CAAC4G,aAAa,CAAC,CAAC;IAC3B;EACJ;EACAP,YAAYA,CAACQ,EAAE,EAAE;IACb,IAAI,CAAC1D,SAAS,GAAG0D,EAAE,IAAI,IAAI,CAAC7D,gBAAgB,EAAEoD,aAAa,IAAI9N,UAAU,CAACwO,UAAU,CAAC,IAAI,CAAC/D,gBAAgB,EAAEqD,aAAa,EAAE,qBAAqB,CAAC;EACrJ;EACAxB,eAAeA,CAAA,EAAG;IACd,IAAI,CAACxB,KAAK,GAAG,IAAI,CAACtH,IAAI,GAAG;MAAEmD,IAAI,EAAE,CAAC;MAAEjD,IAAI,EAAE;IAAE,CAAC,GAAG,CAAC;IACjD,IAAI,CAACqH,IAAI,GAAG,IAAI,CAACvH,IAAI,GAAG;MAAEmD,IAAI,EAAE,CAAC;MAAEjD,IAAI,EAAE;IAAE,CAAC,GAAG,CAAC;IAChD,IAAI,CAACwH,kBAAkB,GAAG,IAAI,CAAC1H,IAAI,GAAG;MAAEmD,IAAI,EAAE,CAAC;MAAEjD,IAAI,EAAE;IAAE,CAAC,GAAG,CAAC;IAC9D,IAAI,CAACyH,aAAa,GAAG,IAAI,CAAC3H,IAAI,GAAG;MAAEoJ,GAAG,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAC,GAAG,CAAC;IACxD,IAAI,CAACtK,SAAS,GAAG,IAAI,CAACqH,QAAQ,IAAI,KAAK;IACvC,IAAI,CAACgB,mBAAmB,GAAG,IAAI,CAAClB,kBAAkB;IAClD,IAAI,CAAC7F,SAAS,GAAG,EAAE;IACnB,IAAI,CAACf,WAAW,GAAG,CAAC,CAAC;IACrB,IAAI,CAACN,YAAY,GAAG,CAAC,CAAC;EAC1B;EACAiM,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAChE,gBAAgB;EAChC;EACA0B,cAAcA,CAAA,EAAG;IACb,OAAOuC,IAAI,CAACC,KAAK,CAAC,CAAC,IAAI,CAAC7D,KAAK,GAAG,IAAI,CAACF,mBAAmB,GAAG,CAAC,KAAK,IAAI,CAACnC,KAAK,IAAI,CAAC,CAAC,CAAC;EACtF;EACAmG,QAAQA,CAAChO,OAAO,EAAE;IACd,IAAI,CAACuK,aAAa,GAAG,IAAI,CAAC3H,IAAI,GAAG;MAAEoJ,GAAG,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAC,GAAG,CAAC;IACxD,IAAI,CAACpC,gBAAgB,EAAEqD,aAAa,EAAEc,QAAQ,CAAChO,OAAO,CAAC;EAC3D;EACAiO,aAAaA,CAAChN,KAAK,EAAEiN,QAAQ,GAAG,MAAM,EAAE;IACpC,MAAM;MAAErF;IAAkB,CAAC,GAAG,IAAI,CAACsF,iBAAiB,CAAC,CAAC;IACtD,MAAMC,UAAU,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;IAC5C,MAAMC,cAAc,GAAGA,CAACC,MAAM,GAAG,CAAC,EAAEC,KAAK,KAAMD,MAAM,IAAIC,KAAK,GAAG,CAAC,GAAGD,MAAO;IAC5E,MAAME,cAAc,GAAGA,CAACC,MAAM,EAAEC,KAAK,EAAEC,KAAK,KAAKF,MAAM,GAAGC,KAAK,GAAGC,KAAK;IACvE,MAAMZ,QAAQ,GAAGA,CAAC/B,IAAI,GAAG,CAAC,EAAED,GAAG,GAAG,CAAC,KAAK,IAAI,CAACgC,QAAQ,CAAC;MAAE/B,IAAI;MAAED,GAAG;MAAEkC;IAAS,CAAC,CAAC;IAC9E,IAAIW,QAAQ,GAAG,CAAC;IAChB,IAAI,IAAI,CAACjM,IAAI,EAAE;MACXiM,QAAQ,GAAG;QAAE9I,IAAI,EAAEuI,cAAc,CAACrN,KAAK,CAAC,CAAC,CAAC,EAAE4H,iBAAiB,CAAC,CAAC,CAAC,CAAC;QAAE/F,IAAI,EAAEwL,cAAc,CAACrN,KAAK,CAAC,CAAC,CAAC,EAAE4H,iBAAiB,CAAC,CAAC,CAAC;MAAE,CAAC;MACzHmF,QAAQ,CAACS,cAAc,CAACI,QAAQ,CAAC/L,IAAI,EAAE,IAAI,CAACuE,SAAS,CAAC,CAAC,CAAC,EAAE+G,UAAU,CAACnC,IAAI,CAAC,EAAEwC,cAAc,CAACI,QAAQ,CAAC9I,IAAI,EAAE,IAAI,CAACsB,SAAS,CAAC,CAAC,CAAC,EAAE+G,UAAU,CAACpC,GAAG,CAAC,CAAC;IACjJ,CAAC,MACI;MACD6C,QAAQ,GAAGP,cAAc,CAACrN,KAAK,EAAE4H,iBAAiB,CAAC;MACnD,IAAI,CAACtD,UAAU,GAAGyI,QAAQ,CAACS,cAAc,CAACI,QAAQ,EAAE,IAAI,CAACxH,SAAS,EAAE+G,UAAU,CAACnC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG+B,QAAQ,CAAC,CAAC,EAAES,cAAc,CAACI,QAAQ,EAAE,IAAI,CAACxH,SAAS,EAAE+G,UAAU,CAACpC,GAAG,CAAC,CAAC;IACpK;IACA,IAAI,CAAC3B,cAAc,GAAG,IAAI,CAACH,KAAK,KAAK2E,QAAQ;IAC7C,IAAI,CAAC3E,KAAK,GAAG2E,QAAQ;EACzB;EACAC,YAAYA,CAAC7N,KAAK,EAAE8N,EAAE,EAAEb,QAAQ,GAAG,MAAM,EAAE;IACvC,IAAIa,EAAE,EAAE;MACJ,MAAM;QAAE7E,KAAK;QAAE8E;MAAS,CAAC,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;MACnD,MAAMjB,QAAQ,GAAGA,CAAC/B,IAAI,GAAG,CAAC,EAAED,GAAG,GAAG,CAAC,KAAK,IAAI,CAACgC,QAAQ,CAAC;QAAE/B,IAAI;QAAED,GAAG;QAAEkC;MAAS,CAAC,CAAC;MAC9E,MAAMgB,SAAS,GAAGH,EAAE,KAAK,UAAU;MACnC,MAAMI,OAAO,GAAGJ,EAAE,KAAK,QAAQ;MAC/B,IAAIG,SAAS,EAAE;QACX,IAAI,IAAI,CAACtM,IAAI,EAAE;UACX,IAAIoM,QAAQ,CAAC9E,KAAK,CAACnE,IAAI,GAAGmE,KAAK,CAACnE,IAAI,GAAG9E,KAAK,CAAC,CAAC,CAAC,EAAE;YAC7C+M,QAAQ,CAACgB,QAAQ,CAAC9E,KAAK,CAACpH,IAAI,GAAG,IAAI,CAACuE,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC2H,QAAQ,CAAC9E,KAAK,CAACnE,IAAI,GAAG,CAAC,IAAI,IAAI,CAACsB,SAAS,CAAC,CAAC,CAAC,CAAC;UACpG,CAAC,MACI,IAAI2H,QAAQ,CAAC9E,KAAK,CAACpH,IAAI,GAAGoH,KAAK,CAACpH,IAAI,GAAG7B,KAAK,CAAC,CAAC,CAAC,EAAE;YAClD+M,QAAQ,CAAC,CAACgB,QAAQ,CAAC9E,KAAK,CAACpH,IAAI,GAAG,CAAC,IAAI,IAAI,CAACuE,SAAS,CAAC,CAAC,CAAC,EAAE2H,QAAQ,CAAC9E,KAAK,CAACnE,IAAI,GAAG,IAAI,CAACsB,SAAS,CAAC,CAAC,CAAC,CAAC;UACpG;QACJ,CAAC,MACI;UACD,IAAI2H,QAAQ,CAAC9E,KAAK,GAAGA,KAAK,GAAGjJ,KAAK,EAAE;YAChC,MAAMmO,GAAG,GAAG,CAACJ,QAAQ,CAAC9E,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC7C,SAAS;YACjD,IAAI,CAAC9B,UAAU,GAAGyI,QAAQ,CAACoB,GAAG,EAAE,CAAC,CAAC,GAAGpB,QAAQ,CAAC,CAAC,EAAEoB,GAAG,CAAC;UACzD;QACJ;MACJ,CAAC,MACI,IAAID,OAAO,EAAE;QACd,IAAI,IAAI,CAACvM,IAAI,EAAE;UACX,IAAIoM,QAAQ,CAAC7E,IAAI,CAACpE,IAAI,GAAGmE,KAAK,CAACnE,IAAI,IAAI9E,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;YACjD+M,QAAQ,CAACgB,QAAQ,CAAC9E,KAAK,CAACpH,IAAI,GAAG,IAAI,CAACuE,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC2H,QAAQ,CAAC9E,KAAK,CAACnE,IAAI,GAAG,CAAC,IAAI,IAAI,CAACsB,SAAS,CAAC,CAAC,CAAC,CAAC;UACpG,CAAC,MACI,IAAI2H,QAAQ,CAAC7E,IAAI,CAACrH,IAAI,GAAGoH,KAAK,CAACpH,IAAI,IAAI7B,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;YACtD+M,QAAQ,CAAC,CAACgB,QAAQ,CAAC9E,KAAK,CAACpH,IAAI,GAAG,CAAC,IAAI,IAAI,CAACuE,SAAS,CAAC,CAAC,CAAC,EAAE2H,QAAQ,CAAC9E,KAAK,CAACnE,IAAI,GAAG,IAAI,CAACsB,SAAS,CAAC,CAAC,CAAC,CAAC;UACpG;QACJ,CAAC,MACI;UACD,IAAI2H,QAAQ,CAAC7E,IAAI,GAAGD,KAAK,IAAIjJ,KAAK,GAAG,CAAC,EAAE;YACpC,MAAMmO,GAAG,GAAG,CAACJ,QAAQ,CAAC9E,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC7C,SAAS;YACjD,IAAI,CAAC9B,UAAU,GAAGyI,QAAQ,CAACoB,GAAG,EAAE,CAAC,CAAC,GAAGpB,QAAQ,CAAC,CAAC,EAAEoB,GAAG,CAAC;UACzD;QACJ;MACJ;IACJ,CAAC,MACI;MACD,IAAI,CAACnB,aAAa,CAAChN,KAAK,EAAEiN,QAAQ,CAAC;IACvC;EACJ;EACAe,gBAAgBA,CAAA,EAAG;IACf,MAAMI,wBAAwB,GAAGA,CAACC,IAAI,EAAEX,KAAK,KAAKb,IAAI,CAACC,KAAK,CAACuB,IAAI,IAAIX,KAAK,IAAIW,IAAI,CAAC,CAAC;IACpF,IAAIC,eAAe,GAAG,IAAI,CAACrF,KAAK;IAChC,IAAIsF,cAAc,GAAG,CAAC;IACtB,IAAI,IAAI,CAAC3F,gBAAgB,EAAEqD,aAAa,EAAE;MACtC,MAAM;QAAEuC,SAAS;QAAEC;MAAW,CAAC,GAAG,IAAI,CAAC7F,gBAAgB,CAACqD,aAAa;MACrE,IAAI,IAAI,CAACtK,IAAI,EAAE;QACX2M,eAAe,GAAG;UAAExJ,IAAI,EAAEsJ,wBAAwB,CAACI,SAAS,EAAE,IAAI,CAACpI,SAAS,CAAC,CAAC,CAAC,CAAC;UAAEvE,IAAI,EAAEuM,wBAAwB,CAACK,UAAU,EAAE,IAAI,CAACrI,SAAS,CAAC,CAAC,CAAC;QAAE,CAAC;QACjJmI,cAAc,GAAG;UAAEzJ,IAAI,EAAEwJ,eAAe,CAACxJ,IAAI,GAAG,IAAI,CAACuE,kBAAkB,CAACvE,IAAI;UAAEjD,IAAI,EAAEyM,eAAe,CAACzM,IAAI,GAAG,IAAI,CAACwH,kBAAkB,CAACxH;QAAK,CAAC;MAC7I,CAAC,MACI;QACD,MAAM6M,SAAS,GAAG,IAAI,CAACpK,UAAU,GAAGmK,UAAU,GAAGD,SAAS;QAC1DF,eAAe,GAAGF,wBAAwB,CAACM,SAAS,EAAE,IAAI,CAACtI,SAAS,CAAC;QACrEmI,cAAc,GAAGD,eAAe,GAAG,IAAI,CAACjF,kBAAkB;MAC9D;IACJ;IACA,OAAO;MACHJ,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBC,IAAI,EAAE,IAAI,CAACA,IAAI;MACf6E,QAAQ,EAAE;QACN9E,KAAK,EAAEqF,eAAe;QACtBpF,IAAI,EAAEqF;MACV;IACJ,CAAC;EACL;EACArB,iBAAiBA,CAAA,EAAG;IAChB,MAAMC,UAAU,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;IAC5C,MAAMuB,YAAY,GAAG,CAAC,IAAI,CAAC/F,gBAAgB,EAAEqD,aAAa,GAAG,IAAI,CAACrD,gBAAgB,CAACqD,aAAa,CAAC2C,WAAW,GAAGzB,UAAU,CAACnC,IAAI,GAAG,CAAC,KAAK,CAAC;IACxI,MAAM6D,aAAa,GAAG,CAAC,IAAI,CAACjG,gBAAgB,EAAEqD,aAAa,GAAG,IAAI,CAACrD,gBAAgB,CAACqD,aAAa,CAAC6C,YAAY,GAAG3B,UAAU,CAACpC,GAAG,GAAG,CAAC,KAAK,CAAC;IACzI,MAAMgE,2BAA2B,GAAGA,CAACC,YAAY,EAAE5I,SAAS,KAAKyG,IAAI,CAACoC,IAAI,CAACD,YAAY,IAAI5I,SAAS,IAAI4I,YAAY,CAAC,CAAC;IACtH,MAAME,0BAA0B,GAAIC,SAAS,IAAKtC,IAAI,CAACoC,IAAI,CAACE,SAAS,GAAG,CAAC,CAAC;IAC1E,MAAM9F,kBAAkB,GAAG,IAAI,CAAC1H,IAAI,GAC9B;MAAEmD,IAAI,EAAEiK,2BAA2B,CAACF,aAAa,EAAE,IAAI,CAACzI,SAAS,CAAC,CAAC,CAAC,CAAC;MAAEvE,IAAI,EAAEkN,2BAA2B,CAACJ,YAAY,EAAE,IAAI,CAACvI,SAAS,CAAC,CAAC,CAAC;IAAE,CAAC,GAC3I2I,2BAA2B,CAAC,IAAI,CAACzK,UAAU,GAAGqK,YAAY,GAAGE,aAAa,EAAE,IAAI,CAACzI,SAAS,CAAC;IACjG,MAAMwB,iBAAiB,GAAG,IAAI,CAACmB,mBAAmB,KAAK,IAAI,CAACpH,IAAI,GAAG,CAACuN,0BAA0B,CAAC7F,kBAAkB,CAACvE,IAAI,CAAC,EAAEoK,0BAA0B,CAAC7F,kBAAkB,CAACxH,IAAI,CAAC,CAAC,GAAGqN,0BAA0B,CAAC7F,kBAAkB,CAAC,CAAC;IAC/N,OAAO;MAAEA,kBAAkB;MAAEzB;IAAkB,CAAC;EACpD;EACA0E,gBAAgBA,CAAA,EAAG;IACf,MAAM;MAAEjD,kBAAkB;MAAEzB;IAAkB,CAAC,GAAG,IAAI,CAACsF,iBAAiB,CAAC,CAAC;IAC1E,MAAMkC,aAAa,GAAGA,CAAC3B,MAAM,EAAE4B,IAAI,EAAE9B,KAAK,EAAE+B,OAAO,GAAG,KAAK,KAAK,IAAI,CAACC,OAAO,CAAC9B,MAAM,GAAG4B,IAAI,GAAG,CAAC5B,MAAM,GAAGF,KAAK,GAAG,CAAC,GAAG,CAAC,IAAIA,KAAK,EAAE+B,OAAO,CAAC;IACvI,MAAMrG,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,MAAMC,IAAI,GAAG,IAAI,CAACvH,IAAI,GAChB;MAAEmD,IAAI,EAAEsK,aAAa,CAAC,IAAI,CAACnG,KAAK,CAACnE,IAAI,EAAEuE,kBAAkB,CAACvE,IAAI,EAAE8C,iBAAiB,CAAC,CAAC,CAAC,CAAC;MAAE/F,IAAI,EAAEuN,aAAa,CAAC,IAAI,CAACnG,KAAK,CAACpH,IAAI,EAAEwH,kBAAkB,CAACxH,IAAI,EAAE+F,iBAAiB,CAAC,CAAC,CAAC,EAAE,IAAI;IAAE,CAAC,GAClLwH,aAAa,CAAC,IAAI,CAACnG,KAAK,EAAEI,kBAAkB,EAAEzB,iBAAiB,CAAC;IACtE,IAAI,CAACsB,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACG,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACN,mBAAmB,GAAGnB,iBAAiB;IAC5C,IAAI,IAAI,CAACD,UAAU,EAAE;MACjB,IAAI,CAAC3F,SAAS,GAAG,IAAI,CAACL,IAAI,GAAG6N,KAAK,CAACC,IAAI,CAAC;QAAEvE,MAAM,EAAE7B,kBAAkB,CAACvE;MAAK,CAAC,CAAC,CAACoF,GAAG,CAAC,MAAMsF,KAAK,CAACC,IAAI,CAAC;QAAEvE,MAAM,EAAE7B,kBAAkB,CAACxH;MAAK,CAAC,CAAC,CAAC,GAAG2N,KAAK,CAACC,IAAI,CAAC;QAAEvE,MAAM,EAAE7B;MAAmB,CAAC,CAAC;IACxL;IACA,IAAI,IAAI,CAAChC,KAAK,EAAE;MACZoE,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QACzB,IAAI,CAACpC,aAAa,GAAG;UACjBN,KAAK,EAAE,IAAI,CAACrC,KAAK,GAAI,IAAI,CAACjF,IAAI,GAAG;YAAEmD,IAAI,EAAE,CAAC;YAAEjD,IAAI,EAAEoH,KAAK,CAACpH;UAAK,CAAC,GAAG,CAAC,GAAIoH,KAAK;UAC3EC,IAAI,EAAE2D,IAAI,CAAC6C,GAAG,CAAC,IAAI,CAAC9I,KAAK,GAAG,IAAI,CAACA,KAAK,GAAG,IAAI,CAACsC,IAAI,EAAE,IAAI,CAAChE,KAAK,CAACgG,MAAM;QACzE,CAAC;QACD,IAAI,CAACyE,YAAY,CAAC,YAAY,EAAE,IAAI,CAACpG,aAAa,CAAC;MACvD,CAAC,CAAC;IACN;EACJ;EACA6B,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACnD,SAAS,IAAI,CAAC,IAAI,CAACvH,SAAS,EAAE;MACnC+K,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QACzB,IAAI,IAAI,CAAC3C,SAAS,EAAE;UAChB,IAAI,CAACA,SAAS,CAAC/C,KAAK,CAAC2J,SAAS,GAAG,IAAI,CAAC5G,SAAS,CAAC/C,KAAK,CAAC4J,QAAQ,GAAG,MAAM;UACvE,IAAI,CAAC7G,SAAS,CAAC/C,KAAK,CAAC6J,QAAQ,GAAG,UAAU;UAC1C,IAAI,CAAClH,gBAAgB,CAACqD,aAAa,CAAChG,KAAK,CAAC8J,OAAO,GAAG,MAAM;UAC1D,MAAM,CAACpB,YAAY,EAAEE,aAAa,CAAC,GAAG,CAAC1Q,UAAU,CAACgO,QAAQ,CAAC,IAAI,CAACnD,SAAS,CAAC,EAAE7K,UAAU,CAACiO,SAAS,CAAC,IAAI,CAACpD,SAAS,CAAC,CAAC;UACjH2F,YAAY,KAAK,IAAI,CAAC7E,mBAAmB,KAAK,IAAI,CAAClB,gBAAgB,CAACqD,aAAa,CAAChG,KAAK,CAAC+J,KAAK,GAAG,EAAE,CAAC;UACnGnB,aAAa,KAAK,IAAI,CAAC9E,oBAAoB,KAAK,IAAI,CAACnB,gBAAgB,CAACqD,aAAa,CAAChG,KAAK,CAACgK,MAAM,GAAG,EAAE,CAAC;UACtG,MAAM,CAACD,KAAK,EAAEC,MAAM,CAAC,GAAG,CAAC9R,UAAU,CAACgO,QAAQ,CAAC,IAAI,CAACvD,gBAAgB,CAACqD,aAAa,CAAC,EAAE9N,UAAU,CAACiO,SAAS,CAAC,IAAI,CAACxD,gBAAgB,CAACqD,aAAa,CAAC,CAAC;UAC7I,CAAC,IAAI,CAACtK,IAAI,IAAI,IAAI,CAAC2C,UAAU,MAAM,IAAI,CAACsE,gBAAgB,CAACqD,aAAa,CAAChG,KAAK,CAAC+J,KAAK,GAAGA,KAAK,GAAG,IAAI,CAACpG,YAAY,GAAGoG,KAAK,GAAG,IAAI,GAAG,IAAI,CAACxJ,YAAY,IAAI,IAAI,CAACoD,YAAY,GAAG,IAAI,CAAC;UAC9K,CAAC,IAAI,CAACjI,IAAI,IAAI,IAAI,CAACqI,QAAQ,MAAM,IAAI,CAACpB,gBAAgB,CAACqD,aAAa,CAAChG,KAAK,CAACgK,MAAM,GAAGA,MAAM,GAAG,IAAI,CAACpG,aAAa,GAAGoG,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC3J,aAAa,IAAI,IAAI,CAACuD,aAAa,GAAG,IAAI,CAAC;UAClL,IAAI,CAACb,SAAS,CAAC/C,KAAK,CAAC2J,SAAS,GAAG,IAAI,CAAC5G,SAAS,CAAC/C,KAAK,CAAC4J,QAAQ,GAAG,EAAE;UACnE,IAAI,CAAC7G,SAAS,CAAC/C,KAAK,CAAC6J,QAAQ,GAAG,EAAE;UAClC,IAAI,CAAClH,gBAAgB,CAACqD,aAAa,CAAChG,KAAK,CAAC8J,OAAO,GAAG,EAAE;QAC1D;MACJ,CAAC,CAAC;IACN;EACJ;EACAR,OAAOA,CAACrG,IAAI,GAAG,CAAC,EAAEgH,MAAM,GAAG,KAAK,EAAE;IAC9B,OAAO,IAAI,CAAC/K,MAAM,GAAG0H,IAAI,CAAC6C,GAAG,CAACQ,MAAM,GAAG,CAAC,IAAI,CAACzI,QAAQ,IAAI,IAAI,CAACtC,MAAM,CAAC,CAAC,CAAC,EAAE+F,MAAM,GAAG,IAAI,CAAC/F,MAAM,CAAC+F,MAAM,EAAEhC,IAAI,CAAC,GAAG,CAAC;EACnH;EACAkE,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACpE,SAAS,EAAE;MAChB,MAAM/C,KAAK,GAAGkK,gBAAgB,CAAC,IAAI,CAACnH,SAAS,CAAC;MAC9C,MAAMgC,IAAI,GAAGoF,UAAU,CAACnK,KAAK,CAACoK,WAAW,CAAC,GAAGxD,IAAI,CAACyD,GAAG,CAACF,UAAU,CAACnK,KAAK,CAAC+E,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;MACrF,MAAMuF,KAAK,GAAGH,UAAU,CAACnK,KAAK,CAACuK,YAAY,CAAC,GAAG3D,IAAI,CAACyD,GAAG,CAACF,UAAU,CAACnK,KAAK,CAACsK,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;MACxF,MAAMxF,GAAG,GAAGqF,UAAU,CAACnK,KAAK,CAACwK,UAAU,CAAC,GAAG5D,IAAI,CAACyD,GAAG,CAACF,UAAU,CAACnK,KAAK,CAAC8E,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;MAClF,MAAM2F,MAAM,GAAGN,UAAU,CAACnK,KAAK,CAAC0K,aAAa,CAAC,GAAG9D,IAAI,CAACyD,GAAG,CAACF,UAAU,CAACnK,KAAK,CAACyK,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;MAC3F,OAAO;QAAE1F,IAAI;QAAEuF,KAAK;QAAExF,GAAG;QAAE2F,MAAM;QAAEE,CAAC,EAAE5F,IAAI,GAAGuF,KAAK;QAAEM,CAAC,EAAE9F,GAAG,GAAG2F;MAAO,CAAC;IACzE;IACA,OAAO;MAAE1F,IAAI,EAAE,CAAC;MAAEuF,KAAK,EAAE,CAAC;MAAExF,GAAG,EAAE,CAAC;MAAE2F,MAAM,EAAE,CAAC;MAAEE,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;EAC/D;EACAxE,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACzD,gBAAgB,EAAEqD,aAAa,EAAE;MACtC,MAAM6E,aAAa,GAAG,IAAI,CAAClI,gBAAgB,CAACqD,aAAa,CAAC6E,aAAa,CAACA,aAAa;MACrF,MAAMd,KAAK,GAAG,IAAI,CAACxJ,YAAY,IAAK,GAAE,IAAI,CAACoC,gBAAgB,CAACqD,aAAa,CAAC2C,WAAW,IAAIkC,aAAa,CAAClC,WAAY,IAAG;MACtH,MAAMqB,MAAM,GAAG,IAAI,CAAC3J,aAAa,IAAK,GAAE,IAAI,CAACsC,gBAAgB,CAACqD,aAAa,CAAC6C,YAAY,IAAIgC,aAAa,CAAChC,YAAa,IAAG;MAC1H,MAAMiC,OAAO,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAM,IAAI,CAACrI,gBAAgB,CAACqD,aAAa,CAAChG,KAAK,CAAC+K,KAAK,CAAC,GAAGC,MAAO;MAC9F,IAAI,IAAI,CAACtP,IAAI,IAAI,IAAI,CAAC2C,UAAU,EAAE;QAC9ByM,OAAO,CAAC,QAAQ,EAAEd,MAAM,CAAC;QACzBc,OAAO,CAAC,OAAO,EAAEf,KAAK,CAAC;MAC3B,CAAC,MACI;QACDe,OAAO,CAAC,QAAQ,EAAEd,MAAM,CAAC;MAC7B;IACJ;EACJ;EACA1D,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACpH,MAAM,EAAE;MACb,MAAMgI,UAAU,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;MAC5C,MAAM2D,OAAO,GAAGA,CAACC,KAAK,EAAEC,MAAM,EAAEvD,KAAK,EAAEC,KAAK,GAAG,CAAC,KAAM,IAAI,CAAC1M,WAAW,GAAG;QAAE,GAAG,IAAI,CAACA,WAAW;QAAE,GAAG;UAAE,CAAE,GAAE+P,KAAM,EAAC,GAAG,CAACC,MAAM,IAAI,EAAE,EAAE/F,MAAM,GAAGwC,KAAK,GAAGC,KAAK,GAAG;QAAK;MAAE,CAAE;MACpK,IAAI,IAAI,CAAChM,IAAI,EAAE;QACXoP,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC5L,MAAM,EAAE,IAAI,CAACiB,SAAS,CAAC,CAAC,CAAC,EAAE+G,UAAU,CAAC0D,CAAC,CAAC;QAC/DE,OAAO,CAAC,OAAO,EAAE,IAAI,CAACtJ,QAAQ,IAAI,IAAI,CAACtC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACiB,SAAS,CAAC,CAAC,CAAC,EAAE+G,UAAU,CAACyD,CAAC,CAAC;MACtF,CAAC,MACI;QACD,IAAI,CAACtM,UAAU,GAAGyM,OAAO,CAAC,OAAO,EAAE,IAAI,CAACtJ,QAAQ,IAAI,IAAI,CAACtC,MAAM,EAAE,IAAI,CAACiB,SAAS,EAAE+G,UAAU,CAACyD,CAAC,CAAC,GAAGG,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC5L,MAAM,EAAE,IAAI,CAACiB,SAAS,EAAE+G,UAAU,CAAC0D,CAAC,CAAC;MACjK;IACJ;EACJ;EACAK,kBAAkBA,CAAC/C,GAAG,EAAE;IACpB,IAAI,IAAI,CAACnF,SAAS,IAAI,CAAC,IAAI,CAAC9B,WAAW,EAAE;MACrC,MAAM+B,KAAK,GAAGkF,GAAG,GAAGA,GAAG,CAAClF,KAAK,GAAG,IAAI,CAACA,KAAK;MAC1C,MAAMkI,qBAAqB,GAAGA,CAAC1D,MAAM,EAAEC,KAAK,KAAKD,MAAM,GAAGC,KAAK;MAC/D,MAAM0D,YAAY,GAAGA,CAACC,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,KAAM,IAAI,CAAC3Q,YAAY,GAAG;QAAE,GAAG,IAAI,CAACA,YAAY;QAAE,GAAG;UAAE4Q,SAAS,EAAG,eAAcF,EAAG,OAAMC,EAAG;QAAQ;MAAE,CAAE;MAC7I,IAAI,IAAI,CAAC3P,IAAI,EAAE;QACXyP,YAAY,CAACD,qBAAqB,CAAClI,KAAK,CAACpH,IAAI,EAAE,IAAI,CAACuE,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE+K,qBAAqB,CAAClI,KAAK,CAACnE,IAAI,EAAE,IAAI,CAACsB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5H,CAAC,MACI;QACD,MAAMoL,YAAY,GAAGL,qBAAqB,CAAClI,KAAK,EAAE,IAAI,CAAC7C,SAAS,CAAC;QACjE,IAAI,CAAC9B,UAAU,GAAG8M,YAAY,CAACI,YAAY,EAAE,CAAC,CAAC,GAAGJ,YAAY,CAAC,CAAC,EAAEI,YAAY,CAAC;MACnF;IACJ;EACJ;EACAC,sBAAsBA,CAACC,KAAK,EAAE;IAC1B,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAAM;IAC3B,MAAMxE,UAAU,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;IAC5C,MAAMwE,kBAAkB,GAAGA,CAACvD,IAAI,EAAEV,KAAK,KAAMU,IAAI,GAAIA,IAAI,GAAGV,KAAK,GAAGU,IAAI,GAAGV,KAAK,GAAGU,IAAI,GAAI,CAAE;IAC7F,MAAMwD,qBAAqB,GAAGA,CAACxD,IAAI,EAAEX,KAAK,KAAKb,IAAI,CAACC,KAAK,CAACuB,IAAI,IAAIX,KAAK,IAAIW,IAAI,CAAC,CAAC;IACjF,MAAMyD,qBAAqB,GAAGA,CAACC,aAAa,EAAEtE,MAAM,EAAEuE,KAAK,EAAE3C,IAAI,EAAE9B,KAAK,EAAE0E,oBAAoB,KAAK;MAC/F,OAAOF,aAAa,IAAIxE,KAAK,GAAGA,KAAK,GAAG0E,oBAAoB,GAAGD,KAAK,GAAG3C,IAAI,GAAG9B,KAAK,GAAGE,MAAM,GAAGF,KAAK,GAAG,CAAC;IAC5G,CAAC;IACD,MAAMF,cAAc,GAAGA,CAAC0E,aAAa,EAAEG,aAAa,EAAEzE,MAAM,EAAEuE,KAAK,EAAE3C,IAAI,EAAE9B,KAAK,EAAE0E,oBAAoB,KAAK;MACvG,IAAIF,aAAa,IAAIxE,KAAK,EACtB,OAAO,CAAC,CAAC,KAET,OAAOV,IAAI,CAACyD,GAAG,CAAC,CAAC,EAAE2B,oBAAoB,GAAIF,aAAa,GAAGG,aAAa,GAAGzE,MAAM,GAAGsE,aAAa,GAAGxE,KAAK,GAAIwE,aAAa,GAAGG,aAAa,GAAGzE,MAAM,GAAGsE,aAAa,GAAG,CAAC,GAAGxE,KAAK,CAAC;IACxL,CAAC;IACD,MAAM6B,aAAa,GAAGA,CAAC2C,aAAa,EAAEtE,MAAM,EAAEuE,KAAK,EAAE3C,IAAI,EAAE9B,KAAK,EAAE+B,OAAO,GAAG,KAAK,KAAK;MAClF,IAAI6C,SAAS,GAAG1E,MAAM,GAAG4B,IAAI,GAAG,CAAC,GAAG9B,KAAK;MACzC,IAAIwE,aAAa,IAAIxE,KAAK,EAAE;QACxB4E,SAAS,IAAI5E,KAAK,GAAG,CAAC;MAC1B;MACA,OAAO,IAAI,CAACgC,OAAO,CAAC4C,SAAS,EAAE7C,OAAO,CAAC;IAC3C,CAAC;IACD,MAAMd,SAAS,GAAGoD,kBAAkB,CAACD,MAAM,CAACnD,SAAS,EAAErB,UAAU,CAACpC,GAAG,CAAC;IACtE,MAAM0D,UAAU,GAAGmD,kBAAkB,CAACD,MAAM,CAAClD,UAAU,EAAEtB,UAAU,CAACnC,IAAI,CAAC;IACzE,IAAI4C,QAAQ,GAAG,IAAI,CAACjM,IAAI,GAAG;MAAEmD,IAAI,EAAE,CAAC;MAAEjD,IAAI,EAAE;IAAE,CAAC,GAAG,CAAC;IACnD,IAAIuQ,OAAO,GAAG,IAAI,CAAClJ,IAAI;IACvB,IAAIE,cAAc,GAAG,KAAK;IAC1B,IAAIiJ,YAAY,GAAG,IAAI,CAAC/I,aAAa;IACrC,IAAI,IAAI,CAAC3H,IAAI,EAAE;MACX,MAAM2Q,YAAY,GAAG,IAAI,CAAChJ,aAAa,CAACyB,GAAG,IAAIyD,SAAS;MACxD,MAAM+D,aAAa,GAAG,IAAI,CAACjJ,aAAa,CAAC0B,IAAI,IAAIyD,UAAU;MAC3D,IAAI,CAAC,IAAI,CAACvH,WAAW,IAAK,IAAI,CAACA,WAAW,KAAKoL,YAAY,IAAIC,aAAa,CAAE,EAAE;QAC5E,MAAMC,YAAY,GAAG;UAAE1N,IAAI,EAAE+M,qBAAqB,CAACrD,SAAS,EAAE,IAAI,CAACpI,SAAS,CAAC,CAAC,CAAC,CAAC;UAAEvE,IAAI,EAAEgQ,qBAAqB,CAACpD,UAAU,EAAE,IAAI,CAACrI,SAAS,CAAC,CAAC,CAAC;QAAE,CAAC;QAC9I,MAAMqM,YAAY,GAAG;UACjB3N,IAAI,EAAEgN,qBAAqB,CAACU,YAAY,CAAC1N,IAAI,EAAE,IAAI,CAACmE,KAAK,CAACnE,IAAI,EAAE,IAAI,CAACoE,IAAI,CAACpE,IAAI,EAAE,IAAI,CAACuE,kBAAkB,CAACvE,IAAI,EAAE,IAAI,CAACiE,mBAAmB,CAAC,CAAC,CAAC,EAAEuJ,YAAY,CAAC;UACxJzQ,IAAI,EAAEiQ,qBAAqB,CAACU,YAAY,CAAC3Q,IAAI,EAAE,IAAI,CAACoH,KAAK,CAACpH,IAAI,EAAE,IAAI,CAACqH,IAAI,CAACrH,IAAI,EAAE,IAAI,CAACwH,kBAAkB,CAACxH,IAAI,EAAE,IAAI,CAACkH,mBAAmB,CAAC,CAAC,CAAC,EAAEwJ,aAAa;QAC5J,CAAC;QACD3E,QAAQ,GAAG;UACP9I,IAAI,EAAEuI,cAAc,CAACmF,YAAY,CAAC1N,IAAI,EAAE2N,YAAY,CAAC3N,IAAI,EAAE,IAAI,CAACmE,KAAK,CAACnE,IAAI,EAAE,IAAI,CAACoE,IAAI,CAACpE,IAAI,EAAE,IAAI,CAACuE,kBAAkB,CAACvE,IAAI,EAAE,IAAI,CAACiE,mBAAmB,CAAC,CAAC,CAAC,EAAEuJ,YAAY,CAAC;UACpKzQ,IAAI,EAAEwL,cAAc,CAACmF,YAAY,CAAC3Q,IAAI,EAAE4Q,YAAY,CAAC5Q,IAAI,EAAE,IAAI,CAACoH,KAAK,CAACpH,IAAI,EAAE,IAAI,CAACqH,IAAI,CAACrH,IAAI,EAAE,IAAI,CAACwH,kBAAkB,CAACxH,IAAI,EAAE,IAAI,CAACkH,mBAAmB,CAAC,CAAC,CAAC,EAAEwJ,aAAa;QACxK,CAAC;QACDH,OAAO,GAAG;UACNtN,IAAI,EAAEsK,aAAa,CAACoD,YAAY,CAAC1N,IAAI,EAAE8I,QAAQ,CAAC9I,IAAI,EAAE,IAAI,CAACoE,IAAI,CAACpE,IAAI,EAAE,IAAI,CAACuE,kBAAkB,CAACvE,IAAI,EAAE,IAAI,CAACiE,mBAAmB,CAAC,CAAC,CAAC,CAAC;UAChIlH,IAAI,EAAEuN,aAAa,CAACoD,YAAY,CAAC3Q,IAAI,EAAE+L,QAAQ,CAAC/L,IAAI,EAAE,IAAI,CAACqH,IAAI,CAACrH,IAAI,EAAE,IAAI,CAACwH,kBAAkB,CAACxH,IAAI,EAAE,IAAI,CAACkH,mBAAmB,CAAC,CAAC,CAAC,EAAE,IAAI;QACzI,CAAC;QACDK,cAAc,GAAGwE,QAAQ,CAAC9I,IAAI,KAAK,IAAI,CAACmE,KAAK,CAACnE,IAAI,IAAIsN,OAAO,CAACtN,IAAI,KAAK,IAAI,CAACoE,IAAI,CAACpE,IAAI,IAAI8I,QAAQ,CAAC/L,IAAI,KAAK,IAAI,CAACoH,KAAK,CAACpH,IAAI,IAAIuQ,OAAO,CAACvQ,IAAI,KAAK,IAAI,CAACqH,IAAI,CAACrH,IAAI,IAAI,IAAI,CAACuH,cAAc;QACpLiJ,YAAY,GAAG;UAAEtH,GAAG,EAAEyD,SAAS;UAAExD,IAAI,EAAEyD;QAAW,CAAC;MACvD;IACJ,CAAC,MACI;MACD,MAAMC,SAAS,GAAG,IAAI,CAACpK,UAAU,GAAGmK,UAAU,GAAGD,SAAS;MAC1D,MAAMkE,mBAAmB,GAAG,IAAI,CAACpJ,aAAa,IAAIoF,SAAS;MAC3D,IAAI,CAAC,IAAI,CAACxH,WAAW,IAAK,IAAI,CAACA,WAAW,IAAIwL,mBAAoB,EAAE;QAChE,MAAMF,YAAY,GAAGX,qBAAqB,CAACnD,SAAS,EAAE,IAAI,CAACtI,SAAS,CAAC;QACrE,MAAMqM,YAAY,GAAGX,qBAAqB,CAACU,YAAY,EAAE,IAAI,CAACvJ,KAAK,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACG,kBAAkB,EAAE,IAAI,CAACN,mBAAmB,EAAE2J,mBAAmB,CAAC;QACvJ9E,QAAQ,GAAGP,cAAc,CAACmF,YAAY,EAAEC,YAAY,EAAE,IAAI,CAACxJ,KAAK,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACG,kBAAkB,EAAE,IAAI,CAACN,mBAAmB,EAAE2J,mBAAmB,CAAC;QACpJN,OAAO,GAAGhD,aAAa,CAACoD,YAAY,EAAE5E,QAAQ,EAAE,IAAI,CAAC1E,IAAI,EAAE,IAAI,CAACG,kBAAkB,EAAE,IAAI,CAACN,mBAAmB,CAAC;QAC7GK,cAAc,GAAGwE,QAAQ,KAAK,IAAI,CAAC3E,KAAK,IAAImJ,OAAO,KAAK,IAAI,CAAClJ,IAAI,IAAI,IAAI,CAACE,cAAc;QACxFiJ,YAAY,GAAG3D,SAAS;MAC5B;IACJ;IACA,OAAO;MACHzF,KAAK,EAAE2E,QAAQ;MACf1E,IAAI,EAAEkJ,OAAO;MACbhJ,cAAc;MACdsF,SAAS,EAAE2D;IACf,CAAC;EACL;EACAM,cAAcA,CAACjB,KAAK,EAAE;IAClB,MAAM;MAAEzI,KAAK;MAAEC,IAAI;MAAEE,cAAc;MAAEsF;IAAU,CAAC,GAAG,IAAI,CAAC+C,sBAAsB,CAACC,KAAK,CAAC;IACrF,IAAItI,cAAc,EAAE;MAChB,MAAMwJ,QAAQ,GAAG;QAAE3J,KAAK;QAAEC;MAAK,CAAC;MAChC,IAAI,CAACgI,kBAAkB,CAAC0B,QAAQ,CAAC;MACjC,IAAI,CAAC3J,KAAK,GAAGA,KAAK;MAClB,IAAI,CAACC,IAAI,GAAGA,IAAI;MAChB,IAAI,CAACI,aAAa,GAAGoF,SAAS;MAC9B,IAAI,CAACiB,YAAY,CAAC,qBAAqB,EAAEiD,QAAQ,CAAC;MAClD,IAAI,IAAI,CAACvL,KAAK,IAAI,IAAI,CAACgD,aAAa,EAAE;QAClC,MAAMd,aAAa,GAAG;UAClBN,KAAK,EAAE,IAAI,CAACrC,KAAK,GAAGiG,IAAI,CAAC6C,GAAG,CAAC,IAAI,CAACpF,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC1D,KAAK,EAAE,IAAI,CAAC1B,KAAK,CAACgG,MAAM,GAAG,IAAI,CAACtE,KAAK,CAAC,GAAGqC,KAAK;UACxGC,IAAI,EAAE2D,IAAI,CAAC6C,GAAG,CAAC,IAAI,CAAC9I,KAAK,GAAG,CAAC,IAAI,CAAC0D,cAAc,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC1D,KAAK,GAAGsC,IAAI,EAAE,IAAI,CAAChE,KAAK,CAACgG,MAAM;QAClG,CAAC;QACD,MAAM2H,kBAAkB,GAAG,IAAI,CAACtJ,aAAa,CAACN,KAAK,KAAKM,aAAa,CAACN,KAAK,IAAI,IAAI,CAACM,aAAa,CAACL,IAAI,KAAKK,aAAa,CAACL,IAAI;QAC7H2J,kBAAkB,IAAI,IAAI,CAAClD,YAAY,CAAC,YAAY,EAAEpG,aAAa,CAAC;QACpE,IAAI,CAACA,aAAa,GAAGA,aAAa;MACtC;IACJ;EACJ;EACAzF,iBAAiBA,CAAC4N,KAAK,EAAE;IACrB,IAAI,CAAC/B,YAAY,CAAC,UAAU,EAAE;MAAEmD,aAAa,EAAEpB;IAAM,CAAC,CAAC;IACvD,IAAI,IAAI,CAAC5K,MAAM,IAAI,IAAI,CAACuD,aAAa,EAAE;MACnC,IAAI,IAAI,CAACb,aAAa,EAAE;QACpBuJ,YAAY,CAAC,IAAI,CAACvJ,aAAa,CAAC;MACpC;MACA,IAAI,CAAC,IAAI,CAAC9I,SAAS,IAAI,IAAI,CAACiH,UAAU,EAAE;QACpC,MAAM;UAAEyB;QAAe,CAAC,GAAG,IAAI,CAACqI,sBAAsB,CAACC,KAAK,CAAC;QAC7D,MAAMsB,OAAO,GAAG5J,cAAc,KAAK,IAAI,CAACxC,KAAK,GAAG,IAAI,CAACyD,aAAa,GAAG,KAAK,CAAC;QAC3E,IAAI2I,OAAO,EAAE;UACT,IAAI,CAACtS,SAAS,GAAG,IAAI;UACrB,IAAI,CAACmF,EAAE,CAAC4G,aAAa,CAAC,CAAC;QAC3B;MACJ;MACA,IAAI,CAACjD,aAAa,GAAGyJ,UAAU,CAAC,MAAM;QAClC,IAAI,CAACN,cAAc,CAACjB,KAAK,CAAC;QAC1B,IAAI,IAAI,CAAChR,SAAS,IAAI,IAAI,CAACiH,UAAU,KAAK,CAAC,IAAI,CAACN,KAAK,IAAI,IAAI,CAACU,QAAQ,KAAKmL,SAAS,CAAC,EAAE;UACnF,IAAI,CAACxS,SAAS,GAAG,KAAK;UACtB,IAAI,CAACyI,IAAI,GAAG,IAAI,CAACmB,cAAc,CAAC,CAAC;UACjC,IAAI,CAACzE,EAAE,CAAC4G,aAAa,CAAC,CAAC;QAC3B;MACJ,CAAC,EAAE,IAAI,CAAC3F,MAAM,CAAC;IACnB,CAAC,MACI;MACD,CAAC,IAAI,CAACpG,SAAS,IAAI,IAAI,CAACiS,cAAc,CAACjB,KAAK,CAAC;IACjD;EACJ;EACAlF,kBAAkBA,CAAA,EAAG;IACjB,IAAItP,iBAAiB,CAAC,IAAI,CAACyI,UAAU,CAAC,EAAE;MACpC,IAAI,CAAC,IAAI,CAACgE,oBAAoB,EAAE;QAC5B,IAAI,CAAC7D,IAAI,CAACqN,iBAAiB,CAAC,MAAM;UAC9B,MAAMC,MAAM,GAAG,IAAI,CAAC1N,QAAQ,CAAC2N,WAAW;UACxC,MAAM3B,KAAK,GAAGvT,UAAU,CAACmV,aAAa,CAAC,CAAC,GAAG,mBAAmB,GAAG,QAAQ;UACzE,IAAI,CAAC3J,oBAAoB,GAAG,IAAI,CAAC/D,QAAQ,CAAC2N,MAAM,CAACH,MAAM,EAAE1B,KAAK,EAAE,IAAI,CAAC8B,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnG,CAAC,CAAC;MACN;IACJ;EACJ;EACA1H,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACpC,oBAAoB,EAAE;MAC3B,IAAI,CAACA,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAACA,oBAAoB,GAAG,IAAI;IACpC;EACJ;EACA6J,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAAC/J,aAAa,EAAE;MACpBsJ,YAAY,CAAC,IAAI,CAACtJ,aAAa,CAAC;IACpC;IACA,IAAI,CAACA,aAAa,GAAGwJ,UAAU,CAAC,MAAM;MAClC,IAAI9U,UAAU,CAAC6N,SAAS,CAAC,IAAI,CAACpD,gBAAgB,EAAEqD,aAAa,CAAC,EAAE;QAC5D,MAAM,CAAC+D,KAAK,EAAEC,MAAM,CAAC,GAAG,CAAC9R,UAAU,CAACgO,QAAQ,CAAC,IAAI,CAACvD,gBAAgB,EAAEqD,aAAa,CAAC,EAAE9N,UAAU,CAACiO,SAAS,CAAC,IAAI,CAACxD,gBAAgB,EAAEqD,aAAa,CAAC,CAAC;QAC/I,MAAM,CAACyH,WAAW,EAAEC,YAAY,CAAC,GAAG,CAAC3D,KAAK,KAAK,IAAI,CAACpG,YAAY,EAAEqG,MAAM,KAAK,IAAI,CAACpG,aAAa,CAAC;QAChG,MAAM+J,MAAM,GAAG,IAAI,CAACjS,IAAI,GAAG+R,WAAW,IAAIC,YAAY,GAAG,IAAI,CAACrP,UAAU,GAAGoP,WAAW,GAAG,IAAI,CAAC1J,QAAQ,GAAG2J,YAAY,GAAG,KAAK;QAC7HC,MAAM,IACF,IAAI,CAAC9N,IAAI,CAAC+N,GAAG,CAAC,MAAM;UAChB,IAAI,CAAC9K,mBAAmB,GAAG,IAAI,CAAClB,kBAAkB;UAClD,IAAI,CAAC+B,YAAY,GAAGoG,KAAK;UACzB,IAAI,CAACnG,aAAa,GAAGoG,MAAM;UAC3B,IAAI,CAACnG,mBAAmB,GAAG3L,UAAU,CAACgO,QAAQ,CAAC,IAAI,CAACnD,SAAS,CAAC;UAC9D,IAAI,CAACe,oBAAoB,GAAG5L,UAAU,CAACiO,SAAS,CAAC,IAAI,CAACpD,SAAS,CAAC;UAChE,IAAI,CAACmC,IAAI,CAAC,CAAC;QACf,CAAC,CAAC;MACV;IACJ,CAAC,EAAE,IAAI,CAACnE,YAAY,CAAC;EACzB;EACA2I,YAAYA,CAACmE,IAAI,EAAEC,MAAM,EAAE;IACvB;IACA,OAAO,IAAI,CAAChV,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC+U,IAAI,CAAC,GAAG,IAAI,CAAC/U,OAAO,CAAC+U,IAAI,CAAC,CAACC,MAAM,CAAC,GAAG,IAAI,CAACD,IAAI,CAAC,CAACE,IAAI,CAACD,MAAM,CAAC;EACpG;EACApU,iBAAiBA,CAAA,EAAG;IAChB,OAAO;MACHsU,iBAAiB,EAAG,sBAAqB,IAAI,CAACvT,SAAS,GAAG,oBAAoB,GAAG,EAAG,EAAC;MACrFwE,KAAK,EAAE,IAAI,CAACxF,WAAW;MACvBwU,cAAc,EAAGlU,KAAK,IAAK,IAAI,CAACG,UAAU,CAACH,KAAK,CAAC;MACjD8H,OAAO,EAAE,IAAI,CAACpH,SAAS;MACvBgB,gBAAgB,EAAEA,CAAC1B,KAAK,EAAEjB,OAAO,KAAK,IAAI,CAAC2C,gBAAgB,CAAC1B,KAAK,EAAEjB,OAAO,CAAC;MAC3EoH,QAAQ,EAAE,IAAI,CAACC,SAAS;MACxBtB,IAAI,EAAE,IAAI,CAACsF,UAAU;MACrBrF,OAAO,EAAE,IAAI,CAACK,aAAa;MAC3BnE,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BN,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BqJ,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvB1F,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3B3C,IAAI,EAAE,IAAI,CAACA;IACf,CAAC;EACL;EACAxB,UAAUA,CAACgU,aAAa,EAAE;IACtB,MAAMC,KAAK,GAAG,CAAC,IAAI,CAACjP,MAAM,IAAI,EAAE,EAAE+F,MAAM;IACxC,MAAMlL,KAAK,GAAG,IAAI,CAAC2B,IAAI,GAAG,IAAI,CAACsH,KAAK,CAACnE,IAAI,GAAGqP,aAAa,GAAG,IAAI,CAAClL,KAAK,GAAGkL,aAAa;IACtF,OAAO;MACHnU,KAAK;MACLoU,KAAK;MACLnL,KAAK,EAAEjJ,KAAK,KAAK,CAAC;MAClBkJ,IAAI,EAAElJ,KAAK,KAAKoU,KAAK,GAAG,CAAC;MACzBC,IAAI,EAAErU,KAAK,GAAG,CAAC,KAAK,CAAC;MACrBsU,GAAG,EAAEtU,KAAK,GAAG,CAAC,KAAK;IACvB,CAAC;EACL;EACA0B,gBAAgBA,CAAC1B,KAAK,EAAEuU,UAAU,EAAE;IAChC,MAAMH,KAAK,GAAG,IAAI,CAACpS,SAAS,CAACkJ,MAAM;IACnC,OAAO;MACHlL,KAAK;MACLoU,KAAK;MACLnL,KAAK,EAAEjJ,KAAK,KAAK,CAAC;MAClBkJ,IAAI,EAAElJ,KAAK,KAAKoU,KAAK,GAAG,CAAC;MACzBC,IAAI,EAAErU,KAAK,GAAG,CAAC,KAAK,CAAC;MACrBsU,GAAG,EAAEtU,KAAK,GAAG,CAAC,KAAK,CAAC;MACpB,GAAGuU;IACP,CAAC;EACL;EACA,OAAOC,IAAI,YAAAC,iBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFjP,QAAQ,EAAlBpI,EAAE,CAAAsX,iBAAA,CAAkCxX,QAAQ,GAA5CE,EAAE,CAAAsX,iBAAA,CAAuDpX,WAAW,GAApEF,EAAE,CAAAsX,iBAAA,CAA+EtX,EAAE,CAACuX,SAAS,GAA7FvX,EAAE,CAAAsX,iBAAA,CAAwGtX,EAAE,CAACwX,iBAAiB,GAA9HxX,EAAE,CAAAsX,iBAAA,CAAyItX,EAAE,CAACyX,MAAM;EAAA;EAC7O,OAAOC,IAAI,kBAD8E1X,EAAE,CAAA2X,iBAAA;IAAAC,IAAA,EACJxP,QAAQ;IAAAyP,SAAA;IAAAC,cAAA,WAAAC,wBAAA5W,EAAA,EAAAC,GAAA,EAAA4W,QAAA;MAAA,IAAA7W,EAAA;QADNnB,EAAE,CAAAiY,cAAA,CAAAD,QAAA,EACozBpX,aAAa;MAAA;MAAA,IAAAO,EAAA;QAAA,IAAA+W,EAAA;QADn0BlY,EAAE,CAAAmY,cAAA,CAAAD,EAAA,GAAFlY,EAAE,CAAAoY,WAAA,QAAAhX,GAAA,CAAAqK,SAAA,GAAAyM,EAAA;MAAA;IAAA;IAAAG,SAAA,WAAAC,eAAAnX,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFnB,EAAE,CAAAuY,WAAA,CAAAvX,GAAA;QAAFhB,EAAE,CAAAuY,WAAA,CAAAtX,GAAA;MAAA;MAAA,IAAAE,EAAA;QAAA,IAAA+W,EAAA;QAAFlY,EAAE,CAAAmY,cAAA,CAAAD,EAAA,GAAFlY,EAAE,CAAAoY,WAAA,QAAAhX,GAAA,CAAAmK,gBAAA,GAAA2M,EAAA,CAAAtM,KAAA;QAAF5L,EAAE,CAAAmY,cAAA,CAAAD,EAAA,GAAFlY,EAAE,CAAAoY,WAAA,QAAAhX,GAAA,CAAAoK,gBAAA,GAAA0M,EAAA,CAAAtM,KAAA;MAAA;IAAA;IAAA4M,SAAA;IAAAC,MAAA;MAAA/P,EAAA;MAAAE,KAAA;MAAA9D,UAAA;MAAAqC,QAAA;MAAAU,KAAA;MAAAiB,QAAA;MAAAE,YAAA;MAAAE,WAAA;MAAAE,WAAA;MAAAE,IAAA;MAAAE,KAAA;MAAAE,WAAA;MAAAE,UAAA;MAAA5C,MAAA;MAAA+C,IAAA;MAAAE,QAAA;MAAA5C,cAAA;MAAAK,OAAA;MAAA2C,UAAA;MAAAC,UAAA;MAAAC,iBAAA;MAAAE,OAAA;MAAAE,QAAA;MAAAE,OAAA;MAAAnJ,OAAA;IAAA;IAAAgX,OAAA;MAAAtN,UAAA;MAAAC,QAAA;MAAAC,mBAAA;IAAA;IAAAqN,QAAA,GAAF3Y,EAAE,CAAA4Y,oBAAA;IAAAC,kBAAA,EAAA1Q,IAAA;IAAA2Q,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAA9K,QAAA,WAAA+K,kBAAA9X,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFnB,EAAE,CAAAkZ,eAAA;QAAFlZ,EAAE,CAAA6B,UAAA,IAAAmE,gCAAA,0BAyCzE,CAAC;QAzCsEhG,EAAE,CAAA6B,UAAA,IAAAmG,+BAAA,gCAAFhI,EAAE,CAAAqF,sBA+C1E,CAAC;MAAA;MAAA,IAAAlE,EAAA;QAAA,MAAAgY,GAAA,GA/CuEnZ,EAAE,CAAAuF,WAAA;QAAFvF,EAAE,CAAAkC,UAAA,UAAAd,GAAA,CAAA8I,SAEvD,CAAC,aAAAiP,GAAD,CAAC;MAAA;IAAA;IAAAC,YAAA,WAAAA,CAAA;MAAA,QA8C+sBxZ,EAAE,CAACyZ,OAAO,EAA2HzZ,EAAE,CAAC0Z,OAAO,EAA0J1Z,EAAE,CAAC2Z,IAAI,EAAoI3Z,EAAE,CAAC4Z,gBAAgB,EAA2L5Z,EAAE,CAAC6Z,OAAO,EAAkH1Y,WAAW;IAAA;IAAA2Y,MAAA;IAAAC,aAAA;EAAA;AACjgD;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAlD6F5Z,EAAE,CAAA6Z,iBAAA,CAkDJzR,QAAQ,EAAc,CAAC;IACtGwP,IAAI,EAAEzX,SAAS;IACf2Z,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,YAAY;MAAE7L,QAAQ,EAAG;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MAAE8L,eAAe,EAAE5Z,uBAAuB,CAAC6Z,OAAO;MAAEN,aAAa,EAAEtZ,iBAAiB,CAAC6Z,IAAI;MAAEC,IAAI,EAAE;QAC9EC,KAAK,EAAE;MACX,CAAC;MAAEV,MAAM,EAAE,CAAC,woBAAwoB;IAAE,CAAC;EACnqB,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE9B,IAAI,EAAEyC,QAAQ;MAAEC,UAAU,EAAE,CAAC;QAC7D1C,IAAI,EAAEtX,MAAM;QACZwZ,IAAI,EAAE,CAACha,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAE8X,IAAI,EAAE/B,SAAS;MAAEyE,UAAU,EAAE,CAAC;QAClC1C,IAAI,EAAEtX,MAAM;QACZwZ,IAAI,EAAE,CAAC5Z,WAAW;MACtB,CAAC;IAAE,CAAC,EAAE;MAAE0X,IAAI,EAAE5X,EAAE,CAACuX;IAAU,CAAC,EAAE;MAAEK,IAAI,EAAE5X,EAAE,CAACwX;IAAkB,CAAC,EAAE;MAAEI,IAAI,EAAE5X,EAAE,CAACyX;IAAO,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE/O,EAAE,EAAE,CAAC;MAC9GkP,IAAI,EAAErX;IACV,CAAC,CAAC;IAAEqI,KAAK,EAAE,CAAC;MACRgP,IAAI,EAAErX;IACV,CAAC,CAAC;IAAEuE,UAAU,EAAE,CAAC;MACb8S,IAAI,EAAErX;IACV,CAAC,CAAC;IAAE4G,QAAQ,EAAE,CAAC;MACXyQ,IAAI,EAAErX;IACV,CAAC,CAAC;IAAEsH,KAAK,EAAE,CAAC;MACR+P,IAAI,EAAErX;IACV,CAAC,CAAC;IAAEuI,QAAQ,EAAE,CAAC;MACX8O,IAAI,EAAErX;IACV,CAAC,CAAC;IAAEyI,YAAY,EAAE,CAAC;MACf4O,IAAI,EAAErX;IACV,CAAC,CAAC;IAAE2I,WAAW,EAAE,CAAC;MACd0O,IAAI,EAAErX;IACV,CAAC,CAAC;IAAE6I,WAAW,EAAE,CAAC;MACdwO,IAAI,EAAErX;IACV,CAAC,CAAC;IAAE+I,IAAI,EAAE,CAAC;MACPsO,IAAI,EAAErX;IACV,CAAC,CAAC;IAAEiJ,KAAK,EAAE,CAAC;MACRoO,IAAI,EAAErX;IACV,CAAC,CAAC;IAAEmJ,WAAW,EAAE,CAAC;MACdkO,IAAI,EAAErX;IACV,CAAC,CAAC;IAAEqJ,UAAU,EAAE,CAAC;MACbgO,IAAI,EAAErX;IACV,CAAC,CAAC;IAAEyG,MAAM,EAAE,CAAC;MACT4Q,IAAI,EAAErX;IACV,CAAC,CAAC;IAAEwJ,IAAI,EAAE,CAAC;MACP6N,IAAI,EAAErX;IACV,CAAC,CAAC;IAAE0J,QAAQ,EAAE,CAAC;MACX2N,IAAI,EAAErX;IACV,CAAC,CAAC;IAAE8G,cAAc,EAAE,CAAC;MACjBuQ,IAAI,EAAErX;IACV,CAAC,CAAC;IAAEmH,OAAO,EAAE,CAAC;MACVkQ,IAAI,EAAErX;IACV,CAAC,CAAC;IAAE8J,UAAU,EAAE,CAAC;MACbuN,IAAI,EAAErX;IACV,CAAC,CAAC;IAAE+J,UAAU,EAAE,CAAC;MACbsN,IAAI,EAAErX;IACV,CAAC,CAAC;IAAEgK,iBAAiB,EAAE,CAAC;MACpBqN,IAAI,EAAErX;IACV,CAAC,CAAC;IAAEkK,OAAO,EAAE,CAAC;MACVmN,IAAI,EAAErX;IACV,CAAC,CAAC;IAAEoK,QAAQ,EAAE,CAAC;MACXiN,IAAI,EAAErX;IACV,CAAC,CAAC;IAAEsK,OAAO,EAAE,CAAC;MACV+M,IAAI,EAAErX;IACV,CAAC,CAAC;IAAEmB,OAAO,EAAE,CAAC;MACVkW,IAAI,EAAErX;IACV,CAAC,CAAC;IAAE6K,UAAU,EAAE,CAAC;MACbwM,IAAI,EAAEpX;IACV,CAAC,CAAC;IAAE6K,QAAQ,EAAE,CAAC;MACXuM,IAAI,EAAEpX;IACV,CAAC,CAAC;IAAE8K,mBAAmB,EAAE,CAAC;MACtBsM,IAAI,EAAEpX;IACV,CAAC,CAAC;IAAE+K,gBAAgB,EAAE,CAAC;MACnBqM,IAAI,EAAEnX,SAAS;MACfqZ,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAEtO,gBAAgB,EAAE,CAAC;MACnBoM,IAAI,EAAEnX,SAAS;MACfqZ,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAErO,SAAS,EAAE,CAAC;MACZmM,IAAI,EAAElX,eAAe;MACrBoZ,IAAI,EAAE,CAAClZ,aAAa;IACxB,CAAC;EAAE,CAAC;AAAA;AAChB,MAAM2Z,cAAc,CAAC;EACjB,OAAOpD,IAAI,YAAAqD,uBAAAnD,CAAA;IAAA,YAAAA,CAAA,IAAwFkD,cAAc;EAAA;EACjH,OAAOE,IAAI,kBAhL8Eza,EAAE,CAAA0a,gBAAA;IAAA9C,IAAA,EAgLS2C;EAAc;EAClH,OAAOI,IAAI,kBAjL8E3a,EAAE,CAAA4a,gBAAA;IAAAC,OAAA,GAiLmC9a,YAAY,EAAEc,YAAY,EAAEE,WAAW,EAAEF,YAAY;EAAA;AACvL;AACA;EAAA,QAAA+Y,SAAA,oBAAAA,SAAA,KAnL6F5Z,EAAE,CAAA6Z,iBAAA,CAmLJU,cAAc,EAAc,CAAC;IAC5G3C,IAAI,EAAEjX,QAAQ;IACdmZ,IAAI,EAAE,CAAC;MACCe,OAAO,EAAE,CAAC9a,YAAY,EAAEc,YAAY,EAAEE,WAAW,CAAC;MAClD+Z,OAAO,EAAE,CAAC1S,QAAQ,EAAEvH,YAAY,CAAC;MACjCka,YAAY,EAAE,CAAC3S,QAAQ;IAC3B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASA,QAAQ,EAAEmS,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}