{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Input, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\n/**\n * Divider is used to separate contents.\n * @group Components\n */\nconst _c0 = [\"*\"];\nlet Divider = /*#__PURE__*/(() => {\n  class Divider {\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    style;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Specifies the orientation.\n     * @group Props\n     */\n    layout = 'horizontal';\n    /**\n     * Border style type.\n     * @group Props\n     */\n    type = 'solid';\n    /**\n     * Alignment of the content.\n     * @group Props\n     */\n    align;\n    containerClass() {\n      return {\n        'p-divider p-component': true,\n        'p-divider-horizontal': this.layout === 'horizontal',\n        'p-divider-vertical': this.layout === 'vertical',\n        'p-divider-solid': this.type === 'solid',\n        'p-divider-dashed': this.type === 'dashed',\n        'p-divider-dotted': this.type === 'dotted',\n        'p-divider-left': this.layout === 'horizontal' && (!this.align || this.align === 'left'),\n        'p-divider-center': this.layout === 'horizontal' && this.align === 'center' || this.layout === 'vertical' && (!this.align || this.align === 'center'),\n        'p-divider-right': this.layout === 'horizontal' && this.align === 'right',\n        'p-divider-top': this.layout === 'vertical' && this.align === 'top',\n        'p-divider-bottom': this.layout === 'vertical' && this.align === 'bottom'\n      };\n    }\n    static ɵfac = function Divider_Factory(t) {\n      return new (t || Divider)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: Divider,\n      selectors: [[\"p-divider\"]],\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        style: \"style\",\n        styleClass: \"styleClass\",\n        layout: \"layout\",\n        type: \"type\",\n        align: \"align\"\n      },\n      ngContentSelectors: _c0,\n      decls: 3,\n      vars: 6,\n      consts: [[\"role\", \"separator\", 3, \"ngClass\", \"ngStyle\"], [1, \"p-divider-content\"]],\n      template: function Divider_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵprojection(2);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.styleClass);\n          i0.ɵɵproperty(\"ngClass\", ctx.containerClass())(\"ngStyle\", ctx.style);\n          i0.ɵɵattribute(\"aria-orientation\", ctx.layout)(\"data-pc-name\", \"divider\");\n        }\n      },\n      dependencies: [i1.NgClass, i1.NgStyle],\n      styles: [\"@layer primeng{.p-divider-horizontal{display:flex;width:100%;position:relative;align-items:center}.p-divider-horizontal:before{position:absolute;display:block;top:50%;left:0;width:100%;content:\\\"\\\"}.p-divider-horizontal.p-divider-left{justify-content:flex-start}.p-divider-horizontal.p-divider-right{justify-content:flex-end}.p-divider-horizontal.p-divider-center{justify-content:center}.p-divider-content{z-index:1}.p-divider-vertical{min-height:100%;margin:0 1rem;display:flex;position:relative;justify-content:center}.p-divider-vertical:before{position:absolute;display:block;top:0;left:50%;height:100%;content:\\\"\\\"}.p-divider-vertical.p-divider-top{align-items:flex-start}.p-divider-vertical.p-divider-center{align-items:center}.p-divider-vertical.p-divider-bottom{align-items:flex-end}.p-divider-solid.p-divider-horizontal:before{border-top-style:solid}.p-divider-solid.p-divider-vertical:before{border-left-style:solid}.p-divider-dashed.p-divider-horizontal:before{border-top-style:dashed}.p-divider-dashed.p-divider-vertical:before{border-left-style:dashed}.p-divider-dotted.p-divider-horizontal:before{border-top-style:dotted}.p-divider-dotted.p-divider-horizontal:before{border-left-style:dotted}}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return Divider;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet DividerModule = /*#__PURE__*/(() => {\n  class DividerModule {\n    static ɵfac = function DividerModule_Factory(t) {\n      return new (t || DividerModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: DividerModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n  return DividerModule;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Divider, DividerModule };\n//# sourceMappingURL=primeng-divider.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}