.auth-content {
  padding: 1rem;
}

.alert {
  padding: 0.75rem 1rem;
  border-radius: 0.375rem;
  border: 1px solid transparent;
}

.alert-danger {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

.alert-success {
  color: #155724;
  background-color: #d4edda;
  border-color: #c3e6cb;
}

.text-muted {
  color: #6c757d !important;
}

.rounded-circle {
  border-radius: 50% !important;
}

.d-flex {
  display: flex;
}

.gap-2 {
  gap: 0.5rem;
}

.flex-fill {
  flex: 1 1 auto;
}

.w-100 {
  width: 100% !important;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-3 {
  margin-bottom: 1rem;
}

.mb-4 {
  margin-bottom: 1.5rem;
}

.mt-3 {
  margin-top: 1rem;
}

.me-1 {
  margin-right: 0.25rem;
}

.me-2 {
  margin-right: 0.5rem;
}

.text-center {
  text-align: center;
}
