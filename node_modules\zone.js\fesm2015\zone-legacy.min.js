"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2022 Google LLC. https://angular.io/
 * License: MIT
 */let zoneSymbol,_defineProperty,_getOwnPropertyDescriptor,_create,unconfigurablesKey;function propertyPatch(){zoneSymbol=Zone.__symbol__,_defineProperty=Object[zoneSymbol("defineProperty")]=Object.defineProperty,_getOwnPropertyDescriptor=Object[zoneSymbol("getOwnPropertyDescriptor")]=Object.getOwnPropertyDescriptor,_create=Object.create,unconfigurablesKey=zoneSymbol("unconfigurables"),Object.defineProperty=function(e,t,n){if(isUnconfigurable(e,t))throw new TypeError("Cannot assign to read only property '"+t+"' of "+e);const r=n.configurable;return"prototype"!==t&&(n=rewriteDescriptor(e,t,n)),_tryDefineProperty(e,t,n,r)},Object.defineProperties=function(e,t){Object.keys(t).forEach((function(n){Object.defineProperty(e,n,t[n])}));for(const n of Object.getOwnPropertySymbols(t)){const r=Object.getOwnPropertyDescriptor(t,n);r?.enumerable&&Object.defineProperty(e,n,t[n])}return e},Object.create=function(e,t){return"object"!=typeof t||Object.isFrozen(t)||Object.keys(t).forEach((function(n){t[n]=rewriteDescriptor(e,n,t[n])})),_create(e,t)},Object.getOwnPropertyDescriptor=function(e,t){const n=_getOwnPropertyDescriptor(e,t);return n&&isUnconfigurable(e,t)&&(n.configurable=!1),n}}function _redefineProperty(e,t,n){const r=n.configurable;return _tryDefineProperty(e,t,n=rewriteDescriptor(e,t,n),r)}function isUnconfigurable(e,t){return e&&e[unconfigurablesKey]&&e[unconfigurablesKey][t]}function rewriteDescriptor(e,t,n){return Object.isFrozen(n)||(n.configurable=!0),n.configurable||(e[unconfigurablesKey]||Object.isFrozen(e)||_defineProperty(e,unconfigurablesKey,{writable:!0,value:{}}),e[unconfigurablesKey]&&(e[unconfigurablesKey][t]=!0)),n}function _tryDefineProperty(e,t,n,r){try{return _defineProperty(e,t,n)}catch(o){if(!n.configurable)throw o;void 0===r?delete n.configurable:n.configurable=r;try{return _defineProperty(e,t,n)}catch(r){let o=!1;if("createdCallback"!==t&&"attachedCallback"!==t&&"detachedCallback"!==t&&"attributeChangedCallback"!==t||(o=!0),!o)throw r;let a=null;try{a=JSON.stringify(n)}catch(e){a=n.toString()}console.log(`Attempting to configure '${t}' with descriptor '${a}' on object '${e}' and got error, giving up: ${r}`)}}}function eventTargetLegacyPatch(e,t){const{eventNames:n,globalSources:r,zoneSymbolEventNames:o,TRUE_STR:a,FALSE_STR:c,ZONE_SYMBOL_PREFIX:i}=t.getGlobalObjects(),s="ApplicationCache,EventSource,FileReader,InputMethodContext,MediaController,MessagePort,Node,Performance,SVGElementInstance,SharedWorker,TextTrack,TextTrackCue,TextTrackList,WebKitNamedFlow,Window,Worker,WorkerGlobalScope,XMLHttpRequest,XMLHttpRequestEventTarget,XMLHttpRequestUpload,IDBRequest,IDBOpenDBRequest,IDBDatabase,IDBTransaction,IDBCursor,DBIndex,WebSocket".split(","),l="EventTarget";let p=[];const u=e.wtf,d="Anchor,Area,Audio,BR,Base,BaseFont,Body,Button,Canvas,Content,DList,Directory,Div,Embed,FieldSet,Font,Form,Frame,FrameSet,HR,Head,Heading,Html,IFrame,Image,Input,Keygen,LI,Label,Legend,Link,Map,Marquee,Media,Menu,Meta,Meter,Mod,OList,Object,OptGroup,Option,Output,Paragraph,Pre,Progress,Quote,Script,Select,Source,Span,Style,TableCaption,TableCell,TableCol,Table,TableRow,TableSection,TextArea,Title,Track,UList,Unknown,Video".split(",");u?p=d.map((e=>"HTML"+e+"Element")).concat(s):e[l]?p.push(l):p=s;const m=e.__Zone_disable_IE_check||!1,g=e.__Zone_enable_cross_context_check||!1,f=t.isIEOrEdge(),b="[object FunctionWrapper]",y="function __BROWSERTOOLS_CONSOLE_SAFEFUNC() { [native code] }",h={MSPointerCancel:"pointercancel",MSPointerDown:"pointerdown",MSPointerEnter:"pointerenter",MSPointerHover:"pointerhover",MSPointerLeave:"pointerleave",MSPointerMove:"pointermove",MSPointerOut:"pointerout",MSPointerOver:"pointerover",MSPointerUp:"pointerup"};for(let e=0;e<n.length;e++){const t=n[e],r=i+(t+c),s=i+(t+a);o[t]={},o[t][c]=r,o[t][a]=s}for(let e=0;e<d.length;e++){const t=d[e],o=r[t]={};for(let e=0;e<n.length;e++){const r=n[e];o[r]=t+".addEventListener:"+r}}const v=[];for(let t=0;t<p.length;t++){const n=e[p[t]];v.push(n&&n.prototype)}return t.patchEventTarget(e,t,v,{vh:function(e,t,n,r){if(!m&&f)if(g)try{const o=t.toString();if(o===b||o==y)return e.apply(n,r),!1}catch(t){return e.apply(n,r),!1}else{const o=t.toString();if(o===b||o==y)return e.apply(n,r),!1}else if(g)try{t.toString()}catch(t){return e.apply(n,r),!1}return!0},transferEventName:e=>h[e]||e}),Zone[t.symbol("patchEventTarget")]=!!e[l],!0}function apply(e,t){const{ADD_EVENT_LISTENER_STR:n,REMOVE_EVENT_LISTENER_STR:r}=e.getGlobalObjects(),o=t.WebSocket;t.EventTarget||e.patchEventTarget(t,e,[o.prototype]),t.WebSocket=function(t,a){const c=arguments.length>1?new o(t,a):new o(t);let i,s;const l=e.ObjectGetOwnPropertyDescriptor(c,"onmessage");return l&&!1===l.configurable?(i=e.ObjectCreate(c),s=c,[n,r,"send","close"].forEach((function(t){i[t]=function(){const o=e.ArraySlice.call(arguments);if(t===n||t===r){const e=o.length>0?o[0]:void 0;if(e){const t=Zone.__symbol__("ON_PROPERTY"+e);c[t]=i[t]}}return c[t].apply(c,o)}}))):i=c,e.patchOnProperties(i,["close","error","message","open"],s),i};const a=t.WebSocket;for(const e in o)a[e]=o[e]}function propertyDescriptorLegacyPatch(e,t){const{isNode:n,isMix:r}=e.getGlobalObjects();if((!n||r)&&!canPatchViaPropertyDescriptor(e,t)){const n="undefined"!=typeof WebSocket;patchViaCapturingAllTheEvents(e),e.patchClass("XMLHttpRequest"),n&&apply(e,t),Zone[e.symbol("patchEvents")]=!0}}function canPatchViaPropertyDescriptor(e,t){const{isBrowser:n,isMix:r}=e.getGlobalObjects();if((n||r)&&!e.ObjectGetOwnPropertyDescriptor(HTMLElement.prototype,"onclick")&&"undefined"!=typeof Element){const t=e.ObjectGetOwnPropertyDescriptor(Element.prototype,"onclick");if(t&&!t.configurable)return!1;if(t){e.ObjectDefineProperty(Element.prototype,"onclick",{enumerable:!0,configurable:!0,get:function(){return!0}});const n=!!document.createElement("div").onclick;return e.ObjectDefineProperty(Element.prototype,"onclick",t),n}}const o=t.XMLHttpRequest;if(!o)return!1;const a="onreadystatechange",c=o.prototype,i=e.ObjectGetOwnPropertyDescriptor(c,a);if(i){e.ObjectDefineProperty(c,a,{enumerable:!0,configurable:!0,get:function(){return!0}});const t=!!(new o).onreadystatechange;return e.ObjectDefineProperty(c,a,i||{}),t}{const t=e.symbol("fake");e.ObjectDefineProperty(c,a,{enumerable:!0,configurable:!0,get:function(){return this[t]},set:function(e){this[t]=e}});const n=new o,r=()=>{};n.onreadystatechange=r;const i=n[t]===r;return n.onreadystatechange=null,i}}const globalEventHandlersEventNames=["abort","animationcancel","animationend","animationiteration","auxclick","beforeinput","blur","cancel","canplay","canplaythrough","change","compositionstart","compositionupdate","compositionend","cuechange","click","close","contextmenu","curechange","dblclick","drag","dragend","dragenter","dragexit","dragleave","dragover","drop","durationchange","emptied","ended","error","focus","focusin","focusout","gotpointercapture","input","invalid","keydown","keypress","keyup","load","loadstart","loadeddata","loadedmetadata","lostpointercapture","mousedown","mouseenter","mouseleave","mousemove","mouseout","mouseover","mouseup","mousewheel","orientationchange","pause","play","playing","pointercancel","pointerdown","pointerenter","pointerleave","pointerlockchange","mozpointerlockchange","webkitpointerlockerchange","pointerlockerror","mozpointerlockerror","webkitpointerlockerror","pointermove","pointout","pointerover","pointerup","progress","ratechange","reset","resize","scroll","seeked","seeking","select","selectionchange","selectstart","show","sort","stalled","submit","suspend","timeupdate","volumechange","touchcancel","touchmove","touchstart","touchend","transitioncancel","transitionend","waiting","wheel"],documentEventNames=["afterscriptexecute","beforescriptexecute","DOMContentLoaded","freeze","fullscreenchange","mozfullscreenchange","webkitfullscreenchange","msfullscreenchange","fullscreenerror","mozfullscreenerror","webkitfullscreenerror","msfullscreenerror","readystatechange","visibilitychange","resume"],windowEventNames=["absolutedeviceorientation","afterinput","afterprint","appinstalled","beforeinstallprompt","beforeprint","beforeunload","devicelight","devicemotion","deviceorientation","deviceorientationabsolute","deviceproximity","hashchange","languagechange","message","mozbeforepaint","offline","online","paint","pageshow","pagehide","popstate","rejectionhandled","storage","unhandledrejection","unload","userproximity","vrdisplayconnected","vrdisplaydisconnected","vrdisplaypresentchange"],htmlElementEventNames=["beforecopy","beforecut","beforepaste","copy","cut","paste","dragstart","loadend","animationstart","search","transitionrun","transitionstart","webkitanimationend","webkitanimationiteration","webkitanimationstart","webkittransitionend"],ieElementEventNames=["activate","afterupdate","ariarequest","beforeactivate","beforedeactivate","beforeeditfocus","beforeupdate","cellchange","controlselect","dataavailable","datasetchanged","datasetcomplete","errorupdate","filterchange","layoutcomplete","losecapture","move","moveend","movestart","propertychange","resizeend","resizestart","rowenter","rowexit","rowsdelete","rowsinserted","command","compassneedscalibration","deactivate","help","mscontentzoom","msmanipulationstatechanged","msgesturechange","msgesturedoubletap","msgestureend","msgesturehold","msgesturestart","msgesturetap","msgotpointercapture","msinertiastart","mslostpointercapture","mspointercancel","mspointerdown","mspointerenter","mspointerhover","mspointerleave","mspointermove","mspointerout","mspointerover","mspointerup","pointerout","mssitemodejumplistitemremoved","msthumbnailclick","stop","storagecommit"],webglEventNames=["webglcontextrestored","webglcontextlost","webglcontextcreationerror"],formEventNames=["autocomplete","autocompleteerror"],detailEventNames=["toggle"],eventNames=[...globalEventHandlersEventNames,...webglEventNames,...formEventNames,...detailEventNames,...documentEventNames,...windowEventNames,...htmlElementEventNames,...ieElementEventNames];function patchViaCapturingAllTheEvents(e){const t=e.symbol("unbound");for(let n=0;n<eventNames.length;n++){const r=eventNames[n],o="on"+r;self.addEventListener(r,(function(n){let r,a,c=n.target;for(a=c?c.constructor.name+"."+o:"unknown."+o;c;)c[o]&&!c[o][t]&&(r=e.wrapWithCurrentZone(c[o],a),r[t]=c[o],c[o]=r),c=c.parentElement}),!0)}}function registerElementPatch(e,t){const{isBrowser:n,isMix:r}=t.getGlobalObjects();(n||r)&&"registerElement"in e.document&&t.patchCallbacks(t,document,"Document","registerElement",["createdCallback","attachedCallback","detachedCallback","attributeChangedCallback"])}!function(e){const t=e.__Zone_symbol_prefix||"__zone_symbol__";e[function n(e){return t+e}("legacyPatch")]=function(){const t=e.Zone;t.__load_patch("defineProperty",((e,t,n)=>{n._redefineProperty=_redefineProperty,propertyPatch()})),t.__load_patch("registerElement",((e,t,n)=>{registerElementPatch(e,n)})),t.__load_patch("EventTargetLegacy",((e,t,n)=>{eventTargetLegacyPatch(e,n),propertyDescriptorLegacyPatch(n,e)}))}}("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{});