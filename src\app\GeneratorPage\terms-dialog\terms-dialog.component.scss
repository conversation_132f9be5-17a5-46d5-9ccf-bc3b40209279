::ng-deep .terms-dialog {
  .p-dialog-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px 8px 0 0;
  }
  
  .p-dialog-content {
    padding: 0;
    max-height: 75vh;
    overflow-y: auto;
  }
  
  .p-dialog-footer {
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
  }
}

.terms-container {
  padding: 2rem;
  line-height: 1.6;
  color: #333;
}

.terms-content {
  max-width: 800px;
  margin: 0 auto;
}

.terms-section {
  margin-bottom: 2rem;
  
  h2 {
    color: #667eea;
    font-size: 1.8rem;
    margin-bottom: 1rem;
    text-align: center;
    border-bottom: 2px solid #667eea;
    padding-bottom: 0.5rem;
  }
  
  h3 {
    color: #495057;
    font-size: 1.3rem;
    margin-bottom: 1rem;
    border-right: 4px solid #667eea;
    padding-right: 1rem;
  }
  
  h4 {
    color: #495057;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
  }
  
  p {
    margin-bottom: 1rem;
    text-align: justify;
  }
  
  ul {
    margin: 1rem 0;
    padding-right: 1.5rem;
    
    li {
      margin-bottom: 0.5rem;
    }
  }
}

.privacy-highlight {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
  border: 1px solid #28a745;
  border-radius: 8px;
  padding: 1.5rem;
  margin: 1rem 0;
  
  .pi {
    font-size: 2rem;
    color: #28a745;
    margin-top: 0.2rem;
  }
  
  h4 {
    color: #28a745;
    margin-bottom: 0.5rem;
  }
  
  p {
    margin-bottom: 0.5rem;
  }
}

.restriction-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border: 1px solid #ffc107;
  border-radius: 8px;
  padding: 1.5rem;
  margin: 1rem 0;
  
  .pi {
    font-size: 1.5rem;
    color: #dc3545;
    margin-top: 0.2rem;
  }
  
  h4 {
    color: #dc3545;
    margin-bottom: 0.5rem;
  }
  
  ul {
    margin-top: 0.5rem;
  }
}

.warning-box {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  border: 1px solid #dc3545;
  border-radius: 8px;
  padding: 1.5rem;
  margin: 1rem 0;
  
  .pi {
    font-size: 1.5rem;
    color: #dc3545;
    margin-top: 0.2rem;
  }
  
  h4 {
    color: #dc3545;
    margin-bottom: 0.5rem;
  }
}

.acceptance-notice {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
  border: 1px solid #17a2b8;
  border-radius: 8px;
  padding: 1.5rem;
  margin: 2rem 0;
  
  .pi {
    font-size: 1.5rem;
    color: #17a2b8;
    margin-top: 0.2rem;
  }
  
  p {
    margin: 0;
    font-weight: 500;
  }
}

.contact-email {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  
  &:hover {
    text-decoration: underline;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  padding: 1rem;
}

.text-danger {
  color: #dc3545 !important;
}

// Responsive design
@media (max-width: 768px) {
  ::ng-deep .terms-dialog {
    .p-dialog {
      width: 95vw !important;
      height: 95vh !important;
      margin: 0;
    }
  }
  
  .terms-container {
    padding: 1rem;
  }
  
  .terms-section {
    h2 {
      font-size: 1.5rem;
    }
    
    h3 {
      font-size: 1.2rem;
    }
  }
  
  .privacy-highlight,
  .restriction-item,
  .warning-box,
  .acceptance-notice {
    flex-direction: column;
    text-align: center;
    
    .pi {
      align-self: center;
    }
  }
  
  .dialog-footer {
    flex-direction: column;
    
    .p-button {
      width: 100%;
    }
  }
}
