{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Component } from '@angular/core';\nimport { BaseIcon } from 'primeng/baseicon';\nlet MinusIcon = /*#__PURE__*/(() => {\n  class MinusIcon extends BaseIcon {\n    static ɵfac = /* @__PURE__ */function () {\n      let ɵMinusIcon_BaseFactory;\n      return function MinusIcon_Factory(t) {\n        return (ɵMinusIcon_BaseFactory || (ɵMinusIcon_BaseFactory = i0.ɵɵgetInheritedFactory(MinusIcon)))(t || MinusIcon);\n      };\n    }();\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MinusIcon,\n      selectors: [[\"MinusIcon\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 5,\n      consts: [[\"width\", \"14\", \"height\", \"14\", \"viewBox\", \"0 0 14 14\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"d\", \"M13.2222 7.77778H0.777778C0.571498 7.77778 0.373667 7.69584 0.227806 7.54998C0.0819442 7.40412 0 7.20629 0 7.00001C0 6.79373 0.0819442 6.5959 0.227806 6.45003C0.373667 6.30417 0.571498 6.22223 0.777778 6.22223H13.2222C13.4285 6.22223 13.6263 6.30417 13.7722 6.45003C13.9181 6.5959 14 6.79373 14 7.00001C14 7.20629 13.9181 7.40412 13.7722 7.54998C13.6263 7.69584 13.4285 7.77778 13.2222 7.77778Z\", \"fill\", \"currentColor\"]],\n      template: function MinusIcon_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(0, \"svg\", 0);\n          i0.ɵɵelement(1, \"path\", 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.getClassNames());\n          i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel)(\"aria-hidden\", ctx.ariaHidden)(\"role\", ctx.role);\n        }\n      },\n      dependencies: [CommonModule],\n      encapsulation: 2\n    });\n  }\n  return MinusIcon;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MinusIcon };\n//# sourceMappingURL=primeng-icons-minus.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}