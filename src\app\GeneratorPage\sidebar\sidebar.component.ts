import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.scss']
})
export class SidebarComponent {
  @Input() visible: boolean = false;
  @Output() visibleChange = new EventEmitter<boolean>();
  @Output() menuItemClick = new EventEmitter<string>();

  menuItems = [
    {
      id: 'video-settings',
      label: 'إعدادات الفيديو',
      icon: 'pi pi-cog',
      command: () => this.onMenuClick('video-settings')
    },
    {
      id: 'privacy-policy',
      label: 'سياسة الخصوصية',
      icon: 'pi pi-lock',
      command: () => this.onMenuClick('privacy-policy')
    },
    {
      id: 'support',
      label: 'مساعد ودعم',
      icon: 'pi pi-comments',
      command: () => this.onMenuClick('support')
    },
    {
      id: 'terms',
      label: 'شروط الاستخدام',
      icon: 'pi pi-file',
      command: () => this.onMenuClick('terms')
    }
  ];

  onMenuClick(itemId: string): void {
    this.menuItemClick.emit(itemId);
    this.visible = false;
    this.visibleChange.emit(false);
  }

  onHide(): void {
    this.visible = false;
    this.visibleChange.emit(false);
  }
}
