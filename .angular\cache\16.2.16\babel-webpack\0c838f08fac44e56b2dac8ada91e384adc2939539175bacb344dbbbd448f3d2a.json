{"ast": null, "code": "import * as i4 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i2 from '@angular/common/http';\nimport { HttpEventType, HttpClientModule } from '@angular/common/http';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, PLATFORM_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ContentChildren, ViewChild, NgModule } from '@angular/core';\nimport * as i3 from 'primeng/api';\nimport { TranslationKeys, PrimeTemplate, SharedModule } from 'primeng/api';\nimport * as i5 from 'primeng/button';\nimport { ButtonModule } from 'primeng/button';\nimport { DomHandler } from 'primeng/dom';\nimport { PlusIcon } from 'primeng/icons/plus';\nimport { TimesIcon } from 'primeng/icons/times';\nimport { UploadIcon } from 'primeng/icons/upload';\nimport * as i7 from 'primeng/messages';\nimport { MessagesModule } from 'primeng/messages';\nimport * as i6 from 'primeng/progressbar';\nimport { ProgressBarModule } from 'primeng/progressbar';\nimport * as i8 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i1 from '@angular/platform-browser';\n\n/**\n * FileUpload is an advanced uploader with dragdrop support, multi file uploads, auto uploading, progress tracking and validations.\n * @group Components\n */\nconst _c0 = [\"advancedfileinput\"];\nconst _c1 = [\"basicfileinput\"];\nconst _c2 = [\"content\"];\nfunction FileUpload_div_0_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 18);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r3.chooseIcon);\n    i0.ɵɵproperty(\"ngClass\", \"p-button-icon p-button-icon-left\");\n    i0.ɵɵattribute(\"aria-label\", true)(\"data-pc-section\", \"chooseicon\");\n  }\n}\nfunction FileUpload_div_0_ng_container_6_PlusIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"PlusIcon\", 21);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-button-icon p-button-icon-left\");\n    i0.ɵɵattribute(\"aria-label\", true)(\"data-pc-section\", \"chooseicon\");\n  }\n}\nfunction FileUpload_div_0_ng_container_6_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction FileUpload_div_0_ng_container_6_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, FileUpload_div_0_ng_container_6_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction FileUpload_div_0_ng_container_6_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵtemplate(1, FileUpload_div_0_ng_container_6_span_2_1_Template, 1, 0, null, 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(3);\n    i0.ɵɵattribute(\"aria-label\", true)(\"data-pc-section\", \"chooseicon\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r13.chooseIconTemplate);\n  }\n}\nfunction FileUpload_div_0_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, FileUpload_div_0_ng_container_6_PlusIcon_1_Template, 1, 3, \"PlusIcon\", 19);\n    i0.ɵɵtemplate(2, FileUpload_div_0_ng_container_6_span_2_Template, 2, 3, \"span\", 20);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.chooseIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.chooseIconTemplate);\n  }\n}\nfunction FileUpload_div_0_p_button_9_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 25);\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r16.uploadIcon);\n    i0.ɵɵattribute(\"aria-hidden\", true);\n  }\n}\nfunction FileUpload_div_0_p_button_9_ng_container_2_UploadIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"UploadIcon\", 21);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-button-icon p-button-icon-left\");\n  }\n}\nfunction FileUpload_div_0_p_button_9_ng_container_2_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction FileUpload_div_0_p_button_9_ng_container_2_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, FileUpload_div_0_p_button_9_ng_container_2_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction FileUpload_div_0_p_button_9_ng_container_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵtemplate(1, FileUpload_div_0_p_button_9_ng_container_2_span_2_1_Template, 1, 0, null, 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(4);\n    i0.ɵɵattribute(\"aria-hidden\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r19.uploadIconTemplate);\n  }\n}\nfunction FileUpload_div_0_p_button_9_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, FileUpload_div_0_p_button_9_ng_container_2_UploadIcon_1_Template, 1, 1, \"UploadIcon\", 19);\n    i0.ɵɵtemplate(2, FileUpload_div_0_p_button_9_ng_container_2_span_2_Template, 2, 2, \"span\", 20);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r17.uploadIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r17.uploadIconTemplate);\n  }\n}\nfunction FileUpload_div_0_p_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 23);\n    i0.ɵɵlistener(\"onClick\", function FileUpload_div_0_p_button_9_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r22.upload());\n    });\n    i0.ɵɵtemplate(1, FileUpload_div_0_p_button_9_span_1_Template, 1, 2, \"span\", 24);\n    i0.ɵɵtemplate(2, FileUpload_div_0_p_button_9_ng_container_2_Template, 3, 2, \"ng-container\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"label\", ctx_r5.uploadButtonLabel)(\"disabled\", !ctx_r5.hasFiles() || ctx_r5.isFileLimitExceeded())(\"styleClass\", ctx_r5.uploadStyleClass);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.uploadIcon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.uploadIcon);\n  }\n}\nfunction FileUpload_div_0_p_button_10_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 25);\n  }\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r24.cancelIcon);\n  }\n}\nfunction FileUpload_div_0_p_button_10_ng_container_2_TimesIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"TimesIcon\", 21);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-button-icon p-button-icon-left\");\n    i0.ɵɵattribute(\"aria-hidden\", true);\n  }\n}\nfunction FileUpload_div_0_p_button_10_ng_container_2_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction FileUpload_div_0_p_button_10_ng_container_2_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, FileUpload_div_0_p_button_10_ng_container_2_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction FileUpload_div_0_p_button_10_ng_container_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵtemplate(1, FileUpload_div_0_p_button_10_ng_container_2_span_2_1_Template, 1, 0, null, 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r27 = i0.ɵɵnextContext(4);\n    i0.ɵɵattribute(\"aria-hidden\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r27.cancelIconTemplate);\n  }\n}\nfunction FileUpload_div_0_p_button_10_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, FileUpload_div_0_p_button_10_ng_container_2_TimesIcon_1_Template, 1, 2, \"TimesIcon\", 19);\n    i0.ɵɵtemplate(2, FileUpload_div_0_p_button_10_ng_container_2_span_2_Template, 2, 2, \"span\", 20);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r25 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r25.cancelIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.cancelIconTemplate);\n  }\n}\nfunction FileUpload_div_0_p_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 23);\n    i0.ɵɵlistener(\"onClick\", function FileUpload_div_0_p_button_10_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r30 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r30.clear());\n    });\n    i0.ɵɵtemplate(1, FileUpload_div_0_p_button_10_span_1_Template, 1, 1, \"span\", 24);\n    i0.ɵɵtemplate(2, FileUpload_div_0_p_button_10_ng_container_2_Template, 3, 2, \"ng-container\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"label\", ctx_r6.cancelButtonLabel)(\"disabled\", !ctx_r6.hasFiles() || ctx_r6.uploading)(\"styleClass\", ctx_r6.cancelStyleClass);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.cancelIcon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.cancelIcon);\n  }\n}\nfunction FileUpload_div_0_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction FileUpload_div_0_p_progressBar_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-progressBar\", 26);\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", ctx_r9.progress)(\"showValue\", false);\n  }\n}\nfunction FileUpload_div_0_div_16_div_1_div_1_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r41 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 33);\n    i0.ɵɵlistener(\"error\", function FileUpload_div_0_div_16_div_1_div_1_img_2_Template_img_error_0_listener($event) {\n      i0.ɵɵrestoreView(_r41);\n      const ctx_r40 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r40.imageError($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r35 = i0.ɵɵnextContext().$implicit;\n    const ctx_r37 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"src\", file_r35.objectURL, i0.ɵɵsanitizeUrl)(\"width\", ctx_r37.previewWidth);\n  }\n}\nfunction FileUpload_div_0_div_16_div_1_div_1_TimesIcon_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"TimesIcon\");\n  }\n}\nfunction FileUpload_div_0_div_16_div_1_div_1_10_ng_template_0_Template(rf, ctx) {}\nfunction FileUpload_div_0_div_16_div_1_div_1_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, FileUpload_div_0_div_16_div_1_div_1_10_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction FileUpload_div_0_div_16_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r45 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"div\");\n    i0.ɵɵtemplate(2, FileUpload_div_0_div_16_div_1_div_1_img_2_Template, 1, 2, \"img\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 31);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\")(8, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function FileUpload_div_0_div_16_div_1_div_1_Template_button_click_8_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r45);\n      const i_r36 = restoredCtx.index;\n      const ctx_r44 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r44.remove($event, i_r36));\n    });\n    i0.ɵɵtemplate(9, FileUpload_div_0_div_16_div_1_div_1_TimesIcon_9_Template, 1, 0, \"TimesIcon\", 8);\n    i0.ɵɵtemplate(10, FileUpload_div_0_div_16_div_1_div_1_10_Template, 1, 0, null, 11);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const file_r35 = ctx.$implicit;\n    const ctx_r34 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r34.isImage(file_r35));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(file_r35.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r34.formatSize(file_r35.size));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r34.removeStyleClass);\n    i0.ɵɵproperty(\"disabled\", ctx_r34.uploading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r34.cancelIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r34.cancelIconTemplate);\n  }\n}\nfunction FileUpload_div_0_div_16_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, FileUpload_div_0_div_16_div_1_div_1_Template, 11, 8, \"div\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r32 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r32.files);\n  }\n}\nfunction FileUpload_div_0_div_16_div_2_ng_template_1_Template(rf, ctx) {}\nfunction FileUpload_div_0_div_16_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, FileUpload_div_0_div_16_div_2_ng_template_1_Template, 0, 0, \"ng-template\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r33 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r33.files)(\"ngForTemplate\", ctx_r33.fileTemplate);\n  }\n}\nfunction FileUpload_div_0_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtemplate(1, FileUpload_div_0_div_16_div_1_Template, 2, 1, \"div\", 8);\n    i0.ɵɵtemplate(2, FileUpload_div_0_div_16_div_2_Template, 2, 2, \"div\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.fileTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.fileTemplate);\n  }\n}\nfunction FileUpload_div_0_ng_container_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c3 = function (a0, a1) {\n  return {\n    \"p-focus\": a0,\n    \"p-disabled\": a1\n  };\n};\nconst _c4 = function (a0) {\n  return {\n    $implicit: a0\n  };\n};\nfunction FileUpload_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r48 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"span\", 4);\n    i0.ɵɵlistener(\"focus\", function FileUpload_div_0_Template_span_focus_2_listener() {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r47 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r47.onFocus());\n    })(\"blur\", function FileUpload_div_0_Template_span_blur_2_listener() {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r49 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r49.onBlur());\n    })(\"click\", function FileUpload_div_0_Template_span_click_2_listener() {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r50 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r50.choose());\n    })(\"keydown.enter\", function FileUpload_div_0_Template_span_keydown_enter_2_listener() {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r51 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r51.choose());\n    });\n    i0.ɵɵelementStart(3, \"input\", 5, 6);\n    i0.ɵɵlistener(\"change\", function FileUpload_div_0_Template_input_change_3_listener($event) {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r52 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r52.onFileSelect($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, FileUpload_div_0_span_5_Template, 1, 5, \"span\", 7);\n    i0.ɵɵtemplate(6, FileUpload_div_0_ng_container_6_Template, 3, 2, \"ng-container\", 8);\n    i0.ɵɵelementStart(7, \"span\", 9);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(9, FileUpload_div_0_p_button_9_Template, 3, 5, \"p-button\", 10);\n    i0.ɵɵtemplate(10, FileUpload_div_0_p_button_10_Template, 3, 5, \"p-button\", 10);\n    i0.ɵɵtemplate(11, FileUpload_div_0_ng_container_11_Template, 1, 0, \"ng-container\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 12, 13);\n    i0.ɵɵlistener(\"dragenter\", function FileUpload_div_0_Template_div_dragenter_12_listener($event) {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r53 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r53.onDragEnter($event));\n    })(\"dragleave\", function FileUpload_div_0_Template_div_dragleave_12_listener($event) {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r54 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r54.onDragLeave($event));\n    })(\"drop\", function FileUpload_div_0_Template_div_drop_12_listener($event) {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r55 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r55.onDrop($event));\n    });\n    i0.ɵɵtemplate(14, FileUpload_div_0_p_progressBar_14_Template, 1, 2, \"p-progressBar\", 14);\n    i0.ɵɵelement(15, \"p-messages\", 15);\n    i0.ɵɵtemplate(16, FileUpload_div_0_div_16_Template, 3, 2, \"div\", 16);\n    i0.ɵɵtemplate(17, FileUpload_div_0_ng_container_17_Template, 1, 0, \"ng-container\", 17);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r0.styleClass);\n    i0.ɵɵproperty(\"ngClass\", \"p-fileupload p-fileupload-advanced p-component\")(\"ngStyle\", ctx_r0.style);\n    i0.ɵɵattribute(\"data-pc-name\", \"fileupload\")(\"data-pc-section\", \"root\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵattribute(\"data-pc-section\", \"buttonbar\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r0.chooseStyleClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(30, _c3, ctx_r0.focus, ctx_r0.disabled || ctx_r0.isChooseDisabled()));\n    i0.ɵɵattribute(\"data-pc-section\", \"choosebutton\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"multiple\", ctx_r0.multiple)(\"accept\", ctx_r0.accept)(\"disabled\", ctx_r0.disabled || ctx_r0.isChooseDisabled());\n    i0.ɵɵattribute(\"title\", \"\")(\"data-pc-section\", \"input\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.chooseIcon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.chooseIcon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵattribute(\"data-pc-section\", \"choosebuttonlabel\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r0.chooseButtonLabel);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.auto && ctx_r0.showUploadButton);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.auto && ctx_r0.showCancelButton);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.toolbarTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵattribute(\"data-pc-section\", \"content\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.hasFiles());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", ctx_r0.msgs)(\"enableService\", false);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.hasFiles());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.contentTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(33, _c4, ctx_r0.files));\n  }\n}\nfunction FileUpload_div_1_ng_container_3_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 25);\n  }\n  if (rf & 2) {\n    const ctx_r61 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r61.uploadIcon);\n  }\n}\nfunction FileUpload_div_1_ng_container_3_ng_container_2_UploadIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"UploadIcon\", 21);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-button-icon p-button-icon-left\");\n  }\n}\nfunction FileUpload_div_1_ng_container_3_ng_container_2_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction FileUpload_div_1_ng_container_3_ng_container_2_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, FileUpload_div_1_ng_container_3_ng_container_2_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction FileUpload_div_1_ng_container_3_ng_container_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵtemplate(1, FileUpload_div_1_ng_container_3_ng_container_2_span_2_1_Template, 1, 0, null, 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r64 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r64.uploadIconTemplate);\n  }\n}\nfunction FileUpload_div_1_ng_container_3_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, FileUpload_div_1_ng_container_3_ng_container_2_UploadIcon_1_Template, 1, 1, \"UploadIcon\", 19);\n    i0.ɵɵtemplate(2, FileUpload_div_1_ng_container_3_ng_container_2_span_2_Template, 2, 1, \"span\", 20);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r62 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r62.uploadIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r62.uploadIconTemplate);\n  }\n}\nfunction FileUpload_div_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, FileUpload_div_1_ng_container_3_span_1_Template, 1, 1, \"span\", 24);\n    i0.ɵɵtemplate(2, FileUpload_div_1_ng_container_3_ng_container_2_Template, 3, 2, \"ng-container\", 8);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r56 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r56.uploadIcon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r56.uploadIcon);\n  }\n}\nfunction FileUpload_div_1_ng_template_4_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 42);\n  }\n  if (rf & 2) {\n    const ctx_r67 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r67.chooseIcon);\n  }\n}\nfunction FileUpload_div_1_ng_template_4_ng_container_1_PlusIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"PlusIcon\", 21);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-button-icon p-button-icon-left pi\");\n    i0.ɵɵattribute(\"aria-hidden\", true)(\"data-pc-section\", \"uploadicon\");\n  }\n}\nfunction FileUpload_div_1_ng_template_4_ng_container_1_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction FileUpload_div_1_ng_template_4_ng_container_1_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, FileUpload_div_1_ng_template_4_ng_container_1_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction FileUpload_div_1_ng_template_4_ng_container_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 44);\n    i0.ɵɵtemplate(1, FileUpload_div_1_ng_template_4_ng_container_1_span_2_1_Template, 1, 0, null, 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r70 = i0.ɵɵnextContext(4);\n    i0.ɵɵattribute(\"aria-hidden\", true)(\"data-pc-section\", \"uploadicon\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r70.chooseIconTemplate);\n  }\n}\nfunction FileUpload_div_1_ng_template_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, FileUpload_div_1_ng_template_4_ng_container_1_PlusIcon_1_Template, 1, 3, \"PlusIcon\", 19);\n    i0.ɵɵtemplate(2, FileUpload_div_1_ng_template_4_ng_container_1_span_2_Template, 2, 3, \"span\", 43);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r68 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r68.chooseIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r68.chooseIconTemplate);\n  }\n}\nfunction FileUpload_div_1_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, FileUpload_div_1_ng_template_4_span_0_Template, 1, 1, \"span\", 41);\n    i0.ɵɵtemplate(1, FileUpload_div_1_ng_template_4_ng_container_1_Template, 3, 2, \"ng-container\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r58 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r58.chooseIcon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r58.chooseIcon);\n  }\n}\nfunction FileUpload_div_1_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r59 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"data-pc-section\", \"label\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r59.basicButtonLabel);\n  }\n}\nfunction FileUpload_div_1_input_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r75 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 45, 46);\n    i0.ɵɵlistener(\"change\", function FileUpload_div_1_input_7_Template_input_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r75);\n      const ctx_r74 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r74.onFileSelect($event));\n    })(\"focus\", function FileUpload_div_1_input_7_Template_input_focus_0_listener() {\n      i0.ɵɵrestoreView(_r75);\n      const ctx_r76 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r76.onFocus());\n    })(\"blur\", function FileUpload_div_1_input_7_Template_input_blur_0_listener() {\n      i0.ɵɵrestoreView(_r75);\n      const ctx_r77 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r77.onBlur());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r60 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"accept\", ctx_r60.accept)(\"multiple\", ctx_r60.multiple)(\"disabled\", ctx_r60.disabled);\n    i0.ɵɵattribute(\"data-pc-section\", \"input\");\n  }\n}\nconst _c5 = function (a1, a2, a3, a4) {\n  return {\n    \"p-button p-component p-fileupload-choose\": true,\n    \"p-button-icon-only\": a1,\n    \"p-fileupload-choose-selected\": a2,\n    \"p-focus\": a3,\n    \"p-disabled\": a4\n  };\n};\nfunction FileUpload_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r79 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"p-messages\", 15);\n    i0.ɵɵelementStart(2, \"span\", 36);\n    i0.ɵɵlistener(\"click\", function FileUpload_div_1_Template_span_click_2_listener() {\n      i0.ɵɵrestoreView(_r79);\n      const ctx_r78 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r78.onBasicUploaderClick());\n    })(\"keydown\", function FileUpload_div_1_Template_span_keydown_2_listener($event) {\n      i0.ɵɵrestoreView(_r79);\n      const ctx_r80 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r80.onBasicKeydown($event));\n    });\n    i0.ɵɵtemplate(3, FileUpload_div_1_ng_container_3_Template, 3, 2, \"ng-container\", 37);\n    i0.ɵɵtemplate(4, FileUpload_div_1_ng_template_4_Template, 2, 2, \"ng-template\", null, 38, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(6, FileUpload_div_1_span_6_Template, 2, 2, \"span\", 39);\n    i0.ɵɵtemplate(7, FileUpload_div_1_input_7_Template, 2, 4, \"input\", 40);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const _r57 = i0.ɵɵreference(5);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"data-pc-name\", \"fileupload\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", ctx_r1.msgs)(\"enableService\", false);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r1.styleClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(12, _c5, !ctx_r1.basicButtonLabel, ctx_r1.hasFiles(), ctx_r1.focus, ctx_r1.disabled))(\"ngStyle\", ctx_r1.style);\n    i0.ɵɵattribute(\"data-pc-section\", \"choosebutton\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasFiles() && !ctx_r1.auto)(\"ngIfElse\", _r57);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.basicButtonLabel);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.hasFiles());\n  }\n}\nlet FileUpload = /*#__PURE__*/(() => {\n  class FileUpload {\n    document;\n    platformId;\n    renderer;\n    el;\n    sanitizer;\n    zone;\n    http;\n    cd;\n    config;\n    /**\n     * Name of the request parameter to identify the files at backend.\n     * @group Props\n     */\n    name;\n    /**\n     * Remote url to upload the files.\n     * @group Props\n     */\n    url;\n    /**\n     * HTTP method to send the files to the url such as \"post\" and \"put\".\n     * @group Props\n     */\n    method = 'post';\n    /**\n     * Used to select multiple files at once from file dialog.\n     * @group Props\n     */\n    multiple;\n    /**\n     * Comma-separated list of pattern to restrict the allowed file types. Can be any combination of either the MIME types (such as \"image/*\") or the file extensions (such as \".jpg\").\n     * @group Props\n     */\n    accept;\n    /**\n     * Disables the upload functionality.\n     * @group Props\n     */\n    disabled;\n    /**\n     * When enabled, upload begins automatically after selection is completed.\n     * @group Props\n     */\n    auto;\n    /**\n     * Cross-site Access-Control requests should be made using credentials such as cookies, authorization headers or TLS client certificates.\n     * @group Props\n     */\n    withCredentials;\n    /**\n     * Maximum file size allowed in bytes.\n     * @group Props\n     */\n    maxFileSize;\n    /**\n     * Summary message of the invalid file size.\n     * @group Props\n     */\n    invalidFileSizeMessageSummary = '{0}: Invalid file size, ';\n    /**\n     * Detail message of the invalid file size.\n     * @group Props\n     */\n    invalidFileSizeMessageDetail = 'maximum upload size is {0}.';\n    /**\n     * Summary message of the invalid file type.\n     * @group Props\n     */\n    invalidFileTypeMessageSummary = '{0}: Invalid file type, ';\n    /**\n     * Detail message of the invalid file type.\n     * @group Props\n     */\n    invalidFileTypeMessageDetail = 'allowed file types: {0}.';\n    /**\n     * Detail message of the invalid file type.\n     * @group Props\n     */\n    invalidFileLimitMessageDetail = 'limit is {0} at most.';\n    /**\n     * Summary message of the invalid file type.\n     * @group Props\n     */\n    invalidFileLimitMessageSummary = 'Maximum number of files exceeded, ';\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    style;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Width of the image thumbnail in pixels.\n     * @group Props\n     */\n    previewWidth = 50;\n    /**\n     * Label of the choose button. Defaults to PrimeVue Locale configuration.\n     * @group Props\n     */\n    chooseLabel;\n    /**\n     * Label of the upload button. Defaults to PrimeVue Locale configuration.\n     * @group Props\n     */\n    uploadLabel;\n    /**\n     * Label of the cancel button. Defaults to PrimeVue Locale configuration.\n     * @group Props\n     */\n    cancelLabel;\n    /**\n     * Icon of the choose button.\n     * @group Props\n     */\n    chooseIcon;\n    /**\n     * Icon of the upload button.\n     * @group Props\n     */\n    uploadIcon;\n    /**\n     * Icon of the cancel button.\n     * @group Props\n     */\n    cancelIcon;\n    /**\n     * Whether to show the upload button.\n     * @group Props\n     */\n    showUploadButton = true;\n    /**\n     * Whether to show the cancel button.\n     * @group Props\n     */\n    showCancelButton = true;\n    /**\n     * Defines the UI of the component.\n     * @group Props\n     */\n    mode = 'advanced';\n    /**\n     * HttpHeaders class represents the header configuration options for an HTTP request.\n     * @group Props\n     */\n    headers;\n    /**\n     * Whether to use the default upload or a manual implementation defined in uploadHandler callback. Defaults to PrimeNG Locale configuration.\n     * @group Props\n     */\n    customUpload;\n    /**\n     * Maximum number of files that can be uploaded.\n     * @group Props\n     */\n    fileLimit;\n    /**\n     * Style class of the upload button.\n     * @group Props\n     */\n    uploadStyleClass;\n    /**\n     * Style class of the cancel button.\n     * @group Props\n     */\n    cancelStyleClass;\n    /**\n     * Style class of the remove button.\n     * @group Props\n     */\n    removeStyleClass;\n    /**\n     * Style class of the choose button.\n     * @group Props\n     */\n    chooseStyleClass;\n    /**\n     * Callback to invoke before file upload is initialized.\n     * @param {FileBeforeUploadEvent} event - Custom upload event.\n     * @group Emits\n     */\n    onBeforeUpload = new EventEmitter();\n    /**\n     * An event indicating that the request was sent to the server. Useful when a request may be retried multiple times, to distinguish between retries on the final event stream.\n     * @param {FileSendEvent} event - Custom send event.\n     * @group Emits\n     */\n    onSend = new EventEmitter();\n    /**\n     * Callback to invoke when file upload is complete.\n     * @param {FileUploadEvent} event - Custom upload event.\n     * @group Emits\n     */\n    onUpload = new EventEmitter();\n    /**\n     * Callback to invoke if file upload fails.\n     * @param {FileUploadErrorEvent} event - Custom error event.\n     * @group Emits\n     */\n    onError = new EventEmitter();\n    /**\n     * Callback to invoke when files in queue are removed without uploading using clear all button.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    onClear = new EventEmitter();\n    /**\n     * Callback to invoke when a file is removed without uploading using clear button of a file.\n     * @param {FileRemoveEvent} event - Remove event.\n     * @group Emits\n     */\n    onRemove = new EventEmitter();\n    /**\n     * Callback to invoke when files are selected.\n     * @param {FileSelectEvent} event - Select event.\n     * @group Emits\n     */\n    onSelect = new EventEmitter();\n    /**\n     * Callback to invoke when files are being uploaded.\n     * @param {FileProgressEvent} event - Progress event.\n     * @group Emits\n     */\n    onProgress = new EventEmitter();\n    /**\n     * Callback to invoke in custom upload mode to upload the files manually.\n     * @param {FileUploadHandlerEvent} event - Upload handler event.\n     * @group Emits\n     */\n    uploadHandler = new EventEmitter();\n    /**\n     * This event is triggered if an error occurs while loading an image file.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    onImageError = new EventEmitter();\n    templates;\n    advancedFileInput;\n    basicFileInput;\n    content;\n    set files(files) {\n      this._files = [];\n      for (let i = 0; i < files.length; i++) {\n        let file = files[i];\n        if (this.validate(file)) {\n          if (this.isImage(file)) {\n            file.objectURL = this.sanitizer.bypassSecurityTrustUrl(window.URL.createObjectURL(files[i]));\n          }\n          this._files.push(files[i]);\n        }\n      }\n    }\n    get files() {\n      return this._files;\n    }\n    get basicButtonLabel() {\n      if (this.auto || !this.hasFiles()) {\n        return this.chooseLabel;\n      }\n      return this.uploadLabel ?? this.files[0].name;\n    }\n    _files = [];\n    progress = 0;\n    dragHighlight;\n    msgs;\n    fileTemplate;\n    contentTemplate;\n    toolbarTemplate;\n    chooseIconTemplate;\n    uploadIconTemplate;\n    cancelIconTemplate;\n    uploadedFileCount = 0;\n    focus;\n    uploading;\n    duplicateIEEvent; // flag to recognize duplicate onchange event for file input\n    translationSubscription;\n    dragOverListener;\n    constructor(document, platformId, renderer, el, sanitizer, zone, http, cd, config) {\n      this.document = document;\n      this.platformId = platformId;\n      this.renderer = renderer;\n      this.el = el;\n      this.sanitizer = sanitizer;\n      this.zone = zone;\n      this.http = http;\n      this.cd = cd;\n      this.config = config;\n    }\n    ngAfterContentInit() {\n      this.templates?.forEach(item => {\n        switch (item.getType()) {\n          case 'file':\n            this.fileTemplate = item.template;\n            break;\n          case 'content':\n            this.contentTemplate = item.template;\n            break;\n          case 'toolbar':\n            this.toolbarTemplate = item.template;\n            break;\n          case 'chooseicon':\n            this.chooseIconTemplate = item.template;\n            break;\n          case 'uploadicon':\n            this.uploadIconTemplate = item.template;\n            break;\n          case 'cancelicon':\n            this.cancelIconTemplate = item.template;\n            break;\n          default:\n            this.fileTemplate = item.template;\n            break;\n        }\n      });\n    }\n    ngOnInit() {\n      this.translationSubscription = this.config.translationObserver.subscribe(() => {\n        this.cd.markForCheck();\n      });\n    }\n    ngAfterViewInit() {\n      if (isPlatformBrowser(this.platformId)) {\n        if (this.mode === 'advanced') {\n          this.zone.runOutsideAngular(() => {\n            if (this.content) {\n              this.dragOverListener = this.renderer.listen(this.content.nativeElement, 'dragover', this.onDragOver.bind(this));\n            }\n          });\n        }\n      }\n    }\n    getTranslation(option) {\n      return this.config.getTranslation(option);\n    }\n    choose() {\n      this.advancedFileInput?.nativeElement.click();\n    }\n    onFileSelect(event) {\n      if (event.type !== 'drop' && this.isIE11() && this.duplicateIEEvent) {\n        this.duplicateIEEvent = false;\n        return;\n      }\n      this.msgs = [];\n      if (!this.multiple) {\n        this.files = [];\n      }\n      let files = event.dataTransfer ? event.dataTransfer.files : event.target.files;\n      for (let i = 0; i < files.length; i++) {\n        let file = files[i];\n        if (!this.isFileSelected(file)) {\n          if (this.validate(file)) {\n            if (this.isImage(file)) {\n              file.objectURL = this.sanitizer.bypassSecurityTrustUrl(window.URL.createObjectURL(files[i]));\n            }\n            this.files.push(files[i]);\n          }\n        }\n      }\n      this.onSelect.emit({\n        originalEvent: event,\n        files: files,\n        currentFiles: this.files\n      });\n      if (this.fileLimit) {\n        this.checkFileLimit();\n      }\n      if (this.hasFiles() && this.auto && (!(this.mode === 'advanced') || !this.isFileLimitExceeded())) {\n        this.upload();\n      }\n      if (event.type !== 'drop' && this.isIE11()) {\n        this.clearIEInput();\n      } else {\n        this.clearInputElement();\n      }\n    }\n    isFileSelected(file) {\n      for (let sFile of this.files) {\n        if (sFile.name + sFile.type + sFile.size === file.name + file.type + file.size) {\n          return true;\n        }\n      }\n      return false;\n    }\n    isIE11() {\n      if (isPlatformBrowser(this.platformId)) {\n        return !!this.document.defaultView['MSInputMethodContext'] && !!this.document['documentMode'];\n      }\n    }\n    validate(file) {\n      this.msgs = this.msgs || [];\n      if (this.accept && !this.isFileTypeValid(file)) {\n        this.msgs.push({\n          severity: 'error',\n          summary: this.invalidFileTypeMessageSummary.replace('{0}', file.name),\n          detail: this.invalidFileTypeMessageDetail.replace('{0}', this.accept)\n        });\n        return false;\n      }\n      if (this.maxFileSize && file.size > this.maxFileSize) {\n        this.msgs.push({\n          severity: 'error',\n          summary: this.invalidFileSizeMessageSummary.replace('{0}', file.name),\n          detail: this.invalidFileSizeMessageDetail.replace('{0}', this.formatSize(this.maxFileSize))\n        });\n        return false;\n      }\n      return true;\n    }\n    isFileTypeValid(file) {\n      let acceptableTypes = this.accept?.split(',').map(type => type.trim());\n      for (let type of acceptableTypes) {\n        let acceptable = this.isWildcard(type) ? this.getTypeClass(file.type) === this.getTypeClass(type) : file.type == type || this.getFileExtension(file).toLowerCase() === type.toLowerCase();\n        if (acceptable) {\n          return true;\n        }\n      }\n      return false;\n    }\n    getTypeClass(fileType) {\n      return fileType.substring(0, fileType.indexOf('/'));\n    }\n    isWildcard(fileType) {\n      return fileType.indexOf('*') !== -1;\n    }\n    getFileExtension(file) {\n      return '.' + file.name.split('.').pop();\n    }\n    isImage(file) {\n      return /^image\\//.test(file.type);\n    }\n    onImageLoad(img) {\n      window.URL.revokeObjectURL(img.src);\n    }\n    /**\n     * Uploads the selected files.\n     * @group Method\n     */\n    upload() {\n      if (this.customUpload) {\n        if (this.fileLimit) {\n          this.uploadedFileCount += this.files.length;\n        }\n        this.uploadHandler.emit({\n          files: this.files\n        });\n        this.cd.markForCheck();\n      } else {\n        this.uploading = true;\n        this.msgs = [];\n        let formData = new FormData();\n        this.onBeforeUpload.emit({\n          formData: formData\n        });\n        for (let i = 0; i < this.files.length; i++) {\n          formData.append(this.name, this.files[i], this.files[i].name);\n        }\n        this.http.request(this.method, this.url, {\n          body: formData,\n          headers: this.headers,\n          reportProgress: true,\n          observe: 'events',\n          withCredentials: this.withCredentials\n        }).subscribe(event => {\n          switch (event.type) {\n            case HttpEventType.Sent:\n              this.onSend.emit({\n                originalEvent: event,\n                formData: formData\n              });\n              break;\n            case HttpEventType.Response:\n              this.uploading = false;\n              this.progress = 0;\n              if (event['status'] >= 200 && event['status'] < 300) {\n                if (this.fileLimit) {\n                  this.uploadedFileCount += this.files.length;\n                }\n                this.onUpload.emit({\n                  originalEvent: event,\n                  files: this.files\n                });\n              } else {\n                this.onError.emit({\n                  files: this.files\n                });\n              }\n              this.clear();\n              break;\n            case HttpEventType.UploadProgress:\n              {\n                if (event['loaded']) {\n                  this.progress = Math.round(event['loaded'] * 100 / event['total']);\n                }\n                this.onProgress.emit({\n                  originalEvent: event,\n                  progress: this.progress\n                });\n                break;\n              }\n          }\n          this.cd.markForCheck();\n        }, error => {\n          this.uploading = false;\n          this.onError.emit({\n            files: this.files,\n            error: error\n          });\n        });\n      }\n    }\n    /**\n     * Clears the files list.\n     * @group Method\n     */\n    clear() {\n      this.files = [];\n      this.uploadedFileCount = 0;\n      this.onClear.emit();\n      this.clearInputElement();\n      this.cd.markForCheck();\n    }\n    remove(event, index) {\n      this.clearInputElement();\n      this.onRemove.emit({\n        originalEvent: event,\n        file: this.files[index]\n      });\n      this.files.splice(index, 1);\n      this.checkFileLimit();\n    }\n    isFileLimitExceeded() {\n      const isAutoMode = this.auto;\n      const totalFileCount = isAutoMode ? this.files.length : this.files.length + this.uploadedFileCount;\n      if (this.fileLimit && this.fileLimit <= totalFileCount && this.focus) {\n        this.focus = false;\n      }\n      return this.fileLimit && this.fileLimit < totalFileCount;\n    }\n    isChooseDisabled() {\n      if (this.auto) {\n        return this.fileLimit && this.fileLimit <= this.files.length;\n      } else {\n        return this.fileLimit && this.fileLimit <= this.files.length + this.uploadedFileCount;\n      }\n    }\n    checkFileLimit() {\n      this.msgs ??= [];\n      if (this.isFileLimitExceeded()) {\n        this.msgs.push({\n          severity: 'error',\n          summary: this.invalidFileLimitMessageSummary.replace('{0}', this.fileLimit.toString()),\n          detail: this.invalidFileLimitMessageDetail.replace('{0}', this.fileLimit.toString())\n        });\n      }\n    }\n    clearInputElement() {\n      if (this.advancedFileInput && this.advancedFileInput.nativeElement) {\n        this.advancedFileInput.nativeElement.value = '';\n      }\n      if (this.basicFileInput && this.basicFileInput.nativeElement) {\n        this.basicFileInput.nativeElement.value = '';\n      }\n    }\n    clearIEInput() {\n      if (this.advancedFileInput && this.advancedFileInput.nativeElement) {\n        this.duplicateIEEvent = true; //IE11 fix to prevent onFileChange trigger again\n        this.advancedFileInput.nativeElement.value = '';\n      }\n    }\n    hasFiles() {\n      return this.files && this.files.length > 0;\n    }\n    onDragEnter(e) {\n      if (!this.disabled) {\n        e.stopPropagation();\n        e.preventDefault();\n      }\n    }\n    onDragOver(e) {\n      if (!this.disabled) {\n        DomHandler.addClass(this.content?.nativeElement, 'p-fileupload-highlight');\n        this.dragHighlight = true;\n        e.stopPropagation();\n        e.preventDefault();\n      }\n    }\n    onDragLeave(event) {\n      if (!this.disabled) {\n        DomHandler.removeClass(this.content?.nativeElement, 'p-fileupload-highlight');\n      }\n    }\n    onDrop(event) {\n      if (!this.disabled) {\n        DomHandler.removeClass(this.content?.nativeElement, 'p-fileupload-highlight');\n        event.stopPropagation();\n        event.preventDefault();\n        let files = event.dataTransfer ? event.dataTransfer.files : event.target.files;\n        let allowDrop = this.multiple || files && files.length === 1;\n        if (allowDrop) {\n          this.onFileSelect(event);\n        }\n      }\n    }\n    onFocus() {\n      this.focus = true;\n    }\n    onBlur() {\n      this.focus = false;\n    }\n    formatSize(bytes) {\n      const k = 1024;\n      const dm = 3;\n      const sizes = this.getTranslation(TranslationKeys.FILE_SIZE_TYPES);\n      if (bytes === 0) {\n        return `0 ${sizes[0]}`;\n      }\n      const i = Math.floor(Math.log(bytes) / Math.log(k));\n      const formattedSize = (bytes / Math.pow(k, i)).toFixed(dm);\n      return `${formattedSize} ${sizes[i]}`;\n    }\n    onBasicUploaderClick() {\n      if (this.hasFiles()) this.upload();else this.basicFileInput?.nativeElement.click();\n    }\n    onBasicKeydown(event) {\n      switch (event.code) {\n        case 'Space':\n        case 'Enter':\n          this.onBasicUploaderClick();\n          event.preventDefault();\n          break;\n      }\n    }\n    imageError(event) {\n      this.onImageError.emit(event);\n    }\n    getBlockableElement() {\n      return this.el.nativeElement.children[0];\n    }\n    get chooseButtonLabel() {\n      return this.chooseLabel || this.config.getTranslation(TranslationKeys.CHOOSE);\n    }\n    get uploadButtonLabel() {\n      return this.uploadLabel || this.config.getTranslation(TranslationKeys.UPLOAD);\n    }\n    get cancelButtonLabel() {\n      return this.cancelLabel || this.config.getTranslation(TranslationKeys.CANCEL);\n    }\n    ngOnDestroy() {\n      if (this.content && this.content.nativeElement) {\n        if (this.dragOverListener) {\n          this.dragOverListener();\n          this.dragOverListener = null;\n        }\n      }\n      if (this.translationSubscription) {\n        this.translationSubscription.unsubscribe();\n      }\n    }\n    static ɵfac = function FileUpload_Factory(t) {\n      return new (t || FileUpload)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.DomSanitizer), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i3.PrimeNGConfig));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: FileUpload,\n      selectors: [[\"p-fileUpload\"]],\n      contentQueries: function FileUpload_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      viewQuery: function FileUpload_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.advancedFileInput = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.basicFileInput = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.content = _t.first);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        name: \"name\",\n        url: \"url\",\n        method: \"method\",\n        multiple: \"multiple\",\n        accept: \"accept\",\n        disabled: \"disabled\",\n        auto: \"auto\",\n        withCredentials: \"withCredentials\",\n        maxFileSize: \"maxFileSize\",\n        invalidFileSizeMessageSummary: \"invalidFileSizeMessageSummary\",\n        invalidFileSizeMessageDetail: \"invalidFileSizeMessageDetail\",\n        invalidFileTypeMessageSummary: \"invalidFileTypeMessageSummary\",\n        invalidFileTypeMessageDetail: \"invalidFileTypeMessageDetail\",\n        invalidFileLimitMessageDetail: \"invalidFileLimitMessageDetail\",\n        invalidFileLimitMessageSummary: \"invalidFileLimitMessageSummary\",\n        style: \"style\",\n        styleClass: \"styleClass\",\n        previewWidth: \"previewWidth\",\n        chooseLabel: \"chooseLabel\",\n        uploadLabel: \"uploadLabel\",\n        cancelLabel: \"cancelLabel\",\n        chooseIcon: \"chooseIcon\",\n        uploadIcon: \"uploadIcon\",\n        cancelIcon: \"cancelIcon\",\n        showUploadButton: \"showUploadButton\",\n        showCancelButton: \"showCancelButton\",\n        mode: \"mode\",\n        headers: \"headers\",\n        customUpload: \"customUpload\",\n        fileLimit: \"fileLimit\",\n        uploadStyleClass: \"uploadStyleClass\",\n        cancelStyleClass: \"cancelStyleClass\",\n        removeStyleClass: \"removeStyleClass\",\n        chooseStyleClass: \"chooseStyleClass\",\n        files: \"files\"\n      },\n      outputs: {\n        onBeforeUpload: \"onBeforeUpload\",\n        onSend: \"onSend\",\n        onUpload: \"onUpload\",\n        onError: \"onError\",\n        onClear: \"onClear\",\n        onRemove: \"onRemove\",\n        onSelect: \"onSelect\",\n        onProgress: \"onProgress\",\n        uploadHandler: \"uploadHandler\",\n        onImageError: \"onImageError\"\n      },\n      decls: 2,\n      vars: 2,\n      consts: [[3, \"ngClass\", \"ngStyle\", \"class\", 4, \"ngIf\"], [\"class\", \"p-fileupload p-fileupload-basic p-component\", 4, \"ngIf\"], [3, \"ngClass\", \"ngStyle\"], [1, \"p-fileupload-buttonbar\"], [\"pRipple\", \"\", \"tabindex\", \"0\", 1, \"p-button\", \"p-component\", \"p-fileupload-choose\", 3, \"ngClass\", \"focus\", \"blur\", \"click\", \"keydown.enter\"], [\"type\", \"file\", 3, \"multiple\", \"accept\", \"disabled\", \"change\"], [\"advancedfileinput\", \"\"], [3, \"ngClass\", \"class\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"p-button-label\"], [\"type\", \"button\", 3, \"label\", \"disabled\", \"styleClass\", \"onClick\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\"], [1, \"p-fileupload-content\", 3, \"dragenter\", \"dragleave\", \"drop\"], [\"content\", \"\"], [3, \"value\", \"showValue\", 4, \"ngIf\"], [3, \"value\", \"enableService\"], [\"class\", \"p-fileupload-files\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [3, \"ngClass\"], [3, \"styleClass\", 4, \"ngIf\"], [\"class\", \"p-button-icon p-button-icon-left\", 4, \"ngIf\"], [3, \"styleClass\"], [1, \"p-button-icon\", \"p-button-icon-left\"], [\"type\", \"button\", 3, \"label\", \"disabled\", \"styleClass\", \"onClick\"], [\"class\", \"p-button-icon p-button-icon-left\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"p-button-icon\", \"p-button-icon-left\", 3, \"ngClass\"], [3, \"value\", \"showValue\"], [1, \"p-fileupload-files\"], [\"class\", \"p-fileupload-row\", 4, \"ngFor\", \"ngForOf\"], [1, \"p-fileupload-row\"], [3, \"src\", \"width\", \"error\", 4, \"ngIf\"], [1, \"p-fileupload-filename\"], [\"type\", \"button\", \"pButton\", \"\", 1, \"p-button-icon-only\", 3, \"disabled\", \"click\"], [3, \"src\", \"width\", \"error\"], [\"ngFor\", \"\", 3, \"ngForOf\", \"ngForTemplate\"], [1, \"p-fileupload\", \"p-fileupload-basic\", \"p-component\"], [\"tabindex\", \"0\", \"pRipple\", \"\", 3, \"ngClass\", \"ngStyle\", \"click\", \"keydown\"], [4, \"ngIf\", \"ngIfElse\"], [\"chooseSection\", \"\"], [\"class\", \"p-button-label\", 4, \"ngIf\"], [\"type\", \"file\", 3, \"accept\", \"multiple\", \"disabled\", \"change\", \"focus\", \"blur\", 4, \"ngIf\"], [\"class\", \"p-button-icon p-button-icon-left pi\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"p-button-icon\", \"p-button-icon-left\", \"pi\", 3, \"ngClass\"], [\"class\", \"p-button-icon p-button-icon-left pi\", 4, \"ngIf\"], [1, \"p-button-icon\", \"p-button-icon-left\", \"pi\"], [\"type\", \"file\", 3, \"accept\", \"multiple\", \"disabled\", \"change\", \"focus\", \"blur\"], [\"basicfileinput\", \"\"]],\n      template: function FileUpload_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, FileUpload_div_0_Template, 18, 35, \"div\", 0);\n          i0.ɵɵtemplate(1, FileUpload_div_1_Template, 8, 17, \"div\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.mode === \"advanced\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.mode === \"basic\");\n        }\n      },\n      dependencies: function () {\n        return [i4.NgClass, i4.NgForOf, i4.NgIf, i4.NgTemplateOutlet, i4.NgStyle, i5.ButtonDirective, i5.Button, i6.ProgressBar, i7.Messages, i8.Ripple, PlusIcon, UploadIcon, TimesIcon];\n      },\n      styles: [\"@layer primeng{.p-fileupload-content{position:relative}.p-fileupload-row{display:flex;align-items:center}.p-fileupload-row>div{flex:1 1 auto;width:25%}.p-fileupload-row>div:last-child{text-align:right}.p-fileupload-content .p-progressbar{width:100%;position:absolute;top:0;left:0}.p-button.p-fileupload-choose{position:relative;overflow:hidden}.p-button.p-fileupload-choose input[type=file],.p-fileupload-choose.p-fileupload-choose-selected input[type=file]{display:none}.p-fluid .p-fileupload .p-button{width:auto}.p-fileupload-filename{word-break:break-all}}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return FileUpload;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet FileUploadModule = /*#__PURE__*/(() => {\n  class FileUploadModule {\n    static ɵfac = function FileUploadModule_Factory(t) {\n      return new (t || FileUploadModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: FileUploadModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, HttpClientModule, SharedModule, ButtonModule, ProgressBarModule, MessagesModule, RippleModule, PlusIcon, UploadIcon, TimesIcon, SharedModule, ButtonModule, ProgressBarModule, MessagesModule]\n    });\n  }\n  return FileUploadModule;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { FileUpload, FileUploadModule };\n//# sourceMappingURL=primeng-fileupload.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}