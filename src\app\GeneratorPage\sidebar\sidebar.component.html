<p-sidebar 
  [(visible)]="visible" 
  position="right" 
  [modal]="true"
  [dismissible]="true"
  styleClass="custom-sidebar"
  (onHide)="onHide()">
  
  <ng-template pTemplate="header">
    <div class="sidebar-header">
      <h3 class="sidebar-title">القائمة الرئيسية</h3>
    </div>
  </ng-template>

  <div class="sidebar-content">
    <div class="menu-items">
      <div 
        *ngFor="let item of menuItems" 
        class="menu-item"
        (click)="item.command()">
        <i [class]="item.icon" class="menu-icon"></i>
        <span class="menu-label">{{ item.label }}</span>
        <i class="pi pi-chevron-left menu-arrow"></i>
      </div>
    </div>
    
    <div class="sidebar-footer">
      <p class="app-info">
        <i class="pi pi-info-circle"></i>
        مولد مقاطع القرآن الكريم
      </p>
      <p class="version">الإصدار 2.0</p>
    </div>
  </div>
</p-sidebar>
