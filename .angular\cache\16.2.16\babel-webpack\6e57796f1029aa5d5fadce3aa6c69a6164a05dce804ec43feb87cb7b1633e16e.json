{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/\\u062A\\u0637\\u0628\\u064A\\u0642 \\u0646\\u0627\\u0634\\u0631 \\u0627\\u064A\\u0627\\u062A \\u0642\\u0626\\u0627\\u0646\\u064A\\u0629/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../Services/gemini.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"primeng/inputtext\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"primeng/dropdown\";\nimport * as i8 from \"primeng/dialog\";\nimport * as i9 from \"primeng/panel\";\nimport * as i10 from \"primeng/slider\";\nimport * as i11 from \"primeng/fileupload\";\nconst _c0 = function (a0, a1, a2) {\n  return {\n    \"success-message\": a0,\n    \"warning-message\": a1,\n    \"error-message\": a2\n  };\n};\nfunction VideoSettingsComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"p\", 38);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(2, _c0, ctx_r0.connectionTestResult.includes(\"\\u2705\"), ctx_r0.connectionTestResult.includes(\"\\u26A0\\uFE0F\") || ctx_r0.connectionTestResult.includes(\"\\uD83D\\uDCB0\"), ctx_r0.connectionTestResult.includes(\"\\u274C\")));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.connectionTestResult, \" \");\n  }\n}\nfunction VideoSettingsComponent_ng_template_16_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 43);\n    i0.ɵɵtext(1, \"\\u0645\\u062F\\u0641\\u0648\\u0639\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction VideoSettingsComponent_ng_template_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"div\", 40);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 41);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, VideoSettingsComponent_ng_template_16_span_5_Template, 2, 0, \"span\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(option_r4.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(option_r4.description);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", option_r4.isPaid);\n  }\n}\nfunction VideoSettingsComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"label\");\n    i0.ɵɵtext(2, \"\\u0631\\u0641\\u0639 \\u062E\\u0637 \\u0645\\u062E\\u0635\\u0635\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p-fileUpload\", 44);\n    i0.ɵɵlistener(\"onSelect\", function VideoSettingsComponent_div_23_Template_p_fileUpload_onSelect_3_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onFontUpload($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"maxFileSize\", 5000000);\n  }\n}\nfunction VideoSettingsComponent_ng_template_57_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"p-button\", 46);\n    i0.ɵɵlistener(\"onClick\", function VideoSettingsComponent_ng_template_57_Template_p_button_onClick_1_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.resetToDefaults());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"p-button\", 47);\n    i0.ɵɵlistener(\"onClick\", function VideoSettingsComponent_ng_template_57_Template_p_button_onClick_2_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onHide());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p-button\", 48);\n    i0.ɵɵlistener(\"onClick\", function VideoSettingsComponent_ng_template_57_Template_p_button_onClick_3_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.onSave());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !ctx_r3.settingsForm.valid);\n  }\n}\nconst _c1 = function () {\n  return {\n    width: \"90vw\",\n    height: \"90vh\"\n  };\n};\nexport let VideoSettingsComponent = /*#__PURE__*/(() => {\n  class VideoSettingsComponent {\n    constructor(fb, geminiService) {\n      this.fb = fb;\n      this.geminiService = geminiService;\n      this.visible = false;\n      this.visibleChange = new EventEmitter();\n      this.settingsSaved = new EventEmitter();\n      this.geminiModels = [];\n      this.isTestingConnection = false;\n      this.connectionTestResult = null;\n      this.fontFamilies = [{\n        label: 'Al-Quran Al-Kareem',\n        value: 'Al-QuranAlKareem'\n      }, {\n        label: 'Amiri',\n        value: 'Amiri'\n      }, {\n        label: 'Noto Sans Arabic',\n        value: 'NotoSansArabic'\n      }, {\n        label: 'Cairo',\n        value: 'Cairo'\n      }, {\n        label: 'خط مخصص',\n        value: 'custom'\n      }];\n      this.videoQualities = [{\n        label: '1080p (Full HD)',\n        value: '1080p'\n      }, {\n        label: '720p (HD)',\n        value: '720p'\n      }, {\n        label: '480p (SD)',\n        value: '480p'\n      }];\n      this.audioQualities = [{\n        label: 'عالية (320 kbps)',\n        value: 'high'\n      }, {\n        label: 'متوسطة (192 kbps)',\n        value: 'medium'\n      }, {\n        label: 'منخفضة (128 kbps)',\n        value: 'low'\n      }];\n      this.subtitlePositions = [{\n        label: 'أسفل',\n        value: 'bottom'\n      }, {\n        label: 'وسط',\n        value: 'center'\n      }, {\n        label: 'أعلى',\n        value: 'top'\n      }];\n    }\n    ngOnInit() {\n      this.loadGeminiModels();\n      this.initializeForm();\n    }\n    loadGeminiModels() {\n      const models = this.geminiService.getAvailableModels();\n      this.geminiModels = models.map(model => ({\n        label: model.name,\n        value: model.id,\n        description: model.description,\n        isPaid: model.isPaid\n      }));\n    }\n    initializeForm() {\n      this.settingsForm = this.fb.group({\n        geminiApiKey: ['', [Validators.required]],\n        geminiModel: ['gemini-2.5-flash', [Validators.required]],\n        fontSize: [18, [Validators.required, Validators.min(12), Validators.max(48)]],\n        fontFamily: ['Al-QuranAlKareem', [Validators.required]],\n        videoQuality: ['1080p', [Validators.required]],\n        audioQuality: ['high', [Validators.required]],\n        subtitlePosition: ['bottom', [Validators.required]],\n        backgroundColor: ['#000000'],\n        textColor: ['#FFFFFF'],\n        outlineColor: ['#000000'],\n        enableShadow: [true],\n        shadowColor: ['#000000']\n      });\n    }\n    onSave() {\n      if (this.settingsForm.valid) {\n        const settings = this.settingsForm.value;\n        this.settingsSaved.emit(settings);\n        this.onHide();\n      }\n    }\n    onHide() {\n      this.visible = false;\n      this.visibleChange.emit(false);\n    }\n    onFontUpload(event) {\n      const file = event.files[0];\n      if (file) {\n        // Handle custom font upload\n        console.log('Custom font uploaded:', file.name);\n      }\n    }\n    resetToDefaults() {\n      this.initializeForm();\n    }\n    testGeminiConnection() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        const apiKey = _this.settingsForm.get('geminiApiKey')?.value;\n        const model = _this.settingsForm.get('geminiModel')?.value;\n        if (!apiKey) {\n          _this.connectionTestResult = 'يرجى إدخال مفتاح API أولاً';\n          return;\n        }\n        _this.isTestingConnection = true;\n        _this.connectionTestResult = null;\n        try {\n          const isConnected = yield _this.geminiService.testConnection(apiKey, model);\n          if (isConnected) {\n            _this.connectionTestResult = 'تم الاتصال بنجاح! ✅';\n            // Check model availability\n            const availability = yield _this.geminiService.checkModelAvailability(model, apiKey);\n            if (!availability.available) {\n              _this.connectionTestResult = `تحذير: ${availability.error} ⚠️`;\n            }\n          } else {\n            _this.connectionTestResult = 'فشل في الاتصال. يرجى التحقق من مفتاح API ❌';\n          }\n        } catch (error) {\n          _this.connectionTestResult = `خطأ: ${error.message || 'فشل في الاتصال'} ❌`;\n        } finally {\n          _this.isTestingConnection = false;\n        }\n      })();\n    }\n    onModelChange() {\n      const selectedModel = this.settingsForm.get('geminiModel')?.value;\n      const modelInfo = this.geminiService.getModelInfo(selectedModel);\n      if (modelInfo?.isPaid) {\n        this.connectionTestResult = 'تنبيه: هذا النموذج مدفوع وقد يتطلب اشتراك 💰';\n      } else {\n        this.connectionTestResult = null;\n      }\n    }\n    static {\n      this.ɵfac = function VideoSettingsComponent_Factory(t) {\n        return new (t || VideoSettingsComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.GeminiService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: VideoSettingsComponent,\n        selectors: [[\"app-video-settings\"]],\n        inputs: {\n          visible: \"visible\"\n        },\n        outputs: {\n          visibleChange: \"visibleChange\",\n          settingsSaved: \"settingsSaved\"\n        },\n        decls: 58,\n        vars: 25,\n        consts: [[\"header\", \"\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\", \"styleClass\", \"video-settings-dialog\", 3, \"visible\", \"modal\", \"closable\", \"resizable\", \"maximizable\", \"visibleChange\", \"onHide\"], [1, \"settings-container\"], [1, \"settings-form\", 3, \"formGroup\"], [\"header\", \"\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0630\\u0643\\u0627\\u0621 \\u0627\\u0644\\u0627\\u0635\\u0637\\u0646\\u0627\\u0639\\u064A\", 3, \"toggleable\"], [1, \"form-section\"], [1, \"form-row\"], [\"for\", \"geminiApiKey\"], [1, \"api-key-container\"], [\"id\", \"geminiApiKey\", \"type\", \"password\", \"pInputText\", \"\", \"formControlName\", \"geminiApiKey\", \"placeholder\", \"\\u0623\\u062F\\u062E\\u0644 \\u0645\\u0641\\u062A\\u0627\\u062D API \\u0627\\u0644\\u062E\\u0627\\u0635 \\u0628\\u0643\", 1, \"w-100\"], [\"label\", \"\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631 \\u0627\\u0644\\u0627\\u062A\\u0635\\u0627\\u0644\", \"icon\", \"pi pi-check\", \"severity\", \"secondary\", \"size\", \"small\", \"loadingIcon\", \"pi pi-spin pi-spinner\", 3, \"loading\", \"onClick\"], [\"class\", \"connection-result\", 4, \"ngIf\"], [\"for\", \"geminiModel\"], [\"id\", \"geminiModel\", \"formControlName\", \"geminiModel\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"placeholder\", \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0646\\u0645\\u0648\\u0630\\u062C\", 1, \"w-100\", 3, \"options\", \"onChange\"], [\"pTemplate\", \"item\"], [\"header\", \"\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u062E\\u0637\", 3, \"toggleable\"], [\"for\", \"fontFamily\"], [\"id\", \"fontFamily\", \"formControlName\", \"fontFamily\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"placeholder\", \"\\u0627\\u062E\\u062A\\u0631 \\u0646\\u0648\\u0639 \\u0627\\u0644\\u062E\\u0637\", 1, \"w-100\", 3, \"options\"], [\"class\", \"form-row\", 4, \"ngIf\"], [\"for\", \"fontSize\"], [\"id\", \"fontSize\", \"formControlName\", \"fontSize\", 1, \"w-100\", 3, \"min\", \"max\", \"step\"], [\"header\", \"\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u062C\\u0648\\u062F\\u0629 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\", 3, \"toggleable\"], [\"for\", \"videoQuality\"], [\"id\", \"videoQuality\", \"formControlName\", \"videoQuality\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"placeholder\", \"\\u0627\\u062E\\u062A\\u0631 \\u062C\\u0648\\u062F\\u0629 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\", 1, \"w-100\", 3, \"options\"], [\"for\", \"audioQuality\"], [\"id\", \"audioQuality\", \"formControlName\", \"audioQuality\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"placeholder\", \"\\u0627\\u062E\\u062A\\u0631 \\u062C\\u0648\\u062F\\u0629 \\u0627\\u0644\\u0635\\u0648\\u062A\", 1, \"w-100\", 3, \"options\"], [\"header\", \"\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u062A\\u0631\\u062C\\u0645\\u0629\", 3, \"toggleable\"], [\"for\", \"subtitlePosition\"], [\"id\", \"subtitlePosition\", \"formControlName\", \"subtitlePosition\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"placeholder\", \"\\u0627\\u062E\\u062A\\u0631 \\u0645\\u0648\\u0636\\u0639 \\u0627\\u0644\\u062A\\u0631\\u062C\\u0645\\u0629\", 1, \"w-100\", 3, \"options\"], [1, \"color-settings\"], [1, \"color-row\"], [\"for\", \"textColor\"], [\"id\", \"textColor\", \"type\", \"color\", \"formControlName\", \"textColor\", 1, \"color-picker\"], [\"for\", \"outlineColor\"], [\"id\", \"outlineColor\", \"type\", \"color\", \"formControlName\", \"outlineColor\", 1, \"color-picker\"], [\"for\", \"backgroundColor\"], [\"id\", \"backgroundColor\", \"type\", \"color\", \"formControlName\", \"backgroundColor\", 1, \"color-picker\"], [\"pTemplate\", \"footer\"], [1, \"connection-result\"], [3, \"ngClass\"], [1, \"model-option\"], [1, \"model-name\"], [1, \"model-description\"], [\"class\", \"model-badge\", 4, \"ngIf\"], [1, \"model-badge\"], [\"mode\", \"basic\", \"accept\", \".ttf,.otf,.woff,.woff2\", \"chooseLabel\", \"\\u0627\\u062E\\u062A\\u0631 \\u0645\\u0644\\u0641 \\u0627\\u0644\\u062E\\u0637\", 3, \"maxFileSize\", \"onSelect\"], [1, \"dialog-footer\"], [\"label\", \"\\u0625\\u0639\\u0627\\u062F\\u0629 \\u062A\\u0639\\u064A\\u064A\\u0646\", \"icon\", \"pi pi-refresh\", \"severity\", \"secondary\", 3, \"onClick\"], [\"label\", \"\\u0625\\u0644\\u063A\\u0627\\u0621\", \"icon\", \"pi pi-times\", \"severity\", \"secondary\", 3, \"onClick\"], [\"label\", \"\\u062D\\u0641\\u0638 \\u0627\\u0644\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A\", \"icon\", \"pi pi-check\", \"severity\", \"primary\", 3, \"disabled\", \"onClick\"]],\n        template: function VideoSettingsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"p-dialog\", 0);\n            i0.ɵɵlistener(\"visibleChange\", function VideoSettingsComponent_Template_p_dialog_visibleChange_0_listener($event) {\n              return ctx.visible = $event;\n            })(\"onHide\", function VideoSettingsComponent_Template_p_dialog_onHide_0_listener() {\n              return ctx.onHide();\n            });\n            i0.ɵɵelementStart(1, \"div\", 1)(2, \"form\", 2)(3, \"p-panel\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"label\", 6);\n            i0.ɵɵtext(7, \"\\u0645\\u0641\\u062A\\u0627\\u062D Gemini API *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"div\", 7);\n            i0.ɵɵelement(9, \"input\", 8);\n            i0.ɵɵelementStart(10, \"p-button\", 9);\n            i0.ɵɵlistener(\"onClick\", function VideoSettingsComponent_Template_p_button_onClick_10_listener() {\n              return ctx.testGeminiConnection();\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(11, VideoSettingsComponent_div_11_Template, 3, 6, \"div\", 10);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"div\", 5)(13, \"label\", 11);\n            i0.ɵɵtext(14, \"\\u0646\\u0645\\u0648\\u0630\\u062C Gemini *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"p-dropdown\", 12);\n            i0.ɵɵlistener(\"onChange\", function VideoSettingsComponent_Template_p_dropdown_onChange_15_listener() {\n              return ctx.onModelChange();\n            });\n            i0.ɵɵtemplate(16, VideoSettingsComponent_ng_template_16_Template, 6, 3, \"ng-template\", 13);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(17, \"p-panel\", 14)(18, \"div\", 4)(19, \"div\", 5)(20, \"label\", 15);\n            i0.ɵɵtext(21, \"\\u0646\\u0648\\u0639 \\u0627\\u0644\\u062E\\u0637 *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(22, \"p-dropdown\", 16);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(23, VideoSettingsComponent_div_23_Template, 4, 1, \"div\", 17);\n            i0.ɵɵelementStart(24, \"div\", 5)(25, \"label\", 18);\n            i0.ɵɵtext(26);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(27, \"p-slider\", 19);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(28, \"p-panel\", 20)(29, \"div\", 4)(30, \"div\", 5)(31, \"label\", 21);\n            i0.ɵɵtext(32, \"\\u062C\\u0648\\u062F\\u0629 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648 *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(33, \"p-dropdown\", 22);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"div\", 5)(35, \"label\", 23);\n            i0.ɵɵtext(36, \"\\u062C\\u0648\\u062F\\u0629 \\u0627\\u0644\\u0635\\u0648\\u062A *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(37, \"p-dropdown\", 24);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(38, \"p-panel\", 25)(39, \"div\", 4)(40, \"div\", 5)(41, \"label\", 26);\n            i0.ɵɵtext(42, \"\\u0645\\u0648\\u0636\\u0639 \\u0627\\u0644\\u062A\\u0631\\u062C\\u0645\\u0629 *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(43, \"p-dropdown\", 27);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(44, \"div\", 28)(45, \"div\", 29)(46, \"label\", 30);\n            i0.ɵɵtext(47, \"\\u0644\\u0648\\u0646 \\u0627\\u0644\\u0646\\u0635\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(48, \"input\", 31);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(49, \"div\", 29)(50, \"label\", 32);\n            i0.ɵɵtext(51, \"\\u0644\\u0648\\u0646 \\u0627\\u0644\\u062D\\u062F\\u0648\\u062F\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(52, \"input\", 33);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(53, \"div\", 29)(54, \"label\", 34);\n            i0.ɵɵtext(55, \"\\u0644\\u0648\\u0646 \\u0627\\u0644\\u062E\\u0644\\u0641\\u064A\\u0629\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(56, \"input\", 35);\n            i0.ɵɵelementEnd()()()()()();\n            i0.ɵɵtemplate(57, VideoSettingsComponent_ng_template_57_Template, 4, 1, \"ng-template\", 36);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            let tmp_13_0;\n            let tmp_14_0;\n            i0.ɵɵstyleMap(i0.ɵɵpureFunction0(24, _c1));\n            i0.ɵɵproperty(\"visible\", ctx.visible)(\"modal\", true)(\"closable\", true)(\"resizable\", false)(\"maximizable\", true);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"formGroup\", ctx.settingsForm);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"toggleable\", true);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"loading\", ctx.isTestingConnection);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.connectionTestResult);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"options\", ctx.geminiModels);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"toggleable\", true);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"options\", ctx.fontFamilies);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_13_0 = ctx.settingsForm.get(\"fontFamily\")) == null ? null : tmp_13_0.value) === \"custom\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\"\\u062D\\u062C\\u0645 \\u0627\\u0644\\u062E\\u0637: \", (tmp_14_0 = ctx.settingsForm.get(\"fontSize\")) == null ? null : tmp_14_0.value, \"\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"min\", 12)(\"max\", 48)(\"step\", 1);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"toggleable\", true);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"options\", ctx.videoQualities);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"options\", ctx.audioQualities);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"toggleable\", true);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"options\", ctx.subtitlePositions);\n          }\n        },\n        dependencies: [i3.NgClass, i3.NgIf, i4.InputText, i5.PrimeTemplate, i6.Button, i7.Dropdown, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i8.Dialog, i9.Panel, i10.Slider, i11.FileUpload],\n        styles: [\".video-settings-dialog .p-dialog-header{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);color:#fff;border-radius:8px 8px 0 0}  .video-settings-dialog .p-dialog-content{padding:0;max-height:70vh;overflow-y:auto}  .video-settings-dialog .p-dialog-footer{background:#f8f9fa;border-top:1px solid #dee2e6}.settings-container[_ngcontent-%COMP%]{padding:1.5rem}.settings-form[_ngcontent-%COMP%]   .p-panel[_ngcontent-%COMP%]{margin-bottom:1.5rem;border:1px solid #e9ecef;border-radius:8px}.settings-form[_ngcontent-%COMP%]   .p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]{background:#f8f9fa;border-bottom:1px solid #e9ecef;font-weight:600;color:#495057}.settings-form[_ngcontent-%COMP%]   .p-panel[_ngcontent-%COMP%]   .p-panel-content[_ngcontent-%COMP%]{padding:1.5rem}.form-section[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]{margin-bottom:1.5rem}.form-section[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:block;margin-bottom:.5rem;font-weight:500;color:#495057}.form-section[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%], .form-section[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .p-dropdown[_ngcontent-%COMP%], .form-section[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .p-slider[_ngcontent-%COMP%]{width:100%}.api-key-container[_ngcontent-%COMP%]{display:flex;gap:.5rem;align-items:center}.api-key-container[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{flex:1}.connection-result[_ngcontent-%COMP%]{margin-top:.5rem}.connection-result[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{padding:.75rem;border-radius:6px;margin:0;font-weight:500}.connection-result[_ngcontent-%COMP%]   p.success-message[_ngcontent-%COMP%]{background:#d4edda;color:#155724;border:1px solid #c3e6cb}.connection-result[_ngcontent-%COMP%]   p.warning-message[_ngcontent-%COMP%]{background:#fff3cd;color:#856404;border:1px solid #ffeaa7}.connection-result[_ngcontent-%COMP%]   p.error-message[_ngcontent-%COMP%]{background:#f8d7da;color:#721c24;border:1px solid #f5c6cb}.model-option[_ngcontent-%COMP%]{padding:.5rem 0}.model-option[_ngcontent-%COMP%]   .model-name[_ngcontent-%COMP%]{font-weight:600;color:#495057;margin-bottom:.25rem}.model-option[_ngcontent-%COMP%]   .model-description[_ngcontent-%COMP%]{font-size:.85rem;color:#6c757d;margin-bottom:.25rem}.model-option[_ngcontent-%COMP%]   .model-badge[_ngcontent-%COMP%]{display:inline-block;background:#ffc107;color:#212529;padding:.2rem .5rem;border-radius:12px;font-size:.75rem;font-weight:600}.color-settings[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:1rem;margin-top:1rem}.color-settings[_ngcontent-%COMP%]   .color-row[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}.color-settings[_ngcontent-%COMP%]   .color-row[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{margin-bottom:0;flex:1}.color-settings[_ngcontent-%COMP%]   .color-row[_ngcontent-%COMP%]   .color-picker[_ngcontent-%COMP%]{width:50px;height:40px;border:1px solid #ced4da;border-radius:4px;cursor:pointer}.color-settings[_ngcontent-%COMP%]   .color-row[_ngcontent-%COMP%]   .color-picker[_ngcontent-%COMP%]:hover{border-color:#80bdff}.dialog-footer[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;gap:.5rem;padding:1rem}@media (max-width: 768px){  .video-settings-dialog .p-dialog{width:95vw!important;height:95vh!important;margin:0}.color-settings[_ngcontent-%COMP%]{grid-template-columns:1fr}.api-key-container[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch}.dialog-footer[_ngcontent-%COMP%]{flex-direction:column}.dialog-footer[_ngcontent-%COMP%]   .p-button[_ngcontent-%COMP%]{width:100%}}\"]\n      });\n    }\n  }\n  return VideoSettingsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}