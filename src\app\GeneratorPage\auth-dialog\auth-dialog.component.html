<p-dialog 
  header="تسجيل الدخول إلى YouTube" 
  [(visible)]="visible" 
  [modal]="true"
  [closable]="true"
  [style]="{width: '450px'}"
  (onHide)="closeDialog()">
  
  <div class="auth-content">
    <div *ngIf="authStatus$ | async as authStatus">
      
      <!-- حالة عدم تسجيل الدخول -->
      <div *ngIf="!authStatus.isAuthenticated" class="text-center">
        <div class="mb-4">
          <i class="pi pi-youtube" style="font-size: 3rem; color: #FF0000;"></i>
        </div>
        
        <h4 class="mb-3">تسجيل الدخول إلى YouTube</h4>
        <p class="text-muted mb-4">
          يرجى تسجيل الدخول إلى حساب Google الخاص بك لتتمكن من نشر الفيديوهات على قناتك في YouTube
        </p>
        
        <div *ngIf="errorMessage" class="alert alert-danger mb-3">
          {{ errorMessage }}
        </div>
        
        <p-button 
          label="تسجيل الدخول بـ Google"
          icon="pi pi-google"
          [loading]="isSigningIn"
          loadingIcon="pi pi-spinner pi-spin"
          styleClass="p-button-danger w-100"
          (onClick)="signIn()">
        </p-button>
        
        <div class="mt-3">
          <small class="text-muted">
            <i class="pi pi-info-circle me-1"></i>
            سيتم طلب إذن للوصول إلى قناة YouTube الخاصة بك لرفع الفيديوهات
          </small>
        </div>
      </div>
      
      <!-- حالة تسجيل الدخول بنجاح -->
      <div *ngIf="authStatus.isAuthenticated && authStatus.user" class="text-center">
        <div class="mb-3">
          <img 
            [src]="authStatus.user.picture" 
            [alt]="authStatus.user.name"
            class="rounded-circle"
            style="width: 80px; height: 80px;">
        </div>
        
        <h5 class="mb-2">مرحباً، {{ authStatus.user.name }}</h5>
        <p class="text-muted mb-3">{{ authStatus.user.email }}</p>
        
        <div class="alert alert-success mb-3">
          <i class="pi pi-check-circle me-2"></i>
          تم تسجيل الدخول بنجاح! يمكنك الآن نشر الفيديوهات على YouTube
        </div>
        
        <div class="d-flex gap-2">
          <p-button 
            label="تسجيل الخروج"
            icon="pi pi-sign-out"
            styleClass="p-button-secondary flex-fill"
            (onClick)="signOut()">
          </p-button>
          
          <p-button 
            label="متابعة"
            icon="pi pi-check"
            styleClass="p-button-success flex-fill"
            (onClick)="closeDialog()">
          </p-button>
        </div>
      </div>
      
    </div>
  </div>
</p-dialog>
